---
title: Domo DDX Evidence Dashboard
---

# Domo Dataset to DuckDB Workflow

Welcome to your Evidence dashboard powered by Domo datasets! This application allows you to select Domo datasets and load them into DuckDB for analysis.

## Workflow Picker

<Details title='How to use the Workflow Picker'>
  1. Select a Domo dataset from the dropdown below
  2. Configure your data loading preferences
  3. Click "Load Dataset" to import the data into DuckDB
  4. Use the data in your Evidence visualizations
</Details>

<!-- Workflow Picker Component will be inserted here -->
<div id="workflow-picker-container">
  <h3>Select Domo Dataset</h3>

  <div class="workflow-step">
    <label for="dataset-selector">Available Datasets:</label>
    <select id="dataset-selector" class="dataset-dropdown">
      <option value="">Select a dataset...</option>
      <!-- Options will be populated dynamically -->
    </select>
  </div>

  <div class="workflow-step" id="dataset-preview" style="display: none;">
    <h4>Dataset Preview</h4>
    <div id="preview-content">
      <!-- Preview will be shown here -->
    </div>
  </div>

  <div class="workflow-step">
    <h4>Loading Options</h4>
    <div class="options-grid">
      <div class="option-item">
        <label for="table-name">Table Name in DuckDB:</label>
        <input type="text" id="table-name" placeholder="my_dataset" />
      </div>
      <div class="option-item">
        <label for="refresh-mode">Refresh Mode:</label>
        <select id="refresh-mode">
          <option value="replace">Replace existing data</option>
          <option value="append">Append to existing data</option>
        </select>
      </div>
    </div>
  </div>

  <div class="workflow-actions">
    <button id="load-dataset-btn" class="primary-btn" disabled>Load Dataset into DuckDB</button>
    <button id="preview-btn" class="secondary-btn" disabled>Preview Data</button>
  </div>

  <div id="loading-status" style="display: none;">
    <div class="loading-spinner"></div>
    <p>Loading dataset into DuckDB...</p>
  </div>
</div>

## Current Data Analysis

<Details title='Sample Analysis with Loaded Data'>
  Once you load a dataset, you can create SQL queries and visualizations like the example below.
</Details>

```sql categories
  select
      category
  from needful_things.orders
  group by category
```

<Dropdown data={categories} name=category value=category>
    <DropdownOption value="%" valueLabel="All Categories"/>
</Dropdown>

<Dropdown name=year>
    <DropdownOption value=% valueLabel="All Years"/>
    <DropdownOption value=2019/>
    <DropdownOption value=2020/>
    <DropdownOption value=2021/>
</Dropdown>

```sql orders_by_category
  select
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${inputs.category.value}'
  and date_part('year', order_datetime) like '${inputs.year.value}'
  group by all
  order by sales_usd desc
```

<BarChart
    data={orders_by_category}
    title="Sales by Month, {inputs.category.label}"
    x=month
    y=sales_usd
    series=category
/>

## What's Next?
- Load your Domo datasets using the workflow picker above
- Create custom SQL queries against your loaded data
- Build interactive dashboards with Evidence components
- Deploy your DDX app to Domo

<style>
  .workflow-step {
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #f9f9f9;
  }

  .dataset-dropdown, #table-name, #refresh-mode {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
  }

  .options-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
  }

  .option-item label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
  }

  .workflow-actions {
    margin: 20px 0;
    text-align: center;
  }

  .primary-btn, .secondary-btn {
    padding: 10px 20px;
    margin: 0 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
  }

  .primary-btn {
    background-color: #2563eb;
    color: white;
  }

  .primary-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }

  .secondary-btn {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
  }

  .loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 2s linear infinite;
    margin: 0 auto;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  #loading-status {
    text-align: center;
    padding: 20px;
  }
</style>
