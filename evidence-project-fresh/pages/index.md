---
title: Evidence Dashboard for Domo
---

<script>
  let isDomoEnvironment = false;

  // Check Domo environment
  if (typeof window !== 'undefined') {
    isDomoEnvironment = typeof window.domo !== 'undefined';
  }
</script>

# Evidence Dashboard for Domo DDX

{#if isDomoEnvironment}
  <div class="domo-banner">
    <h3>🚀 Running in Domo DDX Environment</h3>
    <p>This Evidence dashboard is connected to your Domo instance and ready to analyze your data!</p>
  </div>
{:else}
  <div class="dev-banner">
    <h3>🛠️ Development Mode</h3>
    <p>This is a preview of your Evidence dashboard. Deploy to Domo DDX to access real datasets.</p>
  </div>
{/if}

Welcome to your Evidence dashboard! This application combines the power of Evidence's analytics framework with Domo's data platform, allowing you to create interactive dashboards and reports using your Domo datasets.

## Quick Start

<div class="quick-start-grid">
  <div class="quick-start-card">
    <h4>📊 Load Domo Data</h4>
    <p>Use the workflow picker to select and load Domo datasets into DuckDB for analysis.</p>
    <a href="/workflow" class="btn-primary">Open Workflow Picker</a>
  </div>

  <div class="quick-start-card">
    <h4>📈 View Sample Analysis</h4>
    <p>See how Evidence works with the sample data below.</p>
    <a href="#sample-analysis" class="btn-secondary">View Sample</a>
  </div>

  <div class="quick-start-card">
    <h4>📚 Learn More</h4>
    <p>Explore Evidence documentation and best practices.</p>
    <a href="https://docs.evidence.dev" target="_blank" class="btn-secondary">Documentation</a>
  </div>
</div>



## Sample Analysis

<div id="sample-analysis"></div>

<Details title='How Evidence Works with Your Data'>
  This example shows how Evidence creates interactive dashboards from SQL queries. Once you load your Domo datasets, you can create similar analyses with your own data.
</Details>

```sql categories
  select
      category
  from needful_things.orders
  group by category
```

<Dropdown data={categories} name=category value=category>
    <DropdownOption value="%" valueLabel="All Categories"/>
</Dropdown>

<Dropdown name=year>
    <DropdownOption value=% valueLabel="All Years"/>
    <DropdownOption value=2019/>
    <DropdownOption value=2020/>
    <DropdownOption value=2021/>
</Dropdown>

```sql orders_by_category
  select
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${inputs.category.value}'
  and date_part('year', order_datetime) like '${inputs.year.value}'
  group by all
  order by sales_usd desc
```

<BarChart
    data={orders_by_category}
    title="Sales by Month, {inputs.category.label}"
    x=month
    y=sales_usd
    series=category
/>

## What's Next?

<div class="next-steps">
  <div class="step">
    <h4>1. Load Your Data</h4>
    <p>Use the <a href="/workflow">workflow picker</a> to select and load Domo datasets into DuckDB</p>
  </div>

  <div class="step">
    <h4>2. Create Queries</h4>
    <p>Write SQL queries against your loaded data using Evidence's query blocks</p>
  </div>

  <div class="step">
    <h4>3. Build Visualizations</h4>
    <p>Use Evidence components like BarChart, LineChart, and DataTable to create interactive dashboards</p>
  </div>

  <div class="step">
    <h4>4. Deploy to Domo</h4>
    <p>Package your Evidence app and deploy it to Domo DDX for your team to use</p>
  </div>
</div>



<style>
  .domo-banner {
    margin: 20px 0;
    padding: 20px;
    border: 2px solid #00d4aa;
    border-radius: 12px;
    background: linear-gradient(135deg, #f0fdf9 0%, #ecfdf5 100%);
    color: #065f46;
    text-align: center;
  }

  .domo-banner h3 {
    margin: 0 0 10px 0;
    color: #047857;
  }

  .dev-banner {
    margin: 20px 0;
    padding: 20px;
    border: 2px solid #f59e0b;
    border-radius: 12px;
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    color: #92400e;
    text-align: center;
  }

  .dev-banner h3 {
    margin: 0 0 10px 0;
    color: #d97706;
  }

  .quick-start-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 30px 0;
  }

  .quick-start-card {
    padding: 25px;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    background-color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s, box-shadow 0.2s;
  }

  .quick-start-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  }

  .quick-start-card h4 {
    margin: 0 0 15px 0;
    color: #1f2937;
    font-size: 18px;
  }

  .quick-start-card p {
    margin: 0 0 20px 0;
    color: #6b7280;
    line-height: 1.5;
  }

  .btn-primary, .btn-secondary {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .btn-primary {
    background-color: #2563eb;
    color: white;
  }

  .btn-primary:hover {
    background-color: #1d4ed8;
    text-decoration: none;
    color: white;
  }

  .btn-secondary {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
  }

  .btn-secondary:hover {
    background-color: #e5e7eb;
    text-decoration: none;
    color: #374151;
  }



  .next-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 30px 0;
  }

  .step {
    padding: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background-color: #fafafa;
  }

  .step h4 {
    margin: 0 0 10px 0;
    color: #1f2937;
    font-size: 16px;
  }

  .step p {
    margin: 0;
    color: #6b7280;
    line-height: 1.5;
  }

  .step a {
    color: #2563eb;
    text-decoration: none;
  }

  .step a:hover {
    text-decoration: underline;
  }
</style>
