---
title: Domo Dataset Workflow
---

<script>
  import DomoWorkflowPicker from '../src/components/DomoWorkflowPicker.svelte';
  
  let loadedDatasets = [];
  
  function handleDatasetLoaded(event) {
    console.log('Dataset loaded:', event);
    loadedDatasets = [...loadedDatasets, event];
  }
</script>

# Domo Dataset to DuckDB Workflow

This page allows you to select and load Domo datasets into DuckDB for analysis in Evidence.

## How it Works

1. **Select a Dataset**: Choose from available Domo datasets in your instance
2. **Preview Data**: Review the dataset schema and sample data
3. **Configure Loading**: Set table name and refresh mode
4. **Load into DuckDB**: Import the data for use in Evidence queries

<DomoWorkflowPicker 
  onDatasetLoaded={handleDatasetLoaded}
  showPreview={true}
  allowCustomTableName={true}
/>

## Loaded Datasets

{#if loadedDatasets.length > 0}
  <div class="loaded-datasets">
    <h3>Successfully Loaded Datasets</h3>
    {#each loadedDatasets as dataset}
      <div class="dataset-card">
        <h4>{dataset.datasetName}</h4>
        <p><strong>Table Name:</strong> {dataset.tableName}</p>
        <p><strong>Rows:</strong> {dataset.rowCount.toLocaleString()}</p>
        <p><strong>Dataset ID:</strong> {dataset.datasetId}</p>
      </div>
    {/each}
  </div>
{:else}
  <div class="no-datasets">
    <p>No datasets loaded yet. Use the workflow picker above to load your first dataset.</p>
  </div>
{/if}

## Using Your Data

Once you've loaded datasets, you can use them in Evidence pages with SQL queries:

```sql my_analysis
SELECT * FROM your_table_name LIMIT 10
```

### Example Queries

Here are some example queries you can run on your loaded datasets:

**Basic Data Exploration:**
```sql
-- Get row count and basic stats
SELECT 
  COUNT(*) as total_rows,
  COUNT(DISTINCT column_name) as unique_values
FROM your_table_name;
```

**Time Series Analysis:**
```sql
-- Aggregate by date (if you have date columns)
SELECT 
  DATE_TRUNC('month', date_column) as month,
  SUM(numeric_column) as total
FROM your_table_name
GROUP BY month
ORDER BY month;
```

**Category Analysis:**
```sql
-- Group by categorical columns
SELECT 
  category_column,
  COUNT(*) as count,
  AVG(numeric_column) as average
FROM your_table_name
GROUP BY category_column
ORDER BY count DESC;
```

## Next Steps

1. **Create Visualizations**: Use Evidence components like `<BarChart>`, `<LineChart>`, etc.
2. **Add Interactivity**: Use `<Dropdown>` and other input components
3. **Build Dashboards**: Create multiple pages with different analyses
4. **Deploy to Domo**: Package your Evidence app for Domo DDX

## Troubleshooting

### Common Issues

**Dataset Not Loading:**
- Check your Domo permissions
- Verify the dataset exists and is accessible
- Try refreshing the page

**Table Name Conflicts:**
- Use unique table names
- Choose "Replace existing data" to overwrite

**Performance Issues:**
- Large datasets may take time to load
- Consider filtering data in Domo before loading
- Use appropriate data types for better performance

### Getting Help

- Check the browser console for error messages
- Verify your Domo DDX environment is properly configured
- Contact your Domo administrator for dataset access issues

<style>
  .loaded-datasets {
    margin: 30px 0;
    padding: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background-color: #f9fafb;
  }
  
  .dataset-card {
    margin: 15px 0;
    padding: 15px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background-color: white;
  }
  
  .dataset-card h4 {
    margin: 0 0 10px 0;
    color: #1f2937;
  }
  
  .dataset-card p {
    margin: 5px 0;
    color: #6b7280;
  }
  
  .no-datasets {
    margin: 30px 0;
    padding: 40px;
    text-align: center;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    color: #6b7280;
  }
  
  .no-datasets p {
    margin: 0;
    font-style: italic;
  }
</style>
