import{s as ge,v as $,d as c,b as d,i as N,e as m,h as I,j as b,Z as Le,m as v,_ as Oe,C as Qe,L as be,p as _e,l as le,G as V,k as P,n as B,w as Q,x as U,y as k,z as ze,F as ye,$ as et,r as Ce,a0 as tt,a1 as lt,q as Re,t as pe,Q as ee,a2 as it,I as Ae,D as nt,a3 as rt}from"./scheduler.C5eBzNnH.js";import{S as Ie,i as ve,d as se,t as H,f as ie,h as $e,a as w,m as oe,b as ae,e as ue,g as de,c as he,j as st}from"./index.BSd9q3aW.js";import{g as Ze,m as te,M as ot,e as ne,N as je,O as at,R as ut,S as Ee,P as De,U as ft,V as Ue}from"./VennDiagram.svelte_svelte_type_style_lang.CJe6lW-S.js";import{p as ct}from"./stores.C41LEeiH.js";const Wt=typeof window<"u"?window:typeof globalThis<"u"?globalThis:global;function dt(n){let e,t,l;return{c(){e=v("span"),t=Oe("svg"),l=Oe("path"),this.h()},l(i){e=I(i,"SPAN",{"aria-expanded":!0,class:!0});var r=b(e);t=Le(r,"svg",{viewBox:!0,width:!0,height:!0,class:!0});var s=b(t);l=Le(s,"path",{fill:!0,"fill-rule":!0,d:!0}),b(l).forEach(c),s.forEach(c),r.forEach(c),this.h()},h(){d(l,"fill",n[3]),d(l,"fill-rule","evenodd"),d(l,"d","M6.22 3.22a.75.75 0 011.06 0l4.25 4.25a.75.75 0 010 1.06l-4.25 4.25a.75.75 0 01-1.06-1.06L9.94 8 6.22 4.28a.75.75 0 010-1.06z"),d(t,"viewBox","0 0 16 16"),d(t,"width",n[1]),d(t,"height",n[1]),d(t,"class","svelte-lqleyo"),d(e,"aria-expanded",n[0]),d(e,"class","svelte-lqleyo")},m(i,r){N(i,e,r),m(e,t),m(t,l)},p(i,[r]){r&8&&d(l,"fill",i[3]),r&2&&d(t,"width",i[1]),r&2&&d(t,"height",i[1]),r&1&&d(e,"aria-expanded",i[0])},i:$,o:$,d(i){i&&c(e)}}}function ht(n,e,t){let l,i,r=$,s=()=>(r(),r=Qe(l,E=>t(3,i=E)),l);n.$$.on_destroy.push(()=>r());const{resolveColor:o}=Ze();let{toggled:a=!1}=e,{color:u="base-content"}=e,{size:f=10}=e;return n.$$set=E=>{"toggled"in E&&t(0,a=E.toggled),"color"in E&&t(4,u=E.color),"size"in E&&t(1,f=E.size)},n.$$.update=()=>{n.$$.dirty&16&&s(t(2,l=o(u)))},[a,f,l,i,u]}class Je extends Ie{constructor(e){super(),ve(this,e,ht,dt,ge,{toggled:0,color:4,size:1})}}function ke(n,e,t){const l=n.slice();return l[12]=e[t],l[14]=t,l}function we(n,e,t){const l=n.slice();return l[15]=e[t],l[17]=t,l}function Me(n,e,t){const l=n.slice();return l[15]=e[t],l}function Pe(n,e,t){const l=n.slice();return l[15]=e[t],l}function Be(n){let e,t=n[15].id+"",l,i,r,s;return{c(){e=v("th"),l=k(t),this.h()},l(o){e=I(o,"TH",{class:!0,style:!0,evidencetype:!0,evidencetypefidelity:!0});var a=b(e);l=U(a,t),a.forEach(c),this.h()},h(){var o,a;d(e,"class",i="py-0 px-2 font-medium "+n[15].type+" svelte-ghf30y"),V(e,"width",n[6]+"%"),d(e,"evidencetype",r=((o=n[15].evidenceColumnType)==null?void 0:o.evidenceType)||"unavailable"),d(e,"evidencetypefidelity",s=((a=n[15].evidenceColumnType)==null?void 0:a.typeFidelity)||"unavailable")},m(o,a){N(o,e,a),m(e,l)},p(o,a){var u,f;a&8&&t!==(t=o[15].id+"")&&Q(l,t),a&8&&i!==(i="py-0 px-2 font-medium "+o[15].type+" svelte-ghf30y")&&d(e,"class",i),a&64&&V(e,"width",o[6]+"%"),a&8&&r!==(r=((u=o[15].evidenceColumnType)==null?void 0:u.evidenceType)||"unavailable")&&d(e,"evidencetype",r),a&8&&s!==(s=((f=o[15].evidenceColumnType)==null?void 0:f.typeFidelity)||"unavailable")&&d(e,"evidencetypefidelity",s)},d(o){o&&c(e)}}}function Fe(n){let e,t=n[15].type+"",l,i,r,s;return{c(){e=v("th"),l=k(t),this.h()},l(o){e=I(o,"TH",{class:!0,style:!0,evidencetype:!0,evidencetypefidelity:!0});var a=b(e);l=U(a,t),a.forEach(c),this.h()},h(){var o,a;d(e,"class",i=n[15].type+" type-indicator text-base-content-muted font-normal py-0 px-2 svelte-ghf30y"),V(e,"width",n[6]+"%"),d(e,"evidencetype",r=((o=n[15].evidenceColumnType)==null?void 0:o.evidenceType)||"unavailable"),d(e,"evidencetypefidelity",s=((a=n[15].evidenceColumnType)==null?void 0:a.typeFidelity)||"unavailable")},m(o,a){N(o,e,a),m(e,l)},p(o,a){var u,f;a&8&&t!==(t=o[15].type+"")&&Q(l,t),a&8&&i!==(i=o[15].type+" type-indicator text-base-content-muted font-normal py-0 px-2 svelte-ghf30y")&&d(e,"class",i),a&64&&V(e,"width",o[6]+"%"),a&8&&r!==(r=((u=o[15].evidenceColumnType)==null?void 0:u.evidenceType)||"unavailable")&&d(e,"evidencetype",r),a&8&&s!==(s=((f=o[15].evidenceColumnType)==null?void 0:f.typeFidelity)||"unavailable")&&d(e,"evidencetypefidelity",s)},d(o){o&&c(e)}}}function Et(n){let e=(n[2]+n[14]+1).toLocaleString()+"",t;return{c(){t=k(e)},l(l){t=U(l,e)},m(l,i){N(l,t,i)},p(l,i){i&4&&e!==(e=(l[2]+l[14]+1).toLocaleString()+"")&&Q(t,e)},d(l){l&&c(t)}}}function _t(n){let e=(n[2]+n[14]+1).toLocaleString()+"",t;return{c(){t=k(e)},l(l){t=U(l,e)},m(l,i){N(l,t,i)},p(l,i){i&4&&e!==(e=(l[2]+l[14]+1).toLocaleString()+"")&&Q(t,e)},d(l){l&&c(t)}}}function Tt(n){let e,t=(n[12][n[15].id]||"Ø")+"",l;return{c(){e=v("td"),l=k(t),this.h()},l(i){e=I(i,"TD",{class:!0,style:!0});var r=b(e);l=U(r,t),r.forEach(c),this.h()},h(){d(e,"class","other svelte-ghf30y"),V(e,"width",n[6]+"%")},m(i,r){N(i,e,r),m(e,l)},p(i,r){r&40&&t!==(t=(i[12][i[15].id]||"Ø")+"")&&Q(l,t),r&64&&V(e,"width",i[6]+"%")},d(i){i&&c(e)}}}function mt(n){let e,t,l=(n[12][n[15].id]??"Ø")+"",i,r;return{c(){e=v("td"),t=v("div"),i=k(l),this.h()},l(s){e=I(s,"TD",{class:!0,style:!0,title:!0});var o=b(e);t=I(o,"DIV",{class:!0});var a=b(t);i=U(a,l),a.forEach(c),o.forEach(c),this.h()},h(){d(t,"class","svelte-ghf30y"),d(e,"class","boolean svelte-ghf30y"),V(e,"width",n[6]+"%"),d(e,"title",r=n[12][n[15].id])},m(s,o){N(s,e,o),m(e,t),m(t,i)},p(s,o){o&40&&l!==(l=(s[12][s[15].id]??"Ø")+"")&&Q(i,l),o&64&&V(e,"width",s[6]+"%"),o&40&&r!==(r=s[12][s[15].id])&&d(e,"title",r)},d(s){s&&c(e)}}}function pt(n){let e,t,l=(n[12][n[15].id]||"Ø")+"",i,r;return{c(){e=v("td"),t=v("div"),i=k(l),this.h()},l(s){e=I(s,"TD",{class:!0,style:!0,title:!0});var o=b(e);t=I(o,"DIV",{class:!0});var a=b(t);i=U(a,l),a.forEach(c),o.forEach(c),this.h()},h(){d(t,"class","svelte-ghf30y"),d(e,"class","string svelte-ghf30y"),V(e,"width",n[6]+"%"),d(e,"title",r=n[12][n[15].id])},m(s,o){N(s,e,o),m(e,t),m(t,i)},p(s,o){o&40&&l!==(l=(s[12][s[15].id]||"Ø")+"")&&Q(i,l),o&64&&V(e,"width",s[6]+"%"),o&40&&r!==(r=s[12][s[15].id])&&d(e,"title",r)},d(s){s&&c(e)}}}function gt(n){let e,t,l=Ee(n[12][n[15].id],n[3][n[17]].format,n[3][n[17]].columnUnitSummary)+"",i,r;return{c(){e=v("td"),t=v("div"),i=k(l),this.h()},l(s){e=I(s,"TD",{class:!0,style:!0,title:!0});var o=b(e);t=I(o,"DIV",{class:!0});var a=b(t);i=U(a,l),a.forEach(c),o.forEach(c),this.h()},h(){d(t,"class","svelte-ghf30y"),d(e,"class","string svelte-ghf30y"),V(e,"width",n[6]+"%"),d(e,"title",r=Ee(n[12][n[15].id],n[3][n[17]].format,n[3][n[17]].columnUnitSummary))},m(s,o){N(s,e,o),m(e,t),m(t,i)},p(s,o){o&40&&l!==(l=Ee(s[12][s[15].id],s[3][s[17]].format,s[3][s[17]].columnUnitSummary)+"")&&Q(i,l),o&64&&V(e,"width",s[6]+"%"),o&40&&r!==(r=Ee(s[12][s[15].id],s[3][s[17]].format,s[3][s[17]].columnUnitSummary))&&d(e,"title",r)},d(s){s&&c(e)}}}function It(n){let e,t=Ee(n[12][n[15].id],n[3][n[17]].format,n[3][n[17]].columnUnitSummary)+"",l;return{c(){e=v("td"),l=k(t),this.h()},l(i){e=I(i,"TD",{class:!0,style:!0});var r=b(e);l=U(r,t),r.forEach(c),this.h()},h(){d(e,"class","number svelte-ghf30y"),V(e,"width",n[6]+"%")},m(i,r){N(i,e,r),m(e,l)},p(i,r){r&40&&t!==(t=Ee(i[12][i[15].id],i[3][i[17]].format,i[3][i[17]].columnUnitSummary)+"")&&Q(l,t),r&64&&V(e,"width",i[6]+"%")},d(i){i&&c(e)}}}function vt(n){let e,t="Ø",l,i;return{c(){e=v("td"),l=k(t),this.h()},l(r){e=I(r,"TD",{class:!0,style:!0});var s=b(e);l=U(s,t),s.forEach(c),this.h()},h(){d(e,"class",i="text-base-content-muted "+n[3][n[17]].type+" svelte-ghf30y"),V(e,"width",n[6]+"%")},m(r,s){N(r,e,s),m(e,l)},p(r,s){s&8&&i!==(i="text-base-content-muted "+r[3][r[17]].type+" svelte-ghf30y")&&d(e,"class",i),s&64&&V(e,"width",r[6]+"%")},d(r){r&&c(e)}}}function Ge(n){let e;function t(r,s){return r[12][r[15].id]==null?vt:r[3][r[17]].type==="number"?It:r[3][r[17]].type==="date"?gt:r[3][r[17]].type==="string"?pt:r[3][r[17]].type==="boolean"?mt:Tt}let l=t(n),i=l(n);return{c(){i.c(),e=Ce()},l(r){i.l(r),e=Ce()},m(r,s){i.m(r,s),N(r,e,s)},p(r,s){l===(l=t(r))&&i?i.p(r,s):(i.d(1),i=l(r),i&&(i.c(),i.m(e.parentNode,e)))},d(r){r&&c(e),i.d(r)}}}function He(n){let e,t,l,i;function r(f,E){return f[14]===0?_t:Et}let o=r(n)(n),a=te(n[3]),u=[];for(let f=0;f<a.length;f+=1)u[f]=Ge(we(n,a,f));return{c(){e=v("tr"),t=v("td"),o.c(),l=B();for(let f=0;f<u.length;f+=1)u[f].c();i=B(),this.h()},l(f){e=I(f,"TR",{});var E=b(e);t=I(E,"TD",{class:!0,style:!0});var _=b(t);o.l(_),_.forEach(c),l=P(E);for(let R=0;R<u.length;R+=1)u[R].l(E);i=P(E),E.forEach(c),this.h()},h(){d(t,"class","index text-base-content-muted svelte-ghf30y"),V(t,"width","10%")},m(f,E){N(f,e,E),m(e,t),o.m(t,null),m(e,l);for(let _=0;_<u.length;_+=1)u[_]&&u[_].m(e,null);m(e,i)},p(f,E){if(o.p(f,E),E&104){a=te(f[3]);let _;for(_=0;_<a.length;_+=1){const R=we(f,a,_);u[_]?u[_].p(R,E):(u[_]=Ge(R),u[_].c(),u[_].m(e,i))}for(;_<u.length;_+=1)u[_].d(1);u.length=a.length}},d(f){f&&c(e),o.d(),be(u,f)}}}function Ve(n){let e,t,l,i,r=(n[2]+re).toLocaleString()+"",s,o,a=(n[4]+re).toLocaleString()+"",u,f,E;return{c(){e=v("div"),t=v("input"),l=B(),i=v("span"),s=k(r),o=k(" of "),u=k(a),this.h()},l(_){e=I(_,"DIV",{class:!0});var R=b(e);t=I(R,"INPUT",{type:!0,max:!0,step:!0,class:!0}),l=P(R),i=I(R,"SPAN",{class:!0});var L=b(i);s=U(L,r),o=U(L," of "),u=U(L,a),L.forEach(c),R.forEach(c),this.h()},h(){d(t,"type","range"),d(t,"max",n[4]),d(t,"step","1"),d(t,"class","slider bg-info/30 hover:bg-info/40 transition-colors svelte-ghf30y"),d(i,"class","text-xs svelte-ghf30y"),d(e,"class","pagination svelte-ghf30y")},m(_,R){N(_,e,R),m(e,t),ye(t,n[2]),m(e,l),m(e,i),m(i,s),m(i,o),m(i,u),f||(E=[le(t,"change",n[9]),le(t,"input",n[9]),le(t,"input",n[7])],f=!0)},p(_,R){R&16&&d(t,"max",_[4]),R&4&&ye(t,_[2]),R&4&&r!==(r=(_[2]+re).toLocaleString()+"")&&Q(s,r),R&16&&a!==(a=(_[4]+re).toLocaleString()+"")&&Q(u,a)},d(_){_&&c(e),f=!1,ze(E)}}}function bt(n){let e,t,l,i,r,s,o,a,u,f,E,_,R,L,h,y,F,A,D,x,Y,Z,O,p,C,S,W=te(n[3]),G=[];for(let T=0;T<W.length;T+=1)G[T]=Be(Pe(n,W,T));let fe=te(n[3]),K=[];for(let T=0;T<fe.length;T+=1)K[T]=Fe(Me(n,fe,T));let ce=te(n[5]),X=[];for(let T=0;T<ce.length;T+=1)X[T]=He(ke(n,ce,T));let z=n[4]>0&&Ve(n);return Z=new ot({props:{class:"download-button",data:n[1],queryID:n[0],display:!0}}),{c(){e=v("div"),t=v("div"),l=v("table"),i=v("thead"),r=v("tr"),s=v("th"),o=B();for(let T=0;T<G.length;T+=1)G[T].c();a=B(),u=v("tr"),f=B(),E=v("tr"),_=v("th"),R=B();for(let T=0;T<K.length;T+=1)K[T].c();L=B(),h=v("tr"),y=B(),F=v("tbody");for(let T=0;T<X.length;T+=1)X[T].c();D=B(),z&&z.c(),x=B(),Y=v("div"),ue(Z.$$.fragment),this.h()},l(T){e=I(T,"DIV",{class:!0});var q=b(e);t=I(q,"DIV",{class:!0});var M=b(t);l=I(M,"TABLE",{class:!0});var g=b(l);i=I(g,"THEAD",{});var j=b(i);r=I(j,"TR",{});var Te=b(r);s=I(Te,"TH",{class:!0,style:!0}),b(s).forEach(c),o=P(Te);for(let J=0;J<G.length;J+=1)G[J].l(Te);a=P(Te),Te.forEach(c),u=I(j,"TR",{}),b(u).forEach(c),f=P(j),E=I(j,"TR",{class:!0});var me=b(E);_=I(me,"TH",{class:!0,style:!0}),b(_).forEach(c),R=P(me);for(let J=0;J<K.length;J+=1)K[J].l(me);L=P(me),me.forEach(c),h=I(j,"TR",{}),b(h).forEach(c),j.forEach(c),y=P(g),F=I(g,"TBODY",{});var Ne=b(F);for(let J=0;J<X.length;J+=1)X[J].l(Ne);Ne.forEach(c),g.forEach(c),M.forEach(c),D=P(q),z&&z.l(q),x=P(q),Y=I(q,"DIV",{class:!0});var Se=b(Y);ae(Z.$$.fragment,Se),Se.forEach(c),q.forEach(c),this.h()},h(){d(s,"class","py-0 px-2 font-medium index text-base-content-muted svelte-ghf30y"),V(s,"width","10%"),d(_,"class","py-0 px-2 index type-indicator text-base-content-muted font-normal svelte-ghf30y"),V(_,"width","10%"),d(E,"class","type-indicator svelte-ghf30y"),d(l,"class","text-xs svelte-ghf30y"),d(t,"class","scrollbox pretty-scrollbar svelte-ghf30y"),d(Y,"class","footer svelte-ghf30y"),d(e,"class","results-pane py-1 svelte-ghf30y")},m(T,q){N(T,e,q),m(e,t),m(t,l),m(l,i),m(i,r),m(r,s),m(r,o);for(let M=0;M<G.length;M+=1)G[M]&&G[M].m(r,null);m(r,a),m(i,u),m(i,f),m(i,E),m(E,_),m(E,R);for(let M=0;M<K.length;M+=1)K[M]&&K[M].m(E,null);m(E,L),m(i,h),m(l,y),m(l,F);for(let M=0;M<X.length;M+=1)X[M]&&X[M].m(F,null);m(e,D),z&&z.m(e,null),m(e,x),m(e,Y),oe(Z,Y,null),p=!0,C||(S=le(F,"wheel",n[8]),C=!0)},p(T,[q]){if(q&72){W=te(T[3]);let g;for(g=0;g<W.length;g+=1){const j=Pe(T,W,g);G[g]?G[g].p(j,q):(G[g]=Be(j),G[g].c(),G[g].m(r,a))}for(;g<G.length;g+=1)G[g].d(1);G.length=W.length}if(q&72){fe=te(T[3]);let g;for(g=0;g<fe.length;g+=1){const j=Me(T,fe,g);K[g]?K[g].p(j,q):(K[g]=Fe(j),K[g].c(),K[g].m(E,L))}for(;g<K.length;g+=1)K[g].d(1);K.length=fe.length}if(q&108){ce=te(T[5]);let g;for(g=0;g<ce.length;g+=1){const j=ke(T,ce,g);X[g]?X[g].p(j,q):(X[g]=He(j),X[g].c(),X[g].m(F,null))}for(;g<X.length;g+=1)X[g].d(1);X.length=ce.length}T[4]>0?z?z.p(T,q):(z=Ve(T),z.c(),z.m(e,x)):z&&(z.d(1),z=null);const M={};q&2&&(M.data=T[1]),q&1&&(M.queryID=T[0]),Z.$set(M)},i(T){p||(T&&(A||_e(()=>{A=$e(l,je,{}),A.start()})),w(Z.$$.fragment,T),T&&_e(()=>{p&&(O||(O=ie(e,ne,{},!0)),O.run(1))}),p=!0)},o(T){H(Z.$$.fragment,T),T&&(O||(O=ie(e,ne,{},!1)),O.run(0)),p=!1},d(T){T&&c(e),be(G,T),be(K,T),be(X,T),z&&z.d(),se(Z),T&&O&&O.end(),C=!1,S()}}}let re=5;function Rt(n,e,t){let l,i,r,s,{queryID:o}=e,{data:a}=e,u=0,f;function E(){f=a.slice(u,u+re),t(5,s=f)}const _=ut(h=>{t(2,u=Math.min(Math.max(0,u+Math.floor(h.deltaY/Math.abs(h.deltaY))),r)),E()},60);function R(h){if(Math.abs(h.deltaX)>=Math.abs(h.deltaY))return;const y=h.deltaY<0&&u===0,F=h.deltaY>0&&u===r;y||F||(h.preventDefault(),_(h))}function L(){u=et(this.value),t(2,u)}return n.$$set=h=>{"queryID"in h&&t(0,o=h.queryID),"data"in h&&t(1,a=h.data)},n.$$.update=()=>{n.$$.dirty&2&&t(3,l=at(a,"array")),n.$$.dirty&8&&t(6,i=90/(l.length+1)),n.$$.dirty&2&&t(4,r=Math.max(a.length-re,0)),n.$$.dirty&6&&t(5,s=a.slice(u,u+re))},[o,a,u,l,r,s,i,E,R,L]}class Nt extends Ie{constructor(e){super(),ve(this,e,Rt,bt,ge,{queryID:0,data:1})}}const Ye={comment:{pattern:/(^|[^\\])(?:\/\*[\s\S]*?\*\/|(?:--|\/\/|#).*)/,lookbehind:!0},variable:[{pattern:/@(["'`])(?:\\[\s\S]|(?!\1)[^\\])+\1/,greedy:!0},/@[\w.$]+/],string:{pattern:/(^|[^@\\])("|')(?:\\[\s\S]|(?!\2)[^\\]|\2\2)*\2/,greedy:!0,lookbehind:!0},identifier:{pattern:/(^|[^@\\])`(?:\\[\s\S]|[^`\\]|``)*`/,greedy:!0,lookbehind:!0,inside:{punctuation:/^`|`$/}},function:/\b(?:AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\s*\()/i,keyword:/\b(?:ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:COL|_INSERT)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|KEYS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:ING|S)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\b/i,boolean:/\b(?:FALSE|NULL|TRUE)\b/i,number:/\b0x[\da-f]+\b|\b\d+(?:\.\d*)?|\B\.\d+\b/i,operator:/[-+*/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\b(?:AND|BETWEEN|DIV|ILIKE|IN|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\b/i,punctuation:/[;[\]()`,.]/};function St(n){let e,t,l,i,r=De.highlight(n[0],Ye)+"",s;return{c(){e=v("pre"),t=k("  "),l=v("code"),i=new lt(!1),s=k(`
`),this.h()},l(o){e=I(o,"PRE",{class:!0});var a=b(e);t=U(a,"  "),l=I(a,"CODE",{class:!0});var u=b(l);i=tt(u,!1),u.forEach(c),s=U(a,`
`),a.forEach(c),this.h()},h(){i.a=null,d(l,"class","language-sql svelte-re3fhx"),d(e,"class","text-xs max-h-56 overflow-auto pretty-scrollbar")},m(o,a){N(o,e,a),m(e,t),m(e,l),i.m(r,l),m(e,s)},p(o,[a]){a&1&&r!==(r=De.highlight(o[0],Ye)+"")&&i.p(r)},i:$,o:$,d(o){o&&c(e)}}}function Lt(n,e,t){let{code:l=""}=e;return n.$$set=i=>{"code"in i&&t(0,l=i.code)},[l]}class xe extends Ie{constructor(e){super(),ve(this,e,Lt,St,ge,{code:0})}}function Ot(n){let e,t="Compiled",l,i,r="Written",s,o;return{c(){e=v("button"),e.textContent=t,l=B(),i=v("button"),i.textContent=r,this.h()},l(a){e=I(a,"BUTTON",{class:!0,"data-svelte-h":!0}),Re(e)!=="svelte-1vzm9jy"&&(e.textContent=t),l=P(a),i=I(a,"BUTTON",{class:!0,"data-svelte-h":!0}),Re(i)!=="svelte-qu81ez"&&(i.textContent=r),this.h()},h(){d(e,"class","off svelte-ska6l4"),d(i,"class","text-info bg-info/10 border border-info svelte-ska6l4")},m(a,u){N(a,e,u),N(a,l,u),N(a,i,u),s||(o=le(e,"click",n[1]),s=!0)},p:$,d(a){a&&(c(e),c(l),c(i)),s=!1,o()}}}function yt(n){let e,t="Compiled",l,i,r="Written",s,o;return{c(){e=v("button"),e.textContent=t,l=B(),i=v("button"),i.textContent=r,this.h()},l(a){e=I(a,"BUTTON",{class:!0,"data-svelte-h":!0}),Re(e)!=="svelte-wrfleh"&&(e.textContent=t),l=P(a),i=I(a,"BUTTON",{class:!0,"data-svelte-h":!0}),Re(i)!=="svelte-v36xno"&&(i.textContent=r),this.h()},h(){d(e,"class","text-info bg-info/10 border border-info svelte-ska6l4"),d(i,"class","off svelte-ska6l4")},m(a,u){N(a,e,u),N(a,l,u),N(a,i,u),s||(o=le(i,"click",n[1]),s=!0)},p:$,d(a){a&&(c(e),c(l),c(i)),s=!1,o()}}}function Ct(n){let e,t,l;function i(o,a){return o[0]?yt:Ot}let r=i(n),s=r(n);return{c(){e=v("div"),s.c(),this.h()},l(o){e=I(o,"DIV",{class:!0});var a=b(e);s.l(a),a.forEach(c),this.h()},h(){d(e,"class","toggle svelte-ska6l4")},m(o,a){N(o,e,a),s.m(e,null),l=!0},p(o,[a]){r===(r=i(o))&&s?s.p(o,a):(s.d(1),s=r(o),s&&(s.c(),s.m(e,null)))},i(o){l||(o&&_e(()=>{l&&(t||(t=ie(e,ne,{},!0)),t.run(1))}),l=!0)},o(o){o&&(t||(t=ie(e,ne,{},!1)),t.run(0)),l=!1},d(o){o&&c(e),s.d(),o&&t&&t.end()}}}function At(n,e,t){let{showCompiled:l}=e;const i=function(){t(0,l=!l)};return n.$$set=r=>{"showCompiled"in r&&t(0,l=r.showCompiled)},[l,i]}class Dt extends Ie{constructor(e){super(),ve(this,e,At,Ct,ge,{showCompiled:0})}}function qe(n){let e,t,l,i,r,s,o,a,u,f,E,_,R,L,h,y,F;i=new Je({props:{toggled:n[10]}});let A=n[10]&&n[4]&&We(n),D=n[10]&&Ke(n);const x=[Bt,Pt,Mt,wt],Y=[];function Z(p,C){return p[6]?0:p[8]?1:p[2].loading?2:3}E=Z(n),_=Y[E]=x[E](n);let O=n[8]>0&&!n[6]&&n[9]&&Xe(n);return{c(){e=v("div"),t=v("div"),l=v("button"),ue(i.$$.fragment),r=B(),s=k(n[0]),o=B(),A&&A.c(),a=B(),D&&D.c(),u=B(),f=v("button"),_.c(),R=B(),O&&O.c(),this.h()},l(p){e=I(p,"DIV",{class:!0});var C=b(e);t=I(C,"DIV",{class:!0});var S=b(t);l=I(S,"BUTTON",{type:!0,"aria-label":!0,class:!0});var W=b(l);ae(i.$$.fragment,W),r=P(W),s=U(W,n[0]),W.forEach(c),o=P(S),A&&A.l(S),a=P(S),D&&D.l(S),S.forEach(c),u=P(C),f=I(C,"BUTTON",{type:!0,"aria-label":!0,class:!0});var G=b(f);_.l(G),G.forEach(c),R=P(C),O&&O.l(C),C.forEach(c),this.h()},h(){d(l,"type","button"),d(l,"aria-label","show-sql"),d(l,"class","title svelte-1ursthx"),d(t,"class","container-a svelte-1ursthx"),d(f,"type","button"),d(f,"aria-label","view-query"),d(f,"class",it("status-bar")+" svelte-1ursthx"),ee(f,"error",n[6]),ee(f,"success",!n[6]),ee(f,"open",n[9]),ee(f,"closed",!n[9]),d(e,"class","scrollbox my-3 svelte-1ursthx")},m(p,C){N(p,e,C),m(e,t),m(t,l),oe(i,l,null),m(l,r),m(l,s),m(t,o),A&&A.m(t,null),m(t,a),D&&D.m(t,null),m(e,u),m(e,f),Y[E].m(f,null),m(e,R),O&&O.m(e,null),h=!0,y||(F=[le(l,"click",n[15]),le(f,"click",n[16])],y=!0)},p(p,C){const S={};C&1024&&(S.toggled=p[10]),i.$set(S),(!h||C&1)&&Q(s,p[0]),p[10]&&p[4]?A?(A.p(p,C),C&1040&&w(A,1)):(A=We(p),A.c(),w(A,1),A.m(t,a)):A&&(de(),H(A,1,1,()=>{A=null}),he()),p[10]?D?(D.p(p,C),C&1024&&w(D,1)):(D=Ke(p),D.c(),w(D,1),D.m(t,null)):D&&(de(),H(D,1,1,()=>{D=null}),he());let W=E;E=Z(p),E===W?Y[E].p(p,C):(de(),H(Y[W],1,1,()=>{Y[W]=null}),he(),_=Y[E],_?_.p(p,C):(_=Y[E]=x[E](p),_.c()),w(_,1),_.m(f,null)),(!h||C&64)&&ee(f,"error",p[6]),(!h||C&64)&&ee(f,"success",!p[6]),(!h||C&512)&&ee(f,"open",p[9]),(!h||C&512)&&ee(f,"closed",!p[9]),p[8]>0&&!p[6]&&p[9]?O?(O.p(p,C),C&832&&w(O,1)):(O=Xe(p),O.c(),w(O,1),O.m(e,null)):O&&(de(),H(O,1,1,()=>{O=null}),he())},i(p){h||(w(i.$$.fragment,p),w(A),w(D),w(_),w(O),p&&_e(()=>{h&&(L||(L=ie(e,ne,{},!0)),L.run(1))}),h=!0)},o(p){H(i.$$.fragment,p),H(A),H(D),H(_),H(O),p&&(L||(L=ie(e,ne,{},!1)),L.run(0)),h=!1},d(p){p&&c(e),se(i),A&&A.d(),D&&D.d(),Y[E].d(),O&&O.d(),p&&L&&L.end(),y=!1,ze(F)}}}function We(n){let e,t,l;function i(s){n[20](s)}let r={};return n[5]!==void 0&&(r.showCompiled=n[5]),e=new Dt({props:r}),nt.push(()=>st(e,"showCompiled",i)),{c(){ue(e.$$.fragment)},l(s){ae(e.$$.fragment,s)},m(s,o){oe(e,s,o),l=!0},p(s,o){const a={};!t&&o&32&&(t=!0,a.showCompiled=s[5],rt(()=>t=!1)),e.$set(a)},i(s){l||(w(e.$$.fragment,s),l=!0)},o(s){H(e.$$.fragment,s),l=!1},d(s){se(e,s)}}}function Ke(n){let e,t,l,i,r;const s=[kt,Ut],o=[];function a(u,f){return u[5]?0:1}return t=a(n),l=o[t]=s[t](n),{c(){e=v("div"),l.c(),this.h()},l(u){e=I(u,"DIV",{class:!0});var f=b(e);l.l(f),f.forEach(c),this.h()},h(){d(e,"class","code-container svelte-1ursthx")},m(u,f){N(u,e,f),o[t].m(e,null),r=!0},p(u,f){let E=t;t=a(u),t===E?o[t].p(u,f):(de(),H(o[E],1,1,()=>{o[E]=null}),he(),l=o[t],l?l.p(u,f):(l=o[t]=s[t](u),l.c()),w(l,1),l.m(e,null))},i(u){r||(w(l),u&&_e(()=>{r&&(i||(i=ie(e,ne,{},!0)),i.run(1))}),r=!0)},o(u){H(l),u&&(i||(i=ie(e,ne,{},!1)),i.run(0)),r=!1},d(u){u&&c(e),o[t].d(),u&&i&&i.end()}}}function Ut(n){let e,t;return e=new xe({props:{code:n[3]}}),{c(){ue(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,i){oe(e,l,i),t=!0},p(l,i){const r={};i&8&&(r.code=l[3]),e.$set(r)},i(l){t||(w(e.$$.fragment,l),t=!0)},o(l){H(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function kt(n){let e,t;return e=new xe({props:{code:n[1].originalText}}),{c(){ue(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,i){oe(e,l,i),t=!0},p(l,i){const r={};i&2&&(r.code=l[1].originalText),e.$set(r)},i(l){t||(w(e.$$.fragment,l),t=!0)},o(l){H(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function wt(n){let e;return{c(){e=k("ran successfully but no data was returned")},l(t){e=U(t,"ran successfully but no data was returned")},m(t,l){N(t,e,l)},p:$,i:$,o:$,d(t){t&&c(e)}}}function Mt(n){let e;return{c(){e=k("loading...")},l(t){e=U(t,"loading...")},m(t,l){N(t,e,l)},p:$,i:$,o:$,d(t){t&&c(e)}}}function Pt(n){let e,t,l=n[8].toLocaleString()+"",i,r,s=n[8]>1?"records":"record",o,a,u=n[7].toLocaleString()+"",f,E,_=n[7]>1?"properties":"property",R,L;return e=new Je({props:{toggled:n[9],color:n[12].colors.info}}),{c(){ue(e.$$.fragment),t=B(),i=k(l),r=B(),o=k(s),a=k(" with "),f=k(u),E=B(),R=k(_)},l(h){ae(e.$$.fragment,h),t=P(h),i=U(h,l),r=P(h),o=U(h,s),a=U(h," with "),f=U(h,u),E=P(h),R=U(h,_)},m(h,y){oe(e,h,y),N(h,t,y),N(h,i,y),N(h,r,y),N(h,o,y),N(h,a,y),N(h,f,y),N(h,E,y),N(h,R,y),L=!0},p(h,y){const F={};y&512&&(F.toggled=h[9]),y&4096&&(F.color=h[12].colors.info),e.$set(F),(!L||y&256)&&l!==(l=h[8].toLocaleString()+"")&&Q(i,l),(!L||y&256)&&s!==(s=h[8]>1?"records":"record")&&Q(o,s),(!L||y&128)&&u!==(u=h[7].toLocaleString()+"")&&Q(f,u),(!L||y&128)&&_!==(_=h[7]>1?"properties":"property")&&Q(R,_)},i(h){L||(w(e.$$.fragment,h),L=!0)},o(h){H(e.$$.fragment,h),L=!1},d(h){h&&(c(t),c(i),c(r),c(o),c(a),c(f),c(E),c(R)),se(e,h)}}}function Bt(n){let e=n[6].message+"",t;return{c(){t=k(e)},l(l){t=U(l,e)},m(l,i){N(l,t,i)},p(l,i){i&64&&e!==(e=l[6].message+"")&&Q(t,e)},i:$,o:$,d(l){l&&c(t)}}}function Xe(n){let e,t;return e=new Nt({props:{data:n[1],queryID:n[0]}}),{c(){ue(e.$$.fragment)},l(l){ae(e.$$.fragment,l)},m(l,i){oe(e,l,i),t=!0},p(l,i){const r={};i&2&&(r.data=l[1]),i&1&&(r.queryID=l[0]),e.$set(r)},i(l){t||(w(e.$$.fragment,l),t=!0)},o(l){H(e.$$.fragment,l),t=!1},d(l){se(e,l)}}}function Ft(n){let e,t,l,i=n[11]&&qe(n);return{c(){e=v("div"),i&&i.c(),this.h()},l(r){e=I(r,"DIV",{class:!0});var s=b(e);i&&i.l(s),s.forEach(c),this.h()},h(){d(e,"class","over-container svelte-1ursthx")},m(r,s){N(r,e,s),i&&i.m(e,null),l=!0},p(r,[s]){r[11]?i?(i.p(r,s),s&2048&&w(i,1)):(i=qe(r),i.c(),w(i,1),i.m(e,null)):i&&(de(),H(i,1,1,()=>{i=null}),he())},i(r){l||(w(i),r&&(t||_e(()=>{t=$e(e,je,{}),t.start()})),l=!0)},o(r){H(i),l=!1},d(r){r&&c(e),i&&i.d()}}}function Gt(n,e,t){let l,i,r,s,o=$,a=()=>(o(),o=Qe(h,S=>t(2,s=S)),h),u,f,E,_,R;pe(n,ct,S=>t(19,E=S)),pe(n,ft,S=>t(11,_=S)),n.$$.on_destroy.push(()=>o());let{queryID:L}=e,{queryResult:h}=e;a();let y=Ue("showSQL_".concat(L),!1);pe(n,y,S=>t(10,f=S));let F=Ue(`showResults_${L}`);pe(n,F,S=>t(9,u=S));const A=function(){Ae(y,f=!f,f)},D=function(){!O&&s.length>0&&Ae(F,u=!u,u)};let x,Y,Z=!0,O;const{theme:p}=Ze();pe(n,p,S=>t(12,R=S));function C(S){Z=S,t(5,Z)}return n.$$set=S=>{"queryID"in S&&t(0,L=S.queryID),"queryResult"in S&&a(t(1,h=S.queryResult))},n.$$.update=()=>{if(n.$$.dirty&524288&&t(18,l=E.data.evidencemeta.queries),n.$$.dirty&4&&(s?t(6,O=s.error):t(6,O=new Error("queryResult is undefined"))),n.$$.dirty&4&&t(8,i=(s==null?void 0:s.length)??0),n.$$.dirty&4&&t(7,r=s.columns.length??(s==null?void 0:s._evidenceColumnTypes.length)??0),n.$$.dirty&262145){let S=l==null?void 0:l.find(W=>W.id===L);S&&(t(3,x=S.inputQueryString),t(4,Y=S.compiled&&S.compileError===void 0))}},[L,h,s,x,Y,Z,O,r,i,u,f,_,R,y,F,A,D,p,l,E,C]}class Kt extends Ie{constructor(e){super(),ve(this,e,Gt,Ft,ge,{queryID:0,queryResult:1})}}export{Kt as Q,Wt as g};
