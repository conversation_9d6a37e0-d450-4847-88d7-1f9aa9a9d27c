import{S as Cl,s as me,d as g,i as S,r as te,U as ue,t as Be,V as U,W as Re,c as he,u as _e,g as ge,a as be,v as we,ai as Le,A as Ze,h as G,j as le,m as R,D as ot,K as Al,ah as Sl,p as nl,z as sl,l as Xe,Q as tn,b as V,B as rl,ak as ks,e as ke,x as Ue,k as Q,y as Ye,n as K,I as zi,F as ln,o as Hl,a3 as xl,w as bt,X as nn,G as W,al as Ys,J as Ni,aj as Qs,C as gt,L as Cs,a2 as sn,E as jt,q as Ct,am as Ks,P as Js,Z as ql,_ as Gl,an as Zs,H as xs}from"../chunks/scheduler.C5eBzNnH.js";import{S as Ae,i as Se,t as C,a as b,g as fe,c as ae,f as Ql,k as ws,h as Hi,j as $l,d as J,m as Z,b as x,e as $}from"../chunks/index.BSd9q3aW.js";import{z as rn,W as $s,Y as er,K as tr,X as lr,ad as ir,ae as nr,A as yl,af as Ei,ag as sr,a2 as pi,J as on,Z as rr,$ as fn,a0 as Rl,ah as or,n as Wl,a3 as Xl,ai as fr,aj as an,ak as ar,al as ur,y as cr,a6 as As,a5 as Ss,a7 as Os,B as Ve,am as dr,a8 as mr,aa as De,an as hr,ao as Dl,ap as _r,aq as ei,ar as Ts,as as Kl,at as gr,au as br,av as yr,aw as kr,ax as Cr,ay as wr,F as ze,H as Tt,I as ti,az as Ar,aA as Ls,aB as Sr,aC as un,m as pt,u as vs,q as Ds,aD as Or,aE as Tr,aF as Lr,aG as vr,ab as Dr,aH as _t,aI as Er,ac as Es,aJ as pr,e as cn,aK as Ol,aL as Mr,aM as Ir,aN as qi,aO as Gi,aP as ps,aQ as Pr,g as El,f as Fr,h as Nr,M as Ms,aR as Nt,aS as Br,aT as jr,aU as Mi,aV as Bi,aW as ji,aX as dn,O as Ii,aY as Vr,aZ as Ot,a_ as Ul,S as Et,a$ as zr,b0 as Is,b1 as Ps,b2 as Fs,b3 as Hr,Q as Tl,b4 as qr,b5 as Gr,b6 as Rr,j as Wr,k as Xr,p as Ur,l as mn,r as hn,o as Yr}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.CJe6lW-S.js";import{w as Jl,d as Bt}from"../chunks/entry.CjmEikbu.js";import{h as Yl,p as Qr}from"../chunks/setTrackProxy.DjIbdjlZ.js";import{g as Kr,Q as Ns}from"../chunks/QueryViewer.Ok3Ma9J7.js";import{p as Bs}from"../chunks/stores.C41LEeiH.js";import{r as Jr}from"../chunks/scroll.B6wI3tb1.js";import{h as _n,c as Zr,B as xr}from"../chunks/button.BTWiJWck.js";import{c as $r}from"../chunks/checkRequiredProps.o_C_V3S5.js";const Vi=(i,e={serializeStrings:!0})=>i==null?"null":typeof i=="string"?e.serializeStrings!==!1?`'${i.replaceAll("'","''")}'`:i:typeof i=="number"||typeof i=="bigint"||typeof i=="boolean"?String(i):i instanceof Date?`'${i.toISOString()}'::TIMESTAMP_MS`:Array.isArray(i)?`[${i.map(t=>Vi(t,e)).join(", ")}]`:JSON.stringify(i),eo={positioning:{placement:"bottom"},arrowSize:8,defaultOpen:!1,disableFocusTrap:!1,closeOnEscape:!0,preventScroll:!1,onOpenChange:void 0,closeOnOutsideClick:!0,portal:void 0,forceVisible:!1,openFocus:void 0,closeFocus:void 0,onOutsideClick:void 0},{name:kl}=rr("popover"),to=["trigger","content"];function lo(i){const e={...eo,...i},t=rn($s(e,"open","ids")),{positioning:l,arrowSize:n,disableFocusTrap:s,preventScroll:r,closeOnEscape:f,closeOnOutsideClick:o,portal:a,forceVisible:u,openFocus:d,closeFocus:c,onOutsideClick:m}=t,_=e.open??Jl(e.defaultOpen),h=er(_,e==null?void 0:e.onOpenChange),w=tr.writable(null),E=rn({...lr(to),...e.ids});ir(()=>{w.set(document.getElementById(E.trigger.get()))});function v(){h.set(!1);const D=document.getElementById(E.trigger.get());_n({prop:c.get(),defaultEl:D})}const M=nr({open:h,activeTrigger:w,forceVisible:u}),p=yl(kl("content"),{stores:[M,a,E.content],returned:([D,T,y])=>({hidden:D&&on?void 0:!0,tabindex:-1,style:pi({display:D?void 0:"none"}),id:y,"data-state":D?"open":"closed","data-portal":sr(T)}),action:D=>{let T=Wl;const y=Ei([M,w,l,s,f,o,a],([N,q,X,Ce,ye,se,qe])=>{T(),!(!N||!q)&&Cl().then(()=>{T(),T=fr(D,{anchorElement:q,open:h,options:{floating:X,focusTrap:Ce?null:{returnFocusOnDeactivate:!1,clickOutsideDeactivates:se,allowOutsideClick:!0,escapeDeactivates:ye},modal:{shouldCloseOnInteractOutside:Y,onClose:v,open:N,closeOnInteractOutside:se},escapeKeydown:ye?{handler:()=>{v()}}:null,portal:an(D,qe)}}).destroy})});return{destroy(){y(),T()}}}});function F(D){h.update(T=>!T),D&&D!==w.get()&&w.set(D)}function Y(D){var N;if((N=m.get())==null||N(D),D.defaultPrevented)return!1;const T=D.target,y=document.getElementById(E.trigger.get());return!(y&&ar(T)&&(T===y||y.contains(T)))}const H=yl(kl("trigger"),{stores:[M,E.content,E.trigger],returned:([D,T,y])=>({role:"button","aria-haspopup":"dialog","aria-expanded":D?"true":"false","data-state":gn(D),"aria-controls":T,id:y}),action:D=>({destroy:fn(Rl(D,"click",()=>{F(D)}),Rl(D,"keydown",y=>{y.key!==Xl.ENTER&&y.key!==Xl.SPACE||(y.preventDefault(),F(D))}))})}),ne=yl(kl("overlay"),{stores:[M],returned:([D])=>({hidden:D?void 0:!0,tabindex:-1,style:pi({display:D?void 0:"none"}),"aria-hidden":"true","data-state":gn(D)}),action:D=>{let T=Wl,y=Wl,N=Wl;if(f.get()){const q=or(D,{handler:()=>{v()}});q&&q.destroy&&(T=q.destroy)}return y=Ei([a],([q])=>{if(N(),q===null)return;const X=an(D,q);X!==null&&(N=ur(D,X).destroy)}),{destroy(){T(),y(),N()}}}}),I=yl(kl("arrow"),{stores:n,returned:D=>({"data-arrow":!0,style:pi({position:"absolute",width:`var(--arrow-size, ${D}px)`,height:`var(--arrow-size, ${D}px)`})})}),k=yl(kl("close"),{returned:()=>({type:"button"}),action:D=>({destroy:fn(Rl(D,"click",y=>{y.defaultPrevented||v()}),Rl(D,"keydown",y=>{y.defaultPrevented||y.key!==Xl.ENTER&&y.key!==Xl.SPACE||(y.preventDefault(),F())}))})});return Ei([h,w,r],([D,T,y])=>{if(!on)return;const N=[];if(D){T||Cl().then(()=>{const X=document.getElementById(E.trigger.get());cr(X)&&w.set(X)}),y&&N.push(Jr());const q=T??document.getElementById(E.trigger.get());_n({prop:d.get(),defaultEl:q})}return()=>{N.forEach(q=>q())}}),{ids:E,elements:{trigger:H,content:p,arrow:I,close:k,overlay:ne},states:{open:h},options:t}}function gn(i){return i?"open":"closed"}function io(){return{NAME:"separator",PARTS:["root"]}}function no(i){const{NAME:e,PARTS:t}=io(),l=As(e,t),n={...Zr(Ss(i)),getAttrs:l};return{...n,updateOption:Os(n.options)}}const so=i=>({builder:i&4}),bn=i=>({builder:i[2]});function ro(i){let e,t,l,n=[i[2],i[4]],s={};for(let r=0;r<n.length;r+=1)s=U(s,n[r]);return{c(){e=R("div"),this.h()},l(r){e=G(r,"DIV",{}),le(e).forEach(g),this.h()},h(){Le(e,s)},m(r,f){S(r,e,f),i[10](e),t||(l=Ze(i[2].action(e)),t=!0)},p(r,f){Le(e,s=Ve(n,[f&4&&r[2],f&16&&r[4]]))},i:we,o:we,d(r){r&&g(e),i[10](null),t=!1,l()}}}function oo(i){let e;const t=i[9].default,l=he(t,i,i[8],bn);return{c(){l&&l.c()},l(n){l&&l.l(n)},m(n,s){l&&l.m(n,s),e=!0},p(n,s){l&&l.p&&(!e||s&260)&&_e(l,t,n,n[8],e?be(t,n[8],s,so):ge(n[8]),bn)},i(n){e||(b(l,n),e=!0)},o(n){C(l,n),e=!1},d(n){l&&l.d(n)}}}function fo(i){let e,t,l,n;const s=[oo,ro],r=[];function f(o,a){return o[1]?0:1}return e=f(i),t=r[e]=s[e](i),{c(){t.c(),l=te()},l(o){t.l(o),l=te()},m(o,a){r[e].m(o,a),S(o,l,a),n=!0},p(o,[a]){let u=e;e=f(o),e===u?r[e].p(o,a):(fe(),C(r[u],1,1,()=>{r[u]=null}),ae(),t=r[e],t?t.p(o,a):(t=r[e]=s[e](o),t.c()),b(t,1),t.m(l.parentNode,l))},i(o){n||(b(t),n=!0)},o(o){C(t),n=!1},d(o){o&&g(l),r[e].d(o)}}}function ao(i,e,t){let l;const n=["orientation","decorative","asChild","el"];let s=ue(e,n),r,{$$slots:f={},$$scope:o}=e,{orientation:a="horizontal"}=e,{decorative:u=!0}=e,{asChild:d=!1}=e,{el:c=void 0}=e;const{elements:{root:m},updateOption:_,getAttrs:h}=no({orientation:a,decorative:u});Be(i,m,v=>t(7,r=v));const w=h("root");function E(v){ot[v?"unshift":"push"](()=>{c=v,t(0,c)})}return i.$$set=v=>{e=U(U({},e),Re(v)),t(4,s=ue(e,n)),"orientation"in v&&t(5,a=v.orientation),"decorative"in v&&t(6,u=v.decorative),"asChild"in v&&t(1,d=v.asChild),"el"in v&&t(0,c=v.el),"$$scope"in v&&t(8,o=v.$$scope)},i.$$.update=()=>{i.$$.dirty&32&&_("orientation",a),i.$$.dirty&64&&_("decorative",u),i.$$.dirty&128&&t(2,l=r),i.$$.dirty&4&&Object.assign(l,w)},[c,d,l,m,s,a,u,r,o,f,E]}let uo=class extends Ae{constructor(e){super(),Se(this,e,ao,fo,me,{orientation:5,decorative:6,asChild:1,el:0})}};function js(){return{NAME:"popover",PARTS:["arrow","close","content","trigger"]}}function co(i){const{NAME:e,PARTS:t}=js(),l=As(e,t),n={...lo({positioning:{placement:"bottom",gutter:0},...Ss(i),forceVisible:!0}),getAttrs:l};return Al(e,n),{...n,updateOption:Os(n.options)}}function Ri(){const{NAME:i}=js();return Sl(i)}function mo(i){const t={...{side:"bottom",align:"center"},...i},{options:{positioning:l}}=Ri();dr(l)(t)}const ho=i=>({ids:i&1}),yn=i=>({ids:i[0]});function _o(i){let e;const t=i[13].default,l=he(t,i,i[12],yn);return{c(){l&&l.c()},l(n){l&&l.l(n)},m(n,s){l&&l.m(n,s),e=!0},p(n,[s]){l&&l.p&&(!e||s&4097)&&_e(l,t,n,n[12],e?be(t,n[12],s,ho):ge(n[12]),yn)},i(n){e||(b(l,n),e=!0)},o(n){C(l,n),e=!1},d(n){l&&l.d(n)}}}function go(i,e,t){let l,{$$slots:n={},$$scope:s}=e,{disableFocusTrap:r=void 0}=e,{closeOnEscape:f=void 0}=e,{closeOnOutsideClick:o=void 0}=e,{preventScroll:a=void 0}=e,{portal:u=void 0}=e,{open:d=void 0}=e,{onOpenChange:c=void 0}=e,{openFocus:m=void 0}=e,{closeFocus:_=void 0}=e,{onOutsideClick:h=void 0}=e;const{updateOption:w,states:{open:E},ids:v}=co({disableFocusTrap:r,closeOnEscape:f,closeOnOutsideClick:o,preventScroll:a,portal:u,defaultOpen:d,openFocus:m,closeFocus:_,onOutsideClick:h,onOpenChange:({next:p})=>(d!==p&&(c==null||c(p),t(2,d=p)),p),positioning:{gutter:0,offset:{mainAxis:1}}}),M=Bt([v.content,v.trigger],([p,F])=>({content:p,trigger:F}));return Be(i,M,p=>t(0,l=p)),i.$$set=p=>{"disableFocusTrap"in p&&t(3,r=p.disableFocusTrap),"closeOnEscape"in p&&t(4,f=p.closeOnEscape),"closeOnOutsideClick"in p&&t(5,o=p.closeOnOutsideClick),"preventScroll"in p&&t(6,a=p.preventScroll),"portal"in p&&t(7,u=p.portal),"open"in p&&t(2,d=p.open),"onOpenChange"in p&&t(8,c=p.onOpenChange),"openFocus"in p&&t(9,m=p.openFocus),"closeFocus"in p&&t(10,_=p.closeFocus),"onOutsideClick"in p&&t(11,h=p.onOutsideClick),"$$scope"in p&&t(12,s=p.$$scope)},i.$$.update=()=>{i.$$.dirty&4&&d!==void 0&&E.set(d),i.$$.dirty&8&&w("disableFocusTrap",r),i.$$.dirty&16&&w("closeOnEscape",f),i.$$.dirty&32&&w("closeOnOutsideClick",o),i.$$.dirty&64&&w("preventScroll",a),i.$$.dirty&128&&w("portal",u),i.$$.dirty&512&&w("openFocus",m),i.$$.dirty&1024&&w("closeFocus",_),i.$$.dirty&2048&&w("onOutsideClick",h)},[l,M,d,r,f,o,a,u,c,m,_,h,s,n]}class bo extends Ae{constructor(e){super(),Se(this,e,go,_o,me,{disableFocusTrap:3,closeOnEscape:4,closeOnOutsideClick:5,preventScroll:6,portal:7,open:2,onOpenChange:8,openFocus:9,closeFocus:10,onOutsideClick:11})}}const yo=i=>({builder:i[0]&256}),kn=i=>({builder:i[8]}),ko=i=>({builder:i[0]&256}),Cn=i=>({builder:i[8]}),Co=i=>({builder:i[0]&256}),wn=i=>({builder:i[8]}),wo=i=>({builder:i[0]&256}),An=i=>({builder:i[8]}),Ao=i=>({builder:i[0]&256}),Sn=i=>({builder:i[8]}),So=i=>({builder:i[0]&256}),On=i=>({builder:i[8]});function Oo(i){let e,t,l,n;const s=i[27].default,r=he(s,i,i[26],kn);let f=[i[8],i[12]],o={};for(let a=0;a<f.length;a+=1)o=U(o,f[a]);return{c(){e=R("div"),r&&r.c(),this.h()},l(a){e=G(a,"DIV",{});var u=le(e);r&&r.l(u),u.forEach(g),this.h()},h(){Le(e,o)},m(a,u){S(a,e,u),r&&r.m(e,null),i[32](e),t=!0,l||(n=Ze(i[8].action(e)),l=!0)},p(a,u){r&&r.p&&(!t||u[0]&67109120)&&_e(r,s,a,a[26],t?be(s,a[26],u,yo):ge(a[26]),kn),Le(e,o=Ve(f,[u[0]&256&&a[8],u[0]&4096&&a[12]]))},i(a){t||(b(r,a),t=!0)},o(a){C(r,a),t=!1},d(a){a&&g(e),r&&r.d(a),i[32](null),l=!1,n()}}}function To(i){let e,t,l,n,s;const r=i[27].default,f=he(r,i,i[26],Cn);let o=[i[8],i[12]],a={};for(let u=0;u<o.length;u+=1)a=U(a,o[u]);return{c(){e=R("div"),f&&f.c(),this.h()},l(u){e=G(u,"DIV",{});var d=le(e);f&&f.l(d),d.forEach(g),this.h()},h(){Le(e,a)},m(u,d){S(u,e,d),f&&f.m(e,null),i[31](e),l=!0,n||(s=Ze(i[8].action(e)),n=!0)},p(u,d){i=u,f&&f.p&&(!l||d[0]&67109120)&&_e(f,r,i,i[26],l?be(r,i[26],d,ko):ge(i[26]),Cn),Le(e,a=Ve(o,[d[0]&256&&i[8],d[0]&4096&&i[12]]))},i(u){l||(b(f,u),t&&t.end(1),l=!0)},o(u){C(f,u),u&&(t=ws(e,i[5],i[6])),l=!1},d(u){u&&g(e),f&&f.d(u),i[31](null),u&&t&&t.end(),n=!1,s()}}}function Lo(i){let e,t,l,n,s;const r=i[27].default,f=he(r,i,i[26],wn);let o=[i[8],i[12]],a={};for(let u=0;u<o.length;u+=1)a=U(a,o[u]);return{c(){e=R("div"),f&&f.c(),this.h()},l(u){e=G(u,"DIV",{});var d=le(e);f&&f.l(d),d.forEach(g),this.h()},h(){Le(e,a)},m(u,d){S(u,e,d),f&&f.m(e,null),i[30](e),l=!0,n||(s=Ze(i[8].action(e)),n=!0)},p(u,d){i=u,f&&f.p&&(!l||d[0]&67109120)&&_e(f,r,i,i[26],l?be(r,i[26],d,Co):ge(i[26]),wn),Le(e,a=Ve(o,[d[0]&256&&i[8],d[0]&4096&&i[12]]))},i(u){l||(b(f,u),u&&(t||nl(()=>{t=Hi(e,i[3],i[4]),t.start()})),l=!0)},o(u){C(f,u),l=!1},d(u){u&&g(e),f&&f.d(u),i[30](null),n=!1,s()}}}function vo(i){let e,t,l,n,s,r;const f=i[27].default,o=he(f,i,i[26],An);let a=[i[8],i[12]],u={};for(let d=0;d<a.length;d+=1)u=U(u,a[d]);return{c(){e=R("div"),o&&o.c(),this.h()},l(d){e=G(d,"DIV",{});var c=le(e);o&&o.l(c),c.forEach(g),this.h()},h(){Le(e,u)},m(d,c){S(d,e,c),o&&o.m(e,null),i[29](e),n=!0,s||(r=Ze(i[8].action(e)),s=!0)},p(d,c){i=d,o&&o.p&&(!n||c[0]&67109120)&&_e(o,f,i,i[26],n?be(f,i[26],c,wo):ge(i[26]),An),Le(e,u=Ve(a,[c[0]&256&&i[8],c[0]&4096&&i[12]]))},i(d){n||(b(o,d),d&&nl(()=>{n&&(l&&l.end(1),t=Hi(e,i[3],i[4]),t.start())}),n=!0)},o(d){C(o,d),t&&t.invalidate(),d&&(l=ws(e,i[5],i[6])),n=!1},d(d){d&&g(e),o&&o.d(d),i[29](null),d&&l&&l.end(),s=!1,r()}}}function Do(i){let e,t,l,n,s;const r=i[27].default,f=he(r,i,i[26],Sn);let o=[i[8],i[12]],a={};for(let u=0;u<o.length;u+=1)a=U(a,o[u]);return{c(){e=R("div"),f&&f.c(),this.h()},l(u){e=G(u,"DIV",{});var d=le(e);f&&f.l(d),d.forEach(g),this.h()},h(){Le(e,a)},m(u,d){S(u,e,d),f&&f.m(e,null),i[28](e),l=!0,n||(s=Ze(i[8].action(e)),n=!0)},p(u,d){i=u,f&&f.p&&(!l||d[0]&67109120)&&_e(f,r,i,i[26],l?be(r,i[26],d,Ao):ge(i[26]),Sn),Le(e,a=Ve(o,[d[0]&256&&i[8],d[0]&4096&&i[12]]))},i(u){l||(b(f,u),u&&nl(()=>{l&&(t||(t=Ql(e,i[1],i[2],!0)),t.run(1))}),l=!0)},o(u){C(f,u),u&&(t||(t=Ql(e,i[1],i[2],!1)),t.run(0)),l=!1},d(u){u&&g(e),f&&f.d(u),i[28](null),u&&t&&t.end(),n=!1,s()}}}function Eo(i){let e;const t=i[27].default,l=he(t,i,i[26],On);return{c(){l&&l.c()},l(n){l&&l.l(n)},m(n,s){l&&l.m(n,s),e=!0},p(n,s){l&&l.p&&(!e||s[0]&67109120)&&_e(l,t,n,n[26],e?be(t,n[26],s,So):ge(n[26]),On)},i(n){e||(b(l,n),e=!0)},o(n){C(l,n),e=!1},d(n){l&&l.d(n)}}}function po(i){let e,t,l,n;const s=[Eo,Do,vo,Lo,To,Oo],r=[];function f(o,a){return o[7]&&o[9]?0:o[1]&&o[9]?1:o[3]&&o[5]&&o[9]?2:o[3]&&o[9]?3:o[5]&&o[9]?4:o[9]?5:-1}return~(e=f(i))&&(t=r[e]=s[e](i)),{c(){t&&t.c(),l=te()},l(o){t&&t.l(o),l=te()},m(o,a){~e&&r[e].m(o,a),S(o,l,a),n=!0},p(o,a){let u=e;e=f(o),e===u?~e&&r[e].p(o,a):(t&&(fe(),C(r[u],1,1,()=>{r[u]=null}),ae()),~e?(t=r[e],t?t.p(o,a):(t=r[e]=s[e](o),t.c()),b(t,1),t.m(l.parentNode,l)):t=null)},i(o){n||(b(t),n=!0)},o(o){C(t),n=!1},d(o){o&&g(l),~e&&r[e].d(o)}}}function Mo(i,e,t){let l;const n=["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","id","side","align","sideOffset","alignOffset","collisionPadding","avoidCollisions","collisionBoundary","sameWidth","fitViewport","strategy","overlap","el"];let s=ue(e,n),r,f,{$$slots:o={},$$scope:a}=e,{transition:u=void 0}=e,{transitionConfig:d=void 0}=e,{inTransition:c=void 0}=e,{inTransitionConfig:m=void 0}=e,{outTransition:_=void 0}=e,{outTransitionConfig:h=void 0}=e,{asChild:w=!1}=e,{id:E=void 0}=e,{side:v="bottom"}=e,{align:M="center"}=e,{sideOffset:p=0}=e,{alignOffset:F=0}=e,{collisionPadding:Y=8}=e,{avoidCollisions:H=!0}=e,{collisionBoundary:ne=void 0}=e,{sameWidth:I=!1}=e,{fitViewport:k=!1}=e,{strategy:D="absolute"}=e,{overlap:T=!1}=e,{el:y=void 0}=e;const{elements:{content:N},states:{open:q},ids:X,getAttrs:Ce}=Ri();Be(i,N,j=>t(25,f=j)),Be(i,q,j=>t(9,r=j));const ye=Ce("content");function se(j){ot[j?"unshift":"push"](()=>{y=j,t(0,y)})}function qe(j){ot[j?"unshift":"push"](()=>{y=j,t(0,y)})}function Ee(j){ot[j?"unshift":"push"](()=>{y=j,t(0,y)})}function oe(j){ot[j?"unshift":"push"](()=>{y=j,t(0,y)})}function Oe(j){ot[j?"unshift":"push"](()=>{y=j,t(0,y)})}return i.$$set=j=>{e=U(U({},e),Re(j)),t(12,s=ue(e,n)),"transition"in j&&t(1,u=j.transition),"transitionConfig"in j&&t(2,d=j.transitionConfig),"inTransition"in j&&t(3,c=j.inTransition),"inTransitionConfig"in j&&t(4,m=j.inTransitionConfig),"outTransition"in j&&t(5,_=j.outTransition),"outTransitionConfig"in j&&t(6,h=j.outTransitionConfig),"asChild"in j&&t(7,w=j.asChild),"id"in j&&t(13,E=j.id),"side"in j&&t(14,v=j.side),"align"in j&&t(15,M=j.align),"sideOffset"in j&&t(16,p=j.sideOffset),"alignOffset"in j&&t(17,F=j.alignOffset),"collisionPadding"in j&&t(18,Y=j.collisionPadding),"avoidCollisions"in j&&t(19,H=j.avoidCollisions),"collisionBoundary"in j&&t(20,ne=j.collisionBoundary),"sameWidth"in j&&t(21,I=j.sameWidth),"fitViewport"in j&&t(22,k=j.fitViewport),"strategy"in j&&t(23,D=j.strategy),"overlap"in j&&t(24,T=j.overlap),"el"in j&&t(0,y=j.el),"$$scope"in j&&t(26,a=j.$$scope)},i.$$.update=()=>{i.$$.dirty[0]&8192&&E&&X.content.set(E),i.$$.dirty[0]&33554432&&t(8,l=f),i.$$.dirty[0]&256&&Object.assign(l,ye),i.$$.dirty[0]&33538560&&r&&mo({side:v,align:M,sideOffset:p,alignOffset:F,collisionPadding:Y,avoidCollisions:H,collisionBoundary:ne,sameWidth:I,fitViewport:k,strategy:D,overlap:T})},[y,u,d,c,m,_,h,w,l,r,N,q,s,E,v,M,p,F,Y,H,ne,I,k,D,T,f,a,o,se,qe,Ee,oe,Oe]}let Io=class extends Ae{constructor(e){super(),Se(this,e,Mo,po,me,{transition:1,transitionConfig:2,inTransition:3,inTransitionConfig:4,outTransition:5,outTransitionConfig:6,asChild:7,id:13,side:14,align:15,sideOffset:16,alignOffset:17,collisionPadding:18,avoidCollisions:19,collisionBoundary:20,sameWidth:21,fitViewport:22,strategy:23,overlap:24,el:0},null,[-1,-1])}};const Po=i=>({builder:i&4}),Tn=i=>({builder:i[2]}),Fo=i=>({builder:i&4}),Ln=i=>({builder:i[2]});function No(i){let e,t,l,n;const s=i[12].default,r=he(s,i,i[11],Tn);let f=[i[2],{type:"button"},i[6]],o={};for(let a=0;a<f.length;a+=1)o=U(o,f[a]);return{c(){e=R("button"),r&&r.c(),this.h()},l(a){e=G(a,"BUTTON",{type:!0});var u=le(e);r&&r.l(u),u.forEach(g),this.h()},h(){Le(e,o)},m(a,u){S(a,e,u),r&&r.m(e,null),e.autofocus&&e.focus(),i[13](e),t=!0,l||(n=[Ze(i[2].action(e)),Xe(e,"m-click",i[5]),Xe(e,"m-keydown",i[5])],l=!0)},p(a,u){r&&r.p&&(!t||u&2052)&&_e(r,s,a,a[11],t?be(s,a[11],u,Po):ge(a[11]),Tn),Le(e,o=Ve(f,[u&4&&a[2],{type:"button"},u&64&&a[6]]))},i(a){t||(b(r,a),t=!0)},o(a){C(r,a),t=!1},d(a){a&&g(e),r&&r.d(a),i[13](null),l=!1,sl(n)}}}function Bo(i){let e;const t=i[12].default,l=he(t,i,i[11],Ln);return{c(){l&&l.c()},l(n){l&&l.l(n)},m(n,s){l&&l.m(n,s),e=!0},p(n,s){l&&l.p&&(!e||s&2052)&&_e(l,t,n,n[11],e?be(t,n[11],s,Fo):ge(n[11]),Ln)},i(n){e||(b(l,n),e=!0)},o(n){C(l,n),e=!1},d(n){l&&l.d(n)}}}function jo(i){let e,t,l,n;const s=[Bo,No],r=[];function f(o,a){return o[1]?0:1}return e=f(i),t=r[e]=s[e](i),{c(){t.c(),l=te()},l(o){t.l(o),l=te()},m(o,a){r[e].m(o,a),S(o,l,a),n=!0},p(o,[a]){let u=e;e=f(o),e===u?r[e].p(o,a):(fe(),C(r[u],1,1,()=>{r[u]=null}),ae(),t=r[e],t?t.p(o,a):(t=r[e]=s[e](o),t.c()),b(t,1),t.m(l.parentNode,l))},i(o){n||(b(t),n=!0)},o(o){C(t),n=!1},d(o){o&&g(l),r[e].d(o)}}}function Vo(i,e,t){let l,n;const s=["asChild","id","el"];let r=ue(e,s),f,o,{$$slots:a={},$$scope:u}=e,{asChild:d=!1}=e,{id:c=void 0}=e,{el:m=void 0}=e;const{elements:{trigger:_},states:{open:h},ids:w,getAttrs:E}=Ri();Be(i,_,F=>t(9,f=F)),Be(i,h,F=>t(10,o=F));const v=mr(),M=E("trigger");function p(F){ot[F?"unshift":"push"](()=>{m=F,t(0,m)})}return i.$$set=F=>{e=U(U({},e),Re(F)),t(6,r=ue(e,s)),"asChild"in F&&t(1,d=F.asChild),"id"in F&&t(7,c=F.id),"el"in F&&t(0,m=F.el),"$$scope"in F&&t(11,u=F.$$scope)},i.$$.update=()=>{i.$$.dirty&128&&c&&w.trigger.set(c),i.$$.dirty&1024&&t(8,l={...M,"aria-controls":o?w.content:void 0}),i.$$.dirty&512&&t(2,n=f),i.$$.dirty&260&&Object.assign(n,l)},[m,d,n,_,h,v,r,c,l,f,o,u,a,p]}class zo extends Ae{constructor(e){super(),Se(this,e,Vo,jo,me,{asChild:1,id:7,el:0})}}function Ho(i){let e,t;const l=i[2].default,n=he(l,i,i[1],null);return{c(){e=R("div"),n&&n.c(),this.h()},l(s){e=G(s,"DIV",{class:!0});var r=le(e);n&&n.l(r),r.forEach(g),this.h()},h(){V(e,"class","contents"),tn(e,"print:hidden",i[0])},m(s,r){S(s,e,r),n&&n.m(e,null),t=!0},p(s,[r]){n&&n.p&&(!t||r&2)&&_e(n,l,s,s[1],t?be(l,s[1],r,null):ge(s[1]),null),(!t||r&1)&&tn(e,"print:hidden",s[0])},i(s){t||(b(n,s),t=!0)},o(s){C(n,s),t=!1},d(s){s&&g(e),n&&n.d(s)}}}function qo(i,e,t){let{$$slots:l={},$$scope:n}=e,{enabled:s=!0}=e;return i.$$set=r=>{"enabled"in r&&t(0,s=r.enabled),"$$scope"in r&&t(1,n=r.$$scope)},i.$$.update=()=>{i.$$.dirty&1&&t(0,s=De(s))},[s,n,l]}class Go extends Ae{constructor(e){super(),Se(this,e,qo,Ho,me,{enabled:0})}}const Vs=Symbol("EVIDENCE_DROPDOWN_CTX");let Ro=0;function Wo(i,e,t){let{value:l}=e,{valueLabel:n=l}=e,{idx:s=-1}=e,{__auto:r=!1}=e;r||(s=Ro++);const f=Sl(Vs);return rl(()=>f.registerOption({value:l,label:n,idx:s,__auto:r})),i.$$set=o=>{"value"in o&&t(1,l=o.value),"valueLabel"in o&&t(2,n=o.valueLabel),"idx"in o&&t(0,s=o.idx),"__auto"in o&&t(3,r=o.__auto)},[s,l,n,r]}class ll extends Ae{constructor(e){super(),Se(this,e,Wo,null,me,{value:1,valueLabel:2,idx:0,__auto:3})}}function Xo(i){return Object.keys(i).reduce((e,t)=>i[t]===void 0?e:e+`${t}:${i[t]};`,"")}const Uo={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"};function Zl(i,e,t,l){const n=Array.isArray(e)?e:[e];return n.forEach(s=>i.addEventListener(s,t,l)),()=>{n.forEach(s=>i.removeEventListener(s,t,l))}}function zs(...i){return(...e)=>{for(const t of i)typeof t=="function"&&t(...e)}}const Yo=i=>i&4,Qo=i=>({}),vn=i=>({...i[2]}),Ko=i=>i&4,Jo=i=>({}),Dn=i=>({...i[2]});function Zo(i){let e,t,l=(i[0]??"")+"",n,s,r,f,o,a=[i[6]],u={};for(let h=0;h<a.length;h+=1)u=U(u,a[h]);const d=i[18].default,c=he(d,i,i[17],vn);let m=[i[5],i[7]],_={};for(let h=0;h<m.length;h+=1)_=U(_,m[h]);return{c(){e=R("div"),t=R("label"),n=Ye(l),s=K(),c&&c.c(),this.h()},l(h){e=G(h,"DIV",{});var w=le(e);t=G(w,"LABEL",{});var E=le(t);n=Ue(E,l),E.forEach(g),s=Q(w),c&&c.l(w),w.forEach(g),this.h()},h(){Le(t,u),Le(e,_)},m(h,w){S(h,e,w),ke(e,t),ke(t,n),ke(e,s),c&&c.m(e,null),r=!0,f||(o=Ze(i[4].call(null,e)),f=!0)},p(h,w){(!r||w&1)&&l!==(l=(h[0]??"")+"")&&ks(n,l,u.contenteditable),c&&c.p&&(!r||w&131076)&&_e(c,d,h,h[17],Yo(w)||!r?ge(h[17]):be(d,h[17],w,Qo),vn),Le(e,_=Ve(m,[h[5],w&128&&h[7]]))},i(h){r||(b(c,h),r=!0)},o(h){C(c,h),r=!1},d(h){h&&g(e),c&&c.d(h),f=!1,o()}}}function xo(i){let e;const t=i[18].default,l=he(t,i,i[17],Dn);return{c(){l&&l.c()},l(n){l&&l.l(n)},m(n,s){l&&l.m(n,s),e=!0},p(n,s){l&&l.p&&(!e||s&131076)&&_e(l,t,n,n[17],Ko(s)||!e?ge(n[17]):be(t,n[17],s,Jo),Dn)},i(n){e||(b(l,n),e=!0)},o(n){C(l,n),e=!1},d(n){l&&l.d(n)}}}function $o(i){let e,t,l,n;const s=[xo,Zo],r=[];function f(o,a){return o[1]?0:1}return e=f(i),t=r[e]=s[e](i),{c(){t.c(),l=te()},l(o){t.l(o),l=te()},m(o,a){r[e].m(o,a),S(o,l,a),n=!0},p(o,[a]){let u=e;e=f(o),e===u?r[e].p(o,a):(fe(),C(r[u],1,1,()=>{r[u]=null}),ae(),t=r[e],t?t.p(o,a):(t=r[e]=s[e](o),t.c()),b(t,1),t.m(l.parentNode,l))},i(o){n||(b(t),n=!0)},o(o){C(t),n=!1},d(o){o&&g(l),r[e].d(o)}}}function ef(i,e,t){let l;const n=["label","shouldFilter","filter","value","onValueChange","loop","onKeydown","state","ids","asChild"];let s=ue(e,n),r,{$$slots:f={},$$scope:o}=e,{label:a=void 0}=e,{shouldFilter:u=!0}=e,{filter:d=void 0}=e,{value:c=void 0}=e,{onValueChange:m=void 0}=e,{loop:_=void 0}=e,{onKeydown:h=void 0}=e,{state:w=void 0}=e,{ids:E=void 0}=e,{asChild:v=!1}=e;const{commandEl:M,handleRootKeydown:p,ids:F,state:Y}=hr({label:a,shouldFilter:u,filter:d,value:c,onValueChange:y=>{y!==c&&(t(8,c=y),m==null||m(y))},loop:_,state:w,ids:E});Be(i,Y,y=>t(16,r=y));function H(y){y&&y!==r.value&&zi(Y,r.value=y,r)}function ne(y){return M.set(y),{destroy:zs(Zl(y,"keydown",D))}}const I={role:"application",id:F.root,"data-cmdk-root":""},k={"data-cmdk-label":"",for:F.input,id:F.label,style:Xo(Uo)};function D(y){h==null||h(y),!y.defaultPrevented&&p(y)}const T={action:ne,attrs:I};return i.$$set=y=>{e=U(U({},e),Re(y)),t(7,s=ue(e,n)),"label"in y&&t(0,a=y.label),"shouldFilter"in y&&t(9,u=y.shouldFilter),"filter"in y&&t(10,d=y.filter),"value"in y&&t(8,c=y.value),"onValueChange"in y&&t(11,m=y.onValueChange),"loop"in y&&t(12,_=y.loop),"onKeydown"in y&&t(13,h=y.onKeydown),"state"in y&&t(14,w=y.state),"ids"in y&&t(15,E=y.ids),"asChild"in y&&t(1,v=y.asChild),"$$scope"in y&&t(17,o=y.$$scope)},i.$$.update=()=>{i.$$.dirty&256&&H(c),i.$$.dirty&65536&&t(2,l={root:T,label:{attrs:k},stateStore:Y,state:r})},[a,v,l,Y,ne,I,k,s,c,u,d,m,_,h,w,E,r,o,f]}let tf=class extends Ae{constructor(e){super(),Se(this,e,ef,$o,me,{label:0,shouldFilter:9,filter:10,value:8,onValueChange:11,loop:12,onKeydown:13,state:14,ids:15,asChild:1})}};const lf=i=>({}),En=i=>({attrs:i[4]});function pn(i){let e,t,l,n;const s=[sf,nf],r=[];function f(o,a){return o[0]?0:1}return e=f(i),t=r[e]=s[e](i),{c(){t.c(),l=te()},l(o){t.l(o),l=te()},m(o,a){r[e].m(o,a),S(o,l,a),n=!0},p(o,a){let u=e;e=f(o),e===u?r[e].p(o,a):(fe(),C(r[u],1,1,()=>{r[u]=null}),ae(),t=r[e],t?t.p(o,a):(t=r[e]=s[e](o),t.c()),b(t,1),t.m(l.parentNode,l))},i(o){n||(b(t),n=!0)},o(o){C(t),n=!1},d(o){o&&g(l),r[e].d(o)}}}function nf(i){let e,t;const l=i[8].default,n=he(l,i,i[7],null);let s=[i[4],i[5]],r={};for(let f=0;f<s.length;f+=1)r=U(r,s[f]);return{c(){e=R("div"),n&&n.c(),this.h()},l(f){e=G(f,"DIV",{});var o=le(e);n&&n.l(o),o.forEach(g),this.h()},h(){Le(e,r)},m(f,o){S(f,e,o),n&&n.m(e,null),t=!0},p(f,o){n&&n.p&&(!t||o&128)&&_e(n,l,f,f[7],t?be(l,f[7],o,null):ge(f[7]),null),Le(e,r=Ve(s,[f[4],o&32&&f[5]]))},i(f){t||(b(n,f),t=!0)},o(f){C(n,f),t=!1},d(f){f&&g(e),n&&n.d(f)}}}function sf(i){let e;const t=i[8].default,l=he(t,i,i[7],En);return{c(){l&&l.c()},l(n){l&&l.l(n)},m(n,s){l&&l.m(n,s),e=!0},p(n,s){l&&l.p&&(!e||s&128)&&_e(l,t,n,n[7],e?be(t,n[7],s,lf):ge(n[7]),En)},i(n){e||(b(l,n),e=!0)},o(n){C(l,n),e=!1},d(n){l&&l.d(n)}}}function rf(i){let e,t,l=!i[1]&&i[2]&&pn(i);return{c(){l&&l.c(),e=te()},l(n){l&&l.l(n),e=te()},m(n,s){l&&l.m(n,s),S(n,e,s),t=!0},p(n,[s]){!n[1]&&n[2]?l?(l.p(n,s),s&6&&b(l,1)):(l=pn(n),l.c(),b(l,1),l.m(e.parentNode,e)):l&&(fe(),C(l,1,1,()=>{l=null}),ae())},i(n){t||(b(l),t=!0)},o(n){C(l),t=!1},d(n){n&&g(e),l&&l.d(n)}}}function of(i,e,t){let l;const n=["asChild"];let s=ue(e,n),r,{$$slots:f={},$$scope:o}=e,{asChild:a=!1}=e,u=!0;rl(()=>{t(1,u=!1)});const d=Dl();Be(i,d,m=>t(6,r=m));const c={"data-cmdk-empty":"",role:"presentation"};return i.$$set=m=>{e=U(U({},e),Re(m)),t(5,s=ue(e,n)),"asChild"in m&&t(0,a=m.asChild),"$$scope"in m&&t(7,o=m.$$scope)},i.$$.update=()=>{i.$$.dirty&64&&t(2,l=r.filtered.count===0)},[a,u,l,d,c,s,r,o,f]}class ff extends Ae{constructor(e){super(),Se(this,e,of,rf,me,{asChild:0})}}const af=i=>({container:i&32,group:i&16}),Mn=i=>({container:i[5],group:i[4],heading:{attrs:i[8]}}),uf=i=>({container:i&32,group:i&16}),In=i=>({container:i[5],group:i[4],heading:{attrs:i[8]}});function cf(i){let e,t,l,n,s,r,f=i[0]&&Pn(i);const o=i[14].default,a=he(o,i,i[13],Mn);let u=[i[2]],d={};for(let _=0;_<u.length;_+=1)d=U(d,u[_]);let c=[i[3],i[9]],m={};for(let _=0;_<c.length;_+=1)m=U(m,c[_]);return{c(){e=R("div"),f&&f.c(),t=K(),l=R("div"),a&&a.c(),this.h()},l(_){e=G(_,"DIV",{});var h=le(e);f&&f.l(h),t=Q(h),l=G(h,"DIV",{});var w=le(l);a&&a.l(w),w.forEach(g),h.forEach(g),this.h()},h(){Le(l,d),Le(e,m)},m(_,h){S(_,e,h),f&&f.m(e,null),ke(e,t),ke(e,l),a&&a.m(l,null),n=!0,s||(r=Ze(i[7].call(null,e)),s=!0)},p(_,h){_[0]?f?f.p(_,h):(f=Pn(_),f.c(),f.m(e,t)):f&&(f.d(1),f=null),a&&a.p&&(!n||h&8240)&&_e(a,o,_,_[13],n?be(o,_[13],h,af):ge(_[13]),Mn),Le(l,d=Ve(u,[h&4&&_[2]])),Le(e,m=Ve(c,[h&8&&_[3],h&512&&_[9]]))},i(_){n||(b(a,_),n=!0)},o(_){C(a,_),n=!1},d(_){_&&g(e),f&&f.d(),a&&a.d(_),s=!1,r()}}}function df(i){let e;const t=i[14].default,l=he(t,i,i[13],In);return{c(){l&&l.c()},l(n){l&&l.l(n)},m(n,s){l&&l.m(n,s),e=!0},p(n,s){l&&l.p&&(!e||s&8240)&&_e(l,t,n,n[13],e?be(t,n[13],s,uf):ge(n[13]),In)},i(n){e||(b(l,n),e=!0)},o(n){C(l,n),e=!1},d(n){l&&l.d(n)}}}function Pn(i){let e,t,l=[i[8]],n={};for(let s=0;s<l.length;s+=1)n=U(n,l[s]);return{c(){e=R("div"),t=Ye(i[0]),this.h()},l(s){e=G(s,"DIV",{});var r=le(e);t=Ue(r,i[0]),r.forEach(g),this.h()},h(){Le(e,n)},m(s,r){S(s,e,r),ke(e,t)},p(s,r){r&1&&ks(t,s[0],n.contenteditable)},d(s){s&&g(e)}}}function mf(i){let e,t,l,n;const s=[df,cf],r=[];function f(o,a){return o[1]?0:1}return e=f(i),t=r[e]=s[e](i),{c(){t.c(),l=te()},l(o){t.l(o),l=te()},m(o,a){r[e].m(o,a),S(o,l,a),n=!0},p(o,[a]){let u=e;e=f(o),e===u?r[e].p(o,a):(fe(),C(r[u],1,1,()=>{r[u]=null}),ae(),t=r[e],t?t.p(o,a):(t=r[e]=s[e](o),t.c()),b(t,1),t.m(l.parentNode,l))},i(o){n||(b(t),n=!0)},o(o){C(t),n=!1},d(o){o&&g(l),r[e].d(o)}}}function hf(i,e,t){let l,n,s,r;const f=["heading","value","alwaysRender","asChild"];let o=ue(e,f),a,{$$slots:u={},$$scope:d}=e,{heading:c=void 0}=e,{value:m=""}=e,{alwaysRender:_=!1}=e,{asChild:h=!1}=e;const{id:w}=_r(_),E=ei(),v=Dl(),M=Ts(),p=Bt(v,H=>_||E.filter()===!1||!H.search?!0:H.filtered.groups.has(w));Be(i,p,H=>t(12,a=H)),rl(()=>E.group(w));function F(H){if(m){E.value(w,m),H.setAttribute(Kl,m);return}c?t(10,m=c.trim().toLowerCase()):H.textContent&&t(10,m=H.textContent.trim().toLowerCase()),E.value(w,m),H.setAttribute(Kl,m)}const Y={"data-cmdk-group-heading":"","aria-hidden":!0,id:M};return i.$$set=H=>{e=U(U({},e),Re(H)),t(9,o=ue(e,f)),"heading"in H&&t(0,c=H.heading),"value"in H&&t(10,m=H.value),"alwaysRender"in H&&t(11,_=H.alwaysRender),"asChild"in H&&t(1,h=H.asChild),"$$scope"in H&&t(13,d=H.$$scope)},i.$$.update=()=>{i.$$.dirty&5120&&t(3,l={"data-cmdk-group":"",role:"presentation",hidden:a?void 0:!0,"data-value":m}),i.$$.dirty&1&&t(2,n={"data-cmdk-group-items":"",role:"group","aria-labelledby":c?M:void 0}),i.$$.dirty&8&&t(5,s={action:F,attrs:l}),i.$$.dirty&4&&t(4,r={attrs:n})},[c,h,n,l,r,s,p,F,Y,o,m,_,a,d,u]}class _f extends Ae{constructor(e){super(),Se(this,e,hf,mf,me,{heading:0,value:10,alwaysRender:11,asChild:1})}}function gf(i){return new Promise(e=>setTimeout(e,i))}const bf=i=>({attrs:i&8}),Fn=i=>({action:i[6],attrs:i[3]});function yf(i){let e,t,l,n=[i[3],i[7]],s={};for(let r=0;r<n.length;r+=1)s=U(s,n[r]);return{c(){e=R("input"),this.h()},l(r){e=G(r,"INPUT",{}),this.h()},h(){Le(e,s)},m(r,f){S(r,e,f),e.autofocus&&e.focus(),i[16](e),ln(e,i[0]),t||(l=[Xe(e,"input",i[17]),Ze(i[6].call(null,e)),Xe(e,"input",i[12]),Xe(e,"focus",i[13]),Xe(e,"blur",i[14]),Xe(e,"change",i[15])],t=!0)},p(r,f){Le(e,s=Ve(n,[f&8&&r[3],f&128&&r[7]])),f&1&&e.value!==r[0]&&ln(e,r[0])},i:we,o:we,d(r){r&&g(e),i[16](null),t=!1,sl(l)}}}function kf(i){let e;const t=i[11].default,l=he(t,i,i[10],Fn);return{c(){l&&l.c()},l(n){l&&l.l(n)},m(n,s){l&&l.m(n,s),e=!0},p(n,s){l&&l.p&&(!e||s&1032)&&_e(l,t,n,n[10],e?be(t,n[10],s,bf):ge(n[10]),Fn)},i(n){e||(b(l,n),e=!0)},o(n){C(l,n),e=!1},d(n){l&&l.d(n)}}}function Cf(i){let e,t,l,n;const s=[kf,yf],r=[];function f(o,a){return o[2]?0:1}return e=f(i),t=r[e]=s[e](i),{c(){t.c(),l=te()},l(o){t.l(o),l=te()},m(o,a){r[e].m(o,a),S(o,l,a),n=!0},p(o,[a]){let u=e;e=f(o),e===u?r[e].p(o,a):(fe(),C(r[u],1,1,()=>{r[u]=null}),ae(),t=r[e],t?t.p(o,a):(t=r[e]=s[e](o),t.c()),b(t,1),t.m(l.parentNode,l))},i(o){n||(b(t),n=!0)},o(o){C(t),n=!1},d(o){o&&g(l),r[e].d(o)}}}function wf(i,e,t){const l=["autofocus","value","asChild","el"];let n=ue(e,l),s,r,{$$slots:f={},$$scope:o}=e;const{ids:a,commandEl:u}=ei(),d=Dl(),c=Bt(d,T=>T.search);Be(i,c,T=>t(18,r=T));const m=Bt(d,T=>T.value);let{autofocus:_=void 0}=e,{value:h=r}=e,{asChild:w=!1}=e,{el:E=void 0}=e;const v=Bt([m,u],([T,y])=>{if(!br)return;const N=y==null?void 0:y.querySelector(`${yr}[${Kl}="${T}"]`);return N==null?void 0:N.getAttribute("id")});Be(i,v,T=>t(9,s=T));function M(T){d.updateState("search",T)}function p(T){return _&&gf(10).then(()=>T.focus()),{destroy:Zl(T,"change",N=>{gr(N.target)&&d.updateState("search",N.target.value)})}}let F;function Y(T){Hl.call(this,i,T)}function H(T){Hl.call(this,i,T)}function ne(T){Hl.call(this,i,T)}function I(T){Hl.call(this,i,T)}function k(T){ot[T?"unshift":"push"](()=>{E=T,t(1,E)})}function D(){h=this.value,t(0,h)}return i.$$set=T=>{e=U(U({},e),Re(T)),t(7,n=ue(e,l)),"autofocus"in T&&t(8,_=T.autofocus),"value"in T&&t(0,h=T.value),"asChild"in T&&t(2,w=T.asChild),"el"in T&&t(1,E=T.el),"$$scope"in T&&t(10,o=T.$$scope)},i.$$.update=()=>{i.$$.dirty&1&&M(h),i.$$.dirty&512&&t(3,F={type:"text","data-cmdk-input":"",autocomplete:"off",autocorrect:"off",spellcheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":a.list,"aria-labelledby":a.label,"aria-activedescendant":s??void 0,id:a.input})},[h,E,w,F,c,v,p,n,_,s,o,f,Y,H,ne,I,k,D]}class Af extends Ae{constructor(e){super(),Se(this,e,wf,Cf,me,{autofocus:8,value:0,asChild:2,el:1})}}const Sf=i=>({attrs:i&4}),Nn=i=>({action:i[6],attrs:i[2]}),Of=i=>({attrs:i&4}),Bn=i=>({action:i[6],attrs:i[2]});function jn(i){let e,t,l,n;const s=[Lf,Tf],r=[];function f(o,a){return o[0]?0:1}return e=f(i),t=r[e]=s[e](i),{c(){t.c(),l=te()},l(o){t.l(o),l=te()},m(o,a){r[e].m(o,a),S(o,l,a),n=!0},p(o,a){let u=e;e=f(o),e===u?r[e].p(o,a):(fe(),C(r[u],1,1,()=>{r[u]=null}),ae(),t=r[e],t?t.p(o,a):(t=r[e]=s[e](o),t.c()),b(t,1),t.m(l.parentNode,l))},i(o){n||(b(t),n=!0)},o(o){C(t),n=!1},d(o){o&&g(l),r[e].d(o)}}}function Tf(i){let e,t,l,n;const s=i[15].default,r=he(s,i,i[14],Nn);let f=[i[2],i[7]],o={};for(let a=0;a<f.length;a+=1)o=U(o,f[a]);return{c(){e=R("div"),r&&r.c(),this.h()},l(a){e=G(a,"DIV",{});var u=le(e);r&&r.l(u),u.forEach(g),this.h()},h(){Le(e,o)},m(a,u){S(a,e,u),r&&r.m(e,null),t=!0,l||(n=Ze(i[6].call(null,e)),l=!0)},p(a,u){r&&r.p&&(!t||u&16388)&&_e(r,s,a,a[14],t?be(s,a[14],u,Sf):ge(a[14]),Nn),Le(e,o=Ve(f,[u&4&&a[2],u&128&&a[7]]))},i(a){t||(b(r,a),t=!0)},o(a){C(r,a),t=!1},d(a){a&&g(e),r&&r.d(a),l=!1,n()}}}function Lf(i){let e;const t=i[15].default,l=he(t,i,i[14],Bn);return{c(){l&&l.c()},l(n){l&&l.l(n)},m(n,s){l&&l.m(n,s),e=!0},p(n,s){l&&l.p&&(!e||s&16388)&&_e(l,t,n,n[14],e?be(t,n[14],s,Of):ge(n[14]),Bn)},i(n){e||(b(l,n),e=!0)},o(n){C(l,n),e=!1},d(n){l&&l.d(n)}}}function vf(i){let e,t,l=(i[3]||i[1])&&jn(i);return{c(){l&&l.c(),e=te()},l(n){l&&l.l(n),e=te()},m(n,s){l&&l.m(n,s),S(n,e,s),t=!0},p(n,[s]){n[3]||n[1]?l?(l.p(n,s),s&10&&b(l,1)):(l=jn(n),l.c(),b(l,1),l.m(e.parentNode,e)):l&&(fe(),C(l,1,1,()=>{l=null}),ae())},i(n){t||(b(l),t=!0)},o(n){C(l),t=!1},d(n){n&&g(e),l&&l.d(n)}}}function Df(i,e,t){let l;const n=["disabled","value","onSelect","alwaysRender","asChild","id"];let s=ue(e,n),r,f,{$$slots:o={},$$scope:a}=e,{disabled:u=!1}=e,{value:d=""}=e,{onSelect:c=void 0}=e,{alwaysRender:m=!1}=e,{asChild:_=!1}=e,{id:h=Ts()}=e;const w=kr(),E=ei(),v=Dl(),M=m??(w==null?void 0:w.alwaysRender),p=Bt(v,k=>{if(M||E.filter()===!1||!k.search)return!0;const D=k.filtered.items.get(h);return Cr(D)?!1:D>0});Be(i,p,k=>t(3,f=k));let F=!0;rl(()=>(t(1,F=!1),E.item(h,w==null?void 0:w.id)));const Y=Bt(v,k=>k.value===d);Be(i,Y,k=>t(13,r=k));function H(k){!d&&k.textContent&&t(8,d=k.textContent.trim().toLowerCase()),E.value(h,d),k.setAttribute(Kl,d);const D=zs(Zl(k,"pointermove",()=>{u||I()}),Zl(k,"click",()=>{u||ne()}));return{destroy(){D()}}}function ne(){I(),c==null||c(d)}function I(){v.updateState("value",d,!0)}return i.$$set=k=>{e=U(U({},e),Re(k)),t(7,s=ue(e,n)),"disabled"in k&&t(9,u=k.disabled),"value"in k&&t(8,d=k.value),"onSelect"in k&&t(10,c=k.onSelect),"alwaysRender"in k&&t(11,m=k.alwaysRender),"asChild"in k&&t(0,_=k.asChild),"id"in k&&t(12,h=k.id),"$$scope"in k&&t(14,a=k.$$scope)},i.$$.update=()=>{i.$$.dirty&13056&&t(2,l={"aria-disabled":u?!0:void 0,"aria-selected":r?!0:void 0,"data-disabled":u?!0:void 0,"data-selected":r?!0:void 0,"data-cmdk-item":"","data-value":d,role:"option",id:h})},[_,F,l,f,p,Y,H,s,d,u,c,m,h,r,a,o]}class Ef extends Ae{constructor(e){super(),Se(this,e,Df,vf,me,{disabled:9,value:8,onSelect:10,alwaysRender:11,asChild:0,id:12})}}const pf=i=>({}),Vn=i=>({list:i[7],sizer:i[8]});function Mf(i){let e,t,l=i[2].search==="",n,s,r,f=zn(i),o=[i[6]],a={};for(let c=0;c<o.length;c+=1)a=U(a,o[c]);let u=[i[5],i[9]],d={};for(let c=0;c<u.length;c+=1)d=U(d,u[c]);return{c(){e=R("div"),t=R("div"),f.c(),this.h()},l(c){e=G(c,"DIV",{});var m=le(e);t=G(m,"DIV",{});var _=le(t);f.l(_),_.forEach(g),m.forEach(g),this.h()},h(){Le(t,a),Le(e,d)},m(c,m){S(c,e,m),ke(e,t),f.m(t,null),i[12](e),n=!0,s||(r=Ze(i[4].call(null,t)),s=!0)},p(c,m){m&4&&me(l,l=c[2].search==="")?(fe(),C(f,1,1,we),ae(),f=zn(c),f.c(),b(f,1),f.m(t,null)):f.p(c,m),Le(e,d=Ve(u,[c[5],m&512&&c[9]]))},i(c){n||(b(f),n=!0)},o(c){C(f),n=!1},d(c){c&&g(e),f.d(c),i[12](null),s=!1,r()}}}function If(i){let e=i[2].search==="",t,l,n=Hn(i);return{c(){n.c(),t=te()},l(s){n.l(s),t=te()},m(s,r){n.m(s,r),S(s,t,r),l=!0},p(s,r){r&4&&me(e,e=s[2].search==="")?(fe(),C(n,1,1,we),ae(),n=Hn(s),n.c(),b(n,1),n.m(t.parentNode,t)):n.p(s,r)},i(s){l||(b(n),l=!0)},o(s){C(n),l=!1},d(s){s&&g(t),n.d(s)}}}function zn(i){let e;const t=i[11].default,l=he(t,i,i[10],null);return{c(){l&&l.c()},l(n){l&&l.l(n)},m(n,s){l&&l.m(n,s),e=!0},p(n,s){l&&l.p&&(!e||s&1024)&&_e(l,t,n,n[10],e?be(t,n[10],s,null):ge(n[10]),null)},i(n){e||(b(l,n),e=!0)},o(n){C(l,n),e=!1},d(n){l&&l.d(n)}}}function Hn(i){let e;const t=i[11].default,l=he(t,i,i[10],Vn);return{c(){l&&l.c()},l(n){l&&l.l(n)},m(n,s){l&&l.m(n,s),e=!0},p(n,s){l&&l.p&&(!e||s&1024)&&_e(l,t,n,n[10],e?be(t,n[10],s,pf):ge(n[10]),Vn)},i(n){e||(b(l,n),e=!0)},o(n){C(l,n),e=!1},d(n){l&&l.d(n)}}}function Pf(i){let e,t,l,n;const s=[If,Mf],r=[];function f(o,a){return o[1]?0:1}return e=f(i),t=r[e]=s[e](i),{c(){t.c(),l=te()},l(o){t.l(o),l=te()},m(o,a){r[e].m(o,a),S(o,l,a),n=!0},p(o,[a]){let u=e;e=f(o),e===u?r[e].p(o,a):(fe(),C(r[u],1,1,()=>{r[u]=null}),ae(),t=r[e],t?t.p(o,a):(t=r[e]=s[e](o),t.c()),b(t,1),t.m(l.parentNode,l))},i(o){n||(b(t),n=!0)},o(o){C(t),n=!1},d(o){o&&g(l),r[e].d(o)}}}function Ff(i,e,t){const l=["el","asChild"];let n=ue(e,l),s,{$$slots:r={},$$scope:f}=e;const{ids:o}=ei(),a=Dl();Be(i,a,v=>t(2,s=v));let{el:u=void 0}=e,{asChild:d=!1}=e;function c(v){let M;const p=v.closest("[data-cmdk-list]");if(!wr(p))return;const F=new ResizeObserver(()=>{M=requestAnimationFrame(()=>{const Y=v.offsetHeight;p.style.setProperty("--cmdk-list-height",Y.toFixed(1)+"px")})});return F.observe(v),{destroy(){cancelAnimationFrame(M),F.unobserve(v)}}}const m={"data-cmdk-list":"",role:"listbox","aria-label":"Suggestions",id:o.list,"aria-labelledby":o.input},_={"data-cmdk-list-sizer":""},h={attrs:m},w={attrs:_,action:c};function E(v){ot[v?"unshift":"push"](()=>{u=v,t(0,u)})}return i.$$set=v=>{e=U(U({},e),Re(v)),t(9,n=ue(e,l)),"el"in v&&t(0,u=v.el),"asChild"in v&&t(1,d=v.asChild),"$$scope"in v&&t(10,f=v.$$scope)},[u,d,s,a,c,m,_,h,w,n,f,r,E]}class Nf extends Ae{constructor(e){super(),Se(this,e,Ff,Pf,me,{el:0,asChild:1})}}function Bf(i){let e;const t=i[3].default,l=he(t,i,i[5],null);return{c(){l&&l.c()},l(n){l&&l.l(n)},m(n,s){l&&l.m(n,s),e=!0},p(n,s){l&&l.p&&(!e||s&32)&&_e(l,t,n,n[5],e?be(t,n[5],s,null):ge(n[5]),null)},i(n){e||(b(l,n),e=!0)},o(n){C(l,n),e=!1},d(n){l&&l.d(n)}}}function jf(i){let e,t,l;const n=[{class:ze("flex h-full w-full flex-col overflow-hidden rounded-md bg-base-100",i[1])},i[2]];function s(f){i[4](f)}let r={$$slots:{default:[Bf]},$$scope:{ctx:i}};for(let f=0;f<n.length;f+=1)r=U(r,n[f]);return i[0]!==void 0&&(r.value=i[0]),e=new tf({props:r}),ot.push(()=>$l(e,"value",s)),{c(){$(e.$$.fragment)},l(f){x(e.$$.fragment,f)},m(f,o){Z(e,f,o),l=!0},p(f,[o]){const a=o&6?Ve(n,[o&2&&{class:ze("flex h-full w-full flex-col overflow-hidden rounded-md bg-base-100",f[1])},o&4&&Tt(f[2])]):{};o&32&&(a.$$scope={dirty:o,ctx:f}),!t&&o&1&&(t=!0,a.value=f[0],xl(()=>t=!1)),e.$set(a)},i(f){l||(b(e.$$.fragment,f),l=!0)},o(f){C(e.$$.fragment,f),l=!1},d(f){J(e,f)}}}function Vf(i,e,t){const l=["value","class"];let n=ue(e,l),{$$slots:s={},$$scope:r}=e,{value:f=void 0}=e,{class:o=void 0}=e;function a(u){f=u,t(0,f)}return i.$$set=u=>{e=U(U({},e),Re(u)),t(2,n=ue(e,l)),"value"in u&&t(0,f=u.value),"class"in u&&t(1,o=u.class),"$$scope"in u&&t(5,r=u.$$scope)},[f,o,n,s,a,r]}class zf extends Ae{constructor(e){super(),Se(this,e,Vf,jf,me,{value:0,class:1})}}function Hf(i){let e;const t=i[2].default,l=he(t,i,i[3],null);return{c(){l&&l.c()},l(n){l&&l.l(n)},m(n,s){l&&l.m(n,s),e=!0},p(n,s){l&&l.p&&(!e||s&8)&&_e(l,t,n,n[3],e?be(t,n[3],s,null):ge(n[3]),null)},i(n){e||(b(l,n),e=!0)},o(n){C(l,n),e=!1},d(n){l&&l.d(n)}}}function qf(i){let e,t;const l=[{class:ze("py-6 text-center text-sm",i[0])},i[1]];let n={$$slots:{default:[Hf]},$$scope:{ctx:i}};for(let s=0;s<l.length;s+=1)n=U(n,l[s]);return e=new ff({props:n}),{c(){$(e.$$.fragment)},l(s){x(e.$$.fragment,s)},m(s,r){Z(e,s,r),t=!0},p(s,[r]){const f=r&3?Ve(l,[r&1&&{class:ze("py-6 text-center text-sm",s[0])},r&2&&Tt(s[1])]):{};r&8&&(f.$$scope={dirty:r,ctx:s}),e.$set(f)},i(s){t||(b(e.$$.fragment,s),t=!0)},o(s){C(e.$$.fragment,s),t=!1},d(s){J(e,s)}}}function Gf(i,e,t){const l=["class"];let n=ue(e,l),{$$slots:s={},$$scope:r}=e,{class:f=void 0}=e;return i.$$set=o=>{e=U(U({},e),Re(o)),t(1,n=ue(e,l)),"class"in o&&t(0,f=o.class),"$$scope"in o&&t(3,r=o.$$scope)},[f,n,s,r]}class Rf extends Ae{constructor(e){super(),Se(this,e,Gf,qf,me,{class:0})}}function Wf(i){let e;const t=i[2].default,l=he(t,i,i[3],null);return{c(){l&&l.c()},l(n){l&&l.l(n)},m(n,s){l&&l.m(n,s),e=!0},p(n,s){l&&l.p&&(!e||s&8)&&_e(l,t,n,n[3],e?be(t,n[3],s,null):ge(n[3]),null)},i(n){e||(b(l,n),e=!0)},o(n){C(l,n),e=!1},d(n){l&&l.d(n)}}}function Xf(i){let e,t;const l=[{class:ze("text-foreground overflow-hidden p-1 [&_[data-cmdk-group-heading]]:px-2 [&_[data-cmdk-group-heading]]:py-1.5 [&_[data-cmdk-group-heading]]:text-xs [&_[data-cmdk-group-heading]]:font-medium [&_[data-cmdk-group-heading]]:text-base-content-muted",i[0])},i[1]];let n={$$slots:{default:[Wf]},$$scope:{ctx:i}};for(let s=0;s<l.length;s+=1)n=U(n,l[s]);return e=new _f({props:n}),{c(){$(e.$$.fragment)},l(s){x(e.$$.fragment,s)},m(s,r){Z(e,s,r),t=!0},p(s,[r]){const f=r&3?Ve(l,[r&1&&{class:ze("text-foreground overflow-hidden p-1 [&_[data-cmdk-group-heading]]:px-2 [&_[data-cmdk-group-heading]]:py-1.5 [&_[data-cmdk-group-heading]]:text-xs [&_[data-cmdk-group-heading]]:font-medium [&_[data-cmdk-group-heading]]:text-base-content-muted",s[0])},r&2&&Tt(s[1])]):{};r&8&&(f.$$scope={dirty:r,ctx:s}),e.$set(f)},i(s){t||(b(e.$$.fragment,s),t=!0)},o(s){C(e.$$.fragment,s),t=!1},d(s){J(e,s)}}}function Uf(i,e,t){const l=["class"];let n=ue(e,l),{$$slots:s={},$$scope:r}=e,{class:f=void 0}=e;return i.$$set=o=>{e=U(U({},e),Re(o)),t(1,n=ue(e,l)),"class"in o&&t(0,f=o.class),"$$scope"in o&&t(3,r=o.$$scope)},[f,n,s,r]}class Yf extends Ae{constructor(e){super(),Se(this,e,Uf,Xf,me,{class:0})}}function Qf(i){let e;const t=i[2].default,l=he(t,i,i[3],null);return{c(){l&&l.c()},l(n){l&&l.l(n)},m(n,s){l&&l.m(n,s),e=!0},p(n,s){l&&l.p&&(!e||s&8)&&_e(l,t,n,n[3],e?be(t,n[3],s,null):ge(n[3]),null)},i(n){e||(b(l,n),e=!0)},o(n){C(l,n),e=!1},d(n){l&&l.d(n)}}}function Kf(i){let e,t;const l=[{class:ze("relative flex cursor-default select-none items-center rounded-xs px-2 py-1.5 text-sm outline-none aria-selected:bg-base-200 data-[disabled]:pointer-events-none data-[disabled]:opacity-50",i[0])},i[1]];let n={$$slots:{default:[Qf]},$$scope:{ctx:i}};for(let s=0;s<l.length;s+=1)n=U(n,l[s]);return e=new Ef({props:n}),{c(){$(e.$$.fragment)},l(s){x(e.$$.fragment,s)},m(s,r){Z(e,s,r),t=!0},p(s,[r]){const f=r&3?Ve(l,[r&1&&{class:ze("relative flex cursor-default select-none items-center rounded-xs px-2 py-1.5 text-sm outline-none aria-selected:bg-base-200 data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s[0])},r&2&&Tt(s[1])]):{};r&8&&(f.$$scope={dirty:r,ctx:s}),e.$set(f)},i(s){t||(b(e.$$.fragment,s),t=!0)},o(s){C(e.$$.fragment,s),t=!1},d(s){J(e,s)}}}function Jf(i,e,t){const l=["class"];let n=ue(e,l),{$$slots:s={},$$scope:r}=e,{class:f=void 0}=e;return i.$$set=o=>{e=U(U({},e),Re(o)),t(1,n=ue(e,l)),"class"in o&&t(0,f=o.class),"$$scope"in o&&t(3,r=o.$$scope)},[f,n,s,r]}class Wi extends Ae{constructor(e){super(),Se(this,e,Jf,Kf,me,{class:0})}}function Zf(i){let e,t,l,n,s,r;t=new ti({props:{src:Ar,class:"mr-2 h-4 w-4 shrink-0 text-base-content-muted"}});const f=[{class:ze("flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-base-content-muted disabled:cursor-not-allowed disabled:opacity-50",i[1])},i[2]];function o(u){i[3](u)}let a={};for(let u=0;u<f.length;u+=1)a=U(a,f[u]);return i[0]!==void 0&&(a.value=i[0]),n=new Af({props:a}),ot.push(()=>$l(n,"value",o)),{c(){e=R("div"),$(t.$$.fragment),l=K(),$(n.$$.fragment),this.h()},l(u){e=G(u,"DIV",{class:!0,"data-cmdk-input-wrapper":!0});var d=le(e);x(t.$$.fragment,d),l=Q(d),x(n.$$.fragment,d),d.forEach(g),this.h()},h(){V(e,"class","flex items-center border-b border-base-300 px-3"),V(e,"data-cmdk-input-wrapper","")},m(u,d){S(u,e,d),Z(t,e,null),ke(e,l),Z(n,e,null),r=!0},p(u,[d]){const c=d&6?Ve(f,[d&2&&{class:ze("flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-base-content-muted disabled:cursor-not-allowed disabled:opacity-50",u[1])},d&4&&Tt(u[2])]):{};!s&&d&1&&(s=!0,c.value=u[0],xl(()=>s=!1)),n.$set(c)},i(u){r||(b(t.$$.fragment,u),b(n.$$.fragment,u),r=!0)},o(u){C(t.$$.fragment,u),C(n.$$.fragment,u),r=!1},d(u){u&&g(e),J(t),J(n)}}}function xf(i,e,t){const l=["class","value"];let n=ue(e,l),{class:s=void 0}=e,{value:r=""}=e;function f(o){r=o,t(0,r)}return i.$$set=o=>{e=U(U({},e),Re(o)),t(2,n=ue(e,l)),"class"in o&&t(1,s=o.class),"value"in o&&t(0,r=o.value)},[r,s,n,f]}class $f extends Ae{constructor(e){super(),Se(this,e,xf,Zf,me,{class:1,value:0})}}function ea(i){let e;const t=i[2].default,l=he(t,i,i[3],null);return{c(){l&&l.c()},l(n){l&&l.l(n)},m(n,s){l&&l.m(n,s),e=!0},p(n,s){l&&l.p&&(!e||s&8)&&_e(l,t,n,n[3],e?be(t,n[3],s,null):ge(n[3]),null)},i(n){e||(b(l,n),e=!0)},o(n){C(l,n),e=!1},d(n){l&&l.d(n)}}}function ta(i){let e,t;const l=[{class:ze("max-h-[300px] overflow-y-auto overflow-x-hidden",i[0])},i[1]];let n={$$slots:{default:[ea]},$$scope:{ctx:i}};for(let s=0;s<l.length;s+=1)n=U(n,l[s]);return e=new Nf({props:n}),{c(){$(e.$$.fragment)},l(s){x(e.$$.fragment,s)},m(s,r){Z(e,s,r),t=!0},p(s,[r]){const f=r&3?Ve(l,[r&1&&{class:ze("max-h-[300px] overflow-y-auto overflow-x-hidden",s[0])},r&2&&Tt(s[1])]):{};r&8&&(f.$$scope={dirty:r,ctx:s}),e.$set(f)},i(s){t||(b(e.$$.fragment,s),t=!0)},o(s){C(e.$$.fragment,s),t=!1},d(s){J(e,s)}}}function la(i,e,t){const l=["class"];let n=ue(e,l),{$$slots:s={},$$scope:r}=e,{class:f=void 0}=e;return i.$$set=o=>{e=U(U({},e),Re(o)),t(1,n=ue(e,l)),"class"in o&&t(0,f=o.class),"$$scope"in o&&t(3,r=o.$$scope)},[f,n,s,r]}class ia extends Ae{constructor(e){super(),Se(this,e,la,ta,me,{class:0})}}function na(i){let e,t,l;return t=new ti({props:{src:Ls,class:ze("h-4 w-4",i[2]?"":"text-transparent")}}),{c(){e=R("div"),$(t.$$.fragment),this.h()},l(n){e=G(n,"DIV",{class:!0});var s=le(e);x(t.$$.fragment,s),s.forEach(g),this.h()},h(){V(e,"class","mr-2 flex h-4 w-4 items-center justify-center")},m(n,s){S(n,e,s),Z(t,e,null),l=!0},p(n,s){const r={};s&4&&(r.class=ze("h-4 w-4",n[2]?"":"text-transparent")),t.$set(r)},i(n){l||(b(t.$$.fragment,n),l=!0)},o(n){C(t.$$.fragment,n),l=!1},d(n){n&&g(e),J(t)}}}function sa(i){let e,t,l,n;return t=new ti({props:{src:Ls,class:ze("h-4 w-4")}}),{c(){e=R("div"),$(t.$$.fragment),this.h()},l(s){e=G(s,"DIV",{class:!0});var r=le(e);x(t.$$.fragment,r),r.forEach(g),this.h()},h(){V(e,"class",l=ze("mr-2 flex h-4 w-4 items-center justify-center rounded-xs border border-base-content",i[2]?"bg-base-content text-base-100":"opacity-50 [&_svg]:invisible"))},m(s,r){S(s,e,r),Z(t,e,null),n=!0},p(s,r){(!n||r&4&&l!==(l=ze("mr-2 flex h-4 w-4 items-center justify-center rounded-xs border border-base-content",s[2]?"bg-base-content text-base-100":"opacity-50 [&_svg]:invisible")))&&V(e,"class",l)},i(s){n||(b(t.$$.fragment,s),n=!0)},o(s){C(t.$$.fragment,s),n=!1},d(s){s&&g(e),J(t)}}}function ra(i){let e,t,l,n,s,r;const f=[sa,na],o=[];function a(u,d){return u[4]?0:1}return e=a(i),t=o[e]=f[e](i),{c(){t.c(),l=K(),n=R("span"),s=Ye(i[1]),this.h()},l(u){t.l(u),l=Q(u),n=G(u,"SPAN",{class:!0});var d=le(n);s=Ue(d,i[1]),d.forEach(g),this.h()},h(){V(n,"class","line-clamp-4")},m(u,d){o[e].m(u,d),S(u,l,d),S(u,n,d),ke(n,s),r=!0},p(u,d){let c=e;e=a(u),e===c?o[e].p(u,d):(fe(),C(o[c],1,1,()=>{o[c]=null}),ae(),t=o[e],t?t.p(u,d):(t=o[e]=f[e](u),t.c()),b(t,1),t.m(l.parentNode,l)),(!r||d&2)&&bt(s,u[1])},i(u){r||(b(t),r=!0)},o(u){C(t),r=!1},d(u){u&&(g(l),g(n)),o[e].d(u)}}}function oa(i){let e,t;return e=new Wi({props:{value:String(i[1]),onSelect:i[5],$$slots:{default:[ra]},$$scope:{ctx:i}}}),{c(){$(e.$$.fragment)},l(l){x(e.$$.fragment,l)},m(l,n){Z(e,l,n),t=!0},p(l,[n]){const s={};n&2&&(s.value=String(l[1])),n&11&&(s.onSelect=l[5]),n&86&&(s.$$scope={dirty:n,ctx:l}),e.$set(s)},i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){C(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function fa(i,e,t){let{value:l}=e,{valueLabel:n=l}=e,{active:s=!1}=e,{handleSelect:r}=e,{multiple:f}=e;const o=()=>r({value:l,label:n});return i.$$set=a=>{"value"in a&&t(0,l=a.value),"valueLabel"in a&&t(1,n=a.valueLabel),"active"in a&&t(2,s=a.active),"handleSelect"in a&&t(3,r=a.handleSelect),"multiple"in a&&t(4,f=a.multiple)},[l,n,s,r,f,o]}class Hs extends Ae{constructor(e){super(),Se(this,e,fa,oa,me,{value:0,valueLabel:1,active:2,handleSelect:3,multiple:4})}}function aa(i){let e;const t=i[6].default,l=he(t,i,i[7],null);return{c(){l&&l.c()},l(n){l&&l.l(n)},m(n,s){l&&l.m(n,s),e=!0},p(n,s){l&&l.p&&(!e||s&128)&&_e(l,t,n,n[7],e?be(t,n[7],s,null):ge(n[7]),null)},i(n){e||(b(l,n),e=!0)},o(n){C(l,n),e=!1},d(n){l&&l.d(n)}}}function ua(i){let e,t;const l=[{transition:i[1]},{transitionConfig:i[2]},{align:i[3]},{sideOffset:i[4]},i[5],{class:ze("z-50 w-72 rounded-md border border-base-300 p-4 shadow-md outline-none bg-base-100",i[0])}];let n={$$slots:{default:[aa]},$$scope:{ctx:i}};for(let s=0;s<l.length;s+=1)n=U(n,l[s]);return e=new Io({props:n}),{c(){$(e.$$.fragment)},l(s){x(e.$$.fragment,s)},m(s,r){Z(e,s,r),t=!0},p(s,[r]){const f=r&63?Ve(l,[r&2&&{transition:s[1]},r&4&&{transitionConfig:s[2]},r&8&&{align:s[3]},r&16&&{sideOffset:s[4]},r&32&&Tt(s[5]),r&1&&{class:ze("z-50 w-72 rounded-md border border-base-300 p-4 shadow-md outline-none bg-base-100",s[0])}]):{};r&128&&(f.$$scope={dirty:r,ctx:s}),e.$set(f)},i(s){t||(b(e.$$.fragment,s),t=!0)},o(s){C(e.$$.fragment,s),t=!1},d(s){J(e,s)}}}function ca(i,e,t){const l=["class","transition","transitionConfig","align","sideOffset"];let n=ue(e,l),{$$slots:s={},$$scope:r}=e,{class:f=void 0}=e,{transition:o=Sr}=e,{transitionConfig:a=void 0}=e,{align:u="center"}=e,{sideOffset:d=4}=e;return i.$$set=c=>{e=U(U({},e),Re(c)),t(5,n=ue(e,l)),"class"in c&&t(0,f=c.class),"transition"in c&&t(1,o=c.transition),"transitionConfig"in c&&t(2,a=c.transitionConfig),"align"in c&&t(3,u=c.align),"sideOffset"in c&&t(4,d=c.sideOffset),"$$scope"in c&&t(7,r=c.$$scope)},[f,o,a,u,d,n,s,r]}class da extends Ae{constructor(e){super(),Se(this,e,ca,ua,me,{class:0,transition:1,transitionConfig:2,align:3,sideOffset:4})}}const ma=bo,ha=zo;function _a(i){let e,t;const l=[{class:ze("shrink-0 bg-base-300",i[1]==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",i[0])},{orientation:i[1]},{decorative:i[2]},i[3]];let n={};for(let s=0;s<l.length;s+=1)n=U(n,l[s]);return e=new uo({props:n}),{c(){$(e.$$.fragment)},l(s){x(e.$$.fragment,s)},m(s,r){Z(e,s,r),t=!0},p(s,[r]){const f=r&15?Ve(l,[r&3&&{class:ze("shrink-0 bg-base-300",s[1]==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",s[0])},r&2&&{orientation:s[1]},r&4&&{decorative:s[2]},r&8&&Tt(s[3])]):{};e.$set(f)},i(s){t||(b(e.$$.fragment,s),t=!0)},o(s){C(e.$$.fragment,s),t=!1},d(s){J(e,s)}}}function ga(i,e,t){const l=["class","orientation","decorative"];let n=ue(e,l),{class:s=void 0}=e,{orientation:r="horizontal"}=e,{decorative:f=void 0}=e;return i.$$set=o=>{e=U(U({},e),Re(o)),t(3,n=ue(e,l)),"class"in o&&t(0,s=o.class),"orientation"in o&&t(1,r=o.orientation),"decorative"in o&&t(2,f=o.decorative)},[s,r,f,n]}class qs extends Ae{constructor(e){super(),Se(this,e,ga,_a,me,{class:0,orientation:1,decorative:2})}}function Pi(i){let e,t,l;const n=i[5].default,s=he(n,i,i[4],null);let r=[{href:i[1]},{class:t=ze(un({variant:i[2],className:i[0]}))},i[3]],f={};for(let o=0;o<r.length;o+=1)f=U(f,r[o]);return{c(){e=R(i[1]?"a":"span"),s&&s.c(),this.h()},l(o){e=G(o,((i[1]?"a":"span")||"null").toUpperCase(),{href:!0,class:!0});var a=le(e);s&&s.l(a),a.forEach(g),this.h()},h(){nn(i[1]?"a":"span")(e,f)},m(o,a){S(o,e,a),s&&s.m(e,null),l=!0},p(o,a){s&&s.p&&(!l||a&16)&&_e(s,n,o,o[4],l?be(n,o[4],a,null):ge(o[4]),null),nn(o[1]?"a":"span")(e,f=Ve(r,[(!l||a&2)&&{href:o[1]},(!l||a&5&&t!==(t=ze(un({variant:o[2],className:o[0]}))))&&{class:t},a&8&&o[3]]))},i(o){l||(b(s,o),l=!0)},o(o){C(s,o),l=!1},d(o){o&&g(e),s&&s.d(o)}}}function ba(i){let e=i[1]?"a":"span",t,l,n=(i[1]?"a":"span")&&Pi(i);return{c(){n&&n.c(),t=te()},l(s){n&&n.l(s),t=te()},m(s,r){n&&n.m(s,r),S(s,t,r),l=!0},p(s,[r]){s[1],e?me(e,s[1]?"a":"span")?(n.d(1),n=Pi(s),e=s[1]?"a":"span",n.c(),n.m(t.parentNode,t)):n.p(s,r):(n=Pi(s),e=s[1]?"a":"span",n.c(),n.m(t.parentNode,t))},i(s){l||(b(n,s),l=!0)},o(s){C(n,s),l=!1},d(s){s&&g(t),n&&n.d(s)}}}function ya(i,e,t){const l=["class","href","variant"];let n=ue(e,l),{$$slots:s={},$$scope:r}=e,{class:f=void 0}=e,{href:o=void 0}=e,{variant:a="default"}=e;return i.$$set=u=>{e=U(U({},e),Re(u)),t(3,n=ue(e,l)),"class"in u&&t(0,f=u.class),"href"in u&&t(1,o=u.href),"variant"in u&&t(2,a=u.variant),"$$scope"in u&&t(4,r=u.$$scope)},[f,o,a,n,r,s]}class Xi extends Ae{constructor(e){super(),Se(this,e,ya,ba,me,{class:0,href:1,variant:2})}}function qn(i){return t=>t.map(l=>{var n;const s={},r=Object.keys(l);for(const f of r){const o=(n=i[f])!=null?n:f;s[o]=l[f]}return s})}function ka(i,e){if(i.length===0||e.length===0)return{};const t=Object.keys(i[0]),l=Object.keys(e[0]),n={};for(const s of t)l.includes(s)&&(n[s]=s);return n}function Ca(i,e,t){for(const l in t){const n=t[l];if(i[n]!==e[l])return!1}return!0}function wa(i,e){return l=>{if(!i.length)return l;const n=ka(l,i),s=Object.keys(i[0]);return l.flatMap(f=>{const o=i.filter(u=>Ca(f,u,n));if(o.length)return o.map(u=>({...f,...u}));const a=Object.fromEntries(s.filter(u=>f[u]==null).map(u=>[u,void 0]));return{...f,...a}})}}function Gn(i){return t=>{const l=t.map(n=>({...n}));for(const n in i){const s=i[n],r=typeof s=="function"?s(l):s,f=r!=null&&r[Symbol.iterator]&&typeof r!="string"?r:t.map(()=>r);let o=-1;for(const a of l)a[n]=f[++o]}return l}}function Aa(i){return t=>{const l=Oa(i),n=[];for(const s in l){const r=l[s];let f;typeof r=="function"?f=r(t):Array.isArray(r)?f=r:f=Array.from(new Set(t.map(o=>o[s]))),n.push(f.map(o=>({[s]:o})))}return Sa(n)}}function Sa(i){function e(l,n,s){if(!s.length&&n!=null){l.push(n);return}const r=s[0],f=s.slice(1);for(const o of r)e(l,{...n,...o},f)}const t=[];return e(t,null,i),t}function Oa(i){if(Array.isArray(i)){const e={};for(const t of i)e[t]=t;return e}else if(typeof i=="object")return i;return{[i]:i}}function Ta(i){return t=>{const l=[];for(const n of t){const s={...n};for(const r in i)s[r]==null&&(s[r]=i[r]);l.push(s)}return l}}function Rn(i,e){return l=>{const n=Aa(i)(l),s=wa(l)(n);return e?Ta(e)(s):s}}function Wn(i,e,t){return i==null||e==null?void 0:e===0&&i===0?0:!t&&e===0?void 0:i/e}function Xn(i,e,t){const l=typeof i=="function"?i:f=>f[i],n=f=>f[e],{predicate:s,allowDivideByZero:r}={};return s==null?(f,o,a)=>{const u=n(f),d=l(f,o,a);return Wn(d,u,r)}:(f,o,a)=>{if(!s(f,o,a))return;const u=n(f),d=l(f,o,a);return Wn(d,u,r)}}function Un(i,e,t){const l=i.slice();return l[22]=e[t],l}const La=i=>({item:i&16}),Yn=i=>({item:i[22].data});function va(i){let e;return{c(){e=Ye("Missing template")},l(t){e=Ue(t,"Missing template")},m(t,l){S(t,e,l)},d(t){t&&g(e)}}}function Qn(i,e){let t,l,n;const s=e[14].default,r=he(s,e,e[13],Yn),f=r||va();return{key:i,first:null,c(){t=R("div"),f&&f.c(),l=K(),this.h()},l(o){t=G(o,"DIV",{class:!0});var a=le(t);f&&f.l(a),l=Q(a),a.forEach(g),this.h()},h(){V(t,"class","row svelte-1youqmj"),this.first=t},m(o,a){S(o,t,a),f&&f.m(t,null),ke(t,l),n=!0},p(o,a){e=o,r&&r.p&&(!n||a&8208)&&_e(r,s,e,e[13],n?be(s,e[13],a,La):ge(e[13]),Yn)},i(o){n||(b(f,o),n=!0)},o(o){C(f,o),n=!1},d(o){o&&g(t),f&&f.d(o)}}}function Da(i){let e,t,l=[],n=new Map,s,r,f,o,a=pt(i[4]);const u=d=>d[22].index;for(let d=0;d<a.length;d+=1){let c=Un(i,a,d),m=u(c);n.set(m,l[d]=Qn(m,c))}return{c(){e=R("div"),t=R("div");for(let d=0;d<l.length;d+=1)l[d].c();this.h()},l(d){e=G(d,"DIV",{style:!0,class:!0});var c=le(e);t=G(c,"DIV",{class:!0,style:!0});var m=le(t);for(let _=0;_<l.length;_+=1)l[_].l(m);m.forEach(g),c.forEach(g),this.h()},h(){V(t,"class","contents svelte-1youqmj"),W(t,"padding-top",i[5]+"px"),W(t,"padding-bottom",i[6]+"px"),W(e,"height",i[0]),V(e,"class","viewport svelte-1youqmj"),nl(()=>i[17].call(e))},m(d,c){S(d,e,c),ke(e,t);for(let m=0;m<l.length;m+=1)l[m]&&l[m].m(t,null);i[15](t),i[16](e),s=Ys(e,i[17].bind(e)),r=!0,f||(o=Xe(e,"scroll",i[7]),f=!0)},p(d,[c]){c&8208&&(a=pt(d[4]),fe(),l=vs(l,c,u,1,d,a,n,t,Ds,Qn,null,Un),ae()),(!r||c&32)&&W(t,"padding-top",d[5]+"px"),(!r||c&64)&&W(t,"padding-bottom",d[6]+"px"),(!r||c&1)&&W(e,"height",d[0])},i(d){if(!r){for(let c=0;c<a.length;c+=1)b(l[c]);r=!0}},o(d){for(let c=0;c<l.length;c+=1)C(l[c]);r=!1},d(d){d&&g(e);for(let c=0;c<l.length;c+=1)l[c].d();i[15](null),i[16](null),s(),f=!1,o()}}}function Ea(i,e,t){let{$$slots:l={},$$scope:n}=e,{items:s}=e,{height:r="100%"}=e,{itemHeight:f=void 0}=e,{start:o=0}=e,{end:a=0}=e,u=[],d,c,m,_=0,h,w,E=0,v=0,M;async function p(I,k,D){const{scrollTop:T}=c;if(await Cl(),!w)return;let y=E-T,N=o;for(;y<k&&N<I.length;){let X=d[N-o];if(!X){if(t(9,a=N+1),await Cl(),!w)return;X=d[N-o]}const Ce=u[N]=D||(X==null?void 0:X.offsetHeight)||Number.MAX_SAFE_INTEGER;y+=Ce,N+=1}t(9,a=N);const q=I.length-a;M=(E+y)/a,t(6,v=q*M),u.length=I.length}async function F(){var N,q;const{scrollTop:I}=c,k=o;for(let X=0;X<d.length;X+=1)u[o+X]=f||((N=d[X])==null?void 0:N.offsetHeight)||Number.MAX_SAFE_INTEGER;let D=0,T=0;for(;D<s.length;){const X=u[D]||M;if(T+X>I){t(8,o=D),t(5,E=T);break}T+=X,D+=1}for(;D<s.length&&(T+=u[D]||M,D+=1,!(T>I+_)););t(9,a=D);const y=s.length-a;for(M=T/a;D<s.length;)u[D++]=M;if(t(6,v=y*M),o<k){await Cl();let X=0,Ce=0;for(let se=o;se<k;se+=1)d[se-o]&&(X+=u[se],Ce+=f||((q=d[se-o])==null?void 0:q.offsetHeight)||Number.MAX_SAFE_INTEGER);const ye=Ce-X;c.scrollTo(0,I+ye)}}rl(()=>(d=m.getElementsByClassName("row"),t(12,w=!0),()=>t(12,w=!1)));function Y(I){ot[I?"unshift":"push"](()=>{m=I,t(3,m)})}function H(I){ot[I?"unshift":"push"](()=>{c=I,t(2,c)})}function ne(){_=this.offsetHeight,t(1,_)}return i.$$set=I=>{"items"in I&&t(10,s=I.items),"height"in I&&t(0,r=I.height),"itemHeight"in I&&t(11,f=I.itemHeight),"start"in I&&t(8,o=I.start),"end"in I&&t(9,a=I.end),"$$scope"in I&&t(13,n=I.$$scope)},i.$$.update=()=>{i.$$.dirty&1792&&t(4,h=s.slice(o,a).map((I,k)=>({index:k+o,data:I}))),i.$$.dirty&7170&&w&&p(s,_,f)},[r,_,c,m,h,E,v,F,o,a,s,f,w,n,l,Y,H,ne]}class pa extends Ae{constructor(e){super(),Se(this,e,Ea,Da,me,{items:10,height:0,itemHeight:11,start:8,end:9})}}const{Boolean:Gs}=Kr;function Kn(i,e,t){const l=i.slice();return l[58]=e[t],l[60]=t,l}function Jn(i,e,t){const l=i.slice();return l[58]=e[t],l}function Zn(i,e,t){const l=i.slice();return l[58]=e[t],l}function xn(i,e){let t,l,n;return l=new ll({props:{value:e[58][e[6]]??e[58].value,valueLabel:e[58][e[7]]??e[58].label,idx:os(e[58]),__auto:!0}}),{key:i,first:null,c(){t=te(),$(l.$$.fragment),this.h()},l(s){t=te(),x(l.$$.fragment,s),this.h()},h(){this.first=t},m(s,r){S(s,t,r),Z(l,s,r),n=!0},p(s,r){e=s;const f={};r[0]&4160&&(f.value=e[58][e[6]]??e[58].value),r[0]&4224&&(f.valueLabel=e[58][e[7]]??e[58].label),r[0]&4096&&(f.idx=os(e[58])),l.$set(f)},i(s){n||(b(l.$$.fragment,s),n=!0)},o(s){C(l.$$.fragment,s),n=!1},d(s){s&&g(t),J(l,s)}}}function Ma(i){let e,t,l;function n(r){i[40](r)}let s={$$slots:{default:[$a]},$$scope:{ctx:i}};return i[8]!==void 0&&(s.open=i[8]),e=new ma({props:s}),ot.push(()=>$l(e,"open",n)),{c(){$(e.$$.fragment)},l(r){x(e.$$.fragment,r)},m(r,f){Z(e,r,f),l=!0},p(r,f){const o={};f[0]&49981|f[1]&1024&&(o.$$scope={dirty:f,ctx:r}),!t&&f[0]&256&&(t=!0,o.open=r[8],xl(()=>t=!1)),e.$set(o)},i(r){l||(b(e.$$.fragment,r),l=!0)},o(r){C(e.$$.fragment,r),l=!1},d(r){J(e,r)}}}function Ia(i){let e,t;return e=new Dr({props:{inputType:"Dropdown",error:i[10],height:"32",width:"140"}}),{c(){$(e.$$.fragment)},l(l){x(e.$$.fragment,l)},m(l,n){Z(e,l,n),t=!0},p(l,n){const s={};n[0]&1024&&(s.error=l[10]),e.$set(s)},i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){C(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function Pa(i){let e=(i[3]??_t(i[4]))+"",t,l,n,s,r=i[5]&&$n(i);return{c(){t=Ye(e),l=K(),r&&r.c(),n=te()},l(f){t=Ue(f,e),l=Q(f),r&&r.l(f),n=te()},m(f,o){S(f,t,o),S(f,l,o),r&&r.m(f,o),S(f,n,o),s=!0},p(f,o){(!s||o[0]&24)&&e!==(e=(f[3]??_t(f[4]))+"")&&bt(t,e),f[5]?r?(r.p(f,o),o[0]&32&&b(r,1)):(r=$n(f),r.c(),b(r,1),r.m(n.parentNode,n)):r&&(fe(),C(r,1,1,()=>{r=null}),ae())},i(f){s||(b(r),s=!0)},o(f){C(r),s=!1},d(f){f&&(g(t),g(l),g(n)),r&&r.d(f)}}}function Fa(i){let e=i[14][0].label+"",t;return{c(){t=Ye(e)},l(l){t=Ue(l,e)},m(l,n){S(l,t,n)},p(l,n){n[0]&16384&&e!==(e=l[14][0].label+"")&&bt(t,e)},i:we,o:we,d(l){l&&g(t)}}}function Na(i){let e,t,l,n,s,r=i[5]&&es(i),f=i[14].length>0&&ts(i);return{c(){e=Ye(i[3]),t=K(),r&&r.c(),l=K(),f&&f.c(),n=te()},l(o){e=Ue(o,i[3]),t=Q(o),r&&r.l(o),l=Q(o),f&&f.l(o),n=te()},m(o,a){S(o,e,a),S(o,t,a),r&&r.m(o,a),S(o,l,a),f&&f.m(o,a),S(o,n,a),s=!0},p(o,a){(!s||a[0]&8)&&bt(e,o[3]),o[5]?r?(r.p(o,a),a[0]&32&&b(r,1)):(r=es(o),r.c(),b(r,1),r.m(l.parentNode,l)):r&&(fe(),C(r,1,1,()=>{r=null}),ae()),o[14].length>0?f?(f.p(o,a),a[0]&16384&&b(f,1)):(f=ts(o),f.c(),b(f,1),f.m(n.parentNode,n)):f&&(fe(),C(f,1,1,()=>{f=null}),ae())},i(o){s||(b(r),b(f),s=!0)},o(o){C(r),C(f),s=!1},d(o){o&&(g(e),g(t),g(l),g(n)),r&&r.d(o),f&&f.d(o)}}}function $n(i){let e,t;return e=new Es({props:{description:i[5],className:"pl-1"}}),{c(){$(e.$$.fragment)},l(l){x(e.$$.fragment,l)},m(l,n){Z(e,l,n),t=!0},p(l,n){const s={};n[0]&32&&(s.description=l[5]),e.$set(s)},i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){C(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function es(i){let e,t;return e=new Es({props:{description:i[5],className:"pl-1"}}),{c(){$(e.$$.fragment)},l(l){x(e.$$.fragment,l)},m(l,n){Z(e,l,n),t=!0},p(l,n){const s={};n[0]&32&&(s.description=l[5]),e.$set(s)},i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){C(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function ts(i){let e,t,l=i[14][0].label+"",n,s;return e=new qs({props:{orientation:"vertical",class:"mx-2 h-4"}}),{c(){$(e.$$.fragment),t=K(),n=Ye(l)},l(r){x(e.$$.fragment,r),t=Q(r),n=Ue(r,l)},m(r,f){Z(e,r,f),S(r,t,f),S(r,n,f),s=!0},p(r,f){(!s||f[0]&16384)&&l!==(l=r[14][0].label+"")&&bt(n,l)},i(r){s||(b(e.$$.fragment,r),s=!0)},o(r){C(e.$$.fragment,r),s=!1},d(r){r&&(g(t),g(n)),J(e,r)}}}function ls(i){let e,t,l,n,s,r,f,o;e=new qs({props:{orientation:"vertical",class:"mx-2 h-4"}}),l=new Xi({props:{variant:"default",class:"rounded-xs px-1 font-normal sm:hidden",$$slots:{default:[Ba]},$$scope:{ctx:i}}});const a=[Va,ja],u=[];function d(c,m){return c[14].length>3?0:1}return r=d(i),f=u[r]=a[r](i),{c(){$(e.$$.fragment),t=K(),$(l.$$.fragment),n=K(),s=R("div"),f.c(),this.h()},l(c){x(e.$$.fragment,c),t=Q(c),x(l.$$.fragment,c),n=Q(c),s=G(c,"DIV",{class:!0});var m=le(s);f.l(m),m.forEach(g),this.h()},h(){V(s,"class","hidden space-x-1 sm:flex")},m(c,m){Z(e,c,m),S(c,t,m),Z(l,c,m),S(c,n,m),S(c,s,m),u[r].m(s,null),o=!0},p(c,m){const _={};m[0]&16384|m[1]&1024&&(_.$$scope={dirty:m,ctx:c}),l.$set(_);let h=r;r=d(c),r===h?u[r].p(c,m):(fe(),C(u[h],1,1,()=>{u[h]=null}),ae(),f=u[r],f?f.p(c,m):(f=u[r]=a[r](c),f.c()),b(f,1),f.m(s,null))},i(c){o||(b(e.$$.fragment,c),b(l.$$.fragment,c),b(f),o=!0)},o(c){C(e.$$.fragment,c),C(l.$$.fragment,c),C(f),o=!1},d(c){c&&(g(t),g(n),g(s)),J(e,c),J(l,c),u[r].d()}}}function Ba(i){let e=i[14].length+"",t;return{c(){t=Ye(e)},l(l){t=Ue(l,e)},m(l,n){S(l,t,n)},p(l,n){n[0]&16384&&e!==(e=l[14].length+"")&&bt(t,e)},d(l){l&&g(t)}}}function ja(i){let e,t,l=pt(i[14]),n=[];for(let r=0;r<l.length;r+=1)n[r]=is(Jn(i,l,r));const s=r=>C(n[r],1,1,()=>{n[r]=null});return{c(){for(let r=0;r<n.length;r+=1)n[r].c();e=te()},l(r){for(let f=0;f<n.length;f+=1)n[f].l(r);e=te()},m(r,f){for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(r,f);S(r,e,f),t=!0},p(r,f){if(f[0]&16384){l=pt(r[14]);let o;for(o=0;o<l.length;o+=1){const a=Jn(r,l,o);n[o]?(n[o].p(a,f),b(n[o],1)):(n[o]=is(a),n[o].c(),b(n[o],1),n[o].m(e.parentNode,e))}for(fe(),o=l.length;o<n.length;o+=1)s(o);ae()}},i(r){if(!t){for(let f=0;f<l.length;f+=1)b(n[f]);t=!0}},o(r){n=n.filter(Gs);for(let f=0;f<n.length;f+=1)C(n[f]);t=!1},d(r){r&&g(e),Cs(n,r)}}}function Va(i){let e,t;return e=new Xi({props:{variant:"default",class:"rounded-xs px-1 font-normal",$$slots:{default:[Ha]},$$scope:{ctx:i}}}),{c(){$(e.$$.fragment)},l(l){x(e.$$.fragment,l)},m(l,n){Z(e,l,n),t=!0},p(l,n){const s={};n[0]&16384|n[1]&1024&&(s.$$scope={dirty:n,ctx:l}),e.$set(s)},i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){C(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function za(i){let e=i[58].label+"",t,l;return{c(){t=Ye(e),l=K()},l(n){t=Ue(n,e),l=Q(n)},m(n,s){S(n,t,s),S(n,l,s)},p(n,s){s[0]&16384&&e!==(e=n[58].label+"")&&bt(t,e)},d(n){n&&(g(t),g(l))}}}function is(i){let e,t;return e=new Xi({props:{variant:"default",class:"rounded-xs px-1 font-normal",$$slots:{default:[za]},$$scope:{ctx:i}}}),{c(){$(e.$$.fragment)},l(l){x(e.$$.fragment,l)},m(l,n){Z(e,l,n),t=!0},p(l,n){const s={};n[0]&16384|n[1]&1024&&(s.$$scope={dirty:n,ctx:l}),e.$set(s)},i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){C(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function Ha(i){let e=i[14].length+"",t,l;return{c(){t=Ye(e),l=Ye(" Selected")},l(n){t=Ue(n,e),l=Ue(n," Selected")},m(n,s){S(n,t,s),S(n,l,s)},p(n,s){s[0]&16384&&e!==(e=n[14].length+"")&&bt(t,e)},d(n){n&&(g(t),g(l))}}}function qa(i){let e,t,l,n,s,r,f;const o=[Na,Fa,Pa],a=[];function u(c,m){return c[3]&&!c[0]?0:c[14].length>0&&!c[0]?1:2}e=u(i),t=a[e]=o[e](i),n=new ti({props:{src:Er,class:"ml-2 h-4 w-4"}});let d=i[14].length>0&&i[0]&&ls(i);return{c(){t.c(),l=K(),$(n.$$.fragment),s=K(),d&&d.c(),r=te()},l(c){t.l(c),l=Q(c),x(n.$$.fragment,c),s=Q(c),d&&d.l(c),r=te()},m(c,m){a[e].m(c,m),S(c,l,m),Z(n,c,m),S(c,s,m),d&&d.m(c,m),S(c,r,m),f=!0},p(c,m){let _=e;e=u(c),e===_?a[e].p(c,m):(fe(),C(a[_],1,1,()=>{a[_]=null}),ae(),t=a[e],t?t.p(c,m):(t=a[e]=o[e](c),t.c()),b(t,1),t.m(l.parentNode,l)),c[14].length>0&&c[0]?d?(d.p(c,m),m[0]&16385&&b(d,1)):(d=ls(c),d.c(),b(d,1),d.m(r.parentNode,r)):d&&(fe(),C(d,1,1,()=>{d=null}),ae())},i(c){f||(b(t),b(n.$$.fragment,c),b(d),f=!0)},o(c){C(t),C(n.$$.fragment,c),C(d),f=!1},d(c){c&&(g(l),g(s),g(r)),a[e].d(c),J(n,c),d&&d.d(c)}}}function Ga(i){let e,t;return e=new xr({props:{builders:[i[61]],variant:"outline",role:"combobox",size:"sm",class:"min-w-5 h-8 border border-base-300","aria-label":i[3]??_t(i[4]),$$slots:{default:[qa]},$$scope:{ctx:i}}}),{c(){$(e.$$.fragment)},l(l){x(e.$$.fragment,l)},m(l,n){Z(e,l,n),t=!0},p(l,n){const s={};n[1]&1073741824&&(s.builders=[l[61]]),n[0]&24&&(s["aria-label"]=l[3]??_t(l[4])),n[0]&16441|n[1]&1024&&(s.$$scope={dirty:n,ctx:l}),e.$set(s)},i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){C(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function Ra(i){let e;return{c(){e=Ye("No results found.")},l(t){e=Ue(t,"No results found.")},m(t,l){S(t,e,l)},d(t){t&&g(e)}}}function Wa(i){let e,t;return e=new pa({props:{height:`${Rs*32}px`,items:i[15],$$slots:{default:[Ua,({item:l})=>({58:l}),({item:l})=>[0,l?134217728:0]]},$$scope:{ctx:i}}}),{c(){$(e.$$.fragment)},l(l){x(e.$$.fragment,l)},m(l,n){Z(e,l,n),t=!0},p(l,n){const s={};n[0]&32768&&(s.items=l[15]),n[0]&16641|n[1]&134218752&&(s.$$scope={dirty:n,ctx:l}),e.$set(s)},i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){C(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function Xa(i){let e,t,l=pt(i[15]),n=[];for(let r=0;r<l.length;r+=1)n[r]=ns(Kn(i,l,r));const s=r=>C(n[r],1,1,()=>{n[r]=null});return{c(){for(let r=0;r<n.length;r+=1)n[r].c();e=te()},l(r){for(let f=0;f<n.length;f+=1)n[f].l(r);e=te()},m(r,f){for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(r,f);S(r,e,f),t=!0},p(r,f){if(f[0]&4243713){l=pt(r[15]);let o;for(o=0;o<l.length;o+=1){const a=Kn(r,l,o);n[o]?(n[o].p(a,f),b(n[o],1)):(n[o]=ns(a),n[o].c(),b(n[o],1),n[o].m(e.parentNode,e))}for(fe(),o=l.length;o<n.length;o+=1)s(o);ae()}},i(r){if(!t){for(let f=0;f<l.length;f+=1)b(n[f]);t=!0}},o(r){n=n.filter(Gs);for(let f=0;f<n.length;f+=1)C(n[f]);t=!1},d(r){r&&g(e),Cs(n,r)}}}function Ua(i){var n,s;let e,t;function l(...r){return i[39](i[58],...r)}return e=new Hs({props:{value:(n=i[58])==null?void 0:n.value,valueLabel:(s=i[58])==null?void 0:s.label,handleSelect:i[38],multiple:i[0],active:i[14].some(l)}}),{c(){$(e.$$.fragment)},l(r){x(e.$$.fragment,r)},m(r,f){Z(e,r,f),t=!0},p(r,f){var a,u;i=r;const o={};f[1]&134217728&&(o.value=(a=i[58])==null?void 0:a.value),f[1]&134217728&&(o.valueLabel=(u=i[58])==null?void 0:u.label),f[0]&257&&(o.handleSelect=i[38]),f[0]&1&&(o.multiple=i[0]),f[0]&16384|f[1]&134217728&&(o.active=i[14].some(l)),e.$set(o)},i(r){t||(b(e.$$.fragment,r),t=!0)},o(r){C(e.$$.fragment,r),t=!1},d(r){J(e,r)}}}function ns(i){let e,t;function l(...n){return i[37](i[58],...n)}return e=new Hs({props:{id:i[60],value:i[58].value,valueLabel:i[58].label,handleSelect:i[36],multiple:i[0],active:i[14].some(l)}}),{c(){$(e.$$.fragment)},l(n){x(e.$$.fragment,n)},m(n,s){Z(e,n,s),t=!0},p(n,s){i=n;const r={};s[0]&32768&&(r.value=i[58].value),s[0]&32768&&(r.valueLabel=i[58].label),s[0]&257&&(r.handleSelect=i[36]),s[0]&1&&(r.multiple=i[0]),s[0]&49152&&(r.active=i[14].some(l)),e.$set(r)},i(n){t||(b(e.$$.fragment,n),t=!0)},o(n){C(e.$$.fragment,n),t=!1},d(n){J(e,n)}}}function Ya(i){let e,t,l,n;const s=[Xa,Wa],r=[];function f(o,a){return o[15].length<=Rs?0:1}return e=f(i),t=r[e]=s[e](i),{c(){t.c(),l=te()},l(o){t.l(o),l=te()},m(o,a){r[e].m(o,a),S(o,l,a),n=!0},p(o,a){let u=e;e=f(o),e===u?r[e].p(o,a):(fe(),C(r[u],1,1,()=>{r[u]=null}),ae(),t=r[e],t?t.p(o,a):(t=r[e]=s[e](o),t.c()),b(t,1),t.m(l.parentNode,l))},i(o){n||(b(t),n=!0)},o(o){C(t),n=!1},d(o){o&&g(l),r[e].d(o)}}}function ss(i){let e,t,l,n,s,r=!i[2]&&rs(i);return n=new Wi({props:{disabled:i[14].length===0,class:"justify-center text-center",onSelect:i[21],$$slots:{default:[Ka]},$$scope:{ctx:i}}}),{c(){r&&r.c(),e=K(),t=R("div"),l=K(),$(n.$$.fragment),this.h()},l(f){r&&r.l(f),e=Q(f),t=G(f,"DIV",{class:!0}),le(t).forEach(g),l=Q(f),x(n.$$.fragment,f),this.h()},h(){V(t,"class","-mx-1 h-px bg-base-300")},m(f,o){r&&r.m(f,o),S(f,e,o),S(f,t,o),S(f,l,o),Z(n,f,o),s=!0},p(f,o){f[2]?r&&(fe(),C(r,1,1,()=>{r=null}),ae()):r?(r.p(f,o),o[0]&4&&b(r,1)):(r=rs(f),r.c(),b(r,1),r.m(e.parentNode,e));const a={};o[0]&16384&&(a.disabled=f[14].length===0),o[1]&1024&&(a.$$scope={dirty:o,ctx:f}),n.$set(a)},i(f){s||(b(r),b(n.$$.fragment,f),s=!0)},o(f){C(r),C(n.$$.fragment,f),s=!1},d(f){f&&(g(e),g(t),g(l)),r&&r.d(f),J(n,f)}}}function rs(i){let e,t,l,n;return l=new Wi({props:{class:"justify-center text-center",onSelect:i[20],$$slots:{default:[Qa]},$$scope:{ctx:i}}}),{c(){e=R("div"),t=K(),$(l.$$.fragment),this.h()},l(s){e=G(s,"DIV",{class:!0}),le(e).forEach(g),t=Q(s),x(l.$$.fragment,s),this.h()},h(){V(e,"class","-mx-1 h-px bg-base-300")},m(s,r){S(s,e,r),S(s,t,r),Z(l,s,r),n=!0},p(s,r){const f={};r[1]&1024&&(f.$$scope={dirty:r,ctx:s}),l.$set(f)},i(s){n||(b(l.$$.fragment,s),n=!0)},o(s){C(l.$$.fragment,s),n=!1},d(s){s&&(g(e),g(t)),J(l,s)}}}function Qa(i){let e;return{c(){e=Ye("Select all")},l(t){e=Ue(t,"Select all")},m(t,l){S(t,e,l)},d(t){t&&g(e)}}}function Ka(i){let e;return{c(){e=Ye("Clear selection")},l(t){e=Ue(t,"Clear selection")},m(t,l){S(t,e,l)},d(t){t&&g(e)}}}function Ja(i){let e,t,l,n,s,r;e=new Rf({props:{$$slots:{default:[Ra]},$$scope:{ctx:i}}}),l=new Yf({props:{$$slots:{default:[Ya]},$$scope:{ctx:i}}});let f=i[0]&&ss(i);return{c(){$(e.$$.fragment),t=K(),$(l.$$.fragment),n=K(),f&&f.c(),s=te()},l(o){x(e.$$.fragment,o),t=Q(o),x(l.$$.fragment,o),n=Q(o),f&&f.l(o),s=te()},m(o,a){Z(e,o,a),S(o,t,a),Z(l,o,a),S(o,n,a),f&&f.m(o,a),S(o,s,a),r=!0},p(o,a){const u={};a[1]&1024&&(u.$$scope={dirty:a,ctx:o}),e.$set(u);const d={};a[0]&49409|a[1]&1024&&(d.$$scope={dirty:a,ctx:o}),l.$set(d),o[0]?f?(f.p(o,a),a[0]&1&&b(f,1)):(f=ss(o),f.c(),b(f,1),f.m(s.parentNode,s)):f&&(fe(),C(f,1,1,()=>{f=null}),ae())},i(o){r||(b(e.$$.fragment,o),b(l.$$.fragment,o),b(f),r=!0)},o(o){C(e.$$.fragment,o),C(l.$$.fragment,o),C(f),r=!1},d(o){o&&(g(t),g(n),g(s)),J(e,o),J(l,o),f&&f.d(o)}}}function Za(i){let e,t,l,n,s;function r(o){i[35](o)}let f={placeholder:i[3]};return i[9]!==void 0&&(f.value=i[9]),e=new $f({props:f}),ot.push(()=>$l(e,"value",r)),n=new ia({props:{$$slots:{default:[Ja]},$$scope:{ctx:i}}}),{c(){$(e.$$.fragment),l=K(),$(n.$$.fragment)},l(o){x(e.$$.fragment,o),l=Q(o),x(n.$$.fragment,o)},m(o,a){Z(e,o,a),S(o,l,a),Z(n,o,a),s=!0},p(o,a){const u={};a[0]&8&&(u.placeholder=o[3]),!t&&a[0]&512&&(t=!0,u.value=o[9],xl(()=>t=!1)),e.$set(u);const d={};a[0]&49413|a[1]&1024&&(d.$$scope={dirty:a,ctx:o}),n.$set(d)},i(o){s||(b(e.$$.fragment,o),b(n.$$.fragment,o),s=!0)},o(o){C(e.$$.fragment,o),C(n.$$.fragment,o),s=!1},d(o){o&&g(l),J(e,o),J(n,o)}}}function xa(i){let e,t;return e=new zf({props:{shouldFilter:!1,$$slots:{default:[Za]},$$scope:{ctx:i}}}),{c(){$(e.$$.fragment)},l(l){x(e.$$.fragment,l)},m(l,n){Z(e,l,n),t=!0},p(l,n){const s={};n[0]&49933|n[1]&1024&&(s.$$scope={dirty:n,ctx:l}),e.$set(s)},i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){C(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function $a(i){let e,t,l,n;return e=new ha({props:{asChild:!0,$$slots:{default:[Ga,({builder:s})=>({61:s}),({builder:s})=>[0,s?1073741824:0]]},$$scope:{ctx:i}}}),l=new da({props:{class:"w-[200px] p-0",align:"start",side:"bottom",$$slots:{default:[xa]},$$scope:{ctx:i}}}),{c(){$(e.$$.fragment),t=K(),$(l.$$.fragment)},l(s){x(e.$$.fragment,s),t=Q(s),x(l.$$.fragment,s)},m(s,r){Z(e,s,r),S(s,t,r),Z(l,s,r),n=!0},p(s,r){const f={};r[0]&16441|r[1]&1073742848&&(f.$$scope={dirty:r,ctx:s}),e.$set(f);const o={};r[0]&49933|r[1]&1024&&(o.$$scope={dirty:r,ctx:s}),l.$set(o)},i(s){n||(b(e.$$.fragment,s),b(l.$$.fragment,s),n=!0)},o(s){C(e.$$.fragment,s),C(l.$$.fragment,s),n=!1},d(s){s&&g(t),J(e,s),J(l,s)}}}function eu(i){let e,t,l,n;const s=[Ia,Ma],r=[];function f(o,a){return o[10].length>0?0:1}return t=f(i),l=r[t]=s[t](i),{c(){e=R("div"),l.c(),this.h()},l(o){e=G(o,"DIV",{class:!0});var a=le(e);l.l(a),a.forEach(g),this.h()},h(){V(e,"class","mt-2 mb-4 ml-0 mr-2 inline-block")},m(o,a){S(o,e,a),r[t].m(e,null),n=!0},p(o,a){let u=t;t=f(o),t===u?r[t].p(o,a):(fe(),C(r[u],1,1,()=>{r[u]=null}),ae(),l=r[t],l?l.p(o,a):(l=r[t]=s[t](o),l.c()),b(l,1),l.m(e,null))},i(o){n||(b(l),n=!0)},o(o){C(l),n=!1},d(o){o&&g(e),r[t].d()}}}function tu(i){let e,t=[],l=new Map,n,s,r;const f=i[34].default,o=he(f,i,i[41],null);let a=pt(i[12]);const u=d=>{var c,m;return`${(c=d[58].label)==null?void 0:c.toString()} ${(m=d[58].value)==null?void 0:m.toString()}`};for(let d=0;d<a.length;d+=1){let c=Zn(i,a,d),m=u(c);l.set(m,t[d]=xn(m,c))}return s=new Go({props:{enabled:i[1],$$slots:{default:[eu]},$$scope:{ctx:i}}}),{c(){o&&o.c(),e=K();for(let d=0;d<t.length;d+=1)t[d].c();n=K(),$(s.$$.fragment)},l(d){o&&o.l(d),e=Q(d);for(let c=0;c<t.length;c+=1)t[c].l(d);n=Q(d),x(s.$$.fragment,d)},m(d,c){o&&o.m(d,c),S(d,e,c);for(let m=0;m<t.length;m+=1)t[m]&&t[m].m(d,c);S(d,n,c),Z(s,d,c),r=!0},p(d,c){o&&o.p&&(!r||c[1]&1024)&&_e(o,f,d,d[41],r?be(f,d[41],c,null):ge(d[41]),null),c[0]&4288&&(a=pt(d[12]),fe(),t=vs(t,c,u,1,d,a,l,n.parentNode,Ds,xn,n,Zn),ae());const m={};c[0]&2&&(m.enabled=d[1]),c[0]&51005|c[1]&1024&&(m.$$scope={dirty:c,ctx:d}),s.$set(m)},i(d){if(!r){b(o,d);for(let c=0;c<a.length;c+=1)b(t[c]);b(s.$$.fragment,d),r=!0}},o(d){C(o,d);for(let c=0;c<t.length;c+=1)C(t[c]);C(s.$$.fragment,d),r=!1},d(d){d&&(g(e),g(n)),o&&o.d(d);for(let c=0;c<t.length;c+=1)t[c].d(d);J(s,d)}}}const Rs=5;function os(i){return"similarity"in i?i.similarity*-1:i.ordinal??0}function lu(i,e,t){var it;let l,n,s=we,r=()=>(s(),s=gt(l,B=>t(31,n=B)),l),f,o=we,a=()=>(o(),o=gt(Pe,B=>t(32,f=B)),Pe),u,d,c,m,_;Be(i,Bs,B=>t(45,m=B)),i.$$.on_destroy.push(()=>s()),i.$$.on_destroy.push(()=>o());let{$$slots:h={},$$scope:w}=e;const E=Qs(h),v=Or();Be(i,v,B=>t(44,d=B));let{title:M=void 0}=e,{name:p}=e,{multiple:F=!1}=e,{hideDuringPrint:Y=!0}=e,{disableSelectAll:H=!1}=e,{defaultValue:ne=[]}=e,{noDefault:I=!1}=e,{selectAllByDefault:k=!1}=e,{description:D=void 0}=e,{value:T="value",data:y,label:N=T,order:q=void 0,where:X=void 0}=e;const{results:Ce,update:ye}=Tr({value:T,data:y,label:N,order:q,where:X},`Dropdown-${p}`,(it=m==null?void 0:m.data)==null?void 0:it.data[`Dropdown-${p}_data`]);Be(i,Ce,B=>t(33,c=B));let se=!!y;const qe=p in d&&"rawValues"in d[p]&&Array.isArray(d[p].rawValues)?d[p].rawValues:[],Ee=Lr({multiselect:F,defaultValues:Array.isArray(ne)?ne:[ne],initialOptions:qe,noDefault:I,selectAllByDefault:De(k)}),{addOptions:oe,removeOptions:Oe,options:j,selectedOptions:We,selectAll:ie,deselectAll:je,toggleSelected:pe,pauseSorting:Qe,resumeSorting:ve,forceSort:Me,destroy:ce}=Ee;Be(i,j,B=>t(15,_=B)),Be(i,We,B=>t(14,u=B)),Ni(ce);const re=B=>{JSON.stringify(B)!==JSON.stringify(d[p])&&zi(v,d[p]=B,d)};let L=[],z=u.length>0;Ni(We.subscribe(B=>{if(z||(z=B.length>0),B&&z){const de=B;F?re({label:de.map(dt=>dt.label).join(", "),value:de.length?`(${de.map(dt=>Vi(dt.value))})`:"(select null where 0)",rawValues:de}):de.length?de.length&&re({label:de[0].label,value:Vi(de[0].value,{serializeStrings:!1}),rawValues:de}):re({label:"",value:null,rawValues:[]})}})),Al(Vs,{registerOption:B=>(oe(B),()=>{Oe(B)})});let Fe,He="",xe=0,Pe;const $e=vr(()=>{if(xe++,He&&se){const B=xe,de=l.search(He,"label");de.hash!==(Pe==null?void 0:Pe.hash)&&pr(()=>{B===xe&&(a(t(13,Pe=de)),Me())},de.fetch())}else a(t(13,Pe=l??y))});let Ge=[];T||(y?Ge.push('Missing required prop: "value".'):E.default||Ge.push('Dropdown requires either "value" and "data" props or <DropdownOption />.')),y&&typeof y!="object"&&(typeof y=="string"?Ge.push(`'${y}' is not a recognized query result. Data should be provided in the format: data = {'${y.replace("data.","")}'}`):Ge.push(`'${y}' is not a recognized query result. Data should be an object. e.g data = {QueryName}`));try{$r({name:p})}catch(B){Ge.push(B.message)}let tt=!1;function Ke(B){He=B,t(9,He)}const lt=({value:B,label:de})=>{pe({value:B,label:de}),F||t(8,Fe=!1)},Je=(B,de)=>de.value===B.value&&de.label===B.label,nt=({value:B,label:de})=>{pe({value:B,label:de}),F||t(8,Fe=!1)},st=(B,de)=>de.value===B.value&&de.label===B.label;function et(B){Fe=B,t(8,Fe)}return i.$$set=B=>{"title"in B&&t(3,M=B.title),"name"in B&&t(4,p=B.name),"multiple"in B&&t(0,F=B.multiple),"hideDuringPrint"in B&&t(1,Y=B.hideDuringPrint),"disableSelectAll"in B&&t(2,H=B.disableSelectAll),"defaultValue"in B&&t(25,ne=B.defaultValue),"noDefault"in B&&t(23,I=B.noDefault),"selectAllByDefault"in B&&t(24,k=B.selectAllByDefault),"description"in B&&t(5,D=B.description),"value"in B&&t(6,T=B.value),"data"in B&&t(26,y=B.data),"label"in B&&t(7,N=B.label),"order"in B&&t(27,q=B.order),"where"in B&&t(28,X=B.where),"$$scope"in B&&t(41,w=B.$$scope)},i.$$.update=()=>{i.$$.dirty[0]&1&&t(0,F=De(F)),i.$$.dirty[0]&2&&t(1,Y=De(Y)),i.$$.dirty[0]&4&&t(2,H=De(H)),i.$$.dirty[0]&8388608&&t(23,I=De(I)),i.$$.dirty[0]&16777216&&t(24,k=De(k)),i.$$.dirty[0]&469762240&&ye({value:T,data:y,label:N,order:q,where:X}),i.$$.dirty[1]&4&&t(29,{hasQuery:se,query:l}=c,se,r(t(11,l))),i.$$.dirty[0]&2048&&l&&l.fetch(),i.$$.dirty[0]&67111424&&$e(),i.$$.dirty[0]&256&&(Fe?Qe():ve()),i.$$.dirty[1]&2&&f!=null&&f.dataLoaded&&t(12,L=f),i.$$.dirty[0]&1610613760|i.$$.dirty[1]&1&&n!=null&&n.error&&se&&!tt&&(t(10,Ge=[...Ge,n.error]),t(30,tt=!0))},[F,Y,H,M,p,D,T,N,Fe,He,Ge,l,L,Pe,u,_,v,Ce,j,We,ie,je,pe,I,k,ne,y,q,X,se,tt,n,f,c,h,Ke,lt,Je,nt,st,et,w]}class fs extends Ae{constructor(e){super(),Se(this,e,lu,tu,me,{title:3,name:4,multiple:0,hideDuringPrint:1,disableSelectAll:2,defaultValue:25,noDefault:23,selectAllByDefault:24,description:5,value:6,data:26,label:7,order:27,where:28},null,[-1,-1,-1])}}function iu(i){let e,t,l,n,s,r;const f=i[5].default,o=he(f,i,i[4],null);return{c(){e=R("div"),t=R("span"),l=Ye(i[2]),n=K(),s=R("div"),o&&o.c(),this.h()},l(a){e=G(a,"DIV",{class:!0});var u=le(e);t=G(u,"SPAN",{class:!0});var d=le(t);l=Ue(d,i[2]),d.forEach(g),n=Q(u),s=G(u,"DIV",{class:!0});var c=le(s);o&&o.l(c),c.forEach(g),u.forEach(g),this.h()},h(){V(t,"class","text-sm font-semibold inline-flex"),V(s,"class","pt-1 mb-6 text-sm"),V(e,"class","mb-4 mt-2 text-base-content-muted")},m(a,u){S(a,e,u),ke(e,t),ke(t,l),ke(e,n),ke(e,s),o&&o.m(s,null),r=!0},p(a,u){(!r||u&4)&&bt(l,a[2]),o&&o.p&&(!r||u&16)&&_e(o,f,a,a[4],r?be(f,a[4],u,null):ge(a[4]),null)},i(a){r||(b(o,a),r=!0)},o(a){C(o,a),r=!1},d(a){a&&g(e),o&&o.d(a)}}}function nu(i){let e,t,l,n,s,r,f,o,a,u,d,c=i[0]&&as(i);return{c(){e=R("div"),t=R("button"),l=R("span"),s=K(),r=R("span"),f=Ye(i[2]),o=K(),c&&c.c(),this.h()},l(m){e=G(m,"DIV",{class:!0});var _=le(e);t=G(_,"BUTTON",{class:!0});var h=le(t);l=G(h,"SPAN",{class:!0}),le(l).forEach(g),s=Q(h),r=G(h,"SPAN",{});var w=le(r);f=Ue(w,i[2]),w.forEach(g),h.forEach(g),o=Q(_),c&&c.l(_),_.forEach(g),this.h()},h(){V(l,"class",n=sn(i[0]?"marker rotate-marker":"marker")+" svelte-v9l93j"),V(t,"class","text-sm text-base-content-muted cursor-pointer inline-flex gap-2 svelte-v9l93j"),V(e,"class","mb-4 mt-2")},m(m,_){S(m,e,_),ke(e,t),ke(t,l),ke(t,s),ke(t,r),ke(r,f),ke(e,o),c&&c.m(e,null),a=!0,u||(d=Xe(t,"click",i[10]),u=!0)},p(m,_){(!a||_&1&&n!==(n=sn(m[0]?"marker rotate-marker":"marker")+" svelte-v9l93j"))&&V(l,"class",n),(!a||_&4)&&bt(f,m[2]),m[0]?c?(c.p(m,_),_&1&&b(c,1)):(c=as(m),c.c(),b(c,1),c.m(e,null)):c&&(fe(),C(c,1,1,()=>{c=null}),ae())},i(m){a||(b(c),a=!0)},o(m){C(c),a=!1},d(m){m&&g(e),c&&c.d(),u=!1,d()}}}function as(i){let e,t,l;const n=i[5].default,s=he(n,i,i[4],null);return{c(){e=R("div"),s&&s.c(),this.h()},l(r){e=G(r,"DIV",{class:!0});var f=le(e);s&&s.l(f),f.forEach(g),this.h()},h(){V(e,"class","pl-[calc(0.5rem+10px)] pt-3 mb-6 text-sm")},m(r,f){S(r,e,f),s&&s.m(e,null),l=!0},p(r,f){s&&s.p&&(!l||f&16)&&_e(s,n,r,r[4],l?be(n,r[4],f,null):ge(r[4]),null)},i(r){l||(b(s,r),r&&nl(()=>{l&&(t||(t=Ql(e,cn,{},!0)),t.run(1))}),l=!0)},o(r){C(s,r),r&&(t||(t=Ql(e,cn,{},!1)),t.run(0)),l=!1},d(r){r&&g(e),s&&s.d(r),r&&t&&t.end()}}}function su(i){let e,t,l,n,s,r;const f=[nu,iu],o=[];function a(u,d){return!u[3]||!u[1]?0:1}return e=a(i),t=o[e]=f[e](i),{c(){t.c(),l=te()},l(u){t.l(u),l=te()},m(u,d){o[e].m(u,d),S(u,l,d),n=!0,s||(r=[Xe(window,"beforeprint",i[6]),Xe(window,"afterprint",i[7]),Xe(window,"export-beforeprint",i[8]),Xe(window,"export-afterprint",i[9])],s=!0)},p(u,[d]){let c=e;e=a(u),e===c?o[e].p(u,d):(fe(),C(o[c],1,1,()=>{o[c]=null}),ae(),t=o[e],t?t.p(u,d):(t=o[e]=f[e](u),t.c()),b(t,1),t.m(l.parentNode,l))},i(u){n||(b(t),n=!0)},o(u){C(t),n=!1},d(u){u&&g(l),o[e].d(u),s=!1,sl(r)}}}function ru(i,e,t){let{$$slots:l={},$$scope:n}=e,{title:s="Details"}=e,{open:r=!1}=e,{printShowAll:f=!0}=e,o=!1;const a=()=>t(3,o=!0),u=()=>t(3,o=!1),d=()=>t(3,o=!0),c=()=>t(3,o=!1),m=()=>t(0,r=!r);return i.$$set=_=>{"title"in _&&t(2,s=_.title),"open"in _&&t(0,r=_.open),"printShowAll"in _&&t(1,f=_.printShowAll),"$$scope"in _&&t(4,n=_.$$scope)},i.$$.update=()=>{i.$$.dirty&1&&t(0,r=De(r)),i.$$.dirty&2&&t(1,f=De(f))},[r,f,s,o,n,l,a,u,d,c,m]}class ou extends Ae{constructor(e){super(),Se(this,e,ru,su,me,{title:2,open:0,printShowAll:1})}}const il=Symbol.for("__evidence-chart-window-debug__"),fu=(i,e)=>{window[il]||(window[il]={}),window[il][i]=e},au=i=>{window[il]||(window[il]={}),delete window[il][i]},tl=500,uu=(i,e)=>{var _;const t=["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)&&i.clientWidth*3*i.clientHeight*3>16777215;Ol("light",Gi),Ol("dark",ps);let l;const n=()=>{l=qi(i,e.theme,{renderer:t?"svg":e.renderer??"canvas"})};n(),fu(l.id,l),e.connectGroup&&(l.group=e.connectGroup,Mr(e.connectGroup));const s=()=>{if(e.seriesColors){const h=l.getOption();if(!h)return;const w={...h};for(const E of Object.keys(e.seriesColors)){const v=h.series.findIndex(M=>M.name===E);v!==-1&&(w.series[v]={...w.series[v],itemStyle:{...w.series[v].itemStyle,color:e.seriesColors[E]}})}l.setOption(w)}},r=()=>{e.echartsOptions&&l.setOption({...e.echartsOptions})},f=()=>{let h=[];if(e.seriesOptions){const w=e.config.series.reduce((E,{evidenceSeriesType:v},M)=>((v==="reference_line"||v==="reference_area"||v==="reference_point")&&E.push(M),E),[]);for(let E=0;E<e.config.series.length;E++)w.includes(E)?h.push({}):h.push({...e.seriesOptions});l.setOption({series:h})}};l.setOption({...e.config,animationDuration:tl,animationDurationUpdate:tl}),s(),r(),f();const o=e.dispatch;l.on("click",function(h){o("click",h)});const a=i.parentElement,u=Ir(()=>{l.resize({animation:{duration:tl}}),c()},100);let d;window.ResizeObserver&&a?(d=new ResizeObserver(u),d.observe(a)):window.addEventListener("resize",u);const c=()=>{if(e.showAllXAxisLabels){const h=l.getOption();if(!h)return;const w=new Set(h.series.flatMap(M=>{var p;return(p=M.data)==null?void 0:p.map(F=>F[0])})),E=4/5,v=(i==null?void 0:i.clientWidth)??0;if(!e.swapXY){const M={xAxis:{axisLabel:{interval:0,overflow:e.xAxisLabelOverflow,width:v*E/w.size}}};l.setOption(M)}}},m=h=>{h.theme!==e.theme&&(l.dispose(),e=h,n()),e=h,l.setOption({...e.config,animationDuration:tl,animationDurationUpdate:tl},!0),s(),r(),f(),l.resize({animation:{duration:tl}}),c()};return u(),window[_=Symbol.for("chart renders")]??(window[_]=0),window[Symbol.for("chart renders")]++,{update(h){window[Symbol.for("chart renders")]++,m(h)},destroy(){d?d.unobserve(a):window.removeEventListener("resize",u),l.dispose(),au(l.id)}}},cu=(i,e)=>{Ol("light",Gi),Ol("dark",ps),console.log("echartsCanvasDownloadAction",e.theme);const t=qi(i,e.theme,{renderer:"canvas"});e.config.animation=!1,t.setOption(e.config);const l=()=>{if(e.seriesColors){const a=t.getOption();if(!a)return;const u={...a};for(const d of Object.keys(e.seriesColors)){const c=a.series.findIndex(m=>m.name===d);c!==-1&&(u.series[c]={...u.series[c],itemStyle:{...u.series[c].itemStyle,color:e.seriesColors[d]}})}t.setOption(u)}},n=()=>{e.echartsOptions&&t.setOption({...e.echartsOptions})},s=()=>{let a=[];if(e.seriesOptions){const u=e.config.series.reduce((d,{evidenceSeriesType:c},m)=>((c==="reference_line"||c==="reference_area"||c==="reference_point")&&d.push(m),d),[]);for(let d=0;d<e.config.series.length;d++)u.includes(d)?a.push({}):a.push({...e.seriesOptions});t.setOption({series:a})}};n(),l(),s();let r=t.getConnectedDataURL({type:"png",pixelRatio:3,backgroundColor:e.backgroundColor,excludeComponents:["toolbox"]});const f=new Date,o=new Date(f.getTime()-f.getTimezoneOffset()*6e4).toISOString().slice(0,19).replaceAll(":","-");return Pr(r,(e.evidenceChartTitle??e.queryID??"evidence-chart")+`_${o}.png`),t.dispose(),{destroy(){t.dispose()}}},Ll=(i,e)=>{Ol("evidence-light",Gi);const{config:t,ratio:l,echartsOptions:n,seriesOptions:s,seriesColors:r,isMap:f,extraHeight:o,width:a}=e;let u={renderer:"canvas"};f&&(u.height=a*.5+o,i&&i.parentNode&&(i.style.height=u.height+"px",i.parentNode.style.height=u.height+"px"));const d=qi(i,"evidence-light",u);t.animation=!1,d.setOption(t),n&&d.setOption(n);const c=()=>{if(r){const w=d.getOption();if(!w)return;const E={...w};for(const v of Object.keys(r)){const M=w.series.findIndex(p=>p.name===v);M!==-1&&(E.series[M]={...E.series[M],itemStyle:{...E.series[M].itemStyle,color:r[v]}})}d.setOption(E)}},m=()=>{n&&d.setOption({...n})},_=()=>{let w=[];if(s){const E=t.series.reduce((v,{evidenceSeriesType:M},p)=>((M==="reference_line"||M==="reference_area"||M==="reference_point")&&v.push(p),v),[]);for(let v=0;v<t.series.length;v++)E.includes(v)?w.push({}):w.push({...s});d.setOption({series:w})}};m(),c(),_();let h=d.getConnectedDataURL({type:"jpeg",pixelRatio:l,backgroundColor:"#fff",excludeComponents:["toolbox"]});i.innerHTML=`<img src=${h} width="100%" style="
        position: absolute; 
        top: 0;
        user-select: all;
        -webkit-user-select: all;
        -moz-user-select: all;
        -ms-user-select: all;
    " />`,e.config.animation=!0};function du(i){let e;function t(s,r){return s[9]?_u:hu}let l=t(i),n=l(i);return{c(){n.c(),e=te()},l(s){n.l(s),e=te()},m(s,r){n.m(s,r),S(s,e,r)},p(s,r){l===(l=t(s))&&n?n.p(s,r):(n.d(1),n=l(s),n&&(n.c(),n.m(e.parentNode,e)))},d(s){s&&g(e),n.d(s)}}}function mu(i){let e,t,l,n;return{c(){e=R("div"),this.h()},l(s){e=G(s,"DIV",{class:!0,style:!0}),le(e).forEach(g),this.h()},h(){V(e,"class","chart"),W(e,"height",i[1]),W(e,"width",i[2]),W(e,"margin-left","0"),W(e,"margin-top","15px"),W(e,"margin-bottom","10px"),W(e,"overflow","visible"),W(e,"break-inside","avoid")},m(s,r){S(s,e,r),l||(n=Ze(t=Ll.call(null,e,{config:i[0],ratio:2,echartsOptions:i[5],seriesOptions:i[6],seriesColors:i[13]})),l=!0)},p(s,r){r&2&&W(e,"height",s[1]),r&4&&W(e,"width",s[2]),t&&jt(t.update)&&r&8289&&t.update.call(null,{config:s[0],ratio:2,echartsOptions:s[5],seriesOptions:s[6],seriesColors:s[13]})},d(s){s&&g(e),l=!1,n()}}}function hu(i){let e,t,l,n,s,r,f;return{c(){e=R("div"),l=K(),n=R("div"),this.h()},l(o){e=G(o,"DIV",{class:!0,style:!0}),le(e).forEach(g),l=Q(o),n=G(o,"DIV",{class:!0,style:!0}),le(n).forEach(g),this.h()},h(){V(e,"class","chart md:hidden"),W(e,"height",i[1]),W(e,"width","650px"),W(e,"margin-left","0"),W(e,"margin-top","15px"),W(e,"margin-bottom","10px"),W(e,"overflow","visible"),W(e,"break-inside","avoid"),V(n,"class","chart hidden md:block"),W(n,"height",i[1]),W(n,"width","841px"),W(n,"margin-left","0"),W(n,"margin-top","15px"),W(n,"margin-bottom","10px"),W(n,"overflow","visible"),W(n,"break-inside","avoid")},m(o,a){S(o,e,a),S(o,l,a),S(o,n,a),r||(f=[Ze(t=Ll.call(null,e,{config:i[0],ratio:4,echartsOptions:i[5],seriesOptions:i[6],seriesColors:i[13],isMap:i[7],extraHeight:i[8],width:650})),Ze(s=Ll.call(null,n,{config:i[0],ratio:4,echartsOptions:i[5],seriesOptions:i[6],seriesColors:i[13],isMap:i[7],extraHeight:i[8],width:841}))],r=!0)},p(o,a){a&2&&W(e,"height",o[1]),t&&jt(t.update)&&a&8673&&t.update.call(null,{config:o[0],ratio:4,echartsOptions:o[5],seriesOptions:o[6],seriesColors:o[13],isMap:o[7],extraHeight:o[8],width:650}),a&2&&W(n,"height",o[1]),s&&jt(s.update)&&a&8673&&s.update.call(null,{config:o[0],ratio:4,echartsOptions:o[5],seriesOptions:o[6],seriesColors:o[13],isMap:o[7],extraHeight:o[8],width:841})},d(o){o&&(g(e),g(l),g(n)),r=!1,sl(f)}}}function _u(i){let e,t,l,n,s,r,f;return{c(){e=R("div"),l=K(),n=R("div"),this.h()},l(o){e=G(o,"DIV",{class:!0,style:!0}),le(e).forEach(g),l=Q(o),n=G(o,"DIV",{class:!0,style:!0}),le(n).forEach(g),this.h()},h(){V(e,"class","chart md:hidden"),W(e,"height",i[1]),W(e,"width",i[11]+"px"),W(e,"margin-left","0"),W(e,"margin-top","15px"),W(e,"margin-bottom","10px"),W(e,"overflow","visible"),W(e,"break-inside","avoid"),V(n,"class","chart hidden md:block"),W(n,"height",i[1]),W(n,"width",i[10]+"px"),W(n,"margin-left","0"),W(n,"margin-top","15px"),W(n,"margin-bottom","10px"),W(n,"overflow","visible"),W(n,"break-inside","avoid")},m(o,a){S(o,e,a),S(o,l,a),S(o,n,a),r||(f=[Ze(t=Ll.call(null,e,{config:i[0],ratio:4,echartsOptions:i[5],seriesOptions:i[6],seriesColors:i[13],isMap:i[7],extraHeight:i[8],width:i[11]})),Ze(s=Ll.call(null,n,{config:i[0],ratio:4,echartsOptions:i[5],seriesOptions:i[6],seriesColors:i[13],isMap:i[7],extraHeight:i[8],width:i[10]}))],r=!0)},p(o,a){a&2&&W(e,"height",o[1]),a&2048&&W(e,"width",o[11]+"px"),t&&jt(t.update)&&a&10721&&t.update.call(null,{config:o[0],ratio:4,echartsOptions:o[5],seriesOptions:o[6],seriesColors:o[13],isMap:o[7],extraHeight:o[8],width:o[11]}),a&2&&W(n,"height",o[1]),a&1024&&W(n,"width",o[10]+"px"),s&&jt(s.update)&&a&9697&&s.update.call(null,{config:o[0],ratio:4,echartsOptions:o[5],seriesOptions:o[6],seriesColors:o[13],isMap:o[7],extraHeight:o[8],width:o[10]})},d(o){o&&(g(e),g(l),g(n)),r=!1,sl(f)}}}function gu(i){let e;function t(s,r){if(s[3])return mu;if(s[4])return du}let l=t(i),n=l&&l(i);return{c(){n&&n.c(),e=te()},l(s){n&&n.l(s),e=te()},m(s,r){n&&n.m(s,r),S(s,e,r)},p(s,[r]){l===(l=t(s))&&n?n.p(s,r):(n&&n.d(1),n=l&&l(s),n&&(n.c(),n.m(e.parentNode,e)))},i:we,o:we,d(s){s&&g(e),n&&n.d(s)}}}function bu(i,e,t){let l,n,s,r,f,o,a=we,u=()=>(a(),a=gt(l,k=>t(13,o=k)),l);i.$$.on_destroy.push(()=>a());const{resolveColorsObject:d}=El();let{config:c=void 0}=e,{height:m="291px"}=e,{width:_="100%"}=e,{copying:h=!1}=e,{printing:w=!1}=e,{echartsOptions:E=void 0}=e,{seriesOptions:v=void 0}=e,{seriesColors:M=void 0}=e,{isMap:p=!1}=e,{extraHeight:F=void 0}=e,Y=!1,H,ne;const I=Sl("gridConfig");return I&&(Y=!0,{cols:H,gapWidth:ne}=I),i.$$set=k=>{"config"in k&&t(0,c=k.config),"height"in k&&t(1,m=k.height),"width"in k&&t(2,_=k.width),"copying"in k&&t(3,h=k.copying),"printing"in k&&t(4,w=k.printing),"echartsOptions"in k&&t(5,E=k.echartsOptions),"seriesOptions"in k&&t(6,v=k.seriesOptions),"seriesColors"in k&&t(14,M=k.seriesColors),"isMap"in k&&t(7,p=k.isMap),"extraHeight"in k&&t(8,F=k.extraHeight)},i.$$.update=()=>{i.$$.dirty&16384&&u(t(12,l=d(M))),i.$$.dirty&32768&&t(18,n=Math.min(Number(H),2)),i.$$.dirty&327680&&t(11,s=(650-Number(ne)*(n-1))/n),i.$$.dirty&32768&&t(17,r=Math.min(Number(H),3)),i.$$.dirty&196608&&t(10,f=(841-Number(ne)*(r-1))/r)},[c,m,_,h,w,E,v,p,F,Y,f,s,l,o,M,H,ne,r,n]}class yu extends Ae{constructor(e){super(),Se(this,e,bu,gu,me,{config:0,height:1,width:2,copying:3,printing:4,echartsOptions:5,seriesOptions:6,seriesColors:14,isMap:7,extraHeight:8})}}function ku(i){let e,t,l="Loading...",n,s,r;return{c(){e=R("div"),t=R("span"),t.textContent=l,n=K(),s=R("div"),this.h()},l(f){e=G(f,"DIV",{role:!0,class:!0});var o=le(e);t=G(o,"SPAN",{class:!0,"data-svelte-h":!0}),Ct(t)!=="svelte-1wtojot"&&(t.textContent=l),n=Q(o),s=G(o,"DIV",{class:!0,style:!0}),le(s).forEach(g),o.forEach(g),this.h()},h(){V(t,"class","sr-only"),V(s,"class","bg-base-100 rounded-md max-w-[100%]"),W(s,"height",i[0]),W(s,"margin-top","15px"),W(s,"margin-bottom","31px"),V(e,"role","status"),V(e,"class","animate-pulse")},m(f,o){S(f,e,o),ke(e,t),ke(e,n),ke(e,s)},p(f,[o]){o&1&&W(s,"height",f[0])},i(f){f&&(r||nl(()=>{r=Hi(e,Fr,{}),r.start()}))},o:we,d(f){f&&g(e)}}}function Cu(i,e,t){let{height:l="231px"}=e;return i.$$set=n=>{"height"in n&&t(0,l=n.height)},[l]}class wu extends Ae{constructor(e){super(),Se(this,e,Cu,ku,me,{height:0})}}function us(i){let e,t,l,n;const s=[Su,Au],r=[];function f(o,a){return 1}return e=f(),t=r[e]=s[e](i),{c(){t.c(),l=te()},l(o){t.l(o),l=te()},m(o,a){r[e].m(o,a),S(o,l,a),n=!0},p(o,a){t.p(o,a)},i(o){n||(b(t),n=!0)},o(o){C(t),n=!1},d(o){o&&g(l),r[e].d(o)}}}function Au(i){let e,t,l,n;return{c(){e=R("div"),this.h()},l(s){e=G(s,"DIV",{class:!0,style:!0}),le(e).forEach(g),this.h()},h(){V(e,"class","chart svelte-db4qxn"),W(e,"height",i[3]),W(e,"width",i[4]),W(e,"overflow","visible"),W(e,"display",i[15]?"none":"inherit")},m(s,r){S(s,e,r),l||(n=Ze(t=uu.call(null,e,{config:i[0],...i[25],echartsOptions:i[9],seriesOptions:i[10],dispatch:i[24],renderer:i[6],connectGroup:i[12],xAxisLabelOverflow:i[13],seriesColors:i[19],theme:i[20]})),l=!0)},p(s,r){r[0]&8&&W(e,"height",s[3]),r[0]&16&&W(e,"width",s[4]),r[0]&32768&&W(e,"display",s[15]?"none":"inherit"),t&&jt(t.update)&&r[0]&35141185&&t.update.call(null,{config:s[0],...s[25],echartsOptions:s[9],seriesOptions:s[10],dispatch:s[24],renderer:s[6],connectGroup:s[12],xAxisLabelOverflow:s[13],seriesColors:s[19],theme:s[20]})},i:we,o:we,d(s){s&&g(e),l=!1,n()}}}function Su(i){let e,t;return e=new wu({props:{height:i[3]}}),{c(){$(e.$$.fragment)},l(l){x(e.$$.fragment,l)},m(l,n){Z(e,l,n),t=!0},p(l,n){const s={};n[0]&8&&(s.height=l[3]),e.$set(s)},i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){C(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function cs(i){let e,t,l,n=i[8]&&ds(i),s=i[5]&&i[7]&&ms(i);return{c(){e=R("div"),n&&n.c(),t=K(),s&&s.c(),this.h()},l(r){e=G(r,"DIV",{class:!0});var f=le(e);n&&n.l(f),t=Q(f),s&&s.l(f),f.forEach(g),this.h()},h(){V(e,"class","chart-footer svelte-db4qxn")},m(r,f){S(r,e,f),n&&n.m(e,null),ke(e,t),s&&s.m(e,null),l=!0},p(r,f){r[8]?n?(n.p(r,f),f[0]&256&&b(n,1)):(n=ds(r),n.c(),b(n,1),n.m(e,t)):n&&(fe(),C(n,1,1,()=>{n=null}),ae()),r[5]&&r[7]?s?(s.p(r,f),f[0]&160&&b(s,1)):(s=ms(r),s.c(),b(s,1),s.m(e,null)):s&&(fe(),C(s,1,1,()=>{s=null}),ae())},i(r){l||(b(n),b(s),l=!0)},o(r){C(n),C(s),l=!1},d(r){r&&g(e),n&&n.d(),s&&s.d()}}}function ds(i){let e,t;return e=new Ms({props:{text:"Save Image",class:"download-button",downloadData:i[32],display:i[17],queryID:i[1],$$slots:{default:[Ou]},$$scope:{ctx:i}}}),{c(){$(e.$$.fragment)},l(l){x(e.$$.fragment,l)},m(l,n){Z(e,l,n),t=!0},p(l,n){const s={};n[0]&16384&&(s.downloadData=l[32]),n[0]&131072&&(s.display=l[17]),n[0]&2&&(s.queryID=l[1]),n[1]&32&&(s.$$scope={dirty:n,ctx:l}),e.$set(s)},i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){C(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function Ou(i){let e,t,l,n;return{c(){e=Gl("svg"),t=Gl("rect"),l=Gl("circle"),n=Gl("path"),this.h()},l(s){e=ql(s,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0});var r=le(e);t=ql(r,"rect",{x:!0,y:!0,width:!0,height:!0,rx:!0}),le(t).forEach(g),l=ql(r,"circle",{cx:!0,cy:!0,r:!0}),le(l).forEach(g),n=ql(r,"path",{d:!0}),le(n).forEach(g),r.forEach(g),this.h()},h(){V(t,"x","3"),V(t,"y","3"),V(t,"width","18"),V(t,"height","18"),V(t,"rx","2"),V(l,"cx","8.5"),V(l,"cy","8.5"),V(l,"r","1.5"),V(n,"d","M20.4 14.5L16 10 4 20"),V(e,"xmlns","http://www.w3.org/2000/svg"),V(e,"width","12"),V(e,"height","12"),V(e,"viewBox","0 0 24 24"),V(e,"fill","none"),V(e,"stroke","#000"),V(e,"stroke-width","2"),V(e,"stroke-linecap","round"),V(e,"stroke-linejoin","round")},m(s,r){S(s,e,r),ke(e,t),ke(e,l),ke(e,n)},p:we,d(s){s&&g(e)}}}function ms(i){let e,t;return e=new Ms({props:{text:"Download Data",data:i[5],queryID:i[1],class:"download-button",display:i[17]}}),{c(){$(e.$$.fragment)},l(l){x(e.$$.fragment,l)},m(l,n){Z(e,l,n),t=!0},p(l,n){const s={};n[0]&32&&(s.data=l[5]),n[0]&2&&(s.queryID=l[1]),n[0]&131072&&(s.display=l[17]),e.$set(s)},i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){C(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function hs(i){let e,t;return e=new Nr({props:{source:JSON.stringify(i[0],void 0,3),copyToClipboard:!0,$$slots:{default:[Tu]},$$scope:{ctx:i}}}),{c(){$(e.$$.fragment)},l(l){x(e.$$.fragment,l)},m(l,n){Z(e,l,n),t=!0},p(l,n){const s={};n[0]&1&&(s.source=JSON.stringify(l[0],void 0,3)),n[0]&1|n[1]&32&&(s.$$scope={dirty:n,ctx:l}),e.$set(s)},i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){C(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function Tu(i){let e=JSON.stringify(i[0],void 0,3)+"",t;return{c(){t=Ye(e)},l(l){t=Ue(l,e)},m(l,n){S(l,t,n)},p(l,n){n[0]&1&&e!==(e=JSON.stringify(l[0],void 0,3)+"")&&bt(t,e)},d(l){l&&g(t)}}}function _s(i){let e,t,l,n;return{c(){e=R("div"),this.h()},l(s){e=G(s,"DIV",{class:!0,style:!0}),le(e).forEach(g),this.h()},h(){V(e,"class","chart svelte-db4qxn"),W(e,"display","none"),W(e,"visibility","visible"),W(e,"height",i[3]),W(e,"width","666px"),W(e,"margin-left","0"),W(e,"margin-top","15px"),W(e,"margin-bottom","15px"),W(e,"overflow","visible")},m(s,r){S(s,e,r),l||(n=Ze(t=cu.call(null,e,{config:i[0],...i[25],echartsOptions:i[9],seriesOptions:i[10],seriesColors:i[19],queryID:i[1],evidenceChartTitle:i[2],theme:i[20],backgroundColor:i[21].colors["base-100"]})),l=!0)},p(s,r){r[0]&8&&W(e,"height",s[3]),t&&jt(t.update)&&r[0]&37225991&&t.update.call(null,{config:s[0],...s[25],echartsOptions:s[9],seriesOptions:s[10],seriesColors:s[19],queryID:s[1],evidenceChartTitle:s[2],theme:s[20],backgroundColor:s[21].colors["base-100"]})},d(s){s&&g(e),l=!1,n()}}}function Lu(i){let e,t,l,n,s,r,f,o,a,u,d=!i[16]&&us(i);l=new yu({props:{config:i[0],height:i[3],width:i[4],copying:i[15],printing:i[16],echartsOptions:i[9],seriesOptions:i[10],seriesColors:i[18]}});let c=(i[7]||i[8])&&cs(i),m=i[11]&&!i[16]&&hs(i),_=i[14]&&_s(i);return{c(){e=R("div"),d&&d.c(),t=K(),$(l.$$.fragment),n=K(),c&&c.c(),s=K(),m&&m.c(),r=K(),_&&_.c(),f=te(),this.h()},l(h){e=G(h,"DIV",{role:!0,class:!0});var w=le(e);d&&d.l(w),t=Q(w),x(l.$$.fragment,w),n=Q(w),c&&c.l(w),s=Q(w),m&&m.l(w),w.forEach(g),r=Q(h),_&&_.l(h),f=te(),this.h()},h(){V(e,"role","none"),V(e,"class","chart-container mt-2 mb-3 svelte-db4qxn")},m(h,w){S(h,e,w),d&&d.m(e,null),ke(e,t),Z(l,e,null),ke(e,n),c&&c.m(e,null),ke(e,s),m&&m.m(e,null),S(h,r,w),_&&_.m(h,w),S(h,f,w),o=!0,a||(u=[Xe(window,"copy",i[27]),Xe(window,"beforeprint",i[28]),Xe(window,"afterprint",i[29]),Xe(window,"export-beforeprint",i[30]),Xe(window,"export-afterprint",i[31]),Xe(e,"mouseenter",i[33]),Xe(e,"mouseleave",i[34])],a=!0)},p(h,w){h[16]?d&&(fe(),C(d,1,1,()=>{d=null}),ae()):d?(d.p(h,w),w[0]&65536&&b(d,1)):(d=us(h),d.c(),b(d,1),d.m(e,t));const E={};w[0]&1&&(E.config=h[0]),w[0]&8&&(E.height=h[3]),w[0]&16&&(E.width=h[4]),w[0]&32768&&(E.copying=h[15]),w[0]&65536&&(E.printing=h[16]),w[0]&512&&(E.echartsOptions=h[9]),w[0]&1024&&(E.seriesOptions=h[10]),w[0]&262144&&(E.seriesColors=h[18]),l.$set(E),h[7]||h[8]?c?(c.p(h,w),w[0]&384&&b(c,1)):(c=cs(h),c.c(),b(c,1),c.m(e,s)):c&&(fe(),C(c,1,1,()=>{c=null}),ae()),h[11]&&!h[16]?m?(m.p(h,w),w[0]&67584&&b(m,1)):(m=hs(h),m.c(),b(m,1),m.m(e,null)):m&&(fe(),C(m,1,1,()=>{m=null}),ae()),h[14]?_?_.p(h,w):(_=_s(h),_.c(),_.m(f.parentNode,f)):_&&(_.d(1),_=null)},i(h){o||(b(d),b(l.$$.fragment,h),b(c),b(m),o=!0)},o(h){C(d),C(l.$$.fragment,h),C(c),C(m),o=!1},d(h){h&&(g(e),g(r),g(f)),d&&d.d(),J(l),c&&c.d(),m&&m.d(),_&&_.d(h),a=!1,sl(u)}}}function vu(i,e,t){let l;const n=["config","queryID","evidenceChartTitle","height","width","data","renderer","downloadableData","downloadableImage","echartsOptions","seriesOptions","printEchartsConfig","seriesColors","connectGroup","xAxisLabelOverflow"];let s=ue(e,n),r,f=we,o=()=>(f(),f=gt(l,ie=>t(19,r=ie)),l),a,u;i.$$.on_destroy.push(()=>f());const{activeAppearance:d,theme:c,resolveColorsObject:m}=El();Be(i,d,ie=>t(20,a=ie)),Be(i,c,ie=>t(21,u=ie));let{config:_=void 0}=e,{queryID:h=void 0}=e,{evidenceChartTitle:w=void 0}=e,{height:E="291px"}=e,{width:v="100%"}=e,{data:M}=e,{renderer:p=void 0}=e,{downloadableData:F=void 0}=e,{downloadableImage:Y=void 0}=e,{echartsOptions:H=void 0}=e,{seriesOptions:ne=void 0}=e,{printEchartsConfig:I}=e,{seriesColors:k=void 0}=e,{connectGroup:D=void 0}=e,{xAxisLabelOverflow:T=void 0}=e;const y=Ks();let N=!1,q=!1,X=!1,Ce=!1;const ye=()=>{t(15,q=!0),Js(),setTimeout(()=>{t(15,q=!1)},0)},se=()=>t(16,X=!0),qe=()=>t(16,X=!1),Ee=()=>t(16,X=!0),oe=()=>t(16,X=!1),Oe=()=>{t(14,N=!0),setTimeout(()=>{t(14,N=!1)},0)},j=()=>t(17,Ce=!0),We=()=>t(17,Ce=!1);return i.$$set=ie=>{e=U(U({},e),Re(ie)),t(25,s=ue(e,n)),"config"in ie&&t(0,_=ie.config),"queryID"in ie&&t(1,h=ie.queryID),"evidenceChartTitle"in ie&&t(2,w=ie.evidenceChartTitle),"height"in ie&&t(3,E=ie.height),"width"in ie&&t(4,v=ie.width),"data"in ie&&t(5,M=ie.data),"renderer"in ie&&t(6,p=ie.renderer),"downloadableData"in ie&&t(7,F=ie.downloadableData),"downloadableImage"in ie&&t(8,Y=ie.downloadableImage),"echartsOptions"in ie&&t(9,H=ie.echartsOptions),"seriesOptions"in ie&&t(10,ne=ie.seriesOptions),"printEchartsConfig"in ie&&t(11,I=ie.printEchartsConfig),"seriesColors"in ie&&t(26,k=ie.seriesColors),"connectGroup"in ie&&t(12,D=ie.connectGroup),"xAxisLabelOverflow"in ie&&t(13,T=ie.xAxisLabelOverflow)},i.$$.update=()=>{i.$$.dirty[0]&67108864&&o(t(18,l=m(k)))},[_,h,w,E,v,M,p,F,Y,H,ne,I,D,T,N,q,X,Ce,l,r,a,u,d,c,y,s,k,ye,se,qe,Ee,oe,Oe,j,We]}class Du extends Ae{constructor(e){super(),Se(this,e,vu,Lu,me,{config:0,queryID:1,evidenceChartTitle:2,height:3,width:4,data:5,renderer:6,downloadableData:7,downloadableImage:8,echartsOptions:9,seriesOptions:10,printEchartsConfig:11,seriesColors:26,connectGroup:12,xAxisLabelOverflow:13},null,[-1,-1])}}function vl(i,e){const t=new Set(i.map(l=>l[e]));return Array.from(t)}function Eu(i,e){return Nt(i,Br({count:jr(e)}))[0].count}function pu(i,e,t){let l;if(typeof t!="object")l=Nt(i,Bi(e,Gn({xTotal:ji(t)})),Mi({percentOfX:Xn(t,"xTotal")}),qn({percentOfX:t+"_pct"}));else{l=Nt(i,Mi({valueSum:0}));for(let n=0;n<l.length;n++){l[n].valueSum=0;for(let s=0;s<t.length;s++)l[n].valueSum=l[n].valueSum+l[n][t[s]]}l=Nt(l,Bi(e,Gn({xTotal:ji("valueSum")})));for(let n=0;n<t.length;n++)l=Nt(l,Mi({percentOfX:Xn(t[n],"xTotal")}),qn({percentOfX:t[n]+"_pct"}))}return l}function wl(i,e,t){return[...i].sort((l,n)=>(l[e]<n[e]?-1:1)*(t?1:-1))}function Ws(i,e,t){const l=e+t;return i%l<e?0:1}function Mu(i){let e,t;return e=new Fs({props:{error:i[14],title:i[8]}}),{c(){$(e.$$.fragment)},l(l){x(e.$$.fragment,l)},m(l,n){Z(e,l,n),t=!0},p(l,n){const s={};n[0]&16384&&(s.error=l[14]),n[0]&256&&(s.title=l[8]),e.$set(s)},i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){C(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function Iu(i){let e,t,l;const n=i[136].default,s=he(n,i,i[135],null);return t=new Du({props:{config:i[20],height:i[15],width:i[13],data:i[0],queryID:i[6],evidenceChartTitle:i[7],showAllXAxisLabels:i[1],swapXY:i[3],echartsOptions:i[9],seriesOptions:i[10],printEchartsConfig:i[2],renderer:i[11],downloadableData:i[4],downloadableImage:i[5],connectGroup:i[12],xAxisLabelOverflow:i[23],seriesColors:i[16]}}),{c(){s&&s.c(),e=K(),$(t.$$.fragment)},l(r){s&&s.l(r),e=Q(r),x(t.$$.fragment,r)},m(r,f){s&&s.m(r,f),S(r,e,f),Z(t,r,f),l=!0},p(r,f){s&&s.p&&(!l||f[4]&2048)&&_e(s,n,r,r[135],l?be(n,r[135],f,null):ge(r[135]),null);const o={};f[0]&1048576&&(o.config=r[20]),f[0]&32768&&(o.height=r[15]),f[0]&8192&&(o.width=r[13]),f[0]&1&&(o.data=r[0]),f[0]&64&&(o.queryID=r[6]),f[0]&128&&(o.evidenceChartTitle=r[7]),f[0]&2&&(o.showAllXAxisLabels=r[1]),f[0]&8&&(o.swapXY=r[3]),f[0]&512&&(o.echartsOptions=r[9]),f[0]&1024&&(o.seriesOptions=r[10]),f[0]&4&&(o.printEchartsConfig=r[2]),f[0]&2048&&(o.renderer=r[11]),f[0]&16&&(o.downloadableData=r[4]),f[0]&32&&(o.downloadableImage=r[5]),f[0]&4096&&(o.connectGroup=r[12]),f[0]&65536&&(o.seriesColors=r[16]),t.$set(o)},i(r){l||(b(s,r),b(t.$$.fragment,r),l=!0)},o(r){C(s,r),C(t.$$.fragment,r),l=!1},d(r){r&&g(e),s&&s.d(r),J(t,r)}}}function Pu(i){let e,t,l,n;const s=[Iu,Mu],r=[];function f(o,a){return o[14]?1:0}return e=f(i),t=r[e]=s[e](i),{c(){t.c(),l=te()},l(o){t.l(o),l=te()},m(o,a){r[e].m(o,a),S(o,l,a),n=!0},p(o,a){let u=e;e=f(o),e===u?r[e].p(o,a):(fe(),C(r[u],1,1,()=>{r[u]=null}),ae(),t=r[e],t?t.p(o,a):(t=r[e]=s[e](o),t.c()),b(t,1),t.m(l.parentNode,l))},i(o){n||(b(t),n=!0)},o(o){C(t),n=!1},d(o){o&&g(l),r[e].d(o)}}}function Fu(i,e,t){let l,n,s,r,f,o=we,a=()=>(o(),o=gt(s,O=>t(131,f=O)),s),u,d,c=we,m=()=>(c(),c=gt(n,O=>t(133,d=O)),n),_,h=we,w=()=>(h(),h=gt(l,O=>t(134,_=O)),l),E;i.$$.on_destroy.push(()=>o()),i.$$.on_destroy.push(()=>c()),i.$$.on_destroy.push(()=>h());let{$$slots:v={},$$scope:M}=e,p=Jl({}),F=Jl({});Be(i,F,O=>t(20,E=O));const{theme:Y,resolveColor:H,resolveColorsObject:ne,resolveColorPalette:I}=El();Be(i,Y,O=>t(132,u=O));let{data:k=void 0}=e,{queryID:D=void 0}=e,{x:T=void 0}=e,{y=void 0}=e,{y2:N=void 0}=e,{series:q=void 0}=e,{size:X=void 0}=e,{tooltipTitle:Ce=void 0}=e,{showAllXAxisLabels:ye=void 0}=e,{printEchartsConfig:se=!1}=e,qe=!!y,Ee=!!T,{swapXY:oe=!1}=e,{title:Oe=void 0}=e,{subtitle:j=void 0}=e,{chartType:We="Chart"}=e,{bubble:ie=!1}=e,{hist:je=!1}=e,{boxplot:pe=!1}=e,Qe,{xType:ve=void 0}=e,{xAxisTitle:Me="false"}=e,{xBaseline:ce=!0}=e,{xTickMarks:re=!1}=e,{xGridlines:L=!1}=e,{xAxisLabels:z=!0}=e,{sort:Fe=!0}=e,{xFmt:He=void 0}=e,{xMin:xe=void 0}=e,{xMax:Pe=void 0}=e,{yLog:$e=!1}=e,{yType:Ge=$e===!0?"log":"value"}=e,{yLogBase:tt=10}=e,{yAxisTitle:Ke="false"}=e,{yBaseline:lt=!1}=e,{yTickMarks:Je=!1}=e,{yGridlines:nt=!0}=e,{yAxisLabels:st=!0}=e,{yMin:et=void 0}=e,{yMax:it=void 0}=e,{yScale:B=!1}=e,{yFmt:de=void 0}=e,{yAxisColor:dt="true"}=e,{y2AxisTitle:ft="false"}=e,{y2Baseline:mt=!1}=e,{y2TickMarks:P=!1}=e,{y2Gridlines:Ne=!0}=e,{y2AxisLabels:at=!0}=e,{y2Min:wt=void 0}=e,{y2Max:Vt=void 0}=e,{y2Scale:Lt=!1}=e,{y2Fmt:vt=void 0}=e,{y2AxisColor:zt="true"}=e,{sizeFmt:Mt=void 0}=e,{colorPalette:Ht="default"}=e,{legend:ut=void 0}=e,{echartsOptions:qt=void 0}=e,{seriesOptions:ol=void 0}=e,{seriesColors:Gt=void 0}=e,{stackType:Rt=void 0}=e,{stacked100:Dt=!1}=e,{chartAreaHeight:ct}=e,{renderer:fl=void 0}=e,{downloadableData:A=!0}=e,{downloadableImage:pl=!0}=e,{connectGroup:Ui=void 0}=e,{leftPadding:li=void 0}=e,{rightPadding:ii=void 0}=e,{xLabelWrap:al=!1}=e;const Us=al?"break":"truncate";let Ie,Ml,Il=[],Wt=[],ni,ul,yt,si,kt,rt,At,Pl,ri,cl,Fl,oi,Nl,Xt,fi,ai,dl,Ut,ui,ci,di,mi,hi,_i,gi,bi,yi,ki,Ci,Yt,ml,hl,Bl,jl,wi,Ai,Qt,Si,Oi,Vl,Yi,Ti,It=[],Kt=!0,St=[],_l=[],ht,gl,Li,Pt;return i.$$set=O=>{"data"in O&&t(0,k=O.data),"queryID"in O&&t(6,D=O.queryID),"x"in O&&t(24,T=O.x),"y"in O&&t(25,y=O.y),"y2"in O&&t(49,N=O.y2),"series"in O&&t(50,q=O.series),"size"in O&&t(51,X=O.size),"tooltipTitle"in O&&t(52,Ce=O.tooltipTitle),"showAllXAxisLabels"in O&&t(1,ye=O.showAllXAxisLabels),"printEchartsConfig"in O&&t(2,se=O.printEchartsConfig),"swapXY"in O&&t(3,oe=O.swapXY),"title"in O&&t(7,Oe=O.title),"subtitle"in O&&t(53,j=O.subtitle),"chartType"in O&&t(8,We=O.chartType),"bubble"in O&&t(54,ie=O.bubble),"hist"in O&&t(55,je=O.hist),"boxplot"in O&&t(56,pe=O.boxplot),"xType"in O&&t(26,ve=O.xType),"xAxisTitle"in O&&t(27,Me=O.xAxisTitle),"xBaseline"in O&&t(28,ce=O.xBaseline),"xTickMarks"in O&&t(29,re=O.xTickMarks),"xGridlines"in O&&t(30,L=O.xGridlines),"xAxisLabels"in O&&t(31,z=O.xAxisLabels),"sort"in O&&t(32,Fe=O.sort),"xFmt"in O&&t(57,He=O.xFmt),"xMin"in O&&t(58,xe=O.xMin),"xMax"in O&&t(59,Pe=O.xMax),"yLog"in O&&t(33,$e=O.yLog),"yType"in O&&t(60,Ge=O.yType),"yLogBase"in O&&t(61,tt=O.yLogBase),"yAxisTitle"in O&&t(34,Ke=O.yAxisTitle),"yBaseline"in O&&t(35,lt=O.yBaseline),"yTickMarks"in O&&t(36,Je=O.yTickMarks),"yGridlines"in O&&t(37,nt=O.yGridlines),"yAxisLabels"in O&&t(38,st=O.yAxisLabels),"yMin"in O&&t(62,et=O.yMin),"yMax"in O&&t(63,it=O.yMax),"yScale"in O&&t(39,B=O.yScale),"yFmt"in O&&t(64,de=O.yFmt),"yAxisColor"in O&&t(65,dt=O.yAxisColor),"y2AxisTitle"in O&&t(40,ft=O.y2AxisTitle),"y2Baseline"in O&&t(41,mt=O.y2Baseline),"y2TickMarks"in O&&t(42,P=O.y2TickMarks),"y2Gridlines"in O&&t(43,Ne=O.y2Gridlines),"y2AxisLabels"in O&&t(44,at=O.y2AxisLabels),"y2Min"in O&&t(66,wt=O.y2Min),"y2Max"in O&&t(67,Vt=O.y2Max),"y2Scale"in O&&t(45,Lt=O.y2Scale),"y2Fmt"in O&&t(68,vt=O.y2Fmt),"y2AxisColor"in O&&t(69,zt=O.y2AxisColor),"sizeFmt"in O&&t(70,Mt=O.sizeFmt),"colorPalette"in O&&t(71,Ht=O.colorPalette),"legend"in O&&t(46,ut=O.legend),"echartsOptions"in O&&t(9,qt=O.echartsOptions),"seriesOptions"in O&&t(10,ol=O.seriesOptions),"seriesColors"in O&&t(72,Gt=O.seriesColors),"stackType"in O&&t(73,Rt=O.stackType),"stacked100"in O&&t(74,Dt=O.stacked100),"chartAreaHeight"in O&&t(47,ct=O.chartAreaHeight),"renderer"in O&&t(11,fl=O.renderer),"downloadableData"in O&&t(4,A=O.downloadableData),"downloadableImage"in O&&t(5,pl=O.downloadableImage),"connectGroup"in O&&t(12,Ui=O.connectGroup),"leftPadding"in O&&t(75,li=O.leftPadding),"rightPadding"in O&&t(76,ii=O.rightPadding),"xLabelWrap"in O&&t(48,al=O.xLabelWrap),"$$scope"in O&&t(135,M=O.$$scope)},i.$$.update=()=>{var O,Qi,Ki,Ji,Zi,xi;if(i.$$.dirty[0]&4&&t(2,se=De(se)),i.$$.dirty[0]&8&&t(3,oe=De(oe)),i.$$.dirty[0]&268435456&&t(28,ce=De(ce)),i.$$.dirty[0]&536870912&&t(29,re=De(re)),i.$$.dirty[0]&1073741824&&t(30,L=De(L)),i.$$.dirty[1]&1&&t(31,z=De(z)),i.$$.dirty[1]&2&&t(32,Fe=De(Fe)),i.$$.dirty[1]&4&&t(33,$e=De($e)),i.$$.dirty[1]&16&&t(35,lt=De(lt)),i.$$.dirty[1]&32&&t(36,Je=De(Je)),i.$$.dirty[1]&64&&t(37,nt=De(nt)),i.$$.dirty[1]&128&&t(38,st=De(st)),i.$$.dirty[1]&256&&t(39,B=De(B)),i.$$.dirty[2]&8&&w(t(19,l=H(dt))),i.$$.dirty[1]&1024&&t(41,mt=De(mt)),i.$$.dirty[1]&2048&&t(42,P=De(P)),i.$$.dirty[1]&4096&&t(43,Ne=De(Ne)),i.$$.dirty[1]&8192&&t(44,at=De(at)),i.$$.dirty[1]&16384&&t(45,Lt=De(Lt)),i.$$.dirty[2]&128&&m(t(18,n=H(zt))),i.$$.dirty[2]&512&&a(t(17,s=I(Ht))),i.$$.dirty[2]&1024&&t(16,r=ne(Gt)),i.$$.dirty[0]&16&&t(4,A=De(A)),i.$$.dirty[0]&32&&t(5,pl=De(pl)),i.$$.dirty[1]&131072&&t(48,al=De(al)),i.$$.dirty[0]&2130731403|i.$$.dirty[1]&2147352575|i.$$.dirty[2]&2147481975|i.$$.dirty[3]&2147483647|i.$$.dirty[4]&2047)try{if(t(14,gl=void 0),t(124,It=[]),t(83,Wt=[]),t(126,St=[]),t(127,_l=[]),t(85,ul=[]),t(77,qe=!!y),t(78,Ee=!!T),dn(k),t(80,Ie=Ii(k)),t(81,Ml=Object.keys(Ie)),Ee||t(24,T=Ml[0]),!qe){t(82,Il=Ml.filter(function(ee){return![T,q,X].includes(ee)}));for(let ee=0;ee<Il.length;ee++)t(85,ul=Il[ee]),t(84,ni=Ie[ul].type),ni==="number"&&Wt.push(ul);t(25,y=Wt.length>1?Wt:Wt[0])}ie?t(79,Qe={x:T,y,size:X}):je?t(79,Qe={x:T}):pe?t(79,Qe={}):t(79,Qe={x:T,y});for(let ee in Qe)Qe[ee]==null&&It.push(ee);if(It.length===1)throw Error(new Intl.ListFormat().format(It)+" is required");if(It.length>1)throw Error(new Intl.ListFormat().format(It)+" are required");if(Dt===!0&&y.includes("_pct")&&Kt===!1)if(typeof y=="object"){for(let ee=0;ee<y.length;ee++)t(25,y[ee]=y[ee].replace("_pct",""),y);t(125,Kt=!1)}else t(25,y=y.replace("_pct","")),t(125,Kt=!1);if(T&&St.push(T),y)if(typeof y=="object")for(t(128,ht=0);ht<y.length;t(128,ht++,ht))St.push(y[ht]);else St.push(y);if(N)if(typeof N=="object")for(t(128,ht=0);ht<N.length;t(128,ht++,ht))St.push(N[ht]);else St.push(N);if(X&&St.push(X),q&&_l.push(q),Ce&&_l.push(Ce),dn(k,St,_l),Dt===!0){if(t(0,k=pu(k,T,y)),typeof y=="object"){for(let ee=0;ee<y.length;ee++)t(25,y[ee]=y[ee]+"_pct",y);t(125,Kt=!1)}else t(25,y=y+"_pct"),t(125,Kt=!1);t(80,Ie=Ii(k))}switch(t(86,yt=Ie[T].type),yt){case"number":t(86,yt="value");break;case"string":t(86,yt="category");break;case"date":t(86,yt="time");break;default:break}if(t(26,ve=ve==="category"?"category":yt),ye?t(1,ye=ye==="true"||ye===!0):t(1,ye=ve==="category"),oe&&ve!=="category")throw Error("Horizontal charts do not support a value or time-based x-axis. You can either change your SQL query to output string values or set swapXY=false.");if(oe&&N)throw Error("Horizontal charts do not support a secondary y-axis. You can either set swapXY=false or remove the y2 prop from your chart.");if(oe&&t(26,ve="category"),t(87,si=yt==="value"&&ve==="category"),t(0,k=Fe?yt==="category"?wl(k,y,!1):wl(k,T,!0):k),yt==="time"&&t(0,k=wl(k,T,!0)),t(129,Li=Ii(k,"array")),t(130,Pt=Li.filter(ee=>ee.type==="date")),t(130,Pt=Pt.map(ee=>ee.id)),Pt.length>0)for(let ee=0;ee<Pt.length;ee++)t(0,k=Vr(k,Pt[ee]));He?t(88,kt=Ot(He,(O=Ie[T].format)==null?void 0:O.valueType)):t(88,kt=Ie[T].format),y?de?typeof y=="object"?t(89,rt=Ot(de,(Qi=Ie[y[0]].format)==null?void 0:Qi.valueType)):t(89,rt=Ot(de,(Ki=Ie[y].format)==null?void 0:Ki.valueType)):typeof y=="object"?t(89,rt=Ie[y[0]].format):t(89,rt=Ie[y].format):t(89,rt="str"),N&&(vt?typeof N=="object"?t(90,At=Ot(vt,(Ji=Ie[N[0]].format)==null?void 0:Ji.valueType)):t(90,At=Ot(vt,(Zi=Ie[N].format)==null?void 0:Zi.valueType)):typeof N=="object"?t(90,At=Ie[N[0]].format):t(90,At=Ie[N].format)),X&&(Mt?t(91,Pl=Ot(Mt,(xi=Ie[X].format)==null?void 0:xi.valueType)):t(91,Pl=Ie[X].format)),t(92,ri=Ie[T].columnUnitSummary),y&&(typeof y=="object"?t(93,cl=Ie[y[0]].columnUnitSummary):t(93,cl=Ie[y].columnUnitSummary)),N&&(typeof N=="object"?t(94,Fl=Ie[N[0]].columnUnitSummary):t(94,Fl=Ie[N].columnUnitSummary)),t(27,Me=Me==="true"?_t(T,kt):Me==="false"?"":Me),t(34,Ke=Ke==="true"?typeof y=="object"?"":_t(y,rt):Ke==="false"?"":Ke),t(40,ft=ft==="true"?typeof N=="object"?"":_t(N,At):ft==="false"?"":ft);let Jt=typeof y=="object"?y.length:1,$i=q?Eu(k,q):1,bl=Jt*$i,vi=typeof N=="object"?N.length:N?1:0,Di=bl+vi;if(ut!==void 0&&t(46,ut=ut==="true"||ut===!0),t(46,ut=ut??Di>1),Dt===!0&&$e===!0)throw Error("Log axis cannot be used in a 100% stacked chart");if(Rt==="stacked"&&Di>1&&$e===!0)throw Error("Log axis cannot be used in a stacked chart");let Zt;if(typeof y=="object"){Zt=Ie[y[0]].columnUnitSummary.min;for(let ee=0;ee<y.length;ee++)Ie[y[ee]].columnUnitSummary.min<Zt&&(Zt=Ie[y[ee]].columnUnitSummary.min)}else y&&(Zt=Ie[y].columnUnitSummary.min);if($e===!0&&Zt<=0&&Zt!==null)throw Error("Log axis cannot display values less than or equal to zero");p.update(ee=>({...ee,data:k,x:T,y,y2:N,series:q,swapXY:oe,sort:Fe,xType:ve,xFormat:kt,yFormat:rt,y2Format:At,sizeFormat:Pl,xMismatch:si,size:X,yMin:et,y2Min:wt,columnSummary:Ie,xAxisTitle:Me,yAxisTitle:Ke,y2AxisTitle:ft,tooltipTitle:Ce,chartAreaHeight:ct,chartType:We,yCount:Jt,y2Count:vi})),t(95,oi=vl(k,T));let en;if(oe?t(96,Nl={type:Ge,logBase:tt,position:"top",axisLabel:{show:st,hideOverlap:!0,showMaxLabel:!0,formatter(ee){return Ul(ee,rt,cl)},margin:4},min:et,max:it,scale:B,splitLine:{show:nt},axisLine:{show:lt,onZero:!1},axisTick:{show:Je},boundaryGap:!1,z:2}):t(96,Nl={type:ve,min:xe,max:Pe,tooltip:{show:!0,position:"inside",formatter(ee){if(ee.isTruncated())return ee.name}},splitLine:{show:L},axisLine:{show:ce},axisTick:{show:re},axisLabel:{show:z,hideOverlap:!0,showMaxLabel:ve==="category"||ve==="value",formatter:ve==="time"||ve==="category"?!1:function(ee){return Ul(ee,kt,ri)},margin:6},scale:!0,z:2}),oe?t(97,Xt={type:ve,inverse:"true",splitLine:{show:L},axisLine:{show:ce},axisTick:{show:re},axisLabel:{show:z,hideOverlap:!0},scale:!0,min:xe,max:Pe,z:2}):(t(97,Xt={type:Ge,logBase:tt,splitLine:{show:nt},axisLine:{show:lt,onZero:!1},axisTick:{show:Je},axisLabel:{show:st,hideOverlap:!0,margin:4,formatter(ee){return Ul(ee,rt,cl)},color:N?_==="true"?f[0]:_!=="false"?_:void 0:void 0},name:Ke,nameLocation:"end",nameTextStyle:{align:"left",verticalAlign:"top",padding:[0,5,0,0],color:N?_==="true"?f[0]:_!=="false"?_:void 0:void 0},nameGap:6,min:et,max:it,scale:B,boundaryGap:["0%","1%"],z:2}),en={type:"value",show:!1,alignTicks:!0,splitLine:{show:Ne},axisLine:{show:mt,onZero:!1},axisTick:{show:P},axisLabel:{show:at,hideOverlap:!0,margin:4,formatter(ee){return Ul(ee,At,Fl)},color:d==="true"?f[bl]:d!=="false"?d:void 0},name:ft,nameLocation:"end",nameTextStyle:{align:"right",verticalAlign:"top",padding:[0,0,0,5],color:d==="true"?f[bl]:d!=="false"?d:void 0},nameGap:6,min:wt,max:Vt,scale:Lt,boundaryGap:["0%","1%"],z:2},t(97,Xt=[Xt,en])),ct){if(t(47,ct=Number(ct)),isNaN(ct))throw Error("chartAreaHeight must be a number");if(ct<=0)throw Error("chartAreaHeight must be a positive number")}else t(47,ct=180);t(100,dl=!!Oe),t(101,Ut=!!j),t(102,ui=ut*(q!==null||typeof y=="object"&&y.length>1)),t(103,ci=Ke!==""&&oe),t(104,di=Me!==""&&!oe),t(105,mi=15),t(106,hi=13),t(107,_i=6*Ut),t(108,gi=dl*mi+Ut*hi+_i*Math.max(dl,Ut)),t(109,bi=10),t(110,yi=10),t(111,ki=14),t(112,Ci=14),t(113,Yt=15),t(113,Yt=Yt*ui),t(114,ml=7),t(114,ml=ml*Math.max(dl,Ut)),t(115,hl=gi+ml),t(116,Bl=hl+Yt+Ci*ci+bi),t(117,jl=di*ki+yi),t(121,Si=8),t(123,Vl=1),oe&&(t(122,Oi=oi.length),t(123,Vl=Math.max(1,Oi/Si))),t(118,wi=ct*Vl+Bl+jl),t(119,Ai=hl+Yt+7),t(15,Yi=wi+"px"),t(13,Ti="100%"),t(120,Qt=oe?Ke:Me),Qt!==""&&t(120,Qt=Qt+" →"),t(98,fi={id:"horiz-axis-title",type:"text",style:{text:Qt,textAlign:"right",fill:u.colors["base-content-muted"]},cursor:"auto",right:oe?"2%":"3%",top:oe?Ai:null,bottom:oe?null:"2%"}),t(99,ai={title:{text:Oe,subtext:j,subtextStyle:{width:Ti}},tooltip:{trigger:"axis",show:!0,formatter(ee){let xt,$t,el,zl;if(Di>1){$t=ee[0].value[oe?1:0],xt=`<span id="tooltip" style='font-weight: 600;'>${Et($t,kt)}</span>`;for(let Ft=ee.length-1;Ft>=0;Ft--)ee[Ft].seriesName!=="stackTotal"&&(el=ee[Ft].value[oe?0:1],xt=xt+`<br> <span style='font-size: 11px;'>${ee[Ft].marker} ${ee[Ft].seriesName}<span/><span style='float:right; margin-left: 10px; font-size: 12px;'>${Et(el,Ws(ee[Ft].componentIndex,Jt,vi)===0?rt:At)}</span>`)}else ve==="value"?($t=ee[0].value[oe?1:0],el=ee[0].value[oe?0:1],zl=ee[0].seriesName,xt=`<span id="tooltip" style='font-weight: 600;'>${_t(T,kt)}: </span><span style='float:right; margin-left: 10px;'>${Et($t,kt)}</span><br/><span style='font-weight: 600;'>${_t(zl,rt)}: </span><span style='float:right; margin-left: 10px;'>${Et(el,rt)}</span>`):($t=ee[0].value[oe?1:0],el=ee[0].value[oe?0:1],zl=ee[0].seriesName,xt=`<span id="tooltip" style='font-weight: 600;'>${Et($t,kt)}</span><br/><span>${_t(zl,rt)}: </span><span style='float:right; margin-left: 10px;'>${Et(el,rt)}</span>`);return xt},confine:!0,axisPointer:{type:"shadow"},extraCssText:'box-shadow: 0 3px 6px rgba(0,0,0,.15); box-shadow: 0 2px 4px rgba(0,0,0,.12); z-index: 1; font-feature-settings: "cv02", "tnum";',order:"valueDesc"},legend:{show:ut,type:"scroll",top:hl,padding:[0,0,0,0],data:[]},grid:{left:li??(oe?"1%":"0.8%"),right:ii??(oe?"4%":"3%"),bottom:jl,top:Bl,containLabel:!0},xAxis:Nl,yAxis:Xt,series:[],animation:!0,graphic:fi,color:f}),F.update(()=>ai)}catch(Jt){if(t(14,gl=Jt.message),console.error("\x1B[31m%s\x1B[0m",`Error in ${We}: ${Jt.message}`),zr)throw gl;p.update(bl=>({...bl,error:gl}))}i.$$.dirty[0]&1},Al(Is,p),Al(Ps,F),[k,ye,se,oe,A,pl,D,Oe,We,qt,ol,fl,Ui,Ti,gl,Yi,r,s,n,l,E,F,Y,Us,T,y,ve,Me,ce,re,L,z,Fe,$e,Ke,lt,Je,nt,st,B,ft,mt,P,Ne,at,Lt,ut,ct,al,N,q,X,Ce,j,ie,je,pe,He,xe,Pe,Ge,tt,et,it,de,dt,wt,Vt,vt,zt,Mt,Ht,Gt,Rt,Dt,li,ii,qe,Ee,Qe,Ie,Ml,Il,Wt,ni,ul,yt,si,kt,rt,At,Pl,ri,cl,Fl,oi,Nl,Xt,fi,ai,dl,Ut,ui,ci,di,mi,hi,_i,gi,bi,yi,ki,Ci,Yt,ml,hl,Bl,jl,wi,Ai,Qt,Si,Oi,Vl,It,Kt,St,_l,ht,Li,Pt,f,u,d,_,M,v]}class Nu extends Ae{constructor(e){super(),Se(this,e,Fu,Pu,me,{data:0,queryID:6,x:24,y:25,y2:49,series:50,size:51,tooltipTitle:52,showAllXAxisLabels:1,printEchartsConfig:2,swapXY:3,title:7,subtitle:53,chartType:8,bubble:54,hist:55,boxplot:56,xType:26,xAxisTitle:27,xBaseline:28,xTickMarks:29,xGridlines:30,xAxisLabels:31,sort:32,xFmt:57,xMin:58,xMax:59,yLog:33,yType:60,yLogBase:61,yAxisTitle:34,yBaseline:35,yTickMarks:36,yGridlines:37,yAxisLabels:38,yMin:62,yMax:63,yScale:39,yFmt:64,yAxisColor:65,y2AxisTitle:40,y2Baseline:41,y2TickMarks:42,y2Gridlines:43,y2AxisLabels:44,y2Min:66,y2Max:67,y2Scale:45,y2Fmt:68,y2AxisColor:69,sizeFmt:70,colorPalette:71,legend:46,echartsOptions:9,seriesOptions:10,seriesColors:72,stackType:73,stacked100:74,chartAreaHeight:47,renderer:11,downloadableData:4,downloadableImage:5,connectGroup:12,leftPadding:75,rightPadding:76,xLabelWrap:48},null,[-1,-1,-1,-1,-1])}}function Bu(i){let e;const t=i[7].default,l=he(t,i,i[8],null);return{c(){l&&l.c()},l(n){l&&l.l(n)},m(n,s){l&&l.m(n,s),e=!0},p(n,s){l&&l.p&&(!e||s&256)&&_e(l,t,n,n[8],e?be(t,n[8],s,null):ge(n[8]),null)},i(n){e||(b(l,n),e=!0)},o(n){C(l,n),e=!1},d(n){l&&l.d(n)}}}function ju(i){let e,t;const l=[i[5],{data:Tl.isQuery(i[11])?Array.from(i[11]):i[11]},{queryID:i[6]}];let n={$$slots:{default:[Bu]},$$scope:{ctx:i}};for(let s=0;s<l.length;s+=1)n=U(n,l[s]);return e=new Nu({props:n}),{c(){$(e.$$.fragment)},l(s){x(e.$$.fragment,s)},m(s,r){Z(e,s,r),t=!0},p(s,r){const f=r&2144?Ve(l,[r&32&&Tt(s[5]),r&2048&&{data:Tl.isQuery(s[11])?Array.from(s[11]):s[11]},r&64&&{queryID:s[6]}]):{};r&256&&(f.$$scope={dirty:r,ctx:s}),e.$set(f)},i(s){t||(b(e.$$.fragment,s),t=!0)},o(s){C(e.$$.fragment,s),t=!1},d(s){J(e,s)}}}function Vu(i){let e,t;return e=new qr({props:{slot:"empty",emptyMessage:i[2],emptySet:i[1],chartType:i[5].chartType,isInitial:i[4]}}),{c(){$(e.$$.fragment)},l(l){x(e.$$.fragment,l)},m(l,n){Z(e,l,n),t=!0},p(l,n){const s={};n&4&&(s.emptyMessage=l[2]),n&2&&(s.emptySet=l[1]),n&32&&(s.chartType=l[5].chartType),n&16&&(s.isInitial=l[4]),e.$set(s)},i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){C(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function zu(i){let e,t;return e=new Fs({props:{slot:"error",title:i[5].chartType,error:i[11].error.message}}),{c(){$(e.$$.fragment)},l(l){x(e.$$.fragment,l)},m(l,n){Z(e,l,n),t=!0},p(l,n){const s={};n&32&&(s.title=l[5].chartType),n&2048&&(s.error=l[11].error.message),e.$set(s)},i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){C(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function Hu(i){let e,t;return e=new Hr({props:{data:i[0],height:i[3],$$slots:{error:[zu,({loaded:l})=>({11:l}),({loaded:l})=>l?2048:0],empty:[Vu],default:[ju,({loaded:l})=>({11:l}),({loaded:l})=>l?2048:0]},$$scope:{ctx:i}}}),{c(){$(e.$$.fragment)},l(l){x(e.$$.fragment,l)},m(l,n){Z(e,l,n),t=!0},p(l,[n]){const s={};n&1&&(s.data=l[0]),n&8&&(s.height=l[3]),n&2358&&(s.$$scope={dirty:n,ctx:l}),e.$set(s)},i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){C(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function qu(i,e,t){let l,{$$slots:n={},$$scope:s}=e,{data:r}=e;const f=Tl.isQuery(r)?r.hash:void 0;let o=(r==null?void 0:r.hash)===f,{emptySet:a=void 0}=e,{emptyMessage:u=void 0}=e,{height:d=200}=e,c=r==null?void 0:r.id;return i.$$set=m=>{t(10,e=U(U({},e),Re(m))),"data"in m&&t(0,r=m.data),"emptySet"in m&&t(1,a=m.emptySet),"emptyMessage"in m&&t(2,u=m.emptyMessage),"height"in m&&t(3,d=m.height),"$$scope"in m&&t(8,s=m.$$scope)},i.$$.update=()=>{i.$$.dirty&1&&t(4,o=(r==null?void 0:r.hash)===f),t(5,l={...Object.fromEntries(Object.entries(e).filter(([,m])=>m!==void 0))})},e=Re(e),[r,a,u,d,o,l,c,n,s]}class Gu extends Ae{constructor(e){super(),Se(this,e,qu,Hu,me,{data:0,emptySet:1,emptyMessage:2,height:3})}}function Ru(i,e,t,l,n,s,r,f,o,a,u=void 0,d=void 0,c=void 0,m=void 0){function _(k,D,T,y){let N={name:D,data:k,yAxisIndex:T};return N={...y,...N},N}let h,w,E,v=[],M,p,F,Y,H;function ne(k,D){const T=[];function y(q){return typeof q>"u"}function N(q,X){y(q)||(Array.isArray(q)?q.forEach(Ce=>T.push([Ce,X])):T.push([q,X]))}return N(k,0),N(D,1),T}let I=ne(t,c);if(l!=null&&I.length===1)for(Y=vl(i,l),h=0;h<Y.length;h++){if(p=i.filter(k=>k[l]===Y[h]),n?M=p.map(k=>[k[I[0][0]],f?k[e].toString():k[e]]):M=p.map(k=>[f?k[e].toString():k[e],k[I[0][0]]]),u){let k=p.map(D=>D[u]);M.forEach((D,T)=>D.push(k[T]))}if(d){let k=p.map(D=>D[d]);M.forEach((D,T)=>D.push(k[T]))}F=Y[h]??"null",H=I[0][1],E=_(M,F,H,s),v.push(E)}if(l!=null&&I.length>1)for(Y=vl(i,l),h=0;h<Y.length;h++)for(p=i.filter(k=>k[l]===Y[h]),w=0;w<I.length;w++){if(n?M=p.map(k=>[k[I[w][0]],f?k[e].toString():k[e]]):M=p.map(k=>[f?k[e].toString():k[e],k[I[w][0]]]),u){let k=p.map(D=>D[u]);M.forEach((D,T)=>D.push(k[T]))}if(d){let k=p.map(D=>D[d]);M.forEach((D,T)=>D.push(k[T]))}F=(Y[h]??"null")+" - "+o[I[w][0]].title,H=I[w][1],E=_(M,F,H,s),v.push(E)}if(l==null&&I.length>1)for(h=0;h<I.length;h++){if(n?M=i.map(k=>[k[I[h][0]],f?k[e].toString():k[e]]):M=i.map(k=>[f?k[e].toString():k[e],k[I[h][0]]]),u){let k=i.map(D=>D[u]);M.forEach((D,T)=>D.push(k[T]))}if(d){let k=i.map(D=>D[d]);M.forEach((D,T)=>D.push(k[T]))}F=o[I[h][0]].title,H=I[h][1],E=_(M,F,H,s),v.push(E)}if(l==null&&I.length===1){if(n?M=i.map(k=>[k[I[0][0]],f?k[e].toString():k[e]]):M=i.map(k=>[f?k[e].toString():k[e],k[I[0][0]]]),u){let k=i.map(D=>D[u]);M.forEach((D,T)=>D.push(k[T]))}if(d){let k=i.map(D=>D[d]);M.forEach((D,T)=>D.push(k[T]))}F=o[I[0][0]].title,H=I[0][1],E=_(M,F,H,s),v.push(E)}return a&&v.sort((k,D)=>a.indexOf(k.name)-a.indexOf(D.name)),m&&v.forEach(k=>{k.name=Gr(k.name,m)}),v}function Wu(i){let e=[];for(let t=1;t<i.length;t++)e.push(i[t]-i[t-1]);return e}function Xs(i,e){return(typeof i!="number"||isNaN(i))&&(i=0),(typeof e!="number"||isNaN(e))&&(e=0),i=Math.abs(i),e=Math.abs(e),e<=.01?i:Xs(e,i%e)}function Xu(i,e){if(!Array.isArray(i))throw new TypeError("Cannot calculate extent of non-array value.");let t,l;for(const n of i)typeof n=="number"&&(t===void 0?n>=n&&(t=l=n):(t>n&&(t=n),l<n&&(l=n)));return[t,l]}function Uu(i,e){let[t,l]=Xu(i);const n=[];let s=t;for(;s<=l;)n.push(Math.round((s+Number.EPSILON)*1e8)/1e8),s+=e;return n}function Yu(i){if(i.length<=1)return;i.sort(function(t,l){return t-l}),i=i.map(function(t){return t*1e8}),i=Wu(i);let e=i.reduce((t,l)=>Xs(t,l))/1e8;return e=Math.round((e+Number.EPSILON)*1e8)/1e8,e}function Fi(i,e,t,l,n=!1,s=!1){var m;let r=!1;const f=i.map(_=>Object.assign({},_,{[e]:_[e]instanceof Date?(r=!0,_[e].toISOString()):_[e]})).filter(_=>_[e]!==void 0&&_[e]!==null),o=Array.from(f).reduce((_,h)=>(h[e]instanceof Date&&(h[e]=h[e].toISOString(),r=!0),l?(_[h[l]??"null"]||(_[h[l]??"null"]=[]),_[h[l]??"null"].push(h)):(_.default||(_.default=[]),_.default.push(h)),_),{}),a={};let u;const d=((m=f.find(_=>_&&_[e]!==null&&_[e]!==void 0))==null?void 0:m[e])??null;switch(typeof d){case"object":throw d===null?new Error(`Column '${e}' is entirely null. Column must contain at least one non-null value.`):new Error("Unexpected object property, expected string, date, or number");case"number":if(u=vl(f,e),s){const _=Yu(u);a[e]=Uu(u,_)}break;case"string":u=vl(f,e),a[e]=u;break}const c=[];for(const _ of Object.values(o)){const h=l?{[l]:null}:{};if(n)if(t instanceof Array)for(let E=0;E<t.length;E++)h[t[E]]=0;else h[t]=0;else if(t instanceof Array)for(let E=0;E<t.length;E++)h[t[E]]=null;else h[t]=null;l&&(a[l]=l);const w=[];Object.keys(a).length===0?w.push(Rn([e],h)):w.push(Rn(a,h)),c.push(Nt(_,...w))}return r?c.flat().map(_=>({..._,[e]:new Date(_[e])})):c.flat()}function gs(i,e,t){let l=Nt(i,Bi(e,[Rr(t,ji)]));if(typeof t=="object")for(let n=0;n<l.length;n++){l[n].stackTotal=0;for(let s=0;s<t.length;s++)l[n].stackTotal=l[n].stackTotal+l[n][t[s]]}return l}let Qu=60;function Ku(i,e,t){let l,n,s,r,f,o,a,u,d,c,m,_,h,w,E,v,M,p,F,Y,H=we,ne=()=>(H(),H=gt(r,P=>t(49,Y=P)),r),I,k=we,D=()=>(k(),k=gt(s,P=>t(50,I=P)),s),T,y=we,N=()=>(y(),y=gt(f,P=>t(51,T=P)),f),q,X=we,Ce=()=>(X(),X=gt(l,P=>t(52,q=P)),l);i.$$.on_destroy.push(()=>H()),i.$$.on_destroy.push(()=>k()),i.$$.on_destroy.push(()=>y()),i.$$.on_destroy.push(()=>X());const{resolveColor:ye}=El();let{y:se=void 0}=e;const qe=!!se;let{y2:Ee=void 0}=e;const oe=!!Ee;let{series:Oe=void 0}=e;const j=!!Oe;let{options:We=void 0}=e,{name:ie=void 0}=e,{type:je="stacked"}=e,{stackName:pe=void 0}=e,{fillColor:Qe=void 0}=e,{fillOpacity:ve=void 0}=e,{outlineColor:Me=void 0}=e,{outlineWidth:ce=void 0}=e,{labels:re=!1}=e,{seriesLabels:L=!0}=e,{labelSize:z=11}=e,{labelPosition:Fe=void 0}=e,{labelColor:He=void 0}=e,{labelFmt:xe=void 0}=e,Pe;xe&&(Pe=Ot(xe));let{yLabelFmt:$e=void 0}=e,Ge;$e&&(Ge=Ot($e));let{y2LabelFmt:tt=void 0}=e,Ke;tt&&(Ke=Ot(tt));let{y2SeriesType:lt="bar"}=e,{stackTotalLabel:Je=!0}=e,{showAllLabels:nt=!1}=e,{seriesOrder:st=void 0}=e,et,it,B,de;const dt={outside:"top",inside:"inside"},ft={outside:"right",inside:"inside"};let{seriesLabelFmt:mt=void 0}=e;return Zs(()=>{We&&n.update(P=>({...P,...We})),F&&n.update(P=>{if(je.includes("stacked")?P.tooltip={...P.tooltip,order:"seriesDesc"}:P.tooltip={...P.tooltip,order:"seriesAsc"},je==="stacked100"&&(_?P.xAxis={...P.xAxis,max:1}:P.yAxis[0]={...P.yAxis[0],max:1}),_)P.yAxis={...P.yAxis,...F.xAxis},P.xAxis={...P.xAxis,...F.yAxis};else if(P.yAxis[0]={...P.yAxis[0],...F.yAxis},P.xAxis={...P.xAxis,...F.xAxis},Ee&&(P.yAxis[1]={...P.yAxis[1],show:!0},["line","bar","scatter"].includes(lt)))for(let Ne=0;Ne<m;Ne++)P.series[c+Ne].type=lt,P.series[c+Ne].stack=void 0;return P})}),i.$$set=P=>{"y"in P&&t(4,se=P.y),"y2"in P&&t(5,Ee=P.y2),"series"in P&&t(6,Oe=P.series),"options"in P&&t(13,We=P.options),"name"in P&&t(7,ie=P.name),"type"in P&&t(14,je=P.type),"stackName"in P&&t(8,pe=P.stackName),"fillColor"in P&&t(15,Qe=P.fillColor),"fillOpacity"in P&&t(16,ve=P.fillOpacity),"outlineColor"in P&&t(17,Me=P.outlineColor),"outlineWidth"in P&&t(18,ce=P.outlineWidth),"labels"in P&&t(9,re=P.labels),"seriesLabels"in P&&t(10,L=P.seriesLabels),"labelSize"in P&&t(19,z=P.labelSize),"labelPosition"in P&&t(11,Fe=P.labelPosition),"labelColor"in P&&t(20,He=P.labelColor),"labelFmt"in P&&t(21,xe=P.labelFmt),"yLabelFmt"in P&&t(22,$e=P.yLabelFmt),"y2LabelFmt"in P&&t(23,tt=P.y2LabelFmt),"y2SeriesType"in P&&t(24,lt=P.y2SeriesType),"stackTotalLabel"in P&&t(12,Je=P.stackTotalLabel),"showAllLabels"in P&&t(25,nt=P.showAllLabels),"seriesOrder"in P&&t(26,st=P.seriesOrder),"seriesLabelFmt"in P&&t(27,mt=P.seriesLabelFmt)},i.$$.update=()=>{i.$$.dirty[0]&32768&&D(t(2,s=ye(Qe))),i.$$.dirty[0]&131072&&ne(t(1,r=ye(Me))),i.$$.dirty[0]&512&&t(9,re=re==="true"||re===!0),i.$$.dirty[0]&1024&&t(10,L=L==="true"||L===!0),i.$$.dirty[0]&1048576&&N(t(0,f=ye(He))),i.$$.dirty[0]&4096&&t(12,Je=Je==="true"||Je===!0),i.$$.dirty[1]&2097152&&t(46,o=q.data),i.$$.dirty[1]&2097152&&t(42,a=q.x),i.$$.dirty[0]&16|i.$$.dirty[1]&2097152&&t(4,se=qe?se:q.y),i.$$.dirty[0]&32|i.$$.dirty[1]&2097152&&t(5,Ee=oe?Ee:q.y2),i.$$.dirty[1]&2097152&&t(40,u=q.yFormat),i.$$.dirty[1]&2097152&&t(47,d=q.y2Format),i.$$.dirty[1]&2097152&&t(35,c=q.yCount),i.$$.dirty[1]&2097152&&t(36,m=q.y2Count),i.$$.dirty[1]&2097152&&t(37,_=q.swapXY),i.$$.dirty[1]&2097152&&t(39,h=q.xType),i.$$.dirty[1]&2097152&&t(43,w=q.xMismatch),i.$$.dirty[1]&2097152&&t(44,E=q.columnSummary),i.$$.dirty[1]&2097152&&t(48,v=q.sort),i.$$.dirty[0]&64|i.$$.dirty[1]&2097152&&t(6,Oe=j?Oe:q.series),i.$$.dirty[0]&16848|i.$$.dirty[1]&174403&&(!Oe&&typeof se!="object"?(t(7,ie=ie??_t(se,E[se].title)),_&&h!=="category"&&(t(46,o=Fi(o,a,se,Oe,!0,h!=="time")),t(39,h="category")),t(8,pe="stack1"),t(33,B=_?"right":"top")):(v===!0&&h==="category"&&(t(31,et=gs(o,a,se)),typeof se=="object"?t(31,et=wl(et,"stackTotal",!1)):t(31,et=wl(et,se,!1)),t(32,it=et.map(P=>P[a])),t(46,o=[...o].sort(function(P,Ne){return it.indexOf(P[a])-it.indexOf(Ne[a])}))),_||(h==="value"||h==="category")&&je.includes("stacked")?(t(46,o=Fi(o,a,se,Oe,!0,h==="value")),t(39,h="category")):h==="time"&&je.includes("stacked")&&t(46,o=Fi(o,a,se,Oe,!0,!0)),je.includes("stacked")?(t(8,pe=pe??"stack1"),t(33,B="inside")):(t(8,pe=void 0),t(33,B=_?"right":"top")))),i.$$.dirty[0]&16400|i.$$.dirty[1]&34816&&je==="stacked"&&t(34,de=gs(o,a,se)),i.$$.dirty[0]&2048|i.$$.dirty[1]&68&&t(11,Fe=(_?ft[Fe]:dt[Fe])??B),i.$$.dirty[0]&1913458432|i.$$.dirty[1]&1901168&&t(45,M={type:"bar",stack:pe,label:{show:re&&L,formatter(P){return P.value[_?0:1]===0?"":Et(P.value[_?0:1],[Ge??Pe??u,Ke??Pe??d][Ws(P.componentIndex,c,m)])},position:Fe,fontSize:z,color:T},labelLayout:{hideOverlap:!nt},emphasis:{focus:"series"},barMaxWidth:Qu,itemStyle:{color:I,opacity:ve,borderColor:Y,borderWidth:ce}}),i.$$.dirty[0]&201326832|i.$$.dirty[1]&63552&&t(41,p=Ru(o,a,se,Oe,_,M,ie,w,E,st,void 0,void 0,Ee,mt)),i.$$.dirty[0]&268981072|i.$$.dirty[1]&7880&&n.update(P=>(P.series.push(...p),P.legend.data.push(...p.map(Ne=>Ne.name.toString())),re===!0&&je==="stacked"&&typeof se=="object"|Oe!==void 0&&Je===!0&&Oe!==a&&(P.series.push({type:"bar",stack:pe,name:"stackTotal",color:"none",data:de.map(Ne=>[_?0:w?Ne[a].toString():Ne[a],_?w?Ne[a].toString():Ne[a]:0]),label:{show:!0,position:_?"right":"top",formatter(Ne){let at=0;return p.forEach(wt=>{at+=wt.data[Ne.dataIndex][_?0:1]}),at===0?"":Et(at,Pe??u)},fontWeight:"bold",fontSize:z,padding:_?[0,0,0,5]:void 0}}),P.legend.selectedMode=!1),P)),i.$$.dirty[1]&256&&(F={xAxis:{boundaryGap:["1%","2%"],type:h}})},Ce(t(3,l=Sl(Is))),t(38,n=Sl(Ps)),[f,r,s,l,se,Ee,Oe,ie,pe,re,L,Fe,Je,We,je,Qe,ve,Me,ce,z,He,xe,$e,tt,lt,nt,st,mt,Pe,Ge,Ke,et,it,B,de,c,m,_,n,h,u,p,a,w,E,M,o,d,v,Y,I,T,q]}class Ju extends Ae{constructor(e){super(),Se(this,e,Ku,null,me,{y:4,y2:5,series:6,options:13,name:7,type:14,stackName:8,fillColor:15,fillOpacity:16,outlineColor:17,outlineWidth:18,labels:9,seriesLabels:10,labelSize:19,labelPosition:11,labelColor:20,labelFmt:21,yLabelFmt:22,y2LabelFmt:23,y2SeriesType:24,stackTotalLabel:12,showAllLabels:25,seriesOrder:26,seriesLabelFmt:27},null,[-1,-1])}}function Zu(i){let e,t,l;e=new Ju({props:{type:i[38],fillColor:i[72],fillOpacity:i[39],outlineColor:i[71],outlineWidth:i[40],labels:i[43],labelSize:i[44],labelPosition:i[45],labelColor:i[69],labelFmt:i[46],yLabelFmt:i[47],y2LabelFmt:i[48],stackTotalLabel:i[49],seriesLabels:i[50],showAllLabels:i[51],y2SeriesType:i[9],seriesOrder:i[60],seriesLabelFmt:i[62]}});const n=i[81].default,s=he(n,i,i[82],null);return{c(){$(e.$$.fragment),t=K(),s&&s.c()},l(r){x(e.$$.fragment,r),t=Q(r),s&&s.l(r)},m(r,f){Z(e,r,f),S(r,t,f),s&&s.m(r,f),l=!0},p(r,f){const o={};f[1]&128&&(o.type=r[38]),f[2]&1024&&(o.fillColor=r[72]),f[1]&256&&(o.fillOpacity=r[39]),f[2]&512&&(o.outlineColor=r[71]),f[1]&512&&(o.outlineWidth=r[40]),f[1]&4096&&(o.labels=r[43]),f[1]&8192&&(o.labelSize=r[44]),f[1]&16384&&(o.labelPosition=r[45]),f[2]&128&&(o.labelColor=r[69]),f[1]&32768&&(o.labelFmt=r[46]),f[1]&65536&&(o.yLabelFmt=r[47]),f[1]&131072&&(o.y2LabelFmt=r[48]),f[1]&262144&&(o.stackTotalLabel=r[49]),f[1]&524288&&(o.seriesLabels=r[50]),f[1]&1048576&&(o.showAllLabels=r[51]),f[0]&512&&(o.y2SeriesType=r[9]),f[1]&536870912&&(o.seriesOrder=r[60]),f[2]&1&&(o.seriesLabelFmt=r[62]),e.$set(o),s&&s.p&&(!l||f[2]&1048576)&&_e(s,n,r,r[82],l?be(n,r[82],f,null):ge(r[82]),null)},i(r){l||(b(e.$$.fragment,r),b(s,r),l=!0)},o(r){C(e.$$.fragment,r),C(s,r),l=!1},d(r){r&&g(t),J(e,r),s&&s.d(r)}}}function xu(i){let e,t;return e=new Gu({props:{data:i[1],x:i[2],y:i[3],y2:i[4],xFmt:i[12],yFmt:i[10],y2Fmt:i[11],series:i[5],xType:i[6],yLog:i[7],yLogBase:i[8],legend:i[15],xAxisTitle:i[16],yAxisTitle:i[17],y2AxisTitle:i[18],xGridlines:i[19],yGridlines:i[20],y2Gridlines:i[21],xAxisLabels:i[22],yAxisLabels:i[23],y2AxisLabels:i[24],xBaseline:i[25],yBaseline:i[26],y2Baseline:i[27],xTickMarks:i[28],yTickMarks:i[29],y2TickMarks:i[30],yAxisColor:i[68],y2AxisColor:i[67],yMin:i[31],yMax:i[32],yScale:i[33],y2Min:i[34],y2Max:i[35],y2Scale:i[36],swapXY:i[0],title:i[13],subtitle:i[14],chartType:"Bar Chart",stackType:i[38],sort:i[42],stacked100:i[73],chartAreaHeight:i[41],showAllXAxisLabels:i[37],colorPalette:i[70],echartsOptions:i[52],seriesOptions:i[53],printEchartsConfig:i[54],emptySet:i[55],emptyMessage:i[56],renderer:i[57],downloadableData:i[58],downloadableImage:i[59],connectGroup:i[61],xLabelWrap:i[65],seriesColors:i[66],leftPadding:i[63],rightPadding:i[64],$$slots:{default:[Zu]},$$scope:{ctx:i}}}),{c(){$(e.$$.fragment)},l(l){x(e.$$.fragment,l)},m(l,n){Z(e,l,n),t=!0},p(l,n){const s={};n[0]&2&&(s.data=l[1]),n[0]&4&&(s.x=l[2]),n[0]&8&&(s.y=l[3]),n[0]&16&&(s.y2=l[4]),n[0]&4096&&(s.xFmt=l[12]),n[0]&1024&&(s.yFmt=l[10]),n[0]&2048&&(s.y2Fmt=l[11]),n[0]&32&&(s.series=l[5]),n[0]&64&&(s.xType=l[6]),n[0]&128&&(s.yLog=l[7]),n[0]&256&&(s.yLogBase=l[8]),n[0]&32768&&(s.legend=l[15]),n[0]&65536&&(s.xAxisTitle=l[16]),n[0]&131072&&(s.yAxisTitle=l[17]),n[0]&262144&&(s.y2AxisTitle=l[18]),n[0]&524288&&(s.xGridlines=l[19]),n[0]&1048576&&(s.yGridlines=l[20]),n[0]&2097152&&(s.y2Gridlines=l[21]),n[0]&4194304&&(s.xAxisLabels=l[22]),n[0]&8388608&&(s.yAxisLabels=l[23]),n[0]&16777216&&(s.y2AxisLabels=l[24]),n[0]&33554432&&(s.xBaseline=l[25]),n[0]&67108864&&(s.yBaseline=l[26]),n[0]&134217728&&(s.y2Baseline=l[27]),n[0]&268435456&&(s.xTickMarks=l[28]),n[0]&536870912&&(s.yTickMarks=l[29]),n[0]&1073741824&&(s.y2TickMarks=l[30]),n[2]&64&&(s.yAxisColor=l[68]),n[2]&32&&(s.y2AxisColor=l[67]),n[1]&1&&(s.yMin=l[31]),n[1]&2&&(s.yMax=l[32]),n[1]&4&&(s.yScale=l[33]),n[1]&8&&(s.y2Min=l[34]),n[1]&16&&(s.y2Max=l[35]),n[1]&32&&(s.y2Scale=l[36]),n[0]&1&&(s.swapXY=l[0]),n[0]&8192&&(s.title=l[13]),n[0]&16384&&(s.subtitle=l[14]),n[1]&128&&(s.stackType=l[38]),n[1]&2048&&(s.sort=l[42]),n[1]&1024&&(s.chartAreaHeight=l[41]),n[1]&64&&(s.showAllXAxisLabels=l[37]),n[2]&256&&(s.colorPalette=l[70]),n[1]&2097152&&(s.echartsOptions=l[52]),n[1]&4194304&&(s.seriesOptions=l[53]),n[1]&8388608&&(s.printEchartsConfig=l[54]),n[1]&16777216&&(s.emptySet=l[55]),n[1]&33554432&&(s.emptyMessage=l[56]),n[1]&67108864&&(s.renderer=l[57]),n[1]&134217728&&(s.downloadableData=l[58]),n[1]&268435456&&(s.downloadableImage=l[59]),n[1]&1073741824&&(s.connectGroup=l[61]),n[2]&8&&(s.xLabelWrap=l[65]),n[2]&16&&(s.seriesColors=l[66]),n[2]&2&&(s.leftPadding=l[63]),n[2]&4&&(s.rightPadding=l[64]),n[0]&512|n[1]&538964864|n[2]&1050241&&(s.$$scope={dirty:n,ctx:l}),e.$set(s)},i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){C(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function $u(i,e,t){let l,n,s,r,f,o,a,{$$slots:u={},$$scope:d}=e;const{resolveColor:c,resolveColorsObject:m,resolveColorPalette:_}=El();let{data:h=void 0}=e,{x:w=void 0}=e,{y:E=void 0}=e,{y2:v=void 0}=e,{series:M=void 0}=e,{xType:p=void 0}=e,{yLog:F=void 0}=e,{yLogBase:Y=void 0}=e,{y2SeriesType:H=void 0}=e,{yFmt:ne=void 0}=e,{y2Fmt:I=void 0}=e,{xFmt:k=void 0}=e,{title:D=void 0}=e,{subtitle:T=void 0}=e,{legend:y=void 0}=e,{xAxisTitle:N=void 0}=e,{yAxisTitle:q=v?"true":void 0}=e,{y2AxisTitle:X=v?"true":void 0}=e,{xGridlines:Ce=void 0}=e,{yGridlines:ye=void 0}=e,{y2Gridlines:se=void 0}=e,{xAxisLabels:qe=void 0}=e,{yAxisLabels:Ee=void 0}=e,{y2AxisLabels:oe=void 0}=e,{xBaseline:Oe=void 0}=e,{yBaseline:j=void 0}=e,{y2Baseline:We=void 0}=e,{xTickMarks:ie=void 0}=e,{yTickMarks:je=void 0}=e,{y2TickMarks:pe=void 0}=e,{yMin:Qe=void 0}=e,{yMax:ve=void 0}=e,{yScale:Me=void 0}=e,{y2Min:ce=void 0}=e,{y2Max:re=void 0}=e,{y2Scale:L=void 0}=e,{swapXY:z=!1}=e,{showAllXAxisLabels:Fe}=e,{type:He="stacked"}=e,xe=He==="stacked100",{fillColor:Pe=void 0}=e,{fillOpacity:$e=void 0}=e,{outlineColor:Ge=void 0}=e,{outlineWidth:tt=void 0}=e,{chartAreaHeight:Ke=void 0}=e,{sort:lt=void 0}=e,{colorPalette:Je="default"}=e,{labels:nt=void 0}=e,{labelSize:st=void 0}=e,{labelPosition:et=void 0}=e,{labelColor:it=void 0}=e,{labelFmt:B=void 0}=e,{yLabelFmt:de=void 0}=e,{y2LabelFmt:dt=void 0}=e,{stackTotalLabel:ft=void 0}=e,{seriesLabels:mt=void 0}=e,{showAllLabels:P=void 0}=e,{yAxisColor:Ne=void 0}=e,{y2AxisColor:at=void 0}=e,{echartsOptions:wt=void 0}=e,{seriesOptions:Vt=void 0}=e,{printEchartsConfig:Lt=!1}=e,{emptySet:vt=void 0}=e,{emptyMessage:zt=void 0}=e,{renderer:Mt=void 0}=e,{downloadableData:Ht=void 0}=e,{downloadableImage:ut=void 0}=e,{seriesColors:qt=void 0}=e,{seriesOrder:ol=void 0}=e,{connectGroup:Gt=void 0}=e,{seriesLabelFmt:Rt=void 0}=e,{leftPadding:Dt=void 0}=e,{rightPadding:ct=void 0}=e,{xLabelWrap:fl=void 0}=e;return i.$$set=A=>{"data"in A&&t(1,h=A.data),"x"in A&&t(2,w=A.x),"y"in A&&t(3,E=A.y),"y2"in A&&t(4,v=A.y2),"series"in A&&t(5,M=A.series),"xType"in A&&t(6,p=A.xType),"yLog"in A&&t(7,F=A.yLog),"yLogBase"in A&&t(8,Y=A.yLogBase),"y2SeriesType"in A&&t(9,H=A.y2SeriesType),"yFmt"in A&&t(10,ne=A.yFmt),"y2Fmt"in A&&t(11,I=A.y2Fmt),"xFmt"in A&&t(12,k=A.xFmt),"title"in A&&t(13,D=A.title),"subtitle"in A&&t(14,T=A.subtitle),"legend"in A&&t(15,y=A.legend),"xAxisTitle"in A&&t(16,N=A.xAxisTitle),"yAxisTitle"in A&&t(17,q=A.yAxisTitle),"y2AxisTitle"in A&&t(18,X=A.y2AxisTitle),"xGridlines"in A&&t(19,Ce=A.xGridlines),"yGridlines"in A&&t(20,ye=A.yGridlines),"y2Gridlines"in A&&t(21,se=A.y2Gridlines),"xAxisLabels"in A&&t(22,qe=A.xAxisLabels),"yAxisLabels"in A&&t(23,Ee=A.yAxisLabels),"y2AxisLabels"in A&&t(24,oe=A.y2AxisLabels),"xBaseline"in A&&t(25,Oe=A.xBaseline),"yBaseline"in A&&t(26,j=A.yBaseline),"y2Baseline"in A&&t(27,We=A.y2Baseline),"xTickMarks"in A&&t(28,ie=A.xTickMarks),"yTickMarks"in A&&t(29,je=A.yTickMarks),"y2TickMarks"in A&&t(30,pe=A.y2TickMarks),"yMin"in A&&t(31,Qe=A.yMin),"yMax"in A&&t(32,ve=A.yMax),"yScale"in A&&t(33,Me=A.yScale),"y2Min"in A&&t(34,ce=A.y2Min),"y2Max"in A&&t(35,re=A.y2Max),"y2Scale"in A&&t(36,L=A.y2Scale),"swapXY"in A&&t(0,z=A.swapXY),"showAllXAxisLabels"in A&&t(37,Fe=A.showAllXAxisLabels),"type"in A&&t(38,He=A.type),"fillColor"in A&&t(74,Pe=A.fillColor),"fillOpacity"in A&&t(39,$e=A.fillOpacity),"outlineColor"in A&&t(75,Ge=A.outlineColor),"outlineWidth"in A&&t(40,tt=A.outlineWidth),"chartAreaHeight"in A&&t(41,Ke=A.chartAreaHeight),"sort"in A&&t(42,lt=A.sort),"colorPalette"in A&&t(76,Je=A.colorPalette),"labels"in A&&t(43,nt=A.labels),"labelSize"in A&&t(44,st=A.labelSize),"labelPosition"in A&&t(45,et=A.labelPosition),"labelColor"in A&&t(77,it=A.labelColor),"labelFmt"in A&&t(46,B=A.labelFmt),"yLabelFmt"in A&&t(47,de=A.yLabelFmt),"y2LabelFmt"in A&&t(48,dt=A.y2LabelFmt),"stackTotalLabel"in A&&t(49,ft=A.stackTotalLabel),"seriesLabels"in A&&t(50,mt=A.seriesLabels),"showAllLabels"in A&&t(51,P=A.showAllLabels),"yAxisColor"in A&&t(78,Ne=A.yAxisColor),"y2AxisColor"in A&&t(79,at=A.y2AxisColor),"echartsOptions"in A&&t(52,wt=A.echartsOptions),"seriesOptions"in A&&t(53,Vt=A.seriesOptions),"printEchartsConfig"in A&&t(54,Lt=A.printEchartsConfig),"emptySet"in A&&t(55,vt=A.emptySet),"emptyMessage"in A&&t(56,zt=A.emptyMessage),"renderer"in A&&t(57,Mt=A.renderer),"downloadableData"in A&&t(58,Ht=A.downloadableData),"downloadableImage"in A&&t(59,ut=A.downloadableImage),"seriesColors"in A&&t(80,qt=A.seriesColors),"seriesOrder"in A&&t(60,ol=A.seriesOrder),"connectGroup"in A&&t(61,Gt=A.connectGroup),"seriesLabelFmt"in A&&t(62,Rt=A.seriesLabelFmt),"leftPadding"in A&&t(63,Dt=A.leftPadding),"rightPadding"in A&&t(64,ct=A.rightPadding),"xLabelWrap"in A&&t(65,fl=A.xLabelWrap),"$$scope"in A&&t(82,d=A.$$scope)},i.$$.update=()=>{i.$$.dirty[0]&1&&(z==="true"||z===!0?t(0,z=!0):t(0,z=!1)),i.$$.dirty[2]&4096&&t(72,l=c(Pe)),i.$$.dirty[2]&8192&&t(71,n=c(Ge)),i.$$.dirty[2]&16384&&t(70,s=_(Je)),i.$$.dirty[2]&32768&&t(69,r=c(it)),i.$$.dirty[2]&65536&&t(68,f=c(Ne)),i.$$.dirty[2]&131072&&t(67,o=c(at)),i.$$.dirty[2]&262144&&t(66,a=m(qt))},[z,h,w,E,v,M,p,F,Y,H,ne,I,k,D,T,y,N,q,X,Ce,ye,se,qe,Ee,oe,Oe,j,We,ie,je,pe,Qe,ve,Me,ce,re,L,Fe,He,$e,tt,Ke,lt,nt,st,et,B,de,dt,ft,mt,P,wt,Vt,Lt,vt,zt,Mt,Ht,ut,ol,Gt,Rt,Dt,ct,fl,a,o,f,r,s,n,l,xe,Pe,Ge,Je,it,Ne,at,qt,u,d]}class ec extends Ae{constructor(e){super(),Se(this,e,$u,xu,me,{data:1,x:2,y:3,y2:4,series:5,xType:6,yLog:7,yLogBase:8,y2SeriesType:9,yFmt:10,y2Fmt:11,xFmt:12,title:13,subtitle:14,legend:15,xAxisTitle:16,yAxisTitle:17,y2AxisTitle:18,xGridlines:19,yGridlines:20,y2Gridlines:21,xAxisLabels:22,yAxisLabels:23,y2AxisLabels:24,xBaseline:25,yBaseline:26,y2Baseline:27,xTickMarks:28,yTickMarks:29,y2TickMarks:30,yMin:31,yMax:32,yScale:33,y2Min:34,y2Max:35,y2Scale:36,swapXY:0,showAllXAxisLabels:37,type:38,fillColor:74,fillOpacity:39,outlineColor:75,outlineWidth:40,chartAreaHeight:41,sort:42,colorPalette:76,labels:43,labelSize:44,labelPosition:45,labelColor:77,labelFmt:46,yLabelFmt:47,y2LabelFmt:48,stackTotalLabel:49,seriesLabels:50,showAllLabels:51,yAxisColor:78,y2AxisColor:79,echartsOptions:52,seriesOptions:53,printEchartsConfig:54,emptySet:55,emptyMessage:56,renderer:57,downloadableData:58,downloadableImage:59,seriesColors:80,seriesOrder:60,connectGroup:61,seriesLabelFmt:62,leftPadding:63,rightPadding:64,xLabelWrap:65},null,[-1,-1,-1])}}function tc(i){let e,t=Te.title+"",l;return{c(){e=R("h1"),l=Ye(t),this.h()},l(n){e=G(n,"H1",{class:!0});var s=le(e);l=Ue(s,t),s.forEach(g),this.h()},h(){V(e,"class","title")},m(n,s){S(n,e,s),ke(e,l)},p:we,d(n){n&&g(e)}}}function lc(i){return{c(){this.h()},l(e){this.h()},h(){document.title="Evidence"},m:we,p:we,d:we}}function ic(i){let e,t,l,n,s;return document.title=e=Te.title,{c(){t=K(),l=R("meta"),n=K(),s=R("meta"),this.h()},l(r){t=Q(r),l=G(r,"META",{property:!0,content:!0}),n=Q(r),s=G(r,"META",{name:!0,content:!0}),this.h()},h(){var r,f;V(l,"property","og:title"),V(l,"content",((r=Te.og)==null?void 0:r.title)??Te.title),V(s,"name","twitter:title"),V(s,"content",((f=Te.og)==null?void 0:f.title)??Te.title)},m(r,f){S(r,t,f),S(r,l,f),S(r,n,f),S(r,s,f)},p(r,f){f&0&&e!==(e=Te.title)&&(document.title=e)},d(r){r&&(g(t),g(l),g(n),g(s))}}}function nc(i){var s,r;let e,t,l=(Te.description||((s=Te.og)==null?void 0:s.description))&&sc(),n=((r=Te.og)==null?void 0:r.image)&&rc();return{c(){l&&l.c(),e=K(),n&&n.c(),t=te()},l(f){l&&l.l(f),e=Q(f),n&&n.l(f),t=te()},m(f,o){l&&l.m(f,o),S(f,e,o),n&&n.m(f,o),S(f,t,o)},p(f,o){var a,u;(Te.description||(a=Te.og)!=null&&a.description)&&l.p(f,o),(u=Te.og)!=null&&u.image&&n.p(f,o)},d(f){f&&(g(e),g(t)),l&&l.d(f),n&&n.d(f)}}}function sc(i){let e,t,l,n,s;return{c(){e=R("meta"),t=K(),l=R("meta"),n=K(),s=R("meta"),this.h()},l(r){e=G(r,"META",{name:!0,content:!0}),t=Q(r),l=G(r,"META",{property:!0,content:!0}),n=Q(r),s=G(r,"META",{name:!0,content:!0}),this.h()},h(){var r,f,o;V(e,"name","description"),V(e,"content",Te.description??((r=Te.og)==null?void 0:r.description)),V(l,"property","og:description"),V(l,"content",((f=Te.og)==null?void 0:f.description)??Te.description),V(s,"name","twitter:description"),V(s,"content",((o=Te.og)==null?void 0:o.description)??Te.description)},m(r,f){S(r,e,f),S(r,t,f),S(r,l,f),S(r,n,f),S(r,s,f)},p:we,d(r){r&&(g(e),g(t),g(l),g(n),g(s))}}}function rc(i){let e,t,l;return{c(){e=R("meta"),t=K(),l=R("meta"),this.h()},l(n){e=G(n,"META",{property:!0,content:!0}),t=Q(n),l=G(n,"META",{name:!0,content:!0}),this.h()},h(){var n,s;V(e,"property","og:image"),V(e,"content",mn((n=Te.og)==null?void 0:n.image)),V(l,"name","twitter:image"),V(l,"content",mn((s=Te.og)==null?void 0:s.image))},m(n,s){S(n,e,s),S(n,t,s),S(n,l,s)},p:we,d(n){n&&(g(e),g(t),g(l))}}}function oc(i){let e,t='<h3 class="svelte-styow2">🛠️ Development Mode</h3> <p>This is a preview of your Evidence dashboard. Deploy to Domo DDX to access real datasets.</p>';return{c(){e=R("div"),e.innerHTML=t,this.h()},l(l){e=G(l,"DIV",{class:!0,"data-svelte-h":!0}),Ct(e)!=="svelte-1b532x0"&&(e.innerHTML=t),this.h()},h(){V(e,"class","dev-banner svelte-styow2")},m(l,n){S(l,e,n)},d(l){l&&g(e)}}}function fc(i){let e,t='<h3 class="svelte-styow2">🚀 Running in Domo DDX Environment</h3> <p>This Evidence dashboard is connected to your Domo instance and ready to analyze your data!</p>';return{c(){e=R("div"),e.innerHTML=t,this.h()},l(l){e=G(l,"DIV",{class:!0,"data-svelte-h":!0}),Ct(e)!=="svelte-1uzwljv"&&(e.innerHTML=t),this.h()},h(){V(e,"class","domo-banner svelte-styow2")},m(l,n){S(l,e,n)},d(l){l&&g(e)}}}function ac(i){let e;return{c(){e=Ye("This example shows how Evidence creates interactive dashboards from SQL queries. Once you load your Domo datasets, you can create similar analyses with your own data.")},l(t){e=Ue(t,"This example shows how Evidence creates interactive dashboards from SQL queries. Once you load your Domo datasets, you can create similar analyses with your own data.")},m(t,l){S(t,e,l)},d(t){t&&g(e)}}}function bs(i){let e,t;return e=new Ns({props:{queryID:"categories",queryResult:i[1]}}),{c(){$(e.$$.fragment)},l(l){x(e.$$.fragment,l)},m(l,n){Z(e,l,n),t=!0},p(l,n){const s={};n&2&&(s.queryResult=l[1]),e.$set(s)},i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){C(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function uc(i){let e,t;return e=new ll({props:{value:"%",valueLabel:"All Categories"}}),{c(){$(e.$$.fragment)},l(l){x(e.$$.fragment,l)},m(l,n){Z(e,l,n),t=!0},p:we,i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){C(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function cc(i){let e,t,l,n,s,r,f,o;return e=new ll({props:{value:"%",valueLabel:"All Years"}}),l=new ll({props:{value:"2019"}}),s=new ll({props:{value:"2020"}}),f=new ll({props:{value:"2021"}}),{c(){$(e.$$.fragment),t=K(),$(l.$$.fragment),n=K(),$(s.$$.fragment),r=K(),$(f.$$.fragment)},l(a){x(e.$$.fragment,a),t=Q(a),x(l.$$.fragment,a),n=Q(a),x(s.$$.fragment,a),r=Q(a),x(f.$$.fragment,a)},m(a,u){Z(e,a,u),S(a,t,u),Z(l,a,u),S(a,n,u),Z(s,a,u),S(a,r,u),Z(f,a,u),o=!0},p:we,i(a){o||(b(e.$$.fragment,a),b(l.$$.fragment,a),b(s.$$.fragment,a),b(f.$$.fragment,a),o=!0)},o(a){C(e.$$.fragment,a),C(l.$$.fragment,a),C(s.$$.fragment,a),C(f.$$.fragment,a),o=!1},d(a){a&&(g(t),g(n),g(r)),J(e,a),J(l,a),J(s,a),J(f,a)}}}function ys(i){let e,t;return e=new Ns({props:{queryID:"orders_by_category",queryResult:i[2]}}),{c(){$(e.$$.fragment)},l(l){x(e.$$.fragment,l)},m(l,n){Z(e,l,n),t=!0},p(l,n){const s={};n&4&&(s.queryResult=l[2]),e.$set(s)},i(l){t||(b(e.$$.fragment,l),t=!0)},o(l){C(e.$$.fragment,l),t=!1},d(l){J(e,l)}}}function dc(i){let e,t,l,n,s,r,f='<a href="#evidence-dashboard-for-domo-ddx">Evidence Dashboard for Domo DDX</a>',o,a,u,d="Welcome to your Evidence dashboard! This application combines the power of Evidence's analytics framework with Domo's data platform, allowing you to create interactive dashboards and reports using your Domo datasets.",c,m,_='<a href="#quick-start">Quick Start</a>',h,w,E='<div class="quick-start-card svelte-styow2"><h4 class="svelte-styow2">📊 Load Domo Data</h4> <p class="svelte-styow2">Use the workflow picker to select and load Domo datasets into DuckDB for analysis.</p> <a href="/workflow" class="btn-primary svelte-styow2">Open Workflow Picker</a></div> <div class="quick-start-card svelte-styow2"><h4 class="svelte-styow2">📈 View Sample Analysis</h4> <p class="svelte-styow2">See how Evidence works with the sample data below.</p> <a href="#sample-analysis" class="btn-secondary svelte-styow2">View Sample</a></div> <div class="quick-start-card svelte-styow2"><h4 class="svelte-styow2">📚 Learn More</h4> <p class="svelte-styow2">Explore Evidence documentation and best practices.</p> <a href="https://docs.evidence.dev" target="_blank" class="btn-secondary svelte-styow2">Documentation</a></div>',v,M,p='<a href="#sample-analysis">Sample Analysis</a>',F,Y,H,ne,I,k,D,T,y,N,q,X,Ce,ye,se='<a href="#whats-next">What&#39;s Next?</a>',qe,Ee,oe='<div class="step svelte-styow2"><h4 class="svelte-styow2">1. Load Your Data</h4> <p class="svelte-styow2">Use the <a href="/workflow" class="svelte-styow2">workflow picker</a> to select and load Domo datasets into DuckDB</p></div> <div class="step svelte-styow2"><h4 class="svelte-styow2">2. Create Queries</h4> <p class="svelte-styow2">Write SQL queries against your loaded data using Evidence&#39;s query blocks</p></div> <div class="step svelte-styow2"><h4 class="svelte-styow2">3. Build Visualizations</h4> <p class="svelte-styow2">Use Evidence components like BarChart, LineChart, and DataTable to create interactive dashboards</p></div> <div class="step svelte-styow2"><h4 class="svelte-styow2">4. Deploy to Domo</h4> <p class="svelte-styow2">Package your Evidence app and deploy it to Domo DDX for your team to use</p></div>',Oe,j=typeof Te<"u"&&Te.title&&Te.hide_title!==!0&&tc();function We(L,z){return typeof Te<"u"&&Te.title?ic:lc}let je=We()(i),pe=typeof Te=="object"&&nc();function Qe(L,z){return L[3]?fc:oc}let ve=Qe(i),Me=ve(i);ne=new ou({props:{title:"How Evidence Works with Your Data",$$slots:{default:[ac]},$$scope:{ctx:i}}});let ce=i[1]&&bs(i);D=new fs({props:{data:i[1],name:"category",value:"category",$$slots:{default:[uc]},$$scope:{ctx:i}}}),y=new fs({props:{name:"year",$$slots:{default:[cc]},$$scope:{ctx:i}}});let re=i[2]&&ys(i);return X=new ec({props:{data:i[2],title:"Sales by Month, "+i[0].category.label,x:"month",y:"sales_usd",series:"category"}}),{c(){j&&j.c(),e=K(),je.c(),t=R("meta"),l=R("meta"),pe&&pe.c(),n=te(),s=K(),r=R("h1"),r.innerHTML=f,o=K(),Me.c(),a=K(),u=R("p"),u.textContent=d,c=K(),m=R("h2"),m.innerHTML=_,h=K(),w=R("div"),w.innerHTML=E,v=K(),M=R("h2"),M.innerHTML=p,F=K(),Y=R("div"),H=K(),$(ne.$$.fragment),I=K(),ce&&ce.c(),k=K(),$(D.$$.fragment),T=K(),$(y.$$.fragment),N=K(),re&&re.c(),q=K(),$(X.$$.fragment),Ce=K(),ye=R("h2"),ye.innerHTML=se,qe=K(),Ee=R("div"),Ee.innerHTML=oe,this.h()},l(L){j&&j.l(L),e=Q(L);const z=xs("svelte-2igo1p",document.head);je.l(z),t=G(z,"META",{name:!0,content:!0}),l=G(z,"META",{name:!0,content:!0}),pe&&pe.l(z),n=te(),z.forEach(g),s=Q(L),r=G(L,"H1",{class:!0,id:!0,"data-svelte-h":!0}),Ct(r)!=="svelte-dwxo2v"&&(r.innerHTML=f),o=Q(L),Me.l(L),a=Q(L),u=G(L,"P",{class:!0,"data-svelte-h":!0}),Ct(u)!=="svelte-17kjwqn"&&(u.textContent=d),c=Q(L),m=G(L,"H2",{class:!0,id:!0,"data-svelte-h":!0}),Ct(m)!=="svelte-e7fbvj"&&(m.innerHTML=_),h=Q(L),w=G(L,"DIV",{class:!0,"data-svelte-h":!0}),Ct(w)!=="svelte-1ynrehd"&&(w.innerHTML=E),v=Q(L),M=G(L,"H2",{class:!0,id:!0,"data-svelte-h":!0}),Ct(M)!=="svelte-1941s2m"&&(M.innerHTML=p),F=Q(L),Y=G(L,"DIV",{id:!0}),le(Y).forEach(g),H=Q(L),x(ne.$$.fragment,L),I=Q(L),ce&&ce.l(L),k=Q(L),x(D.$$.fragment,L),T=Q(L),x(y.$$.fragment,L),N=Q(L),re&&re.l(L),q=Q(L),x(X.$$.fragment,L),Ce=Q(L),ye=G(L,"H2",{class:!0,id:!0,"data-svelte-h":!0}),Ct(ye)!=="svelte-fy128a"&&(ye.innerHTML=se),qe=Q(L),Ee=G(L,"DIV",{class:!0,"data-svelte-h":!0}),Ct(Ee)!=="svelte-zkky8f"&&(Ee.innerHTML=oe),this.h()},h(){V(t,"name","twitter:card"),V(t,"content","summary_large_image"),V(l,"name","twitter:site"),V(l,"content","@evidence_dev"),V(r,"class","markdown"),V(r,"id","evidence-dashboard-for-domo-ddx"),V(u,"class","markdown"),V(m,"class","markdown"),V(m,"id","quick-start"),V(w,"class","quick-start-grid svelte-styow2"),V(M,"class","markdown"),V(M,"id","sample-analysis"),V(Y,"id","sample-analysis"),V(ye,"class","markdown"),V(ye,"id","whats-next"),V(Ee,"class","next-steps svelte-styow2")},m(L,z){j&&j.m(L,z),S(L,e,z),je.m(document.head,null),ke(document.head,t),ke(document.head,l),pe&&pe.m(document.head,null),ke(document.head,n),S(L,s,z),S(L,r,z),S(L,o,z),Me.m(L,z),S(L,a,z),S(L,u,z),S(L,c,z),S(L,m,z),S(L,h,z),S(L,w,z),S(L,v,z),S(L,M,z),S(L,F,z),S(L,Y,z),S(L,H,z),Z(ne,L,z),S(L,I,z),ce&&ce.m(L,z),S(L,k,z),Z(D,L,z),S(L,T,z),Z(y,L,z),S(L,N,z),re&&re.m(L,z),S(L,q,z),Z(X,L,z),S(L,Ce,z),S(L,ye,z),S(L,qe,z),S(L,Ee,z),Oe=!0},p(L,[z]){typeof Te<"u"&&Te.title&&Te.hide_title!==!0&&j.p(L,z),je.p(L,z),typeof Te=="object"&&pe.p(L,z),ve!==(ve=Qe(L))&&(Me.d(1),Me=ve(L),Me&&(Me.c(),Me.m(a.parentNode,a)));const Fe={};z&33554432&&(Fe.$$scope={dirty:z,ctx:L}),ne.$set(Fe),L[1]?ce?(ce.p(L,z),z&2&&b(ce,1)):(ce=bs(L),ce.c(),b(ce,1),ce.m(k.parentNode,k)):ce&&(fe(),C(ce,1,1,()=>{ce=null}),ae());const He={};z&2&&(He.data=L[1]),z&33554432&&(He.$$scope={dirty:z,ctx:L}),D.$set(He);const xe={};z&33554432&&(xe.$$scope={dirty:z,ctx:L}),y.$set(xe),L[2]?re?(re.p(L,z),z&4&&b(re,1)):(re=ys(L),re.c(),b(re,1),re.m(q.parentNode,q)):re&&(fe(),C(re,1,1,()=>{re=null}),ae());const Pe={};z&4&&(Pe.data=L[2]),z&1&&(Pe.title="Sales by Month, "+L[0].category.label),X.$set(Pe)},i(L){Oe||(b(ne.$$.fragment,L),b(ce),b(D.$$.fragment,L),b(y.$$.fragment,L),b(re),b(X.$$.fragment,L),Oe=!0)},o(L){C(ne.$$.fragment,L),C(ce),C(D.$$.fragment,L),C(y.$$.fragment,L),C(re),C(X.$$.fragment,L),Oe=!1},d(L){L&&(g(e),g(s),g(r),g(o),g(a),g(u),g(c),g(m),g(h),g(w),g(v),g(M),g(F),g(Y),g(H),g(I),g(k),g(T),g(N),g(q),g(Ce),g(ye),g(qe),g(Ee)),j&&j.d(L),je.d(L),g(t),g(l),pe&&pe.d(L),g(n),Me.d(L),J(ne,L),ce&&ce.d(L),J(D,L),J(y,L),re&&re.d(L),J(X,L)}}}const Te={title:"Evidence Dashboard for Domo"};function mc(i,e,t){let l,n;Be(i,Bs,I=>t(14,l=I)),Be(i,hn,I=>t(19,n=I));let{data:s}=e,{data:r={},customFormattingSettings:f,__db:o,inputs:a}=s;zi(hn,n="6666cd76f96956469e7be39d750cc7d9",n);let u=Wr(Jl(a));Ni(u.subscribe(I=>t(0,a=I))),Al(Yr,{getCustomFormats:()=>f.customFormats||[]});const d=(I,k)=>Qr(o.query,I,{query_name:k});Xr(d),l.params,rl(()=>!0);let c={initialData:void 0,initialError:void 0},m=Yl`select
      category
  from needful_things.orders
  group by category`,_=`select
      category
  from needful_things.orders
  group by category`;r.categories_data&&(r.categories_data instanceof Error?c.initialError=r.categories_data:c.initialData=r.categories_data,r.categories_columns&&(c.knownColumns=r.categories_columns));let h,w=!1;const E=Tl.createReactive({callback:I=>{t(1,h=I)},execFn:d},{id:"categories",...c});E(_,{noResolve:m,...c}),globalThis[Symbol.for("categories")]={get value(){return h}};let v={initialData:void 0,initialError:void 0},M=Yl`select
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${a.category.value}'
  and date_part('year', order_datetime) like '${a.year.value}'
  group by all
  order by sales_usd desc`,p=`select
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${a.category.value}'
  and date_part('year', order_datetime) like '${a.year.value}'
  group by all
  order by sales_usd desc`;r.orders_by_category_data&&(r.orders_by_category_data instanceof Error?v.initialError=r.orders_by_category_data:v.initialData=r.orders_by_category_data,r.orders_by_category_columns&&(v.knownColumns=r.orders_by_category_columns));let F,Y=!1;const H=Tl.createReactive({callback:I=>{t(2,F=I)},execFn:d},{id:"orders_by_category",...v});H(p,{noResolve:M,...v}),globalThis[Symbol.for("orders_by_category")]={get value(){return F}};let ne=!1;return typeof window<"u"&&(ne=typeof window.domo<"u"),i.$$set=I=>{"data"in I&&t(4,s=I.data)},i.$$.update=()=>{i.$$.dirty&16&&t(5,{data:r={},customFormattingSettings:f,__db:o}=s,r),i.$$.dirty&32&&Ur.set(Object.keys(r).length>0),i.$$.dirty&16384&&l.params,i.$$.dirty&960&&(m||!w?m||(E(_,{noResolve:m,...c}),t(9,w=!0)):E(_,{noResolve:m})),i.$$.dirty&1&&t(11,M=Yl`select
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${a.category.value}'
  and date_part('year', order_datetime) like '${a.year.value}'
  group by all
  order by sales_usd desc`),i.$$.dirty&1&&t(12,p=`select
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${a.category.value}'
  and date_part('year', order_datetime) like '${a.year.value}'
  group by all
  order by sales_usd desc`),i.$$.dirty&15360&&(M||!Y?M||(H(p,{noResolve:M,...v}),t(13,Y=!0)):H(p,{noResolve:M}))},t(7,m=Yl`select
      category
  from needful_things.orders
  group by category`),t(8,_=`select
      category
  from needful_things.orders
  group by category`),[a,h,F,ne,s,r,c,m,_,w,v,M,p,Y,l]}class vc extends Ae{constructor(e){super(),Se(this,e,mc,dc,me,{data:4})}}export{vc as component};
