import{s as kl,d as s,i as d,e as _,b as n,F as It,G as ht,k as v,H as wl,h as u,r as Tt,q as w,j as q,n as p,m,t as dl,I as Cl,J as bl,K as Tl,B as Ll,v as xe,x as Le,y as ge,L as gl,w as Be}from"../chunks/scheduler.C5eBzNnH.js";import{S as xl,i as Ml,d as kt,t as Ue,a as Oe,c as Dl,m as wt,b as Ct,e as bt,g as Hl}from"../chunks/index.BSd9q3aW.js";import{g as El,Q as Il}from"../chunks/QueryViewer.Ok3Ma9J7.js";import{h as qt,j as ql,k as Sl,Q as Ol,p as Rl,l as cl,m as ul,r as ml,o as Nl}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.CJe6lW-S.js";import{w as Pl}from"../chunks/entry.CjmEikbu.js";import{h as fl,p as Ul}from"../chunks/setTrackProxy.DjIbdjlZ.js";import{p as Vl}from"../chunks/stores.C41LEeiH.js";const{document:ae}=El;function vl(k,t,o){const a=k.slice();return a[22]=t[o],a}function Al(k){let t,o=b.title+"",a;return{c(){t=m("h1"),a=ge(o),this.h()},l(c){t=u(c,"H1",{class:!0});var f=q(t);a=Le(f,o),f.forEach(s),this.h()},h(){n(t,"class","title")},m(c,f){d(c,t,f),_(t,a)},p:xe,d(c){c&&s(t)}}}function Bl(k){return{c(){this.h()},l(t){this.h()},h(){ae.title="Evidence"},m:xe,p:xe,d:xe}}function Fl(k){let t,o,a,c,f;return ae.title=t=b.title,{c(){o=p(),a=m("meta"),c=p(),f=m("meta"),this.h()},l(l){o=v(l),a=u(l,"META",{property:!0,content:!0}),c=v(l),f=u(l,"META",{name:!0,content:!0}),this.h()},h(){var l,r;n(a,"property","og:title"),n(a,"content",((l=b.og)==null?void 0:l.title)??b.title),n(f,"name","twitter:title"),n(f,"content",((r=b.og)==null?void 0:r.title)??b.title)},m(l,r){d(l,o,r),d(l,a,r),d(l,c,r),d(l,f,r)},p(l,r){r&0&&t!==(t=b.title)&&(ae.title=t)},d(l){l&&(s(o),s(a),s(c),s(f))}}}function $l(k){var f,l;let t,o,a=(b.description||((f=b.og)==null?void 0:f.description))&&jl(),c=((l=b.og)==null?void 0:l.image)&&Gl();return{c(){a&&a.c(),t=p(),c&&c.c(),o=Tt()},l(r){a&&a.l(r),t=v(r),c&&c.l(r),o=Tt()},m(r,y){a&&a.m(r,y),d(r,t,y),c&&c.m(r,y),d(r,o,y)},p(r,y){var h,C;(b.description||(h=b.og)!=null&&h.description)&&a.p(r,y),(C=b.og)!=null&&C.image&&c.p(r,y)},d(r){r&&(s(t),s(o)),a&&a.d(r),c&&c.d(r)}}}function jl(k){let t,o,a,c,f;return{c(){t=m("meta"),o=p(),a=m("meta"),c=p(),f=m("meta"),this.h()},l(l){t=u(l,"META",{name:!0,content:!0}),o=v(l),a=u(l,"META",{property:!0,content:!0}),c=v(l),f=u(l,"META",{name:!0,content:!0}),this.h()},h(){var l,r,y;n(t,"name","description"),n(t,"content",b.description??((l=b.og)==null?void 0:l.description)),n(a,"property","og:description"),n(a,"content",((r=b.og)==null?void 0:r.description)??b.description),n(f,"name","twitter:description"),n(f,"content",((y=b.og)==null?void 0:y.description)??b.description)},m(l,r){d(l,t,r),d(l,o,r),d(l,a,r),d(l,c,r),d(l,f,r)},p:xe,d(l){l&&(s(t),s(o),s(a),s(c),s(f))}}}function Gl(k){let t,o,a;return{c(){t=m("meta"),o=p(),a=m("meta"),this.h()},l(c){t=u(c,"META",{property:!0,content:!0}),o=v(c),a=u(c,"META",{name:!0,content:!0}),this.h()},h(){var c,f;n(t,"property","og:image"),n(t,"content",cl((c=b.og)==null?void 0:c.image)),n(a,"name","twitter:image"),n(a,"content",cl((f=b.og)==null?void 0:f.image))},m(c,f){d(c,t,f),d(c,o,f),d(c,a,f)},p:xe,d(c){c&&(s(t),s(o),s(a))}}}function Ql(k){let t,o='<h4 class="svelte-10vyee7">🛠️ Development Mode</h4> <p>Running in development mode with mock Domo data.</p>';return{c(){t=m("div"),t.innerHTML=o,this.h()},l(a){t=u(a,"DIV",{class:!0,"data-svelte-h":!0}),w(t)!=="svelte-eca4iq"&&(t.innerHTML=o),this.h()},h(){n(t,"class","dev-info svelte-10vyee7")},m(a,c){d(a,t,c)},p:xe,d(a){a&&s(t)}}}function zl(k){let t,o,a="🔗 Connected to Domo",c,f,l,r="This Evidence app is running in your Domo environment with access to your datasets.",y=k[3]&&pl(k);return{c(){t=m("div"),o=m("h4"),o.textContent=a,c=p(),y&&y.c(),f=p(),l=m("p"),l.textContent=r,this.h()},l(h){t=u(h,"DIV",{class:!0});var C=q(t);o=u(C,"H4",{class:!0,"data-svelte-h":!0}),w(o)!=="svelte-xeif1v"&&(o.textContent=a),c=v(C),y&&y.l(C),f=v(C),l=u(C,"P",{"data-svelte-h":!0}),w(l)!=="svelte-fqc5um"&&(l.textContent=r),C.forEach(s),this.h()},h(){n(o,"class","svelte-10vyee7"),n(t,"class","domo-info svelte-10vyee7")},m(h,C){d(h,t,C),_(t,o),_(t,c),y&&y.m(t,null),_(t,f),_(t,l)},p(h,C){h[3]?y?y.p(h,C):(y=pl(h),y.c(),y.m(t,f)):y&&(y.d(1),y=null)},d(h){h&&s(t),y&&y.d()}}}function pl(k){let t,o,a=(k[3].displayName||k[3].name)+"",c,f;return{c(){t=m("p"),o=ge("Welcome, "),c=ge(a),f=ge("!")},l(l){t=u(l,"P",{});var r=q(t);o=Le(r,"Welcome, "),c=Le(r,a),f=Le(r,"!"),r.forEach(s)},m(l,r){d(l,t,r),_(t,o),_(t,c),_(t,f)},p(l,r){r&8&&a!==(a=(l[3].displayName||l[3].name)+"")&&Be(c,a)},d(l){l&&s(t)}}}function Wl(k){let t,o='<p class="svelte-10vyee7">No datasets loaded yet. Use the workflow picker above to load your first dataset.</p>';return{c(){t=m("div"),t.innerHTML=o,this.h()},l(a){t=u(a,"DIV",{class:!0,"data-svelte-h":!0}),w(t)!=="svelte-1io82co"&&(t.innerHTML=o),this.h()},h(){n(t,"class","no-datasets svelte-10vyee7")},m(a,c){d(a,t,c)},p:xe,d(a){a&&s(t)}}}function Yl(k){let t,o,a="Successfully Loaded Datasets",c,f=ul(k[1]),l=[];for(let r=0;r<f.length;r+=1)l[r]=_l(vl(k,f,r));return{c(){t=m("div"),o=m("h3"),o.textContent=a,c=p();for(let r=0;r<l.length;r+=1)l[r].c();this.h()},l(r){t=u(r,"DIV",{class:!0});var y=q(t);o=u(y,"H3",{"data-svelte-h":!0}),w(o)!=="svelte-1043zgz"&&(o.textContent=a),c=v(y);for(let h=0;h<l.length;h+=1)l[h].l(y);y.forEach(s),this.h()},h(){n(t,"class","loaded-datasets svelte-10vyee7")},m(r,y){d(r,t,y),_(t,o),_(t,c);for(let h=0;h<l.length;h+=1)l[h]&&l[h].m(t,null)},p(r,y){if(y&2){f=ul(r[1]);let h;for(h=0;h<f.length;h+=1){const C=vl(r,f,h);l[h]?l[h].p(C,y):(l[h]=_l(C),l[h].c(),l[h].m(t,null))}for(;h<l.length;h+=1)l[h].d(1);l.length=f.length}},d(r){r&&s(t),gl(l,r)}}}function _l(k){let t,o,a=k[22].datasetName+"",c,f,l,r,y="Table Name:",h,C=k[22].tableName+"",$,E,L,S,V="Rows:",I,N=k[22].rowCount.toLocaleString()+"",A,g,M,T,ne="Dataset ID:",P,O=k[22].datasetId+"",Me,De;return{c(){t=m("div"),o=m("h4"),c=ge(a),f=p(),l=m("p"),r=m("strong"),r.textContent=y,h=p(),$=ge(C),E=p(),L=m("p"),S=m("strong"),S.textContent=V,I=p(),A=ge(N),g=p(),M=m("p"),T=m("strong"),T.textContent=ne,P=p(),Me=ge(O),De=p(),this.h()},l(H){t=u(H,"DIV",{class:!0});var x=q(t);o=u(x,"H4",{class:!0});var Ve=q(o);c=Le(Ve,a),Ve.forEach(s),f=v(x),l=u(x,"P",{class:!0});var ie=q(l);r=u(ie,"STRONG",{"data-svelte-h":!0}),w(r)!=="svelte-1t5jaf3"&&(r.textContent=y),h=v(ie),$=Le(ie,C),ie.forEach(s),E=v(x),L=u(x,"P",{class:!0});var R=q(L);S=u(R,"STRONG",{"data-svelte-h":!0}),w(S)!=="svelte-wddeo5"&&(S.textContent=V),I=v(R),A=Le(R,N),R.forEach(s),g=v(x),M=u(x,"P",{class:!0});var He=q(M);T=u(He,"STRONG",{"data-svelte-h":!0}),w(T)!=="svelte-oldt31"&&(T.textContent=ne),P=v(He),Me=Le(He,O),He.forEach(s),De=v(x),x.forEach(s),this.h()},h(){n(o,"class","svelte-10vyee7"),n(l,"class","svelte-10vyee7"),n(L,"class","svelte-10vyee7"),n(M,"class","svelte-10vyee7"),n(t,"class","dataset-card svelte-10vyee7")},m(H,x){d(H,t,x),_(t,o),_(o,c),_(t,f),_(t,l),_(l,r),_(l,h),_(l,$),_(t,E),_(t,L),_(L,S),_(L,I),_(L,A),_(t,g),_(t,M),_(M,T),_(M,P),_(M,Me),_(t,De)},p(H,x){x&2&&a!==(a=H[22].datasetName+"")&&Be(c,a),x&2&&C!==(C=H[22].tableName+"")&&Be($,C),x&2&&N!==(N=H[22].rowCount.toLocaleString()+"")&&Be(A,N),x&2&&O!==(O=H[22].datasetId+"")&&Be(Me,O)},d(H){H&&s(t)}}}function yl(k){let t,o;return t=new Il({props:{queryID:"my_analysis",queryResult:k[0]}}),{c(){bt(t.$$.fragment)},l(a){Ct(t.$$.fragment,a)},m(a,c){wt(t,a,c),o=!0},p(a,c){const f={};c&1&&(f.queryResult=a[0]),t.$set(f)},i(a){o||(Oe(t.$$.fragment,a),o=!0)},o(a){Ue(t.$$.fragment,a),o=!1},d(a){kt(t,a)}}}function Xl(k){let t,o,a,c,f,l,r='<a href="#domo-dataset-to-duckdb-workflow">Domo Dataset to DuckDB Workflow</a>',y,h,C,$="This page allows you to select and load Domo datasets into DuckDB for analysis in Evidence.",E,L,S='<a href="#how-it-works">How it Works</a>',V,I,N='<li class="markdown"><strong class="markdown">Select a Dataset</strong>: Choose from available Domo datasets in your instance</li> <li class="markdown"><strong class="markdown">Preview Data</strong>: Review the dataset schema and sample data</li> <li class="markdown"><strong class="markdown">Configure Loading</strong>: Set table name and refresh mode</li> <li class="markdown"><strong class="markdown">Load into DuckDB</strong>: Import the data for use in Evidence queries</li>',A,g,M,T='<h3 class="svelte-10vyee7">Domo Dataset Workflow</h3> <p class="svelte-10vyee7">Select and load Domo datasets into DuckDB for analysis</p>',ne,P,O,Me="Select Dataset:",De,H,x,Ve="Choose a dataset...",ie,R,He='<h4>Dataset Information</h4> <div id="dataset-info" class="dataset-info svelte-10vyee7"></div> <h5>Schema</h5> <div id="schema-table" class="schema-table"></div> <div class="preview-actions svelte-10vyee7"><button id="preview-data-btn" class="btn btn-secondary svelte-10vyee7">Preview Data</button></div> <div id="data-preview" class="data-preview svelte-10vyee7" style="display: none;"></div>',Lt,j,Re,St="Loading Configuration",gt,oe,Ee,Ot='<label for="table-name" class="svelte-10vyee7">Table Name in DuckDB:</label> <input id="table-name" type="text" placeholder="Enter table name" class="svelte-10vyee7"/>',xt,re,de,Rt="Refresh Mode:",Mt,ce,G,Nt="Replace existing data",Q,Pt="Append to existing data",Dt,z,Ut='<button id="load-dataset-btn" class="btn btn-primary svelte-10vyee7">Load Dataset into DuckDB</button>',Ht,W,Vt='<div class="loading-spinner svelte-10vyee7"></div> <p id="loading-message" class="svelte-10vyee7">Loading...</p>',Fe,Y,At='<a href="#loaded-datasets">Loaded Datasets</a>',$e,Ne,X,Bt='<a href="#using-your-data">Using Your Data</a>',je,ue,Ft="Once you've loaded datasets, you can use them in Evidence pages with SQL queries:",Ge,Pe,K,$t='<a href="#example-queries">Example Queries</a>',Qe,me,jt="Here are some example queries you can run on your loaded datasets:",ze,fe,Gt='<strong class="markdown">Basic Data Exploration:</strong>',We,Ie,Ye,ve,Qt='<strong class="markdown">Time Series Analysis:</strong>',Xe,qe,Ke,pe,zt='<strong class="markdown">Category Analysis:</strong>',Je,Se,Ze,J,Wt='<a href="#next-steps">Next Steps</a>',et,_e,Yt='<li class="markdown"><strong class="markdown">Create Visualizations</strong>: Use Evidence components like <code class="markdown">&lt;BarChart&gt;</code>, <code class="markdown">&lt;LineChart&gt;</code>, etc.</li> <li class="markdown"><strong class="markdown">Add Interactivity</strong>: Use <code class="markdown">&lt;Dropdown&gt;</code> and other input components</li> <li class="markdown"><strong class="markdown">Build Dashboards</strong>: Create multiple pages with different analyses</li> <li class="markdown"><strong class="markdown">Deploy to Domo</strong>: Package your Evidence app for Domo DDX</li>',tt,Z,Xt='<a href="#troubleshooting">Troubleshooting</a>',lt,ee,Kt='<a href="#common-issues">Common Issues</a>',st,ye,Jt='<strong class="markdown">Dataset Not Loading:</strong>',at,he,Zt='<li class="markdown">Check your Domo permissions</li> <li class="markdown">Verify the dataset exists and is accessible</li> <li class="markdown">Try refreshing the page</li>',nt,ke,el='<strong class="markdown">Table Name Conflicts:</strong>',it,we,tl='<li class="markdown">Use unique table names</li> <li class="markdown">Choose &quot;Replace existing data&quot; to overwrite</li>',ot,Ce,ll='<strong class="markdown">Performance Issues:</strong>',rt,be,sl='<li class="markdown">Large datasets may take time to load</li> <li class="markdown">Consider filtering data in Domo before loading</li> <li class="markdown">Use appropriate data types for better performance</li>',dt,te,al='<a href="#getting-help">Getting Help</a>',ct,Te,nl='<li class="markdown">Check the browser console for error messages</li> <li class="markdown">Verify your Domo DDX environment is properly configured</li> <li class="markdown">Contact your Domo administrator for dataset access issues</li>',ut,le=typeof b<"u"&&b.title&&b.hide_title!==!0&&Al();function hl(e,i){return typeof b<"u"&&b.title?Fl:Bl}let Ae=hl()(k),se=typeof b=="object"&&$l();function il(e,i){return e[2]?zl:Ql}let mt=il(k),B=mt(k);function ol(e,i){return e[1].length>0?Yl:Wl}let ft=ol(k),F=ft(k),D=k[0]&&yl(k);return Ie=new qt({props:{source:`-- Get row count and basic stats
SELECT 
  COUNT(*) as total_rows,
  COUNT(DISTINCT column_name) as unique_values
FROM your_table_name;`,copyToClipboard:"true",language:"sql"}}),qe=new qt({props:{source:`-- Aggregate by date (if you have date columns)
SELECT 
  DATE_TRUNC('month', date_column) as month,
  SUM(numeric_column) as total
FROM your_table_name
GROUP BY month
ORDER BY month;`,copyToClipboard:"true",language:"sql"}}),Se=new qt({props:{source:`-- Group by categorical columns
SELECT 
  category_column,
  COUNT(*) as count,
  AVG(numeric_column) as average
FROM your_table_name
GROUP BY category_column
ORDER BY count DESC;`,copyToClipboard:"true",language:"sql"}}),{c(){le&&le.c(),t=p(),Ae.c(),o=m("meta"),a=m("meta"),se&&se.c(),c=Tt(),f=p(),l=m("h1"),l.innerHTML=r,y=p(),B.c(),h=p(),C=m("p"),C.textContent=$,E=p(),L=m("h2"),L.innerHTML=S,V=p(),I=m("ol"),I.innerHTML=N,A=p(),g=m("div"),M=m("div"),M.innerHTML=T,ne=p(),P=m("div"),O=m("label"),O.textContent=Me,De=p(),H=m("select"),x=m("option"),x.textContent=Ve,ie=p(),R=m("div"),R.innerHTML=He,Lt=p(),j=m("div"),Re=m("h4"),Re.textContent=St,gt=p(),oe=m("div"),Ee=m("div"),Ee.innerHTML=Ot,xt=p(),re=m("div"),de=m("label"),de.textContent=Rt,Mt=p(),ce=m("select"),G=m("option"),G.textContent=Nt,Q=m("option"),Q.textContent=Pt,Dt=p(),z=m("div"),z.innerHTML=Ut,Ht=p(),W=m("div"),W.innerHTML=Vt,Fe=p(),Y=m("h2"),Y.innerHTML=At,$e=p(),F.c(),Ne=p(),X=m("h2"),X.innerHTML=Bt,je=p(),ue=m("p"),ue.textContent=Ft,Ge=p(),D&&D.c(),Pe=p(),K=m("h3"),K.innerHTML=$t,Qe=p(),me=m("p"),me.textContent=jt,ze=p(),fe=m("p"),fe.innerHTML=Gt,We=p(),bt(Ie.$$.fragment),Ye=p(),ve=m("p"),ve.innerHTML=Qt,Xe=p(),bt(qe.$$.fragment),Ke=p(),pe=m("p"),pe.innerHTML=zt,Je=p(),bt(Se.$$.fragment),Ze=p(),J=m("h2"),J.innerHTML=Wt,et=p(),_e=m("ol"),_e.innerHTML=Yt,tt=p(),Z=m("h2"),Z.innerHTML=Xt,lt=p(),ee=m("h3"),ee.innerHTML=Kt,st=p(),ye=m("p"),ye.innerHTML=Jt,at=p(),he=m("ul"),he.innerHTML=Zt,nt=p(),ke=m("p"),ke.innerHTML=el,it=p(),we=m("ul"),we.innerHTML=tl,ot=p(),Ce=m("p"),Ce.innerHTML=ll,rt=p(),be=m("ul"),be.innerHTML=sl,dt=p(),te=m("h3"),te.innerHTML=al,ct=p(),Te=m("ul"),Te.innerHTML=nl,this.h()},l(e){le&&le.l(e),t=v(e);const i=wl("svelte-2igo1p",ae.head);Ae.l(i),o=u(i,"META",{name:!0,content:!0}),a=u(i,"META",{name:!0,content:!0}),se&&se.l(i),c=Tt(),i.forEach(s),f=v(e),l=u(e,"H1",{class:!0,id:!0,"data-svelte-h":!0}),w(l)!=="svelte-l4qjn8"&&(l.innerHTML=r),y=v(e),B.l(e),h=v(e),C=u(e,"P",{class:!0,"data-svelte-h":!0}),w(C)!=="svelte-1jvntq6"&&(C.textContent=$),E=v(e),L=u(e,"H2",{class:!0,id:!0,"data-svelte-h":!0}),w(L)!=="svelte-m9j39v"&&(L.innerHTML=S),V=v(e),I=u(e,"OL",{class:!0,"data-svelte-h":!0}),w(I)!=="svelte-i541gc"&&(I.innerHTML=N),A=v(e),g=u(e,"DIV",{id:!0,class:!0});var U=q(g);M=u(U,"DIV",{class:!0,"data-svelte-h":!0}),w(M)!=="svelte-1qqhjj8"&&(M.innerHTML=T),ne=v(U),P=u(U,"DIV",{class:!0});var vt=q(P);O=u(vt,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),w(O)!=="svelte-gxhz7z"&&(O.textContent=Me),De=v(vt),H=u(vt,"SELECT",{id:!0,class:!0});var rl=q(H);x=u(rl,"OPTION",{"data-svelte-h":!0}),w(x)!=="svelte-59d9xk"&&(x.textContent=Ve),rl.forEach(s),vt.forEach(s),ie=v(U),R=u(U,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),w(R)!=="svelte-1dp1fod"&&(R.innerHTML=He),Lt=v(U),j=u(U,"DIV",{id:!0,class:!0,style:!0});var pt=q(j);Re=u(pt,"H4",{"data-svelte-h":!0}),w(Re)!=="svelte-1foy07w"&&(Re.textContent=St),gt=v(pt),oe=u(pt,"DIV",{class:!0});var _t=q(oe);Ee=u(_t,"DIV",{class:!0,"data-svelte-h":!0}),w(Ee)!=="svelte-1m428t"&&(Ee.innerHTML=Ot),xt=v(_t),re=u(_t,"DIV",{class:!0});var yt=q(re);de=u(yt,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),w(de)!=="svelte-p1qydn"&&(de.textContent=Rt),Mt=v(yt),ce=u(yt,"SELECT",{id:!0,class:!0});var Et=q(ce);G=u(Et,"OPTION",{"data-svelte-h":!0}),w(G)!=="svelte-qvzdub"&&(G.textContent=Nt),Q=u(Et,"OPTION",{"data-svelte-h":!0}),w(Q)!=="svelte-idsvi6"&&(Q.textContent=Pt),Et.forEach(s),yt.forEach(s),_t.forEach(s),pt.forEach(s),Dt=v(U),z=u(U,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),w(z)!=="svelte-efrb90"&&(z.innerHTML=Ut),Ht=v(U),W=u(U,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),w(W)!=="svelte-mbg3er"&&(W.innerHTML=Vt),U.forEach(s),Fe=v(e),Y=u(e,"H2",{class:!0,id:!0,"data-svelte-h":!0}),w(Y)!=="svelte-1f0ly50"&&(Y.innerHTML=At),$e=v(e),F.l(e),Ne=v(e),X=u(e,"H2",{class:!0,id:!0,"data-svelte-h":!0}),w(X)!=="svelte-d20xgf"&&(X.innerHTML=Bt),je=v(e),ue=u(e,"P",{class:!0,"data-svelte-h":!0}),w(ue)!=="svelte-8qpe1e"&&(ue.textContent=Ft),Ge=v(e),D&&D.l(e),Pe=v(e),K=u(e,"H3",{class:!0,id:!0,"data-svelte-h":!0}),w(K)!=="svelte-1y9nphi"&&(K.innerHTML=$t),Qe=v(e),me=u(e,"P",{class:!0,"data-svelte-h":!0}),w(me)!=="svelte-1jqarqq"&&(me.textContent=jt),ze=v(e),fe=u(e,"P",{class:!0,"data-svelte-h":!0}),w(fe)!=="svelte-fi6le8"&&(fe.innerHTML=Gt),We=v(e),Ct(Ie.$$.fragment,e),Ye=v(e),ve=u(e,"P",{class:!0,"data-svelte-h":!0}),w(ve)!=="svelte-8raaax"&&(ve.innerHTML=Qt),Xe=v(e),Ct(qe.$$.fragment,e),Ke=v(e),pe=u(e,"P",{class:!0,"data-svelte-h":!0}),w(pe)!=="svelte-1m0hm5v"&&(pe.innerHTML=zt),Je=v(e),Ct(Se.$$.fragment,e),Ze=v(e),J=u(e,"H2",{class:!0,id:!0,"data-svelte-h":!0}),w(J)!=="svelte-b2v2p6"&&(J.innerHTML=Wt),et=v(e),_e=u(e,"OL",{class:!0,"data-svelte-h":!0}),w(_e)!=="svelte-1h5tzx0"&&(_e.innerHTML=Yt),tt=v(e),Z=u(e,"H2",{class:!0,id:!0,"data-svelte-h":!0}),w(Z)!=="svelte-1uk16ny"&&(Z.innerHTML=Xt),lt=v(e),ee=u(e,"H3",{class:!0,id:!0,"data-svelte-h":!0}),w(ee)!=="svelte-1iqe0ip"&&(ee.innerHTML=Kt),st=v(e),ye=u(e,"P",{class:!0,"data-svelte-h":!0}),w(ye)!=="svelte-1syr9k0"&&(ye.innerHTML=Jt),at=v(e),he=u(e,"UL",{class:!0,"data-svelte-h":!0}),w(he)!=="svelte-1nuau76"&&(he.innerHTML=Zt),nt=v(e),ke=u(e,"P",{class:!0,"data-svelte-h":!0}),w(ke)!=="svelte-1eb0x8p"&&(ke.innerHTML=el),it=v(e),we=u(e,"UL",{class:!0,"data-svelte-h":!0}),w(we)!=="svelte-1fulwkk"&&(we.innerHTML=tl),ot=v(e),Ce=u(e,"P",{class:!0,"data-svelte-h":!0}),w(Ce)!=="svelte-ta14c3"&&(Ce.innerHTML=ll),rt=v(e),be=u(e,"UL",{class:!0,"data-svelte-h":!0}),w(be)!=="svelte-1sdbkgn"&&(be.innerHTML=sl),dt=v(e),te=u(e,"H3",{class:!0,id:!0,"data-svelte-h":!0}),w(te)!=="svelte-1f92uqx"&&(te.innerHTML=al),ct=v(e),Te=u(e,"UL",{class:!0,"data-svelte-h":!0}),w(Te)!=="svelte-1t69tq3"&&(Te.innerHTML=nl),this.h()},h(){n(o,"name","twitter:card"),n(o,"content","summary_large_image"),n(a,"name","twitter:site"),n(a,"content","@evidence_dev"),n(l,"class","markdown"),n(l,"id","domo-dataset-to-duckdb-workflow"),n(C,"class","markdown"),n(L,"class","markdown"),n(L,"id","how-it-works"),n(I,"class","markdown"),n(M,"class","picker-header svelte-10vyee7"),n(O,"for","dataset-select"),n(O,"class","svelte-10vyee7"),x.__value="",It(x,x.__value),n(H,"id","dataset-select"),n(H,"class","dataset-dropdown svelte-10vyee7"),n(P,"class","workflow-step svelte-10vyee7"),n(R,"id","dataset-preview"),n(R,"class","dataset-preview svelte-10vyee7"),ht(R,"display","none"),n(Ee,"class","config-item"),n(de,"for","refresh-mode"),n(de,"class","svelte-10vyee7"),G.__value="replace",It(G,G.__value),Q.__value="append",It(Q,Q.__value),n(ce,"id","refresh-mode"),n(ce,"class","svelte-10vyee7"),n(re,"class","config-item"),n(oe,"class","config-grid svelte-10vyee7"),n(j,"id","loading-config"),n(j,"class","workflow-step svelte-10vyee7"),ht(j,"display","none"),n(z,"id","workflow-actions"),n(z,"class","workflow-actions svelte-10vyee7"),ht(z,"display","none"),n(W,"id","loading-overlay"),n(W,"class","loading-overlay svelte-10vyee7"),ht(W,"display","none"),n(g,"id","domo-workflow-picker"),n(g,"class","workflow-picker svelte-10vyee7"),n(Y,"class","markdown"),n(Y,"id","loaded-datasets"),n(X,"class","markdown"),n(X,"id","using-your-data"),n(ue,"class","markdown"),n(K,"class","markdown"),n(K,"id","example-queries"),n(me,"class","markdown"),n(fe,"class","markdown"),n(ve,"class","markdown"),n(pe,"class","markdown"),n(J,"class","markdown"),n(J,"id","next-steps"),n(_e,"class","markdown"),n(Z,"class","markdown"),n(Z,"id","troubleshooting"),n(ee,"class","markdown"),n(ee,"id","common-issues"),n(ye,"class","markdown"),n(he,"class","markdown"),n(ke,"class","markdown"),n(we,"class","markdown"),n(Ce,"class","markdown"),n(be,"class","markdown"),n(te,"class","markdown"),n(te,"id","getting-help"),n(Te,"class","markdown")},m(e,i){le&&le.m(e,i),d(e,t,i),Ae.m(ae.head,null),_(ae.head,o),_(ae.head,a),se&&se.m(ae.head,null),_(ae.head,c),d(e,f,i),d(e,l,i),d(e,y,i),B.m(e,i),d(e,h,i),d(e,C,i),d(e,E,i),d(e,L,i),d(e,V,i),d(e,I,i),d(e,A,i),d(e,g,i),_(g,M),_(g,ne),_(g,P),_(P,O),_(P,De),_(P,H),_(H,x),_(g,ie),_(g,R),_(g,Lt),_(g,j),_(j,Re),_(j,gt),_(j,oe),_(oe,Ee),_(oe,xt),_(oe,re),_(re,de),_(re,Mt),_(re,ce),_(ce,G),_(ce,Q),_(g,Dt),_(g,z),_(g,Ht),_(g,W),d(e,Fe,i),d(e,Y,i),d(e,$e,i),F.m(e,i),d(e,Ne,i),d(e,X,i),d(e,je,i),d(e,ue,i),d(e,Ge,i),D&&D.m(e,i),d(e,Pe,i),d(e,K,i),d(e,Qe,i),d(e,me,i),d(e,ze,i),d(e,fe,i),d(e,We,i),wt(Ie,e,i),d(e,Ye,i),d(e,ve,i),d(e,Xe,i),wt(qe,e,i),d(e,Ke,i),d(e,pe,i),d(e,Je,i),wt(Se,e,i),d(e,Ze,i),d(e,J,i),d(e,et,i),d(e,_e,i),d(e,tt,i),d(e,Z,i),d(e,lt,i),d(e,ee,i),d(e,st,i),d(e,ye,i),d(e,at,i),d(e,he,i),d(e,nt,i),d(e,ke,i),d(e,it,i),d(e,we,i),d(e,ot,i),d(e,Ce,i),d(e,rt,i),d(e,be,i),d(e,dt,i),d(e,te,i),d(e,ct,i),d(e,Te,i),ut=!0},p(e,[i]){typeof b<"u"&&b.title&&b.hide_title!==!0&&le.p(e,i),Ae.p(e,i),typeof b=="object"&&se.p(e,i),mt===(mt=il(e))&&B?B.p(e,i):(B.d(1),B=mt(e),B&&(B.c(),B.m(h.parentNode,h))),ft===(ft=ol(e))&&F?F.p(e,i):(F.d(1),F=ft(e),F&&(F.c(),F.m(Ne.parentNode,Ne))),e[0]?D?(D.p(e,i),i&1&&Oe(D,1)):(D=yl(e),D.c(),Oe(D,1),D.m(Pe.parentNode,Pe)):D&&(Hl(),Ue(D,1,1,()=>{D=null}),Dl())},i(e){ut||(Oe(D),Oe(Ie.$$.fragment,e),Oe(qe.$$.fragment,e),Oe(Se.$$.fragment,e),ut=!0)},o(e){Ue(D),Ue(Ie.$$.fragment,e),Ue(qe.$$.fragment,e),Ue(Se.$$.fragment,e),ut=!1},d(e){e&&(s(t),s(f),s(l),s(y),s(h),s(C),s(E),s(L),s(V),s(I),s(A),s(g),s(Fe),s(Y),s($e),s(Ne),s(X),s(je),s(ue),s(Ge),s(Pe),s(K),s(Qe),s(me),s(ze),s(fe),s(We),s(Ye),s(ve),s(Xe),s(Ke),s(pe),s(Je),s(Ze),s(J),s(et),s(_e),s(tt),s(Z),s(lt),s(ee),s(st),s(ye),s(at),s(he),s(nt),s(ke),s(it),s(we),s(ot),s(Ce),s(rt),s(be),s(dt),s(te),s(ct),s(Te)),le&&le.d(e),Ae.d(e),s(o),s(a),se&&se.d(e),s(c),B.d(e),F.d(e),D&&D.d(e),kt(Ie,e),kt(qe,e),kt(Se,e)}}}const b={title:"Domo Dataset Workflow"};function Kl(k,t,o){let a,c;dl(k,Vl,T=>o(10,a=T)),dl(k,ml,T=>o(16,c=T));let{data:f}=t,{data:l={},customFormattingSettings:r,__db:y,inputs:h}=f;Cl(ml,c="fa3a9ba979503cc33be1e8689543296c",c);let C=ql(Pl(h));bl(C.subscribe(T=>h=T)),Tl(Nl,{getCustomFormats:()=>r.customFormats||[]});const $=(T,ne)=>Ul(y.query,T,{query_name:ne});Sl($),a.params,Ll(()=>!0);let E={initialData:void 0,initialError:void 0},L=fl`SELECT * FROM your_table_name LIMIT 10`,S="SELECT * FROM your_table_name LIMIT 10";l.my_analysis_data&&(l.my_analysis_data instanceof Error?E.initialError=l.my_analysis_data:E.initialData=l.my_analysis_data,l.my_analysis_columns&&(E.knownColumns=l.my_analysis_columns));let V,I=!1;const N=Ol.createReactive({callback:T=>{o(0,V=T)},execFn:$},{id:"my_analysis",...E});N(S,{noResolve:L,...E}),globalThis[Symbol.for("my_analysis")]={get value(){return V}};let A=[],g=!1,M=null;return typeof window<"u"&&(g=typeof window.domo<"u",g&&window.domo&&window.domo.get&&window.domo.get("/domo/users/v1/me").then(T=>{o(3,M=T),console.log("Domo user:",M)}).catch(T=>{console.warn("Could not get Domo user info:",T)})),typeof window<"u"&&window.addEventListener("DOMContentLoaded",function(){const T=document.createElement("script");T.src="/static/domo-duckdb-integration.js",T.onload=function(){console.log("Domo integration script loaded")},document.head.appendChild(T)}),k.$$set=T=>{"data"in T&&o(4,f=T.data)},k.$$.update=()=>{k.$$.dirty&16&&o(5,{data:l={},customFormattingSettings:r,__db:y}=f,l),k.$$.dirty&32&&Rl.set(Object.keys(l).length>0),k.$$.dirty&1024&&a.params,k.$$.dirty&960&&(L||!I?L||(N(S,{noResolve:L,...E}),o(9,I=!0)):N(S,{noResolve:L}))},o(7,L=fl`SELECT * FROM your_table_name LIMIT 10`),o(8,S="SELECT * FROM your_table_name LIMIT 10"),[V,A,g,M,f,l,E,L,S,I,a]}class is extends xl{constructor(t){super(),Ml(this,t,Kl,Xl,kl,{data:4})}}export{is as component};
