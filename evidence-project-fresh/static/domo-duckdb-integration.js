/**
 * Domo DDX to DuckDB Integration Module
 * Handles loading Domo datasets into DuckDB for Evidence analysis
 */

class DomoDuckDBIntegration {
    constructor() {
        this.duckdb = null;
        this.connection = null;
        this.availableDatasets = [];
        this.isInitialized = false;
        
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }

    async init() {
        try {
            await this.initializeDuckDB();
            await this.loadAvailableDatasets();
            this.setupEventListeners();
            this.isInitialized = true;
            console.log('Domo-DuckDB integration initialized successfully');
        } catch (error) {
            console.error('Failed to initialize Domo-DuckDB integration:', error);
            this.showError('Failed to initialize data integration. Please refresh the page.');
        }
    }

    async initializeDuckDB() {
        try {
            // Check if we're in a Domo environment or development
            if (typeof window.domo !== 'undefined') {
                // We're in Domo DDX environment
                console.log('Running in Domo DDX environment');
            } else {
                console.log('Running in development environment');
            }

            // For now, we'll simulate DuckDB connection
            // In a real implementation, you'd use @duckdb/duckdb-wasm
            this.duckdb = {
                query: async (sql) => {
                    console.log('Executing SQL:', sql);
                    return { rows: [], columns: [] };
                }
            };
            
            console.log('DuckDB initialized');
        } catch (error) {
            throw new Error(`DuckDB initialization failed: ${error.message}`);
        }
    }

    async loadAvailableDatasets() {
        try {
            // Check if we're in Domo environment
            if (typeof window.domo !== 'undefined' && window.domo.get) {
                // Use Domo API to get available datasets
                const datasets = await window.domo.get('/data/v1/datasets');
                this.availableDatasets = datasets.map(dataset => ({
                    id: dataset.id,
                    name: dataset.name,
                    description: dataset.description,
                    schema: dataset.schema,
                    rowCount: dataset.rowCount,
                    lastUpdated: dataset.lastUpdated
                }));
            } else {
                // Mock data for development
                this.availableDatasets = [
                    {
                        id: 'mock-sales-data',
                        name: 'Sales Data',
                        description: 'Monthly sales data by region and product',
                        schema: [
                            { name: 'date', type: 'DATE' },
                            { name: 'region', type: 'STRING' },
                            { name: 'product', type: 'STRING' },
                            { name: 'sales', type: 'DECIMAL' }
                        ],
                        rowCount: 1250,
                        lastUpdated: '2024-01-15'
                    },
                    {
                        id: 'mock-customer-data',
                        name: 'Customer Data',
                        description: 'Customer demographics and behavior',
                        schema: [
                            { name: 'customer_id', type: 'STRING' },
                            { name: 'age', type: 'LONG' },
                            { name: 'segment', type: 'STRING' },
                            { name: 'lifetime_value', type: 'DECIMAL' }
                        ],
                        rowCount: 5000,
                        lastUpdated: '2024-01-14'
                    },
                    {
                        id: 'mock-inventory-data',
                        name: 'Inventory Data',
                        description: 'Product inventory levels and costs',
                        schema: [
                            { name: 'product_id', type: 'STRING' },
                            { name: 'quantity', type: 'LONG' },
                            { name: 'cost', type: 'DECIMAL' },
                            { name: 'warehouse', type: 'STRING' }
                        ],
                        rowCount: 800,
                        lastUpdated: '2024-01-16'
                    }
                ];
            }

            this.populateDatasetDropdown();
        } catch (error) {
            console.error('Failed to load available datasets:', error);
            this.showError('Failed to load available datasets. Please check your Domo connection.');
        }
    }

    populateDatasetDropdown() {
        const selector = document.getElementById('dataset-selector');
        if (!selector) return;

        // Clear existing options except the first one
        selector.innerHTML = '<option value="">Select a dataset...</option>';

        this.availableDatasets.forEach(dataset => {
            const option = document.createElement('option');
            option.value = dataset.id;
            option.textContent = `${dataset.name} (${dataset.rowCount} rows)`;
            option.dataset.description = dataset.description;
            selector.appendChild(option);
        });
    }

    setupEventListeners() {
        const selector = document.getElementById('dataset-selector');
        const previewBtn = document.getElementById('preview-btn');
        const loadBtn = document.getElementById('load-dataset-btn');
        const tableNameInput = document.getElementById('table-name');

        if (selector) {
            selector.addEventListener('change', (e) => {
                const selectedId = e.target.value;
                if (selectedId) {
                    this.onDatasetSelected(selectedId);
                    if (previewBtn) previewBtn.disabled = false;
                    if (loadBtn) loadBtn.disabled = false;
                    
                    // Auto-populate table name
                    const dataset = this.availableDatasets.find(d => d.id === selectedId);
                    if (dataset && tableNameInput) {
                        tableNameInput.value = dataset.name.toLowerCase().replace(/\s+/g, '_');
                    }
                } else {
                    if (previewBtn) previewBtn.disabled = true;
                    if (loadBtn) loadBtn.disabled = true;
                    this.hideDatasetPreview();
                }
            });
        }

        if (previewBtn) {
            previewBtn.addEventListener('click', () => this.previewDataset());
        }

        if (loadBtn) {
            loadBtn.addEventListener('click', () => this.loadDatasetIntoDuckDB());
        }
    }

    onDatasetSelected(datasetId) {
        const dataset = this.availableDatasets.find(d => d.id === datasetId);
        if (dataset) {
            this.showDatasetPreview(dataset);
        }
    }

    showDatasetPreview(dataset) {
        const previewContainer = document.getElementById('dataset-preview');
        const previewContent = document.getElementById('preview-content');
        
        if (!previewContainer || !previewContent) return;

        const schemaTable = dataset.schema.map(col => 
            `<tr><td>${col.name}</td><td>${col.type}</td></tr>`
        ).join('');

        previewContent.innerHTML = `
            <div class="dataset-info">
                <h5>${dataset.name}</h5>
                <p><strong>Description:</strong> ${dataset.description}</p>
                <p><strong>Rows:</strong> ${dataset.rowCount.toLocaleString()}</p>
                <p><strong>Last Updated:</strong> ${dataset.lastUpdated}</p>
                
                <h6>Schema:</h6>
                <table class="schema-table">
                    <thead>
                        <tr><th>Column</th><th>Type</th></tr>
                    </thead>
                    <tbody>
                        ${schemaTable}
                    </tbody>
                </table>
            </div>
        `;

        previewContainer.style.display = 'block';
    }

    hideDatasetPreview() {
        const previewContainer = document.getElementById('dataset-preview');
        if (previewContainer) {
            previewContainer.style.display = 'none';
        }
    }

    async previewDataset() {
        const selectedId = document.getElementById('dataset-selector').value;
        if (!selectedId) return;

        try {
            this.showLoading('Loading preview...');
            
            // In a real implementation, you'd fetch sample data from Domo
            const sampleData = await this.fetchSampleData(selectedId);
            this.showDataPreview(sampleData);
            
        } catch (error) {
            console.error('Preview failed:', error);
            this.showError('Failed to preview dataset');
        } finally {
            this.hideLoading();
        }
    }

    async loadDatasetIntoDuckDB() {
        const selectedId = document.getElementById('dataset-selector').value;
        const tableName = document.getElementById('table-name').value;
        const refreshMode = document.getElementById('refresh-mode').value;

        if (!selectedId || !tableName) {
            this.showError('Please select a dataset and provide a table name');
            return;
        }

        try {
            this.showLoading('Loading dataset into DuckDB...');
            
            // Fetch the full dataset from Domo
            const dataset = await this.fetchDatasetData(selectedId);
            
            // Load into DuckDB
            await this.createDuckDBTable(tableName, dataset, refreshMode);
            
            this.showSuccess(`Dataset loaded successfully into table: ${tableName}`);
            
            // Optionally refresh the Evidence page to show new data
            setTimeout(() => {
                window.location.reload();
            }, 2000);
            
        } catch (error) {
            console.error('Load failed:', error);
            this.showError(`Failed to load dataset: ${error.message}`);
        } finally {
            this.hideLoading();
        }
    }

    async fetchSampleData(datasetId) {
        // Mock implementation - replace with actual Domo API call
        return {
            columns: ['date', 'region', 'sales'],
            rows: [
                ['2024-01-01', 'North', 1000],
                ['2024-01-01', 'South', 1200],
                ['2024-01-02', 'North', 1100],
                ['2024-01-02', 'South', 1300]
            ]
        };
    }

    async fetchDatasetData(datasetId) {
        if (typeof window.domo !== 'undefined' && window.domo.get) {
            // Use Domo API to fetch dataset data
            return await window.domo.get(`/data/v1/datasets/${datasetId}/data`);
        } else {
            // Mock data for development
            return this.fetchSampleData(datasetId);
        }
    }

    async createDuckDBTable(tableName, dataset, refreshMode) {
        // In a real implementation, this would use DuckDB-WASM
        console.log(`Creating table ${tableName} with mode ${refreshMode}`);
        console.log('Dataset:', dataset);
        
        // Mock implementation
        return Promise.resolve();
    }

    showLoading(message) {
        const loadingStatus = document.getElementById('loading-status');
        if (loadingStatus) {
            loadingStatus.querySelector('p').textContent = message;
            loadingStatus.style.display = 'block';
        }
    }

    hideLoading() {
        const loadingStatus = document.getElementById('loading-status');
        if (loadingStatus) {
            loadingStatus.style.display = 'none';
        }
    }

    showError(message) {
        alert(`Error: ${message}`);
    }

    showSuccess(message) {
        alert(`Success: ${message}`);
    }

    showDataPreview(data) {
        // Implementation for showing data preview
        console.log('Data preview:', data);
    }
}

// Initialize the integration when the script loads
window.domoDuckDBIntegration = new DomoDuckDBIntegration();
