<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="/favicon.ico" sizes="32x32" />
		<link rel="icon" href="/icon.svg" type="image/svg+xml" />
		<link rel="apple-touch-icon" href="/apple-touch-icon.png" />
		<link rel="manifest" href="/manifest.webmanifest" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<script>
			try {
				/** @type {'light' | 'dark' | 'system'} */
				const savedTheme = localStorage.getItem('evidence-theme') ?? 'system';
				const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
				const theme = savedTheme === 'system' ? (prefersDark ? 'dark' : 'light') : savedTheme;
				document.documentElement.classList.add(`theme-${theme}`);
			} catch (e) {}
		</script>
		
		<link href="/_app/immutable/assets/0.CJA0Iyeh.css" rel="stylesheet">
		<link href="/_app/immutable/assets/VennDiagram.D7OGjfZg.css" rel="stylesheet">
		<link href="/_app/immutable/assets/8.CuQP1aO9.css" rel="stylesheet">
		<link rel="modulepreload" href="/_app/immutable/entry/start.4KH39YYg.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/entry.CjmEikbu.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/scheduler.C5eBzNnH.js">
		<link rel="modulepreload" href="/_app/immutable/entry/app.B45R24Eu.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/preload-helper.D7HrI6pR.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/index.BSd9q3aW.js">
		<link rel="modulepreload" href="/_app/immutable/nodes/0.lELrfU5O.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/VennDiagram.svelte_svelte_type_style_lang.CJe6lW-S.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/index.rV6zwFgL.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/setTrackProxy.DjIbdjlZ.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/stores.C41LEeiH.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/index.CpZbXGXa.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/scroll.B6wI3tb1.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/button.BTWiJWck.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/AccordionItem.CzCt2IAG.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/checkRequiredProps.o_C_V3S5.js">
		<link rel="modulepreload" href="/_app/immutable/nodes/8.Bod0HdBS.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/QueryViewer.Ok3Ma9J7.js"><title>Domo Dataset Workflow</title><!-- HEAD_svelte-2igo1p_START --> <meta property="og:title" content="Domo Dataset Workflow"> <meta name="twitter:title" content="Domo Dataset Workflow"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:site" content="@evidence_dev"> <!-- HEAD_svelte-2igo1p_END -->
	</head>
	<body>
		<script>
			
		</script>
		<div>
			<!-- SvelteKit Hydrated Content -->
			   <div class="z-[1] fixed right-0 bottom-0 mx-10 my-6 w-80"></div> <div data-sveltekit-preload-data="hover" class="antialiased"> <header class="fixed w-full top-0 z-40 flex h-12 shrink-0 justify-start items-center gap-x-4 border-b border-base-300/50 bg-base-100/90 backdrop-blur print:hidden"><div class=" max-w-7xl mx-auto px-5 sm:px-6 md:px-12 flex flex-1 items-center justify-between" style="max-width:undefinedpx;"><div class="flex gap-x-4 items-center"><button type="button" class="text-base-content hover:bg-base-200 rounded-lg p-1 -ml-1 transition-all duration-500 md:hidden"><span class="sr-only" data-svelte-h="svelte-73kebv">Open sidebar</span> <svg class="w-5 h-5" width="100%" height="100%" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M4 6l16 0"></path><path d="M4 12l16 0"></path><path d="M4 18l16 0"></path></svg></button> <a href="/" class="text-sm font-bold text-base-content hidden md:block"><img src="/_app/immutable/assets/wordmark-black.rfl-FBgf.png" alt="Home" class="h-5 aspect-auto block dark:hidden" href="/"> <img src="/_app/immutable/assets/wordmark-white.C8ZS96Ri.png" alt="Home" class="h-5 aspect-auto hidden dark:block" href="/"></a></div> <div class="flex gap-2 text-sm items-center"> <div class="flex gap-2 items-center">   </div> <div class="relative"> <button type="button" tabindex="0" aria-controls="gfxfYg4mVE" aria-expanded="false" data-state="closed" id="TQQ8zg6ZtP" data-melt-dropdown-menu-trigger="" data-menu-trigger="" class="inline-flex items-center justify-center font-medium whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-base-content-muted disabled:pointer-events-none disabled:opacity-50 hover:text-base-content h-8 rounded-md text-xs px-1 hover:bg-base-200 shadow-base-200" aria-label="Menu" data-button-root=""><svg class="h-6 w-6" width="100%" height="100%" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M5 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path><path d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path><path d="M19 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path></svg></button> </div></div></div></header> <div class=" max-w-7xl print:w-[650px] print:md:w-[841px] mx-auto print:md:px-0 print:px-0 px-6 sm:px-8 md:px-12 flex justify-start" style="max-width:undefinedpx;"><div class="print:hidden">  <aside class="w-48 flex-none hidden md:flex"><div class="hidden: md:block fixed w-48 top-20 bottom-8 overflow-y-auto flex-1 text-sm pretty-scrollbar"><div class="flex flex-col pb-6"><a class="sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize hover:underline text-base-heading" href="/">Home</a>  <a class="group inline-block py-1 capitalize transition-all duration-100 text-primary" href="/workflow">Domo Dataset Workflow  </a></div> </div> <div class="fixed bottom-0 text-xs py-2" data-svelte-h="svelte-fworv4"><a href="https://www.evidence.dev" class="bg-gradient-to-r inline-block antialiased font-medium">Built with Evidence</a></div></aside></div> <main class="md:pl-8 md:pr-8  mt-16 sm:mt-20 flex-grow overflow-x-hidden print:px-0 print:mt-8"><div class="print:hidden"><div class="flex items-start mt-0 whitespace-nowrap overflow-auto"><div class="inline-flex items-center text-sm capitalize gap-1 text-base-content-muted mb-2 sm:mb-4"><a href="/" class="hover:underline">Home </a><svg class="icon icon-tabler icon-tabler-chevron-right" width="12px" height="12px" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M9 6l6 6l-6 6"></path></svg> <a href="/workflow" class="hover:underline">Domo Dataset Workflow</a></div></div></div> <article id="evidence-main-article" class="select-text markdown pb-10">  <h1 class="title">Domo Dataset Workflow</h1>     <h1 class="markdown" id="domo-dataset-to-duckdb-workflow" data-svelte-h="svelte-l4qjn8"><a href="#domo-dataset-to-duckdb-workflow">Domo Dataset to DuckDB Workflow</a></h1> <div class="dev-info svelte-10vyee7" data-svelte-h="svelte-eca4iq"><h4 class="svelte-10vyee7">🛠️ Development Mode</h4> <p>Running in development mode with mock Domo data.</p></div> <p class="markdown" data-svelte-h="svelte-1jvntq6">This page allows you to select and load Domo datasets into DuckDB for analysis in Evidence.</p> <h2 class="markdown" id="how-it-works" data-svelte-h="svelte-m9j39v"><a href="#how-it-works">How it Works</a></h2> <ol class="markdown" data-svelte-h="svelte-i541gc"><li class="markdown"><strong class="markdown">Select a Dataset</strong>: Choose from available Domo datasets in your instance</li> <li class="markdown"><strong class="markdown">Preview Data</strong>: Review the dataset schema and sample data</li> <li class="markdown"><strong class="markdown">Configure Loading</strong>: Set table name and refresh mode</li> <li class="markdown"><strong class="markdown">Load into DuckDB</strong>: Import the data for use in Evidence queries</li></ol>  <div id="domo-workflow-picker" class="workflow-picker svelte-10vyee7"><div class="picker-header svelte-10vyee7" data-svelte-h="svelte-1qqhjj8"><h3 class="svelte-10vyee7">Domo Dataset Workflow</h3> <p class="svelte-10vyee7">Select and load Domo datasets into DuckDB for analysis</p></div> <div class="workflow-step svelte-10vyee7"><label for="dataset-select" class="svelte-10vyee7" data-svelte-h="svelte-gxhz7z">Select Dataset:</label> <select id="dataset-select" class="dataset-dropdown svelte-10vyee7"><option value="" data-svelte-h="svelte-59d9xk">Choose a dataset...</option></select></div> <div id="dataset-preview" class="dataset-preview svelte-10vyee7" style="display: none;" data-svelte-h="svelte-1dp1fod"><h4>Dataset Information</h4> <div id="dataset-info" class="dataset-info svelte-10vyee7"></div> <h5>Schema</h5> <div id="schema-table" class="schema-table"></div> <div class="preview-actions svelte-10vyee7"><button id="preview-data-btn" class="btn btn-secondary svelte-10vyee7">Preview Data</button></div> <div id="data-preview" class="data-preview svelte-10vyee7" style="display: none;"></div></div> <div id="loading-config" class="workflow-step svelte-10vyee7" style="display: none;"><h4 data-svelte-h="svelte-1foy07w">Loading Configuration</h4> <div class="config-grid svelte-10vyee7"><div class="config-item" data-svelte-h="svelte-1m428t"><label for="table-name" class="svelte-10vyee7">Table Name in DuckDB:</label> <input id="table-name" type="text" placeholder="Enter table name" class="svelte-10vyee7"></div> <div class="config-item"><label for="refresh-mode" class="svelte-10vyee7" data-svelte-h="svelte-p1qydn">Refresh Mode:</label> <select id="refresh-mode" class="svelte-10vyee7"><option value="replace" data-svelte-h="svelte-qvzdub">Replace existing data</option><option value="append" data-svelte-h="svelte-idsvi6">Append to existing data</option></select></div></div></div> <div id="workflow-actions" class="workflow-actions svelte-10vyee7" style="display: none;" data-svelte-h="svelte-efrb90"><button id="load-dataset-btn" class="btn btn-primary svelte-10vyee7">Load Dataset into DuckDB</button></div> <div id="loading-overlay" class="loading-overlay svelte-10vyee7" style="display: none;" data-svelte-h="svelte-mbg3er"><div class="loading-spinner svelte-10vyee7"></div> <p id="loading-message" class="svelte-10vyee7">Loading...</p></div></div> <h2 class="markdown" id="loaded-datasets" data-svelte-h="svelte-1f0ly50"><a href="#loaded-datasets">Loaded Datasets</a></h2> <div class="no-datasets svelte-10vyee7" data-svelte-h="svelte-1io82co"><p class="svelte-10vyee7">No datasets loaded yet. Use the workflow picker above to load your first dataset.</p></div> <h2 class="markdown" id="using-your-data" data-svelte-h="svelte-d20xgf"><a href="#using-your-data">Using Your Data</a></h2> <p class="markdown" data-svelte-h="svelte-8qpe1e">Once you&#39;ve loaded datasets, you can use them in Evidence pages with SQL queries:</p> <div class="over-container svelte-1ursthx"> </div> <h3 class="markdown" id="example-queries" data-svelte-h="svelte-1y9nphi"><a href="#example-queries">Example Queries</a></h3> <p class="markdown" data-svelte-h="svelte-1jqarqq">Here are some example queries you can run on your loaded datasets:</p> <p class="markdown" data-svelte-h="svelte-fi6le8"><strong class="markdown">Basic Data Exploration:</strong></p> <div class="mt-2 mb-4 bg-base-200 border border-base-300 rounded-md px-3 py-2 relative group"><button class="absolute opacity-0 rounded p-1 group-hover:opacity-100 hover:bg-base-300/30 top-2 right-2 h-6 w-6 z-10 transition-all duration-200 ease-in-out text-base-content-muted active:bg-base-300/50"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" fill="currentColor" width="100%" height="100%" preserveAspectRatio="xMidYMid meet"><path d="M28,10V28H10V10H28m0-2H10a2,2,0,0,0-2,2V28a2,2,0,0,0,2,2H28a2,2,0,0,0,2-2V10a2,2,0,0,0-2-2Z"></path><path d="M4,18H2V4A2,2,0,0,1,4,2H18V4H4Z"></path></svg></button> <pre class="overflow-auto pretty-scrollbar"><code class="language-sql text-sm">-- Get row count and basic stats
SELECT 
  COUNT(*) as total_rows,
  COUNT(DISTINCT column_name) as unique_values
FROM your_table_name;</code></pre></div> <p class="markdown" data-svelte-h="svelte-8raaax"><strong class="markdown">Time Series Analysis:</strong></p> <div class="mt-2 mb-4 bg-base-200 border border-base-300 rounded-md px-3 py-2 relative group"><button class="absolute opacity-0 rounded p-1 group-hover:opacity-100 hover:bg-base-300/30 top-2 right-2 h-6 w-6 z-10 transition-all duration-200 ease-in-out text-base-content-muted active:bg-base-300/50"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" fill="currentColor" width="100%" height="100%" preserveAspectRatio="xMidYMid meet"><path d="M28,10V28H10V10H28m0-2H10a2,2,0,0,0-2,2V28a2,2,0,0,0,2,2H28a2,2,0,0,0,2-2V10a2,2,0,0,0-2-2Z"></path><path d="M4,18H2V4A2,2,0,0,1,4,2H18V4H4Z"></path></svg></button> <pre class="overflow-auto pretty-scrollbar"><code class="language-sql text-sm">-- Aggregate by date (if you have date columns)
SELECT 
  DATE_TRUNC('month', date_column) as month,
  SUM(numeric_column) as total
FROM your_table_name
GROUP BY month
ORDER BY month;</code></pre></div> <p class="markdown" data-svelte-h="svelte-1m0hm5v"><strong class="markdown">Category Analysis:</strong></p> <div class="mt-2 mb-4 bg-base-200 border border-base-300 rounded-md px-3 py-2 relative group"><button class="absolute opacity-0 rounded p-1 group-hover:opacity-100 hover:bg-base-300/30 top-2 right-2 h-6 w-6 z-10 transition-all duration-200 ease-in-out text-base-content-muted active:bg-base-300/50"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" fill="currentColor" width="100%" height="100%" preserveAspectRatio="xMidYMid meet"><path d="M28,10V28H10V10H28m0-2H10a2,2,0,0,0-2,2V28a2,2,0,0,0,2,2H28a2,2,0,0,0,2-2V10a2,2,0,0,0-2-2Z"></path><path d="M4,18H2V4A2,2,0,0,1,4,2H18V4H4Z"></path></svg></button> <pre class="overflow-auto pretty-scrollbar"><code class="language-sql text-sm">-- Group by categorical columns
SELECT 
  category_column,
  COUNT(*) as count,
  AVG(numeric_column) as average
FROM your_table_name
GROUP BY category_column
ORDER BY count DESC;</code></pre></div> <h2 class="markdown" id="next-steps" data-svelte-h="svelte-b2v2p6"><a href="#next-steps">Next Steps</a></h2> <ol class="markdown" data-svelte-h="svelte-1h5tzx0"><li class="markdown"><strong class="markdown">Create Visualizations</strong>: Use Evidence components like <code class="markdown">&lt;BarChart&gt;</code>, <code class="markdown">&lt;LineChart&gt;</code>, etc.</li> <li class="markdown"><strong class="markdown">Add Interactivity</strong>: Use <code class="markdown">&lt;Dropdown&gt;</code> and other input components</li> <li class="markdown"><strong class="markdown">Build Dashboards</strong>: Create multiple pages with different analyses</li> <li class="markdown"><strong class="markdown">Deploy to Domo</strong>: Package your Evidence app for Domo DDX</li></ol> <h2 class="markdown" id="troubleshooting" data-svelte-h="svelte-1uk16ny"><a href="#troubleshooting">Troubleshooting</a></h2> <h3 class="markdown" id="common-issues" data-svelte-h="svelte-1iqe0ip"><a href="#common-issues">Common Issues</a></h3> <p class="markdown" data-svelte-h="svelte-1syr9k0"><strong class="markdown">Dataset Not Loading:</strong></p> <ul class="markdown" data-svelte-h="svelte-1nuau76"><li class="markdown">Check your Domo permissions</li> <li class="markdown">Verify the dataset exists and is accessible</li> <li class="markdown">Try refreshing the page</li></ul> <p class="markdown" data-svelte-h="svelte-1eb0x8p"><strong class="markdown">Table Name Conflicts:</strong></p> <ul class="markdown" data-svelte-h="svelte-1fulwkk"><li class="markdown">Use unique table names</li> <li class="markdown">Choose &quot;Replace existing data&quot; to overwrite</li></ul> <p class="markdown" data-svelte-h="svelte-ta14c3"><strong class="markdown">Performance Issues:</strong></p> <ul class="markdown" data-svelte-h="svelte-1sdbkgn"><li class="markdown">Large datasets may take time to load</li> <li class="markdown">Consider filtering data in Domo before loading</li> <li class="markdown">Use appropriate data types for better performance</li></ul> <h3 class="markdown" id="getting-help" data-svelte-h="svelte-1f92uqx"><a href="#getting-help">Getting Help</a></h3> <ul class="markdown" data-svelte-h="svelte-1t69tq3"><li class="markdown">Check the browser console for error messages</li> <li class="markdown">Verify your Domo DDX environment is properly configured</li> <li class="markdown">Contact your Domo administrator for dataset access issues</li></ul></article></main> <div class="print:hidden"><aside class="hidden lg:block w-48"><div class="fixed w-48 top-20 bottom-20 pl-4 px-3 overflow-auto pretty-scrollbar"></div></aside></div></div></div>  
			<script type="application/json" data-sveltekit-fetched data-url="/api/customFormattingSettings.json/GET.json">{"status":200,"statusText":"","headers":{},"body":"{\"customFormattingSettings\":{\"version\":\"1.0\",\"customFormats\":[]}}"}</script>
			<script type="application/json" data-sveltekit-fetched data-url="/api/pagesManifest.json">{"status":200,"statusText":"","headers":{},"body":"{\"label\":\"Home\",\"href\":\"/\",\"children\":{\"workflow\":{\"label\":\"workflow\",\"href\":\"/workflow\",\"children\":{},\"frontMatter\":{\"title\":\"Domo Dataset Workflow\"},\"isTemplated\":false,\"isPage\":true}},\"frontMatter\":{\"title\":\"Evidence Dashboard for Domo\"},\"isTemplated\":false,\"isPage\":true}"}</script>
			<script type="application/json" data-sveltekit-fetched data-url="/api//workflow/evidencemeta.json">{"status":200,"statusText":"","headers":{},"body":"{\"queries\":[{\"id\":\"my_analysis\",\"compiledQueryString\":\"SELECT * FROM your_table_name LIMIT 10\",\"inputQueryString\":\"SELECT * FROM your_table_name LIMIT 10\",\"compiled\":false,\"inline\":true}]}"}</script>
			<script>
				{
					__sveltekit_1ouoygk = {
						base: ""
					};

					const element = document.currentScript.parentElement;

					const data = [null,null];

					Promise.all([
						import("/_app/immutable/entry/start.4KH39YYg.js"),
						import("/_app/immutable/entry/app.B45R24Eu.js")
					]).then(([kit, app]) => {
						kit.start(app, element, {
							node_ids: [0, 8],
							data,
							form: null,
							error: null
						});
					});
				}
			</script>
		

			<!-- SplashScreen -->
			<div
				aria-disabled
				id="__evidence_project_splash"
				data-test-id="__evidence_project_splash"
				style="visibility: hidden"
			>
				<svg width="100" height="100" viewBox="-8 -8 588 588" xmlns="http://www.w3.org/2000/svg">
					<path
						d="M7.19462e-05 74.3583C109.309 74.3583 195.795 86.2578 286.834 37.825C377.872 -10.6077 466.416 1.29174 573.667 1.29175L573.667 126.549C466.416 126.549 377.373 114.91 286.834 163.082C196.294 211.254 109.309 199.615 6.11417e-05 199.615L7.19462e-05 74.3583Z"
						class="draw-path"
					/>
					<path
						d="M573.669 499.31C464.36 499.31 377.874 487.411 286.835 535.843C195.797 584.276 107.252 572.377 0.0014801 572.377V447.12C107.252 447.12 196.295 458.758 286.835 410.586C377.375 362.415 464.36 374.053 573.669 374.053V499.31Z"
						class="draw-path"
					/>
					<path
						d="M452.896 186.499C395.028 187.686 341.581 194.947 286.835 224.074C211.396 264.212 136.995 262.826 52.2355 261.247C35.2696 260.931 17.8887 260.608 0.0014801 260.608V385.865C18.1032 385.865 35.6721 386.204 52.81 386.534C137.212 388.162 211.162 389.589 286.835 349.331C341.838 320.07 395.18 312.831 452.896 311.685V186.499Z"
						class="draw-path"
					/>
				</svg>
			</div>
		</div>
	</body>
</html>

<style>
	#__evidence_project_splash {
		position: fixed;
		top: 0;
		left: 0;
		width: 100vw;
		height: 100vh;
		background-color: #ffffff;
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 9999;
	}

	.theme-dark #__evidence_project_splash {
		background-color: #000000;
	}

	.draw-path {
		fill: #000000;
		animation: blinking-logo 2s;
		animation-fill-mode: both;
		animation-iteration-count: infinite;
		animation-timing-function: ease-in-out;
	}

	.theme-dark .draw-path {
		fill: #ffffff;
	}

	@keyframes blinking-logo {
		0% {
			fill-opacity: 1;
		}
		50% {
			fill-opacity: 0.2;
		}
		100% {
			fill-opacity: 1;
		}
	}
</style>
