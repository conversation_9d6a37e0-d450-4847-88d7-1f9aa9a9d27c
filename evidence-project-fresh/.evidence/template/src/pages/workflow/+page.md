---
title: Domo Dataset Workflow
---

<script>
  let loadedDatasets = [];
  let domoEnvironment = false;
  let domoUser = null;

  // Check Domo environment on component initialization
  if (typeof window !== 'undefined') {
    domoEnvironment = typeof window.domo !== 'undefined';

    if (domoEnvironment && window.domo && window.domo.get) {
      // Try to get user info
      window.domo.get('/domo/users/v1/me')
        .then(user => {
          domoUser = user;
          console.log('Domo user:', domoUser);
        })
        .catch(error => {
          console.warn('Could not get Domo user info:', error);
        });
    }
  }

  function handleDatasetLoaded(event) {
    console.log('Dataset loaded:', event);
    loadedDatasets = [...loadedDatasets, event];

    // Notify Domo of successful data load if in DDX environment
    if (domoEnvironment && window.domo && window.domo.publish) {
      window.domo.publish('dataset-loaded', event);
    }
  }

  // Initialize Domo workflow picker when page loads
  if (typeof window !== 'undefined') {
    window.addEventListener('DOMContentLoaded', function() {
      // Load the Domo integration script
      const script = document.createElement('script');
      script.src = '/static/domo-duckdb-integration.js';
      script.onload = function() {
        console.log('Domo integration script loaded');
      };
      document.head.appendChild(script);
    });
  }
</script>

# Domo Dataset to DuckDB Workflow

{#if domoEnvironment}
  <div class="domo-info">
    <h4>🔗 Connected to Domo</h4>
    {#if domoUser}
      <p>Welcome, {domoUser.displayName || domoUser.name}!</p>
    {/if}
    <p>This Evidence app is running in your Domo environment with access to your datasets.</p>
  </div>
{:else}
  <div class="dev-info">
    <h4>🛠️ Development Mode</h4>
    <p>Running in development mode with mock Domo data.</p>
  </div>
{/if}

This page allows you to select and load Domo datasets into DuckDB for analysis in Evidence.

## How it Works

1. **Select a Dataset**: Choose from available Domo datasets in your instance
2. **Preview Data**: Review the dataset schema and sample data
3. **Configure Loading**: Set table name and refresh mode
4. **Load into DuckDB**: Import the data for use in Evidence queries

<!-- Domo Workflow Picker -->
<div id="domo-workflow-picker" class="workflow-picker">
  <div class="picker-header">
    <h3>Domo Dataset Workflow</h3>
    <p>Select and load Domo datasets into DuckDB for analysis</p>
  </div>

  <div class="workflow-step">
    <label for="dataset-select">Select Dataset:</label>
    <select id="dataset-select" class="dataset-dropdown">
      <option value="">Choose a dataset...</option>
    </select>
  </div>

  <div id="dataset-preview" class="dataset-preview" style="display: none;">
    <h4>Dataset Information</h4>
    <div id="dataset-info" class="dataset-info"></div>

    <h5>Schema</h5>
    <div id="schema-table" class="schema-table"></div>

    <div class="preview-actions">
      <button id="preview-data-btn" class="btn btn-secondary">Preview Data</button>
    </div>

    <div id="data-preview" class="data-preview" style="display: none;"></div>
  </div>

  <div id="loading-config" class="workflow-step" style="display: none;">
    <h4>Loading Configuration</h4>
    <div class="config-grid">
      <div class="config-item">
        <label for="table-name">Table Name in DuckDB:</label>
        <input id="table-name" type="text" placeholder="Enter table name" />
      </div>

      <div class="config-item">
        <label for="refresh-mode">Refresh Mode:</label>
        <select id="refresh-mode">
          <option value="replace">Replace existing data</option>
          <option value="append">Append to existing data</option>
        </select>
      </div>
    </div>
  </div>

  <div id="workflow-actions" class="workflow-actions" style="display: none;">
    <button id="load-dataset-btn" class="btn btn-primary">Load Dataset into DuckDB</button>
  </div>

  <div id="loading-overlay" class="loading-overlay" style="display: none;">
    <div class="loading-spinner"></div>
    <p id="loading-message">Loading...</p>
  </div>
</div>



## Loaded Datasets

{#if loadedDatasets.length > 0}
  <div class="loaded-datasets">
    <h3>Successfully Loaded Datasets</h3>
    {#each loadedDatasets as dataset}
      <div class="dataset-card">
        <h4>{dataset.datasetName}</h4>
        <p><strong>Table Name:</strong> {dataset.tableName}</p>
        <p><strong>Rows:</strong> {dataset.rowCount.toLocaleString()}</p>
        <p><strong>Dataset ID:</strong> {dataset.datasetId}</p>
      </div>
    {/each}
  </div>
{:else}
  <div class="no-datasets">
    <p>No datasets loaded yet. Use the workflow picker above to load your first dataset.</p>
  </div>
{/if}

## Using Your Data

Once you've loaded datasets, you can use them in Evidence pages with SQL queries:

```sql my_analysis
SELECT * FROM your_table_name LIMIT 10
```

### Example Queries

Here are some example queries you can run on your loaded datasets:

**Basic Data Exploration:**
```sql
-- Get row count and basic stats
SELECT 
  COUNT(*) as total_rows,
  COUNT(DISTINCT column_name) as unique_values
FROM your_table_name;
```

**Time Series Analysis:**
```sql
-- Aggregate by date (if you have date columns)
SELECT 
  DATE_TRUNC('month', date_column) as month,
  SUM(numeric_column) as total
FROM your_table_name
GROUP BY month
ORDER BY month;
```

**Category Analysis:**
```sql
-- Group by categorical columns
SELECT 
  category_column,
  COUNT(*) as count,
  AVG(numeric_column) as average
FROM your_table_name
GROUP BY category_column
ORDER BY count DESC;
```

## Next Steps

1. **Create Visualizations**: Use Evidence components like `<BarChart>`, `<LineChart>`, etc.
2. **Add Interactivity**: Use `<Dropdown>` and other input components
3. **Build Dashboards**: Create multiple pages with different analyses
4. **Deploy to Domo**: Package your Evidence app for Domo DDX

## Troubleshooting

### Common Issues

**Dataset Not Loading:**
- Check your Domo permissions
- Verify the dataset exists and is accessible
- Try refreshing the page

**Table Name Conflicts:**
- Use unique table names
- Choose "Replace existing data" to overwrite

**Performance Issues:**
- Large datasets may take time to load
- Consider filtering data in Domo before loading
- Use appropriate data types for better performance

### Getting Help

- Check the browser console for error messages
- Verify your Domo DDX environment is properly configured
- Contact your Domo administrator for dataset access issues

<style>
  .domo-info {
    margin: 20px 0;
    padding: 15px;
    border: 2px solid #00d4aa;
    border-radius: 8px;
    background-color: #f0fdf9;
    color: #065f46;
  }

  .domo-info h4 {
    margin: 0 0 10px 0;
    color: #047857;
  }

  .dev-info {
    margin: 20px 0;
    padding: 15px;
    border: 2px solid #f59e0b;
    border-radius: 8px;
    background-color: #fffbeb;
    color: #92400e;
  }

  .dev-info h4 {
    margin: 0 0 10px 0;
    color: #d97706;
  }

  .loaded-datasets {
    margin: 30px 0;
    padding: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background-color: #f9fafb;
  }

  .dataset-card {
    margin: 15px 0;
    padding: 15px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .dataset-card h4 {
    margin: 0 0 10px 0;
    color: #1f2937;
  }

  .dataset-card p {
    margin: 5px 0;
    color: #6b7280;
  }

  .no-datasets {
    margin: 30px 0;
    padding: 40px;
    text-align: center;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    color: #6b7280;
  }

  .no-datasets p {
    margin: 0;
    font-style: italic;
  }

  /* Workflow Picker Styles */
  .workflow-picker {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .picker-header {
    text-align: center;
    margin-bottom: 30px;
  }

  .picker-header h3 {
    color: #1f2937;
    margin-bottom: 8px;
  }

  .picker-header p {
    color: #6b7280;
    margin: 0;
  }

  .workflow-step {
    margin: 25px 0;
    padding: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background-color: #f9fafb;
  }

  .workflow-step label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
  }

  .dataset-dropdown,
  .workflow-step input,
  .workflow-step select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    background-color: white;
  }

  .dataset-dropdown:focus,
  .workflow-step input:focus,
  .workflow-step select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .dataset-preview {
    margin-top: 20px;
    padding: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background-color: white;
  }

  .dataset-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
  }

  .info-item {
    padding: 10px;
    background-color: #f3f4f6;
    border-radius: 4px;
  }

  .schema-table table,
  .data-preview table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
  }

  .schema-table th,
  .schema-table td,
  .data-preview th,
  .data-preview td {
    padding: 8px 12px;
    text-align: left;
    border: 1px solid #e5e7eb;
  }

  .schema-table th,
  .data-preview th {
    background-color: #f3f4f6;
    font-weight: 600;
  }

  .config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }

  .workflow-actions {
    text-align: center;
    margin: 30px 0;
  }

  .btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    margin: 0 5px;
  }

  .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .btn-primary {
    background-color: #3b82f6;
    color: white;
  }

  .btn-primary:hover:not(:disabled) {
    background-color: #2563eb;
  }

  .btn-secondary {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
  }

  .btn-secondary:hover:not(:disabled) {
    background-color: #e5e7eb;
  }

  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  .loading-overlay p {
    color: white;
    font-size: 16px;
    margin: 0;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .preview-actions {
    margin: 15px 0;
  }

  .data-preview {
    margin-top: 20px;
    max-height: 300px;
    overflow: auto;
  }
</style>
