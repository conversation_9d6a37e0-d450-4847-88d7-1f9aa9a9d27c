{"queries": [{"id": "categories", "compiledQueryString": "select\n      category\n  from needful_things.orders\n  group by category", "inputQueryString": "select\n      category\n  from needful_things.orders\n  group by category", "compiled": false, "inline": true}, {"id": "orders_by_category", "compiledQueryString": "select \n      date_trunc('month', order_datetime) as month,\n      sum(sales) as sales_usd,\n      category\n  from needful_things.orders\n  where category like '${inputs.category.value}'\n  and date_part('year', order_datetime) like '${inputs.year.value}'\n  group by all\n  order by sales_usd desc", "inputQueryString": "select \n      date_trunc('month', order_datetime) as month,\n      sum(sales) as sales_usd,\n      category\n  from needful_things.orders\n  where category like '${inputs.category.value}'\n  and date_part('year', order_datetime) like '${inputs.year.value}'\n  group by all\n  order by sales_usd desc", "compiled": false, "inline": true}]}