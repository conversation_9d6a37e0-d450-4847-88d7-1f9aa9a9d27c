function P(n,...c){return n.call(this,...c)}const d=Symbol("Unset"),b=Symbol("IsSetTracked"),h=Symbol("GetModKeys"),m=Symbol("GetOwnKey"),a=Symbol("GetOwnPath"),y=Symbol("GetParent"),S=(n={},c={},s=void 0,e=void 0)=>{if(s&&!s[b])throw new Error("SetTracked parent must be SetTracked");const f=Object.assign(()=>{},c??{}),l=Object.keys(f),u=new Proxy(f,{get(i,t){switch(t){case d:return!(s!=null&&s[h].includes(e));case h:return l;case m:return e;case y:return s;case a:{const r=[e];let o=s;for(;o!==void 0;)r.unshift(o[m]),o=o[y];return r.join(".")}case b:return!0;case"toJSON":return()=>({...i});case"toString":case"toPrimitive":case Symbol.toPrimitive:return u[d]?e&&e in n?()=>n[e]:()=>"":c.toString.bind(c);default:return t in i||(i[t]=S(n,void 0,u,t)),i[t]}},set(i,t,r){return l.push(t),typeof r=="object"&&(r=S(n,r,u,t)),i[t]=r,!0}});return u},G=(n,...c)=>c.filter(e=>e==null?void 0:e[d]).length!==0;export{G as h,P as p,S as s};
