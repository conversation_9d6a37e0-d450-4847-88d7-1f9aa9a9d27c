import{v as M,s as _t,C as wt,z as vt,E as bt,L as we,B as At}from"./scheduler.CXt6djuF.js";new URL("sveltekit-internal://");function kt(e,n){return e==="/"||n==="ignore"?e:n==="never"?e.endsWith("/")?e.slice(0,-1):e:n==="always"&&!e.endsWith("/")?e+"/":e}function Et(e){return e.split("%25").map(decodeURI).join("%25")}function St(e){for(const n in e)e[n]=decodeURIComponent(e[n]);return e}function ge({href:e}){return e.split("#")[0]}const Rt=["href","pathname","search","toString","toJSON"];function It(e,n,t){const r=new URL(e);Object.defineProperty(r,"searchParams",{value:new Proxy(r.searchParams,{get(a,o){if(o==="get"||o==="getAll"||o==="has")return s=>(t(s),a[o](s));n();const i=Reflect.get(a,o);return typeof i=="function"?i.bind(a):i}}),enumerable:!0,configurable:!0});for(const a of Rt)Object.defineProperty(r,a,{get(){return n(),e[a]},enumerable:!0,configurable:!0});return r}const Lt="/__data.json",Ut=".html__data.json";function Tt(e){return e.endsWith(".html")?e.replace(/\.html$/,Ut):e.replace(/\/$/,"")+Lt}function xt(...e){let n=5381;for(const t of e)if(typeof t=="string"){let r=t.length;for(;r;)n=n*33^t.charCodeAt(--r)}else if(ArrayBuffer.isView(t)){const r=new Uint8Array(t.buffer,t.byteOffset,t.byteLength);let a=r.length;for(;a;)n=n*33^r[--a]}else throw new TypeError("value must be a string or TypedArray");return(n>>>0).toString(36)}function Pt(e){const n=atob(e),t=new Uint8Array(n.length);for(let r=0;r<n.length;r++)t[r]=n.charCodeAt(r);return t.buffer}const Ke=window.fetch;window.fetch=(e,n)=>((e instanceof Request?e.method:(n==null?void 0:n.method)||"GET")!=="GET"&&H.delete(Ee(e)),Ke(e,n));const H=new Map;function Ct(e,n){const t=Ee(e,n),r=document.querySelector(t);if(r!=null&&r.textContent){let{body:a,...o}=JSON.parse(r.textContent);const i=r.getAttribute("data-ttl");return i&&H.set(t,{body:a,init:o,ttl:1e3*Number(i)}),r.getAttribute("data-b64")!==null&&(a=Pt(a)),Promise.resolve(new Response(a,o))}return window.fetch(e,n)}function Nt(e,n,t){if(H.size>0){const r=Ee(e,t),a=H.get(r);if(a){if(performance.now()<a.ttl&&["default","force-cache","only-if-cached",void 0].includes(t==null?void 0:t.cache))return new Response(a.body,a.init);H.delete(r)}}return window.fetch(n,t)}function Ee(e,n){let r=`script[data-sveltekit-fetched][data-url=${JSON.stringify(e instanceof Request?e.url:e)}]`;if(n!=null&&n.headers||n!=null&&n.body){const a=[];n.headers&&a.push([...new Headers(n.headers)].join(",")),n.body&&(typeof n.body=="string"||ArrayBuffer.isView(n.body))&&a.push(n.body),r+=`[data-hash="${xt(...a)}"]`}return r}const Ot=/^(\[)?(\.\.\.)?(\w+)(?:=(\w+))?(\])?$/;function jt(e){const n=[];return{pattern:e==="/"?/^\/$/:new RegExp(`^${Dt(e).map(r=>{const a=/^\[\.\.\.(\w+)(?:=(\w+))?\]$/.exec(r);if(a)return n.push({name:a[1],matcher:a[2],optional:!1,rest:!0,chained:!0}),"(?:/(.*))?";const o=/^\[\[(\w+)(?:=(\w+))?\]\]$/.exec(r);if(o)return n.push({name:o[1],matcher:o[2],optional:!0,rest:!1,chained:!0}),"(?:/([^/]+))?";if(!r)return;const i=r.split(/\[(.+?)\](?!\])/);return"/"+i.map((c,l)=>{if(l%2){if(c.startsWith("x+"))return me(String.fromCharCode(parseInt(c.slice(2),16)));if(c.startsWith("u+"))return me(String.fromCharCode(...c.slice(2).split("-").map(f=>parseInt(f,16))));const d=Ot.exec(c),[,h,g,u,p]=d;return n.push({name:u,matcher:p,optional:!!h,rest:!!g,chained:g?l===1&&i[0]==="":!1}),g?"(.*?)":h?"([^/]*)?":"([^/]+?)"}return me(c)}).join("")}).join("")}/?$`),params:n}}function $t(e){return!/^\([^)]+\)$/.test(e)}function Dt(e){return e.slice(1).split("/").filter($t)}function Bt(e,n,t){const r={},a=e.slice(1),o=a.filter(s=>s!==void 0);let i=0;for(let s=0;s<n.length;s+=1){const c=n[s];let l=a[s-i];if(c.chained&&c.rest&&i&&(l=a.slice(s-i,s+1).filter(d=>d).join("/"),i=0),l===void 0){c.rest&&(r[c.name]="");continue}if(!c.matcher||t[c.matcher](l)){r[c.name]=l;const d=n[s+1],h=a[s+1];d&&!d.rest&&d.optional&&h&&c.chained&&(i=0),!d&&!h&&Object.keys(r).length===o.length&&(i=0);continue}if(c.optional&&c.chained){i++;continue}return}if(!i)return r}function me(e){return e.normalize().replace(/[[\]]/g,"\\$&").replace(/%/g,"%25").replace(/\//g,"%2[Ff]").replace(/\?/g,"%3[Ff]").replace(/#/g,"%23").replace(/[.*+?^${}()|\\]/g,"\\$&")}function Ft({nodes:e,server_loads:n,dictionary:t,matchers:r}){const a=new Set(n);return Object.entries(t).map(([s,[c,l,d]])=>{const{pattern:h,params:g}=jt(s),u={id:s,exec:p=>{const f=h.exec(p);if(f)return Bt(f,g,r)},errors:[1,...d||[]].map(p=>e[p]),layouts:[0,...l||[]].map(i),leaf:o(c)};return u.errors.length=u.layouts.length=Math.max(u.errors.length,u.layouts.length),u});function o(s){const c=s<0;return c&&(s=~s),[c,e[s]]}function i(s){return s===void 0?s:[a.has(s),e[s]]}}function ze(e,n=JSON.parse){try{return n(sessionStorage[e])}catch{}}function Oe(e,n,t=JSON.stringify){const r=t(n);try{sessionStorage[e]=r}catch{}}const j=[];function Vt(e,n){return{subscribe:se(e,n).subscribe}}function se(e,n=M){let t;const r=new Set;function a(s){if(_t(e,s)&&(e=s,t)){const c=!j.length;for(const l of r)l[1](),j.push(l,e);if(c){for(let l=0;l<j.length;l+=2)j[l][0](j[l+1]);j.length=0}}}function o(s){a(s(e))}function i(s,c=M){const l=[s,c];return r.add(l),r.size===1&&(t=n(a,o)||M),s(e),()=>{r.delete(l),r.size===0&&t&&(t(),t=null)}}return{set:a,update:o,subscribe:i}}function mn(e,n,t){const r=!Array.isArray(e),a=r?[e]:e;if(!a.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const o=n.length<2;return Vt(t,(i,s)=>{let c=!1;const l=[];let d=0,h=M;const g=()=>{if(d)return;h();const p=n(r?l[0]:l,i,s);o?i(p):h=bt(p)?p:M},u=a.map((p,f)=>wt(p,m=>{l[f]=m,d&=~(1<<f),c&&g()},()=>{d|=1<<f}));return c=!0,g(),function(){vt(u),h(),c=!1}})}function yn(e){return{subscribe:e.subscribe.bind(e)}}var Me;const U=((Me=globalThis.__sveltekit_1ce3cl)==null?void 0:Me.base)??"";var He;const qt=((He=globalThis.__sveltekit_1ce3cl)==null?void 0:He.assets)??U,Gt="1749303975374",We="sveltekit:snapshot",Ye="sveltekit:scroll",Je="sveltekit:states",Mt="sveltekit:pageurl",D="sveltekit:history",z="sveltekit:navigation",ee={tap:1,hover:2,viewport:3,eager:4,off:-1,false:-1},X=location.origin;function Se(e){if(e instanceof URL)return e;let n=document.baseURI;if(!n){const t=document.getElementsByTagName("base");n=t.length?t[0].href:document.URL}return new URL(e,n)}function Re(){return{x:pageXOffset,y:pageYOffset}}function $(e,n){return e.getAttribute(`data-sveltekit-${n}`)}const je={...ee,"":ee.hover};function Xe(e){let n=e.assignedSlot??e.parentNode;return(n==null?void 0:n.nodeType)===11&&(n=n.host),n}function Ze(e,n){for(;e&&e!==n;){if(e.nodeName.toUpperCase()==="A"&&e.hasAttribute("href"))return e;e=Xe(e)}}function ve(e,n){let t;try{t=new URL(e instanceof SVGAElement?e.href.baseVal:e.href,document.baseURI)}catch{}const r=e instanceof SVGAElement?e.target.baseVal:e.target,a=!t||!!r||ie(t,n)||(e.getAttribute("rel")||"").split(/\s+/).includes("external"),o=(t==null?void 0:t.origin)===X&&e.hasAttribute("download");return{url:t,external:a,target:r,download:o}}function te(e){let n=null,t=null,r=null,a=null,o=null,i=null,s=e;for(;s&&s!==document.documentElement;)r===null&&(r=$(s,"preload-code")),a===null&&(a=$(s,"preload-data")),n===null&&(n=$(s,"keepfocus")),t===null&&(t=$(s,"noscroll")),o===null&&(o=$(s,"reload")),i===null&&(i=$(s,"replacestate")),s=Xe(s);function c(l){switch(l){case"":case"true":return!0;case"off":case"false":return!1;default:return}}return{preload_code:je[r??"off"],preload_data:je[a??"off"],keepfocus:c(n),noscroll:c(t),reload:c(o),replace_state:c(i)}}function $e(e){const n=se(e);let t=!0;function r(){t=!0,n.update(i=>i)}function a(i){t=!1,n.set(i)}function o(i){let s;return n.subscribe(c=>{(s===void 0||t&&c!==s)&&i(s=c)})}return{notify:r,set:a,subscribe:o}}function Ht(){const{set:e,subscribe:n}=se(!1);let t;async function r(){clearTimeout(t);try{const a=await fetch(`${qt}/_app/version.json`,{headers:{pragma:"no-cache","cache-control":"no-cache"}});if(!a.ok)return!1;const i=(await a.json()).version!==Gt;return i&&(e(!0),clearTimeout(t)),i}catch{return!1}}return{subscribe:n,check:r}}function ie(e,n){return e.origin!==X||!e.pathname.startsWith(n)}function De(e){const n=zt(e),t=new ArrayBuffer(n.length),r=new DataView(t);for(let a=0;a<t.byteLength;a++)r.setUint8(a,n.charCodeAt(a));return t}const Kt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function zt(e){e.length%4===0&&(e=e.replace(/==?$/,""));let n="",t=0,r=0;for(let a=0;a<e.length;a++)t<<=6,t|=Kt.indexOf(e[a]),r+=6,r===24&&(n+=String.fromCharCode((t&16711680)>>16),n+=String.fromCharCode((t&65280)>>8),n+=String.fromCharCode(t&255),t=r=0);return r===12?(t>>=4,n+=String.fromCharCode(t)):r===18&&(t>>=2,n+=String.fromCharCode((t&65280)>>8),n+=String.fromCharCode(t&255)),n}const Wt=-1,Yt=-2,Jt=-3,Xt=-4,Zt=-5,Qt=-6;function _n(e,n){return Qe(JSON.parse(e),n)}function Qe(e,n){if(typeof e=="number")return a(e,!0);if(!Array.isArray(e)||e.length===0)throw new Error("Invalid input");const t=e,r=Array(t.length);function a(o,i=!1){if(o===Wt)return;if(o===Jt)return NaN;if(o===Xt)return 1/0;if(o===Zt)return-1/0;if(o===Qt)return-0;if(i)throw new Error("Invalid input");if(o in r)return r[o];const s=t[o];if(!s||typeof s!="object")r[o]=s;else if(Array.isArray(s))if(typeof s[0]=="string"){const c=s[0],l=n==null?void 0:n[c];if(l)return r[o]=l(a(s[1]));switch(c){case"Date":r[o]=new Date(s[1]);break;case"Set":const d=new Set;r[o]=d;for(let u=1;u<s.length;u+=1)d.add(a(s[u]));break;case"Map":const h=new Map;r[o]=h;for(let u=1;u<s.length;u+=2)h.set(a(s[u]),a(s[u+1]));break;case"RegExp":r[o]=new RegExp(s[1],s[2]);break;case"Object":r[o]=Object(s[1]);break;case"BigInt":r[o]=BigInt(s[1]);break;case"null":const g=Object.create(null);r[o]=g;for(let u=1;u<s.length;u+=2)g[s[u]]=a(s[u+1]);break;case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":{const u=globalThis[c],p=s[1],f=De(p),m=new u(f);r[o]=m;break}case"ArrayBuffer":{const u=s[1],p=De(u);r[o]=p;break}default:throw new Error(`Unknown type ${c}`)}}else{const c=new Array(s.length);r[o]=c;for(let l=0;l<s.length;l+=1){const d=s[l];d!==Yt&&(c[l]=a(d))}}else{const c={};r[o]=c;for(const l in s){const d=s[l];c[l]=a(d)}}return r[o]}return a(0)}const et=new Set(["load","prerender","csr","ssr","trailingSlash","config"]);[...et];const en=new Set([...et]);[...en];function tn(e){return e.filter(n=>n!=null)}class ce{constructor(n,t){this.status=n,typeof t=="string"?this.body={message:t}:t?this.body=t:this.body={message:`Error: ${n}`}}toString(){return JSON.stringify(this.body)}}class tt{constructor(n,t){this.status=n,this.location=t}}class Ie extends Error{constructor(n,t,r){super(r),this.status=n,this.text=t}}const nn="x-sveltekit-invalidated",rn="x-sveltekit-trailing-slash";function ne(e){return e instanceof ce||e instanceof Ie?e.status:500}function an(e){return e instanceof Ie?e.text:"Internal Error"}const O=ze(Ye)??{},W=ze(We)??{},x={url:$e({}),page:$e({}),navigating:se(null),updated:Ht()};function Le(e){O[e]=Re()}function on(e,n){let t=e+1;for(;O[t];)delete O[t],t+=1;for(t=n+1;W[t];)delete W[t],t+=1}function F(e){return location.href=e.href,new Promise(()=>{})}async function nt(){if("serviceWorker"in navigator){const e=await navigator.serviceWorker.getRegistration(U||"/");e&&await e.update()}}function Be(){}let le,be,re,T,Ae,q;const rt=[],ae=[];let I=null;const at=[],sn=[];let C=[],_={branch:[],error:null,url:null},Ue=!1,oe=!1,Fe=!0,Y=!1,G=!1,ot=!1,fe=!1,N,E,L,S,V;const K=new Set;let ye;async function wn(e,n,t){var a,o;document.URL!==location.href&&(location.href=location.href),q=e,le=Ft(e),T=document.documentElement,Ae=n,be=e.nodes[0],re=e.nodes[1],be(),re(),E=(a=history.state)==null?void 0:a[D],L=(o=history.state)==null?void 0:o[z],E||(E=L=Date.now(),history.replaceState({...history.state,[D]:E,[z]:L},""));const r=O[E];r&&(history.scrollRestoration="manual",scrollTo(r.x,r.y)),t?await pn(Ae,t):dn(location.href,{replaceState:!0}),hn()}async function cn(){if(await(ye||(ye=Promise.resolve())),!ye)return;ye=null;const e=Z(_.url,!0);I=null;const n=V={},t=e&&await Pe(e);if(!(!t||n!==V)){if(t.type==="redirect")return ue(new URL(t.location,_.url).href,{},1,n);t.props.page&&(S=t.props.page),_=t.state,st(),N.$set(t.props)}}function st(){rt.length=0,fe=!1}function it(e){ae.some(n=>n==null?void 0:n.snapshot)&&(W[e]=ae.map(n=>{var t;return(t=n==null?void 0:n.snapshot)==null?void 0:t.capture()}))}function ct(e){var n;(n=W[e])==null||n.forEach((t,r)=>{var a,o;(o=(a=ae[r])==null?void 0:a.snapshot)==null||o.restore(t)})}function Ve(){Le(E),Oe(Ye,O),it(L),Oe(We,W)}async function ue(e,n,t,r){return Q({type:"goto",url:Se(e),keepfocus:n.keepFocus,noscroll:n.noScroll,replace_state:n.replaceState,state:n.state,redirect_count:t,nav_token:r,accept:()=>{n.invalidateAll&&(fe=!0)}})}async function lt(e){if(e.id!==(I==null?void 0:I.id)){const n={};K.add(n),I={id:e.id,token:n,promise:Pe({...e,preload:n}).then(t=>(K.delete(n),t.type==="loaded"&&t.state.error&&(I=null),t))}}return I.promise}async function _e(e){const n=le.find(t=>t.exec(dt(e)));n&&await Promise.all([...n.layouts,n.leaf].map(t=>t==null?void 0:t[1]()))}function ft(e,n,t){var o;_=e.state;const r=document.querySelector("style[data-sveltekit]");r&&r.remove(),S=e.props.page,N=new q.root({target:n,props:{...e.props,stores:x,components:ae},hydrate:t,sync:!1}),ct(L);const a={from:null,to:{params:_.params,route:{id:((o=_.route)==null?void 0:o.id)??null},url:new URL(location.href)},willUnload:!1,type:"enter",complete:Promise.resolve()};C.forEach(i=>i(a)),oe=!0}function J({url:e,params:n,branch:t,status:r,error:a,route:o,form:i}){let s="never";if(U&&(e.pathname===U||e.pathname===U+"/"))s="always";else for(const u of t)(u==null?void 0:u.slash)!==void 0&&(s=u.slash);e.pathname=kt(e.pathname,s),e.search=e.search;const c={type:"loaded",state:{url:e,params:n,branch:t,error:a,route:o},props:{constructors:tn(t).map(u=>u.node.component),page:S}};i!==void 0&&(c.props.form=i);let l={},d=!S,h=0;for(let u=0;u<Math.max(t.length,_.branch.length);u+=1){const p=t[u],f=_.branch[u];(p==null?void 0:p.data)!==(f==null?void 0:f.data)&&(d=!0),p&&(l={...l,...p.data},d&&(c.props[`data_${h}`]=l),h+=1)}return(!_.url||e.href!==_.url.href||_.error!==a||i!==void 0&&i!==S.form||d)&&(c.props.page={error:a,params:n,route:{id:(o==null?void 0:o.id)??null},state:{},status:r,url:new URL(e),form:i??null,data:d?l:S.data}),c}async function Te({loader:e,parent:n,url:t,params:r,route:a,server_data_node:o}){var d,h,g;let i=null,s=!0;const c={dependencies:new Set,params:new Set,parent:!1,route:!1,url:!1,search_params:new Set},l=await e();if((d=l.universal)!=null&&d.load){let u=function(...f){for(const m of f){const{href:b}=new URL(m,t);c.dependencies.add(b)}};const p={route:new Proxy(a,{get:(f,m)=>(s&&(c.route=!0),f[m])}),params:new Proxy(r,{get:(f,m)=>(s&&c.params.add(m),f[m])}),data:(o==null?void 0:o.data)??null,url:It(t,()=>{s&&(c.url=!0)},f=>{s&&c.search_params.add(f)}),async fetch(f,m){let b;f instanceof Request?(b=f.url,m={body:f.method==="GET"||f.method==="HEAD"?void 0:await f.blob(),cache:f.cache,credentials:f.credentials,headers:[...f.headers].length?f.headers:void 0,integrity:f.integrity,keepalive:f.keepalive,method:f.method,mode:f.mode,redirect:f.redirect,referrer:f.referrer,referrerPolicy:f.referrerPolicy,signal:f.signal,...m}):b=f;const R=new URL(b,t);return s&&u(R.href),R.origin===t.origin&&(b=R.href.slice(t.origin.length)),oe?Nt(b,R.href,m):Ct(b,m)},setHeaders:()=>{},depends:u,parent(){return s&&(c.parent=!0),n()},untrack(f){s=!1;try{return f()}finally{s=!0}}};i=await l.universal.load.call(null,p)??null}return{node:l,loader:e,server:o,universal:(h=l.universal)!=null&&h.load?{type:"data",data:i,uses:c}:null,data:i??(o==null?void 0:o.data)??null,slash:((g=l.universal)==null?void 0:g.trailingSlash)??(o==null?void 0:o.slash)}}function qe(e,n,t,r,a,o){if(fe)return!0;if(!a)return!1;if(a.parent&&e||a.route&&n||a.url&&t)return!0;for(const i of a.search_params)if(r.has(i))return!0;for(const i of a.params)if(o[i]!==_.params[i])return!0;for(const i of a.dependencies)if(rt.some(s=>s(new URL(i))))return!0;return!1}function xe(e,n){return(e==null?void 0:e.type)==="data"?e:(e==null?void 0:e.type)==="skip"?n??null:null}function ln(e,n){if(!e)return new Set(n.searchParams.keys());const t=new Set([...e.searchParams.keys(),...n.searchParams.keys()]);for(const r of t){const a=e.searchParams.getAll(r),o=n.searchParams.getAll(r);a.every(i=>o.includes(i))&&o.every(i=>a.includes(i))&&t.delete(r)}return t}function Ge({error:e,url:n,route:t,params:r}){return{type:"loaded",state:{error:e,url:n,route:t,params:r,branch:[]},props:{page:S,constructors:[]}}}async function Pe({id:e,invalidating:n,url:t,params:r,route:a,preload:o}){if((I==null?void 0:I.id)===e)return K.delete(I.token),I.promise;const{errors:i,layouts:s,leaf:c}=a,l=[...s,c];i.forEach(y=>y==null?void 0:y().catch(()=>{})),l.forEach(y=>y==null?void 0:y[1]().catch(()=>{}));let d=null;const h=_.url?e!==_.url.pathname+_.url.search:!1,g=_.route?a.id!==_.route.id:!1,u=ln(_.url,t);let p=!1;const f=l.map((y,v)=>{var P;const A=_.branch[v],k=!!(y!=null&&y[0])&&((A==null?void 0:A.loader)!==y[1]||qe(p,g,h,u,(P=A.server)==null?void 0:P.uses,r));return k&&(p=!0),k});if(f.some(Boolean)){try{d=await gt(t,f)}catch(y){const v=await B(y,{url:t,params:r,route:{id:e}});return K.has(o)?Ge({error:v,url:t,params:r,route:a}):de({status:ne(y),error:v,url:t,route:a})}if(d.type==="redirect")return d}const m=d==null?void 0:d.nodes;let b=!1;const R=l.map(async(y,v)=>{var he;if(!y)return;const A=_.branch[v],k=m==null?void 0:m[v];if((!k||k.type==="skip")&&y[1]===(A==null?void 0:A.loader)&&!qe(b,g,h,u,(he=A.universal)==null?void 0:he.uses,r))return A;if(b=!0,(k==null?void 0:k.type)==="error")throw k;return Te({loader:y[1],url:t,params:r,route:a,parent:async()=>{var Ne;const Ce={};for(let pe=0;pe<v;pe+=1)Object.assign(Ce,(Ne=await R[pe])==null?void 0:Ne.data);return Ce},server_data_node:xe(k===void 0&&y[0]?{type:"skip"}:k??null,y[0]?A==null?void 0:A.server:void 0)})});for(const y of R)y.catch(()=>{});const w=[];for(let y=0;y<l.length;y+=1)if(l[y])try{w.push(await R[y])}catch(v){if(v instanceof tt)return{type:"redirect",location:v.location};if(K.has(o))return Ge({error:await B(v,{params:r,url:t,route:{id:a.id}}),url:t,params:r,route:a});let A=ne(v),k;if(m!=null&&m.includes(v))A=v.status??A,k=v.error;else if(v instanceof ce)k=v.body;else{if(await x.updated.check())return await nt(),await F(t);k=await B(v,{params:r,url:t,route:{id:a.id}})}const P=await ut(y,w,i);return P?J({url:t,params:r,branch:w.slice(0,P.idx).concat(P.node),status:A,error:k,route:a}):await pt(t,{id:a.id},k,A)}else w.push(void 0);return J({url:t,params:r,branch:w,status:200,error:null,route:a,form:n?void 0:null})}async function ut(e,n,t){for(;e--;)if(t[e]){let r=e;for(;!n[r];)r-=1;try{return{idx:r+1,node:{node:await t[e](),loader:t[e],data:{},server:null,universal:null}}}catch{continue}}}async function de({status:e,error:n,url:t,route:r}){const a={};let o=null;if(q.server_loads[0]===0)try{const l=await gt(t,[!0]);if(l.type!=="data"||l.nodes[0]&&l.nodes[0].type!=="data")throw 0;o=l.nodes[0]??null}catch{(t.origin!==X||t.pathname!==location.pathname||Ue)&&await F(t)}const s=await Te({loader:be,url:t,params:a,route:r,parent:()=>Promise.resolve({}),server_data_node:xe(o)}),c={node:await re(),loader:re,universal:null,server:null,data:null};return J({url:t,params:a,branch:[s,c],status:e,error:n,route:null})}function Z(e,n){if(!e||ie(e,U))return;let t;try{t=q.hooks.reroute({url:new URL(e)})??e.pathname}catch{return}const r=dt(t);for(const a of le){const o=a.exec(r);if(o)return{id:e.pathname+e.search,invalidating:n,route:a,params:St(o),url:e}}}function dt(e){return Et(e.slice(U.length)||"/")}function ht({url:e,type:n,intent:t,delta:r}){let a=!1;const o=yt(_,t,e,n);r!==void 0&&(o.navigation.delta=r);const i={...o.navigation,cancel:()=>{a=!0,o.reject(new Error("navigation cancelled"))}};return Y||at.forEach(s=>s(i)),a?null:o}async function Q({type:e,url:n,popped:t,keepfocus:r,noscroll:a,replace_state:o,state:i={},redirect_count:s=0,nav_token:c={},accept:l=Be,block:d=Be}){const h=Z(n,!1),g=ht({url:n,type:e,delta:t==null?void 0:t.delta,intent:h});if(!g){d();return}const u=E,p=L;l(),Y=!0,oe&&x.navigating.set(g.navigation),V=c;let f=h&&await Pe(h);if(!f){if(ie(n,U))return await F(n);f=await pt(n,{id:null},await B(new Ie(404,"Not Found",`Not found: ${n.pathname}`),{url:n,params:{},route:{id:null}}),404)}if(n=(h==null?void 0:h.url)||n,V!==c)return g.reject(new Error("navigation aborted")),!1;if(f.type==="redirect")if(s>=20)f=await de({status:500,error:await B(new Error("Redirect loop"),{url:n,params:{},route:{id:null}}),url:n,route:{id:null}});else return ue(new URL(f.location,n).href,{},s+1,c),!1;else f.props.page.status>=400&&await x.updated.check()&&(await nt(),await F(n));if(st(),Le(u),it(p),f.props.page.url.pathname!==n.pathname&&(n.pathname=f.props.page.url.pathname),i=t?t.state:i,!t){const w=o?0:1,y={[D]:E+=w,[z]:L+=w,[Je]:i};(o?history.replaceState:history.pushState).call(history,y,"",n),o||on(E,L)}if(I=null,f.props.page.state=i,oe){_=f.state,f.props.page&&(f.props.page.url=n);const w=(await Promise.all(sn.map(y=>y(g.navigation)))).filter(y=>typeof y=="function");if(w.length>0){let y=function(){C=C.filter(v=>!w.includes(v))};w.push(y),C.push(...w)}N.$set(f.props),ot=!0}else ft(f,Ae,!1);const{activeElement:m}=document;await we();const b=t?t.scroll:a?Re():null;if(Fe){const w=n.hash&&document.getElementById(decodeURIComponent(n.hash.slice(1)));b?scrollTo(b.x,b.y):w?w.scrollIntoView():scrollTo(0,0)}const R=document.activeElement!==m&&document.activeElement!==document.body;!r&&!R&&ke(),Fe=!0,f.props.page&&(S=f.props.page),Y=!1,e==="popstate"&&ct(L),g.fulfil(void 0),C.forEach(w=>w(g.navigation)),x.navigating.set(null)}async function pt(e,n,t,r){return e.origin===X&&e.pathname===location.pathname&&!Ue?await de({status:r,error:t,url:e,route:n}):await F(e)}function fn(){let e;T.addEventListener("mousemove",o=>{const i=o.target;clearTimeout(e),e=setTimeout(()=>{r(i,2)},20)});function n(o){o.defaultPrevented||r(o.composedPath()[0],1)}T.addEventListener("mousedown",n),T.addEventListener("touchstart",n,{passive:!0});const t=new IntersectionObserver(o=>{for(const i of o)i.isIntersecting&&(_e(i.target.href),t.unobserve(i.target))},{threshold:0});function r(o,i){const s=Ze(o,T);if(!s)return;const{url:c,external:l,download:d}=ve(s,U);if(l||d)return;const h=te(s),g=c&&_.url.pathname+_.url.search===c.pathname+c.search;if(!h.reload&&!g)if(i<=h.preload_data){const u=Z(c,!1);u&&lt(u)}else i<=h.preload_code&&_e(c.pathname)}function a(){t.disconnect();for(const o of T.querySelectorAll("a")){const{url:i,external:s,download:c}=ve(o,U);if(s||c)continue;const l=te(o);l.reload||(l.preload_code===ee.viewport&&t.observe(o),l.preload_code===ee.eager&&_e(i.pathname))}}C.push(a),a()}function B(e,n){if(e instanceof ce)return e.body;const t=ne(e),r=an(e);return q.hooks.handleError({error:e,event:n,status:t,message:r})??{message:r}}function un(e,n){At(()=>(e.push(n),()=>{const t=e.indexOf(n);e.splice(t,1)}))}function vn(e){un(C,e)}function dn(e,n={}){return e=Se(e),e.origin!==X?Promise.reject(new Error("goto: invalid URL")):ue(e,n,0)}function bn(){return fe=!0,cn()}async function An(e){const n=Se(e),t=Z(n,!1);if(!t)throw new Error(`Attempted to preload a URL that does not belong to this app: ${n}`);const r=await lt(t);if(r.type==="redirect")return{type:r.type,location:r.location};const{status:a,data:o}=r.props.page??S;return{type:r.type,status:a,data:o}}async function kn(e){if(e.type==="error"){const n=new URL(location.href),{branch:t,route:r}=_;if(!r)return;const a=await ut(_.branch.length,t,r.errors);if(a){const o=J({url:n,params:_.params,branch:t.slice(0,a.idx).concat(a.node),status:e.status??500,error:e.error,route:r});_=o.state,N.$set(o.props),we().then(ke)}}else e.type==="redirect"?ue(e.location,{invalidateAll:!0},0):(N.$set({form:null,page:{...S,form:e.data,status:e.status}}),await we(),N.$set({form:e.data}),e.type==="success"&&ke())}function hn(){var n;history.scrollRestoration="manual",addEventListener("beforeunload",t=>{let r=!1;if(Ve(),!Y){const a=yt(_,void 0,null,"leave"),o={...a.navigation,cancel:()=>{r=!0,a.reject(new Error("navigation cancelled"))}};at.forEach(i=>i(o))}r?(t.preventDefault(),t.returnValue=""):history.scrollRestoration="auto"}),addEventListener("visibilitychange",()=>{document.visibilityState==="hidden"&&Ve()}),(n=navigator.connection)!=null&&n.saveData||fn(),T.addEventListener("click",async t=>{if(t.button||t.which!==1||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.defaultPrevented)return;const r=Ze(t.composedPath()[0],T);if(!r)return;const{url:a,external:o,target:i,download:s}=ve(r,U);if(!a)return;if(i==="_parent"||i==="_top"){if(window.parent!==window)return}else if(i&&i!=="_self")return;const c=te(r);if(!(r instanceof SVGAElement)&&a.protocol!==location.protocol&&!(a.protocol==="https:"||a.protocol==="http:")||s)return;const[d,h]=a.href.split("#"),g=d===ge(location);if(o||c.reload&&(!g||!h)){ht({url:a,type:"link"})?Y=!0:t.preventDefault();return}if(h!==void 0&&g){const[,u]=_.url.href.split("#");if(u===h){if(t.preventDefault(),h===""||h==="top"&&r.ownerDocument.getElementById("top")===null)window.scrollTo({top:0});else{const p=r.ownerDocument.getElementById(decodeURIComponent(h));p&&(p.scrollIntoView(),p.focus())}return}if(G=!0,Le(E),e(a),!c.replace_state)return;G=!1}t.preventDefault(),await new Promise(u=>{requestAnimationFrame(()=>{setTimeout(u,0)}),setTimeout(u,100)}),Q({type:"link",url:a,keepfocus:c.keepfocus,noscroll:c.noscroll,replace_state:c.replace_state??a.href===location.href})}),T.addEventListener("submit",t=>{if(t.defaultPrevented)return;const r=HTMLFormElement.prototype.cloneNode.call(t.target),a=t.submitter;if(((a==null?void 0:a.formTarget)||r.target)==="_blank"||((a==null?void 0:a.formMethod)||r.method)!=="get")return;const s=new URL((a==null?void 0:a.hasAttribute("formaction"))&&(a==null?void 0:a.formAction)||r.action);if(ie(s,U))return;const c=t.target,l=te(c);if(l.reload)return;t.preventDefault(),t.stopPropagation();const d=new FormData(c),h=a==null?void 0:a.getAttribute("name");h&&d.append(h,(a==null?void 0:a.getAttribute("value"))??""),s.search=new URLSearchParams(d).toString(),Q({type:"form",url:s,keepfocus:l.keepfocus,noscroll:l.noscroll,replace_state:l.replace_state??s.href===location.href})}),addEventListener("popstate",async t=>{var r;if((r=t.state)!=null&&r[D]){const a=t.state[D];if(V={},a===E)return;const o=O[a],i=t.state[Je]??{},s=new URL(t.state[Mt]??location.href),c=t.state[z],l=ge(location)===ge(_.url);if(c===L&&(ot||l)){e(s),O[E]=Re(),o&&scrollTo(o.x,o.y),i!==S.state&&(S={...S,state:i},N.$set({page:S})),E=a;return}const h=a-E;await Q({type:"popstate",url:s,popped:{state:i,scroll:o,delta:h},accept:()=>{E=a,L=c},block:()=>{history.go(-h)},nav_token:V})}else if(!G){const a=new URL(location.href);e(a)}}),addEventListener("hashchange",()=>{G&&(G=!1,history.replaceState({...history.state,[D]:++E,[z]:L},"",location.href))});for(const t of document.querySelectorAll("link"))t.rel==="icon"&&(t.href=t.href);addEventListener("pageshow",t=>{t.persisted&&x.navigating.set(null)});function e(t){_.url=t,x.page.set({...S,url:t}),x.page.notify()}}async function pn(e,{status:n=200,error:t,node_ids:r,params:a,route:o,data:i,form:s}){Ue=!0;const c=new URL(location.href);({params:a={},route:o={id:null}}=Z(c,!1)||{});let l;try{const d=r.map(async(u,p)=>{const f=i[p];return f!=null&&f.uses&&(f.uses=mt(f.uses)),Te({loader:q.nodes[u],url:c,params:a,route:o,parent:async()=>{const m={};for(let b=0;b<p;b+=1)Object.assign(m,(await d[b]).data);return m},server_data_node:xe(f)})}),h=await Promise.all(d),g=le.find(({id:u})=>u===o.id);if(g){const u=g.layouts;for(let p=0;p<u.length;p++)u[p]||h.splice(p,0,void 0)}l=J({url:c,params:a,branch:h,status:n,error:t,form:s,route:g??null})}catch(d){if(d instanceof tt){await F(new URL(d.location,location.href));return}l=await de({status:ne(d),error:await B(d,{url:c,params:a,route:o}),url:c,route:o})}l.props.page&&(l.props.page.state={}),ft(l,e,!0)}async function gt(e,n){var a;const t=new URL(e);t.pathname=Tt(e.pathname),e.pathname.endsWith("/")&&t.searchParams.append(rn,"1"),t.searchParams.append(nn,n.map(o=>o?"1":"0").join(""));const r=await Ke(t.href);if(!r.ok){let o;throw(a=r.headers.get("content-type"))!=null&&a.includes("application/json")?o=await r.json():r.status===404?o="Not Found":r.status===500&&(o="Internal Error"),new ce(r.status,o)}return new Promise(async o=>{var h;const i=new Map,s=r.body.getReader(),c=new TextDecoder;function l(g){return Qe(g,{Promise:u=>new Promise((p,f)=>{i.set(u,{fulfil:p,reject:f})})})}let d="";for(;;){const{done:g,value:u}=await s.read();if(g&&!d)break;for(d+=!u&&d?`
`:c.decode(u,{stream:!0});;){const p=d.indexOf(`
`);if(p===-1)break;const f=JSON.parse(d.slice(0,p));if(d=d.slice(p+1),f.type==="redirect")return o(f);if(f.type==="data")(h=f.nodes)==null||h.forEach(m=>{(m==null?void 0:m.type)==="data"&&(m.uses=mt(m.uses),m.data=l(m.data))}),o(f);else if(f.type==="chunk"){const{id:m,data:b,error:R}=f,w=i.get(m);i.delete(m),R?w.reject(l(R)):w.fulfil(l(b))}}}})}function mt(e){return{dependencies:new Set((e==null?void 0:e.dependencies)??[]),params:new Set((e==null?void 0:e.params)??[]),parent:!!(e!=null&&e.parent),route:!!(e!=null&&e.route),url:!!(e!=null&&e.url),search_params:new Set((e==null?void 0:e.search_params)??[])}}function ke(){const e=document.querySelector("[autofocus]");if(e)e.focus();else{const n=document.body,t=n.getAttribute("tabindex");n.tabIndex=-1,n.focus({preventScroll:!0,focusVisible:!1}),t!==null?n.setAttribute("tabindex",t):n.removeAttribute("tabindex");const r=getSelection();if(r&&r.type!=="None"){const a=[];for(let o=0;o<r.rangeCount;o+=1)a.push(r.getRangeAt(o));setTimeout(()=>{if(r.rangeCount===a.length){for(let o=0;o<r.rangeCount;o+=1){const i=a[o],s=r.getRangeAt(o);if(i.commonAncestorContainer!==s.commonAncestorContainer||i.startContainer!==s.startContainer||i.endContainer!==s.endContainer||i.startOffset!==s.startOffset||i.endOffset!==s.endOffset)return}r.removeAllRanges()}})}}}function yt(e,n,t,r){var c,l;let a,o;const i=new Promise((d,h)=>{a=d,o=h});return i.catch(()=>{}),{navigation:{from:{params:e.params,route:{id:((c=e.route)==null?void 0:c.id)??null},url:e.url},to:t&&{params:(n==null?void 0:n.params)??null,route:{id:((l=n==null?void 0:n.route)==null?void 0:l.id)??null},url:t},willUnload:!n,type:r,complete:i},fulfil:a,reject:o}}export{kn as a,Vt as b,vn as c,mn as d,An as e,wn as f,dn as g,bn as i,_n as p,yn as r,x as s,se as w};
