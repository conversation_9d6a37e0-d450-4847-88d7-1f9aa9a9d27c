import{v as M,s as _e,C as we,z as ve,E as be,L as wt,B as Ae}from"./scheduler.CXt6djuF.js";new URL("sveltekit-internal://");function ke(t,n){return t==="/"||n==="ignore"?t:n==="never"?t.endsWith("/")?t.slice(0,-1):t:n==="always"&&!t.endsWith("/")?t+"/":t}function Ee(t){return t.split("%25").map(decodeURI).join("%25")}function Se(t){for(const n in t)t[n]=decodeURIComponent(t[n]);return t}function gt({href:t}){return t.split("#")[0]}const Re=["href","pathname","search","toString","toJSON"];function Ie(t,n,e){const r=new URL(t);Object.defineProperty(r,"searchParams",{value:new Proxy(r.searchParams,{get(a,o){if(o==="get"||o==="getAll"||o==="has")return s=>(e(s),a[o](s));n();const i=Reflect.get(a,o);return typeof i=="function"?i.bind(a):i}}),enumerable:!0,configurable:!0});for(const a of Re)Object.defineProperty(r,a,{get(){return n(),t[a]},enumerable:!0,configurable:!0});return r}const Le="/__data.json",Ue=".html__data.json";function Te(t){return t.endsWith(".html")?t.replace(/\.html$/,Ue):t.replace(/\/$/,"")+Le}function xe(...t){let n=5381;for(const e of t)if(typeof e=="string"){let r=e.length;for(;r;)n=n*33^e.charCodeAt(--r)}else if(ArrayBuffer.isView(e)){const r=new Uint8Array(e.buffer,e.byteOffset,e.byteLength);let a=r.length;for(;a;)n=n*33^r[--a]}else throw new TypeError("value must be a string or TypedArray");return(n>>>0).toString(36)}function Pe(t){const n=atob(t),e=new Uint8Array(n.length);for(let r=0;r<n.length;r++)e[r]=n.charCodeAt(r);return e.buffer}const Kt=window.fetch;window.fetch=(t,n)=>((t instanceof Request?t.method:(n==null?void 0:n.method)||"GET")!=="GET"&&H.delete(Et(t)),Kt(t,n));const H=new Map;function Ce(t,n){const e=Et(t,n),r=document.querySelector(e);if(r!=null&&r.textContent){let{body:a,...o}=JSON.parse(r.textContent);const i=r.getAttribute("data-ttl");return i&&H.set(e,{body:a,init:o,ttl:1e3*Number(i)}),r.getAttribute("data-b64")!==null&&(a=Pe(a)),Promise.resolve(new Response(a,o))}return window.fetch(t,n)}function Ne(t,n,e){if(H.size>0){const r=Et(t,e),a=H.get(r);if(a){if(performance.now()<a.ttl&&["default","force-cache","only-if-cached",void 0].includes(e==null?void 0:e.cache))return new Response(a.body,a.init);H.delete(r)}}return window.fetch(n,e)}function Et(t,n){let r=`script[data-sveltekit-fetched][data-url=${JSON.stringify(t instanceof Request?t.url:t)}]`;if(n!=null&&n.headers||n!=null&&n.body){const a=[];n.headers&&a.push([...new Headers(n.headers)].join(",")),n.body&&(typeof n.body=="string"||ArrayBuffer.isView(n.body))&&a.push(n.body),r+=`[data-hash="${xe(...a)}"]`}return r}const Oe=/^(\[)?(\.\.\.)?(\w+)(?:=(\w+))?(\])?$/;function je(t){const n=[];return{pattern:t==="/"?/^\/$/:new RegExp(`^${De(t).map(r=>{const a=/^\[\.\.\.(\w+)(?:=(\w+))?\]$/.exec(r);if(a)return n.push({name:a[1],matcher:a[2],optional:!1,rest:!0,chained:!0}),"(?:/(.*))?";const o=/^\[\[(\w+)(?:=(\w+))?\]\]$/.exec(r);if(o)return n.push({name:o[1],matcher:o[2],optional:!0,rest:!1,chained:!0}),"(?:/([^/]+))?";if(!r)return;const i=r.split(/\[(.+?)\](?!\])/);return"/"+i.map((c,l)=>{if(l%2){if(c.startsWith("x+"))return mt(String.fromCharCode(parseInt(c.slice(2),16)));if(c.startsWith("u+"))return mt(String.fromCharCode(...c.slice(2).split("-").map(f=>parseInt(f,16))));const d=Oe.exec(c),[,h,g,u,p]=d;return n.push({name:u,matcher:p,optional:!!h,rest:!!g,chained:g?l===1&&i[0]==="":!1}),g?"(.*?)":h?"([^/]*)?":"([^/]+?)"}return mt(c)}).join("")}).join("")}/?$`),params:n}}function $e(t){return!/^\([^)]+\)$/.test(t)}function De(t){return t.slice(1).split("/").filter($e)}function Be(t,n,e){const r={},a=t.slice(1),o=a.filter(s=>s!==void 0);let i=0;for(let s=0;s<n.length;s+=1){const c=n[s];let l=a[s-i];if(c.chained&&c.rest&&i&&(l=a.slice(s-i,s+1).filter(d=>d).join("/"),i=0),l===void 0){c.rest&&(r[c.name]="");continue}if(!c.matcher||e[c.matcher](l)){r[c.name]=l;const d=n[s+1],h=a[s+1];d&&!d.rest&&d.optional&&h&&c.chained&&(i=0),!d&&!h&&Object.keys(r).length===o.length&&(i=0);continue}if(c.optional&&c.chained){i++;continue}return}if(!i)return r}function mt(t){return t.normalize().replace(/[[\]]/g,"\\$&").replace(/%/g,"%25").replace(/\//g,"%2[Ff]").replace(/\?/g,"%3[Ff]").replace(/#/g,"%23").replace(/[.*+?^${}()|\\]/g,"\\$&")}function Fe({nodes:t,server_loads:n,dictionary:e,matchers:r}){const a=new Set(n);return Object.entries(e).map(([s,[c,l,d]])=>{const{pattern:h,params:g}=je(s),u={id:s,exec:p=>{const f=h.exec(p);if(f)return Be(f,g,r)},errors:[1,...d||[]].map(p=>t[p]),layouts:[0,...l||[]].map(i),leaf:o(c)};return u.errors.length=u.layouts.length=Math.max(u.errors.length,u.layouts.length),u});function o(s){const c=s<0;return c&&(s=~s),[c,t[s]]}function i(s){return s===void 0?s:[a.has(s),t[s]]}}function zt(t,n=JSON.parse){try{return n(sessionStorage[t])}catch{}}function Ot(t,n,e=JSON.stringify){const r=e(n);try{sessionStorage[t]=r}catch{}}const j=[];function Ve(t,n){return{subscribe:st(t,n).subscribe}}function st(t,n=M){let e;const r=new Set;function a(s){if(_e(t,s)&&(t=s,e)){const c=!j.length;for(const l of r)l[1](),j.push(l,t);if(c){for(let l=0;l<j.length;l+=2)j[l][0](j[l+1]);j.length=0}}}function o(s){a(s(t))}function i(s,c=M){const l=[s,c];return r.add(l),r.size===1&&(e=n(a,o)||M),s(t),()=>{r.delete(l),r.size===0&&e&&(e(),e=null)}}return{set:a,update:o,subscribe:i}}function mn(t,n,e){const r=!Array.isArray(t),a=r?[t]:t;if(!a.every(Boolean))throw new Error("derived() expects stores as input, got a falsy value");const o=n.length<2;return Ve(e,(i,s)=>{let c=!1;const l=[];let d=0,h=M;const g=()=>{if(d)return;h();const p=n(r?l[0]:l,i,s);o?i(p):h=be(p)?p:M},u=a.map((p,f)=>we(p,m=>{l[f]=m,d&=~(1<<f),c&&g()},()=>{d|=1<<f}));return c=!0,g(),function(){ve(u),h(),c=!1}})}function yn(t){return{subscribe:t.subscribe.bind(t)}}var Mt;const U=((Mt=globalThis.__sveltekit_1qdkplj)==null?void 0:Mt.base)??"";var Ht;const qe=((Ht=globalThis.__sveltekit_1qdkplj)==null?void 0:Ht.assets)??U,Ge="1749346874164",Wt="sveltekit:snapshot",Yt="sveltekit:scroll",Jt="sveltekit:states",Me="sveltekit:pageurl",D="sveltekit:history",z="sveltekit:navigation",tt={tap:1,hover:2,viewport:3,eager:4,off:-1,false:-1},X=location.origin;function St(t){if(t instanceof URL)return t;let n=document.baseURI;if(!n){const e=document.getElementsByTagName("base");n=e.length?e[0].href:document.URL}return new URL(t,n)}function Rt(){return{x:pageXOffset,y:pageYOffset}}function $(t,n){return t.getAttribute(`data-sveltekit-${n}`)}const jt={...tt,"":tt.hover};function Xt(t){let n=t.assignedSlot??t.parentNode;return(n==null?void 0:n.nodeType)===11&&(n=n.host),n}function Zt(t,n){for(;t&&t!==n;){if(t.nodeName.toUpperCase()==="A"&&t.hasAttribute("href"))return t;t=Xt(t)}}function vt(t,n){let e;try{e=new URL(t instanceof SVGAElement?t.href.baseVal:t.href,document.baseURI)}catch{}const r=t instanceof SVGAElement?t.target.baseVal:t.target,a=!e||!!r||it(e,n)||(t.getAttribute("rel")||"").split(/\s+/).includes("external"),o=(e==null?void 0:e.origin)===X&&t.hasAttribute("download");return{url:e,external:a,target:r,download:o}}function et(t){let n=null,e=null,r=null,a=null,o=null,i=null,s=t;for(;s&&s!==document.documentElement;)r===null&&(r=$(s,"preload-code")),a===null&&(a=$(s,"preload-data")),n===null&&(n=$(s,"keepfocus")),e===null&&(e=$(s,"noscroll")),o===null&&(o=$(s,"reload")),i===null&&(i=$(s,"replacestate")),s=Xt(s);function c(l){switch(l){case"":case"true":return!0;case"off":case"false":return!1;default:return}}return{preload_code:jt[r??"off"],preload_data:jt[a??"off"],keepfocus:c(n),noscroll:c(e),reload:c(o),replace_state:c(i)}}function $t(t){const n=st(t);let e=!0;function r(){e=!0,n.update(i=>i)}function a(i){e=!1,n.set(i)}function o(i){let s;return n.subscribe(c=>{(s===void 0||e&&c!==s)&&i(s=c)})}return{notify:r,set:a,subscribe:o}}function He(){const{set:t,subscribe:n}=st(!1);let e;async function r(){clearTimeout(e);try{const a=await fetch(`${qe}/_app/version.json`,{headers:{pragma:"no-cache","cache-control":"no-cache"}});if(!a.ok)return!1;const i=(await a.json()).version!==Ge;return i&&(t(!0),clearTimeout(e)),i}catch{return!1}}return{subscribe:n,check:r}}function it(t,n){return t.origin!==X||!t.pathname.startsWith(n)}function Dt(t){const n=ze(t),e=new ArrayBuffer(n.length),r=new DataView(e);for(let a=0;a<e.byteLength;a++)r.setUint8(a,n.charCodeAt(a));return e}const Ke="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function ze(t){t.length%4===0&&(t=t.replace(/==?$/,""));let n="",e=0,r=0;for(let a=0;a<t.length;a++)e<<=6,e|=Ke.indexOf(t[a]),r+=6,r===24&&(n+=String.fromCharCode((e&16711680)>>16),n+=String.fromCharCode((e&65280)>>8),n+=String.fromCharCode(e&255),e=r=0);return r===12?(e>>=4,n+=String.fromCharCode(e)):r===18&&(e>>=2,n+=String.fromCharCode((e&65280)>>8),n+=String.fromCharCode(e&255)),n}const We=-1,Ye=-2,Je=-3,Xe=-4,Ze=-5,Qe=-6;function _n(t,n){return Qt(JSON.parse(t),n)}function Qt(t,n){if(typeof t=="number")return a(t,!0);if(!Array.isArray(t)||t.length===0)throw new Error("Invalid input");const e=t,r=Array(e.length);function a(o,i=!1){if(o===We)return;if(o===Je)return NaN;if(o===Xe)return 1/0;if(o===Ze)return-1/0;if(o===Qe)return-0;if(i)throw new Error("Invalid input");if(o in r)return r[o];const s=e[o];if(!s||typeof s!="object")r[o]=s;else if(Array.isArray(s))if(typeof s[0]=="string"){const c=s[0],l=n==null?void 0:n[c];if(l)return r[o]=l(a(s[1]));switch(c){case"Date":r[o]=new Date(s[1]);break;case"Set":const d=new Set;r[o]=d;for(let u=1;u<s.length;u+=1)d.add(a(s[u]));break;case"Map":const h=new Map;r[o]=h;for(let u=1;u<s.length;u+=2)h.set(a(s[u]),a(s[u+1]));break;case"RegExp":r[o]=new RegExp(s[1],s[2]);break;case"Object":r[o]=Object(s[1]);break;case"BigInt":r[o]=BigInt(s[1]);break;case"null":const g=Object.create(null);r[o]=g;for(let u=1;u<s.length;u+=2)g[s[u]]=a(s[u+1]);break;case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":{const u=globalThis[c],p=s[1],f=Dt(p),m=new u(f);r[o]=m;break}case"ArrayBuffer":{const u=s[1],p=Dt(u);r[o]=p;break}default:throw new Error(`Unknown type ${c}`)}}else{const c=new Array(s.length);r[o]=c;for(let l=0;l<s.length;l+=1){const d=s[l];d!==Ye&&(c[l]=a(d))}}else{const c={};r[o]=c;for(const l in s){const d=s[l];c[l]=a(d)}}return r[o]}return a(0)}const te=new Set(["load","prerender","csr","ssr","trailingSlash","config"]);[...te];const tn=new Set([...te]);[...tn];function en(t){return t.filter(n=>n!=null)}class ct{constructor(n,e){this.status=n,typeof e=="string"?this.body={message:e}:e?this.body=e:this.body={message:`Error: ${n}`}}toString(){return JSON.stringify(this.body)}}class ee{constructor(n,e){this.status=n,this.location=e}}class It extends Error{constructor(n,e,r){super(r),this.status=n,this.text=e}}const nn="x-sveltekit-invalidated",rn="x-sveltekit-trailing-slash";function nt(t){return t instanceof ct||t instanceof It?t.status:500}function an(t){return t instanceof It?t.text:"Internal Error"}const O=zt(Yt)??{},W=zt(Wt)??{},x={url:$t({}),page:$t({}),navigating:st(null),updated:He()};function Lt(t){O[t]=Rt()}function on(t,n){let e=t+1;for(;O[e];)delete O[e],e+=1;for(e=n+1;W[e];)delete W[e],e+=1}function F(t){return location.href=t.href,new Promise(()=>{})}async function ne(){if("serviceWorker"in navigator){const t=await navigator.serviceWorker.getRegistration(U||"/");t&&await t.update()}}function Bt(){}let lt,bt,rt,T,At,q;const re=[],at=[];let I=null;const ae=[],sn=[];let C=[],_={branch:[],error:null,url:null},Ut=!1,ot=!1,Ft=!0,Y=!1,G=!1,oe=!1,ft=!1,N,E,L,S,V;const K=new Set;let yt;async function wn(t,n,e){var a,o;document.URL!==location.href&&(location.href=location.href),q=t,lt=Fe(t),T=document.documentElement,At=n,bt=t.nodes[0],rt=t.nodes[1],bt(),rt(),E=(a=history.state)==null?void 0:a[D],L=(o=history.state)==null?void 0:o[z],E||(E=L=Date.now(),history.replaceState({...history.state,[D]:E,[z]:L},""));const r=O[E];r&&(history.scrollRestoration="manual",scrollTo(r.x,r.y)),e?await pn(At,e):dn(location.href,{replaceState:!0}),hn()}async function cn(){if(await(yt||(yt=Promise.resolve())),!yt)return;yt=null;const t=Z(_.url,!0);I=null;const n=V={},e=t&&await Pt(t);if(!(!e||n!==V)){if(e.type==="redirect")return ut(new URL(e.location,_.url).href,{},1,n);e.props.page&&(S=e.props.page),_=e.state,se(),N.$set(e.props)}}function se(){re.length=0,ft=!1}function ie(t){at.some(n=>n==null?void 0:n.snapshot)&&(W[t]=at.map(n=>{var e;return(e=n==null?void 0:n.snapshot)==null?void 0:e.capture()}))}function ce(t){var n;(n=W[t])==null||n.forEach((e,r)=>{var a,o;(o=(a=at[r])==null?void 0:a.snapshot)==null||o.restore(e)})}function Vt(){Lt(E),Ot(Yt,O),ie(L),Ot(Wt,W)}async function ut(t,n,e,r){return Q({type:"goto",url:St(t),keepfocus:n.keepFocus,noscroll:n.noScroll,replace_state:n.replaceState,state:n.state,redirect_count:e,nav_token:r,accept:()=>{n.invalidateAll&&(ft=!0)}})}async function le(t){if(t.id!==(I==null?void 0:I.id)){const n={};K.add(n),I={id:t.id,token:n,promise:Pt({...t,preload:n}).then(e=>(K.delete(n),e.type==="loaded"&&e.state.error&&(I=null),e))}}return I.promise}async function _t(t){const n=lt.find(e=>e.exec(de(t)));n&&await Promise.all([...n.layouts,n.leaf].map(e=>e==null?void 0:e[1]()))}function fe(t,n,e){var o;_=t.state;const r=document.querySelector("style[data-sveltekit]");r&&r.remove(),S=t.props.page,N=new q.root({target:n,props:{...t.props,stores:x,components:at},hydrate:e,sync:!1}),ce(L);const a={from:null,to:{params:_.params,route:{id:((o=_.route)==null?void 0:o.id)??null},url:new URL(location.href)},willUnload:!1,type:"enter",complete:Promise.resolve()};C.forEach(i=>i(a)),ot=!0}function J({url:t,params:n,branch:e,status:r,error:a,route:o,form:i}){let s="never";if(U&&(t.pathname===U||t.pathname===U+"/"))s="always";else for(const u of e)(u==null?void 0:u.slash)!==void 0&&(s=u.slash);t.pathname=ke(t.pathname,s),t.search=t.search;const c={type:"loaded",state:{url:t,params:n,branch:e,error:a,route:o},props:{constructors:en(e).map(u=>u.node.component),page:S}};i!==void 0&&(c.props.form=i);let l={},d=!S,h=0;for(let u=0;u<Math.max(e.length,_.branch.length);u+=1){const p=e[u],f=_.branch[u];(p==null?void 0:p.data)!==(f==null?void 0:f.data)&&(d=!0),p&&(l={...l,...p.data},d&&(c.props[`data_${h}`]=l),h+=1)}return(!_.url||t.href!==_.url.href||_.error!==a||i!==void 0&&i!==S.form||d)&&(c.props.page={error:a,params:n,route:{id:(o==null?void 0:o.id)??null},state:{},status:r,url:new URL(t),form:i??null,data:d?l:S.data}),c}async function Tt({loader:t,parent:n,url:e,params:r,route:a,server_data_node:o}){var d,h,g;let i=null,s=!0;const c={dependencies:new Set,params:new Set,parent:!1,route:!1,url:!1,search_params:new Set},l=await t();if((d=l.universal)!=null&&d.load){let u=function(...f){for(const m of f){const{href:b}=new URL(m,e);c.dependencies.add(b)}};const p={route:new Proxy(a,{get:(f,m)=>(s&&(c.route=!0),f[m])}),params:new Proxy(r,{get:(f,m)=>(s&&c.params.add(m),f[m])}),data:(o==null?void 0:o.data)??null,url:Ie(e,()=>{s&&(c.url=!0)},f=>{s&&c.search_params.add(f)}),async fetch(f,m){let b;f instanceof Request?(b=f.url,m={body:f.method==="GET"||f.method==="HEAD"?void 0:await f.blob(),cache:f.cache,credentials:f.credentials,headers:[...f.headers].length?f.headers:void 0,integrity:f.integrity,keepalive:f.keepalive,method:f.method,mode:f.mode,redirect:f.redirect,referrer:f.referrer,referrerPolicy:f.referrerPolicy,signal:f.signal,...m}):b=f;const R=new URL(b,e);return s&&u(R.href),R.origin===e.origin&&(b=R.href.slice(e.origin.length)),ot?Ne(b,R.href,m):Ce(b,m)},setHeaders:()=>{},depends:u,parent(){return s&&(c.parent=!0),n()},untrack(f){s=!1;try{return f()}finally{s=!0}}};i=await l.universal.load.call(null,p)??null}return{node:l,loader:t,server:o,universal:(h=l.universal)!=null&&h.load?{type:"data",data:i,uses:c}:null,data:i??(o==null?void 0:o.data)??null,slash:((g=l.universal)==null?void 0:g.trailingSlash)??(o==null?void 0:o.slash)}}function qt(t,n,e,r,a,o){if(ft)return!0;if(!a)return!1;if(a.parent&&t||a.route&&n||a.url&&e)return!0;for(const i of a.search_params)if(r.has(i))return!0;for(const i of a.params)if(o[i]!==_.params[i])return!0;for(const i of a.dependencies)if(re.some(s=>s(new URL(i))))return!0;return!1}function xt(t,n){return(t==null?void 0:t.type)==="data"?t:(t==null?void 0:t.type)==="skip"?n??null:null}function ln(t,n){if(!t)return new Set(n.searchParams.keys());const e=new Set([...t.searchParams.keys(),...n.searchParams.keys()]);for(const r of e){const a=t.searchParams.getAll(r),o=n.searchParams.getAll(r);a.every(i=>o.includes(i))&&o.every(i=>a.includes(i))&&e.delete(r)}return e}function Gt({error:t,url:n,route:e,params:r}){return{type:"loaded",state:{error:t,url:n,route:e,params:r,branch:[]},props:{page:S,constructors:[]}}}async function Pt({id:t,invalidating:n,url:e,params:r,route:a,preload:o}){if((I==null?void 0:I.id)===t)return K.delete(I.token),I.promise;const{errors:i,layouts:s,leaf:c}=a,l=[...s,c];i.forEach(y=>y==null?void 0:y().catch(()=>{})),l.forEach(y=>y==null?void 0:y[1]().catch(()=>{}));let d=null;const h=_.url?t!==_.url.pathname+_.url.search:!1,g=_.route?a.id!==_.route.id:!1,u=ln(_.url,e);let p=!1;const f=l.map((y,v)=>{var P;const A=_.branch[v],k=!!(y!=null&&y[0])&&((A==null?void 0:A.loader)!==y[1]||qt(p,g,h,u,(P=A.server)==null?void 0:P.uses,r));return k&&(p=!0),k});if(f.some(Boolean)){try{d=await ge(e,f)}catch(y){const v=await B(y,{url:e,params:r,route:{id:t}});return K.has(o)?Gt({error:v,url:e,params:r,route:a}):dt({status:nt(y),error:v,url:e,route:a})}if(d.type==="redirect")return d}const m=d==null?void 0:d.nodes;let b=!1;const R=l.map(async(y,v)=>{var ht;if(!y)return;const A=_.branch[v],k=m==null?void 0:m[v];if((!k||k.type==="skip")&&y[1]===(A==null?void 0:A.loader)&&!qt(b,g,h,u,(ht=A.universal)==null?void 0:ht.uses,r))return A;if(b=!0,(k==null?void 0:k.type)==="error")throw k;return Tt({loader:y[1],url:e,params:r,route:a,parent:async()=>{var Nt;const Ct={};for(let pt=0;pt<v;pt+=1)Object.assign(Ct,(Nt=await R[pt])==null?void 0:Nt.data);return Ct},server_data_node:xt(k===void 0&&y[0]?{type:"skip"}:k??null,y[0]?A==null?void 0:A.server:void 0)})});for(const y of R)y.catch(()=>{});const w=[];for(let y=0;y<l.length;y+=1)if(l[y])try{w.push(await R[y])}catch(v){if(v instanceof ee)return{type:"redirect",location:v.location};if(K.has(o))return Gt({error:await B(v,{params:r,url:e,route:{id:a.id}}),url:e,params:r,route:a});let A=nt(v),k;if(m!=null&&m.includes(v))A=v.status??A,k=v.error;else if(v instanceof ct)k=v.body;else{if(await x.updated.check())return await ne(),await F(e);k=await B(v,{params:r,url:e,route:{id:a.id}})}const P=await ue(y,w,i);return P?J({url:e,params:r,branch:w.slice(0,P.idx).concat(P.node),status:A,error:k,route:a}):await pe(e,{id:a.id},k,A)}else w.push(void 0);return J({url:e,params:r,branch:w,status:200,error:null,route:a,form:n?void 0:null})}async function ue(t,n,e){for(;t--;)if(e[t]){let r=t;for(;!n[r];)r-=1;try{return{idx:r+1,node:{node:await e[t](),loader:e[t],data:{},server:null,universal:null}}}catch{continue}}}async function dt({status:t,error:n,url:e,route:r}){const a={};let o=null;if(q.server_loads[0]===0)try{const l=await ge(e,[!0]);if(l.type!=="data"||l.nodes[0]&&l.nodes[0].type!=="data")throw 0;o=l.nodes[0]??null}catch{(e.origin!==X||e.pathname!==location.pathname||Ut)&&await F(e)}const s=await Tt({loader:bt,url:e,params:a,route:r,parent:()=>Promise.resolve({}),server_data_node:xt(o)}),c={node:await rt(),loader:rt,universal:null,server:null,data:null};return J({url:e,params:a,branch:[s,c],status:t,error:n,route:null})}function Z(t,n){if(!t||it(t,U))return;let e;try{e=q.hooks.reroute({url:new URL(t)})??t.pathname}catch{return}const r=de(e);for(const a of lt){const o=a.exec(r);if(o)return{id:t.pathname+t.search,invalidating:n,route:a,params:Se(o),url:t}}}function de(t){return Ee(t.slice(U.length)||"/")}function he({url:t,type:n,intent:e,delta:r}){let a=!1;const o=ye(_,e,t,n);r!==void 0&&(o.navigation.delta=r);const i={...o.navigation,cancel:()=>{a=!0,o.reject(new Error("navigation cancelled"))}};return Y||ae.forEach(s=>s(i)),a?null:o}async function Q({type:t,url:n,popped:e,keepfocus:r,noscroll:a,replace_state:o,state:i={},redirect_count:s=0,nav_token:c={},accept:l=Bt,block:d=Bt}){const h=Z(n,!1),g=he({url:n,type:t,delta:e==null?void 0:e.delta,intent:h});if(!g){d();return}const u=E,p=L;l(),Y=!0,ot&&x.navigating.set(g.navigation),V=c;let f=h&&await Pt(h);if(!f){if(it(n,U))return await F(n);f=await pe(n,{id:null},await B(new It(404,"Not Found",`Not found: ${n.pathname}`),{url:n,params:{},route:{id:null}}),404)}if(n=(h==null?void 0:h.url)||n,V!==c)return g.reject(new Error("navigation aborted")),!1;if(f.type==="redirect")if(s>=20)f=await dt({status:500,error:await B(new Error("Redirect loop"),{url:n,params:{},route:{id:null}}),url:n,route:{id:null}});else return ut(new URL(f.location,n).href,{},s+1,c),!1;else f.props.page.status>=400&&await x.updated.check()&&(await ne(),await F(n));if(se(),Lt(u),ie(p),f.props.page.url.pathname!==n.pathname&&(n.pathname=f.props.page.url.pathname),i=e?e.state:i,!e){const w=o?0:1,y={[D]:E+=w,[z]:L+=w,[Jt]:i};(o?history.replaceState:history.pushState).call(history,y,"",n),o||on(E,L)}if(I=null,f.props.page.state=i,ot){_=f.state,f.props.page&&(f.props.page.url=n);const w=(await Promise.all(sn.map(y=>y(g.navigation)))).filter(y=>typeof y=="function");if(w.length>0){let y=function(){C=C.filter(v=>!w.includes(v))};w.push(y),C.push(...w)}N.$set(f.props),oe=!0}else fe(f,At,!1);const{activeElement:m}=document;await wt();const b=e?e.scroll:a?Rt():null;if(Ft){const w=n.hash&&document.getElementById(decodeURIComponent(n.hash.slice(1)));b?scrollTo(b.x,b.y):w?w.scrollIntoView():scrollTo(0,0)}const R=document.activeElement!==m&&document.activeElement!==document.body;!r&&!R&&kt(),Ft=!0,f.props.page&&(S=f.props.page),Y=!1,t==="popstate"&&ce(L),g.fulfil(void 0),C.forEach(w=>w(g.navigation)),x.navigating.set(null)}async function pe(t,n,e,r){return t.origin===X&&t.pathname===location.pathname&&!Ut?await dt({status:r,error:e,url:t,route:n}):await F(t)}function fn(){let t;T.addEventListener("mousemove",o=>{const i=o.target;clearTimeout(t),t=setTimeout(()=>{r(i,2)},20)});function n(o){o.defaultPrevented||r(o.composedPath()[0],1)}T.addEventListener("mousedown",n),T.addEventListener("touchstart",n,{passive:!0});const e=new IntersectionObserver(o=>{for(const i of o)i.isIntersecting&&(_t(i.target.href),e.unobserve(i.target))},{threshold:0});function r(o,i){const s=Zt(o,T);if(!s)return;const{url:c,external:l,download:d}=vt(s,U);if(l||d)return;const h=et(s),g=c&&_.url.pathname+_.url.search===c.pathname+c.search;if(!h.reload&&!g)if(i<=h.preload_data){const u=Z(c,!1);u&&le(u)}else i<=h.preload_code&&_t(c.pathname)}function a(){e.disconnect();for(const o of T.querySelectorAll("a")){const{url:i,external:s,download:c}=vt(o,U);if(s||c)continue;const l=et(o);l.reload||(l.preload_code===tt.viewport&&e.observe(o),l.preload_code===tt.eager&&_t(i.pathname))}}C.push(a),a()}function B(t,n){if(t instanceof ct)return t.body;const e=nt(t),r=an(t);return q.hooks.handleError({error:t,event:n,status:e,message:r})??{message:r}}function un(t,n){Ae(()=>(t.push(n),()=>{const e=t.indexOf(n);t.splice(e,1)}))}function vn(t){un(C,t)}function dn(t,n={}){return t=St(t),t.origin!==X?Promise.reject(new Error("goto: invalid URL")):ut(t,n,0)}function bn(){return ft=!0,cn()}async function An(t){const n=St(t),e=Z(n,!1);if(!e)throw new Error(`Attempted to preload a URL that does not belong to this app: ${n}`);const r=await le(e);if(r.type==="redirect")return{type:r.type,location:r.location};const{status:a,data:o}=r.props.page??S;return{type:r.type,status:a,data:o}}async function kn(t){if(t.type==="error"){const n=new URL(location.href),{branch:e,route:r}=_;if(!r)return;const a=await ue(_.branch.length,e,r.errors);if(a){const o=J({url:n,params:_.params,branch:e.slice(0,a.idx).concat(a.node),status:t.status??500,error:t.error,route:r});_=o.state,N.$set(o.props),wt().then(kt)}}else t.type==="redirect"?ut(t.location,{invalidateAll:!0},0):(N.$set({form:null,page:{...S,form:t.data,status:t.status}}),await wt(),N.$set({form:t.data}),t.type==="success"&&kt())}function hn(){var n;history.scrollRestoration="manual",addEventListener("beforeunload",e=>{let r=!1;if(Vt(),!Y){const a=ye(_,void 0,null,"leave"),o={...a.navigation,cancel:()=>{r=!0,a.reject(new Error("navigation cancelled"))}};ae.forEach(i=>i(o))}r?(e.preventDefault(),e.returnValue=""):history.scrollRestoration="auto"}),addEventListener("visibilitychange",()=>{document.visibilityState==="hidden"&&Vt()}),(n=navigator.connection)!=null&&n.saveData||fn(),T.addEventListener("click",async e=>{if(e.button||e.which!==1||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.defaultPrevented)return;const r=Zt(e.composedPath()[0],T);if(!r)return;const{url:a,external:o,target:i,download:s}=vt(r,U);if(!a)return;if(i==="_parent"||i==="_top"){if(window.parent!==window)return}else if(i&&i!=="_self")return;const c=et(r);if(!(r instanceof SVGAElement)&&a.protocol!==location.protocol&&!(a.protocol==="https:"||a.protocol==="http:")||s)return;const[d,h]=a.href.split("#"),g=d===gt(location);if(o||c.reload&&(!g||!h)){he({url:a,type:"link"})?Y=!0:e.preventDefault();return}if(h!==void 0&&g){const[,u]=_.url.href.split("#");if(u===h){if(e.preventDefault(),h===""||h==="top"&&r.ownerDocument.getElementById("top")===null)window.scrollTo({top:0});else{const p=r.ownerDocument.getElementById(decodeURIComponent(h));p&&(p.scrollIntoView(),p.focus())}return}if(G=!0,Lt(E),t(a),!c.replace_state)return;G=!1}e.preventDefault(),await new Promise(u=>{requestAnimationFrame(()=>{setTimeout(u,0)}),setTimeout(u,100)}),Q({type:"link",url:a,keepfocus:c.keepfocus,noscroll:c.noscroll,replace_state:c.replace_state??a.href===location.href})}),T.addEventListener("submit",e=>{if(e.defaultPrevented)return;const r=HTMLFormElement.prototype.cloneNode.call(e.target),a=e.submitter;if(((a==null?void 0:a.formTarget)||r.target)==="_blank"||((a==null?void 0:a.formMethod)||r.method)!=="get")return;const s=new URL((a==null?void 0:a.hasAttribute("formaction"))&&(a==null?void 0:a.formAction)||r.action);if(it(s,U))return;const c=e.target,l=et(c);if(l.reload)return;e.preventDefault(),e.stopPropagation();const d=new FormData(c),h=a==null?void 0:a.getAttribute("name");h&&d.append(h,(a==null?void 0:a.getAttribute("value"))??""),s.search=new URLSearchParams(d).toString(),Q({type:"form",url:s,keepfocus:l.keepfocus,noscroll:l.noscroll,replace_state:l.replace_state??s.href===location.href})}),addEventListener("popstate",async e=>{var r;if((r=e.state)!=null&&r[D]){const a=e.state[D];if(V={},a===E)return;const o=O[a],i=e.state[Jt]??{},s=new URL(e.state[Me]??location.href),c=e.state[z],l=gt(location)===gt(_.url);if(c===L&&(oe||l)){t(s),O[E]=Rt(),o&&scrollTo(o.x,o.y),i!==S.state&&(S={...S,state:i},N.$set({page:S})),E=a;return}const h=a-E;await Q({type:"popstate",url:s,popped:{state:i,scroll:o,delta:h},accept:()=>{E=a,L=c},block:()=>{history.go(-h)},nav_token:V})}else if(!G){const a=new URL(location.href);t(a)}}),addEventListener("hashchange",()=>{G&&(G=!1,history.replaceState({...history.state,[D]:++E,[z]:L},"",location.href))});for(const e of document.querySelectorAll("link"))e.rel==="icon"&&(e.href=e.href);addEventListener("pageshow",e=>{e.persisted&&x.navigating.set(null)});function t(e){_.url=e,x.page.set({...S,url:e}),x.page.notify()}}async function pn(t,{status:n=200,error:e,node_ids:r,params:a,route:o,data:i,form:s}){Ut=!0;const c=new URL(location.href);({params:a={},route:o={id:null}}=Z(c,!1)||{});let l;try{const d=r.map(async(u,p)=>{const f=i[p];return f!=null&&f.uses&&(f.uses=me(f.uses)),Tt({loader:q.nodes[u],url:c,params:a,route:o,parent:async()=>{const m={};for(let b=0;b<p;b+=1)Object.assign(m,(await d[b]).data);return m},server_data_node:xt(f)})}),h=await Promise.all(d),g=lt.find(({id:u})=>u===o.id);if(g){const u=g.layouts;for(let p=0;p<u.length;p++)u[p]||h.splice(p,0,void 0)}l=J({url:c,params:a,branch:h,status:n,error:e,form:s,route:g??null})}catch(d){if(d instanceof ee){await F(new URL(d.location,location.href));return}l=await dt({status:nt(d),error:await B(d,{url:c,params:a,route:o}),url:c,route:o})}l.props.page&&(l.props.page.state={}),fe(l,t,!0)}async function ge(t,n){var a;const e=new URL(t);e.pathname=Te(t.pathname),t.pathname.endsWith("/")&&e.searchParams.append(rn,"1"),e.searchParams.append(nn,n.map(o=>o?"1":"0").join(""));const r=await Kt(e.href);if(!r.ok){let o;throw(a=r.headers.get("content-type"))!=null&&a.includes("application/json")?o=await r.json():r.status===404?o="Not Found":r.status===500&&(o="Internal Error"),new ct(r.status,o)}return new Promise(async o=>{var h;const i=new Map,s=r.body.getReader(),c=new TextDecoder;function l(g){return Qt(g,{Promise:u=>new Promise((p,f)=>{i.set(u,{fulfil:p,reject:f})})})}let d="";for(;;){const{done:g,value:u}=await s.read();if(g&&!d)break;for(d+=!u&&d?`
`:c.decode(u,{stream:!0});;){const p=d.indexOf(`
`);if(p===-1)break;const f=JSON.parse(d.slice(0,p));if(d=d.slice(p+1),f.type==="redirect")return o(f);if(f.type==="data")(h=f.nodes)==null||h.forEach(m=>{(m==null?void 0:m.type)==="data"&&(m.uses=me(m.uses),m.data=l(m.data))}),o(f);else if(f.type==="chunk"){const{id:m,data:b,error:R}=f,w=i.get(m);i.delete(m),R?w.reject(l(R)):w.fulfil(l(b))}}}})}function me(t){return{dependencies:new Set((t==null?void 0:t.dependencies)??[]),params:new Set((t==null?void 0:t.params)??[]),parent:!!(t!=null&&t.parent),route:!!(t!=null&&t.route),url:!!(t!=null&&t.url),search_params:new Set((t==null?void 0:t.search_params)??[])}}function kt(){const t=document.querySelector("[autofocus]");if(t)t.focus();else{const n=document.body,e=n.getAttribute("tabindex");n.tabIndex=-1,n.focus({preventScroll:!0,focusVisible:!1}),e!==null?n.setAttribute("tabindex",e):n.removeAttribute("tabindex");const r=getSelection();if(r&&r.type!=="None"){const a=[];for(let o=0;o<r.rangeCount;o+=1)a.push(r.getRangeAt(o));setTimeout(()=>{if(r.rangeCount===a.length){for(let o=0;o<r.rangeCount;o+=1){const i=a[o],s=r.getRangeAt(o);if(i.commonAncestorContainer!==s.commonAncestorContainer||i.startContainer!==s.startContainer||i.endContainer!==s.endContainer||i.startOffset!==s.startOffset||i.endOffset!==s.endOffset)return}r.removeAllRanges()}})}}}function ye(t,n,e,r){var c,l;let a,o;const i=new Promise((d,h)=>{a=d,o=h});return i.catch(()=>{}),{navigation:{from:{params:t.params,route:{id:((c=t.route)==null?void 0:c.id)??null},url:t.url},to:e&&{params:(n==null?void 0:n.params)??null,route:{id:((l=n==null?void 0:n.route)==null?void 0:l.id)??null},url:e},willUnload:!n,type:r,complete:i},fulfil:a,reject:o}}export{kn as a,Ve as b,vn as c,mn as d,An as e,wn as f,dn as g,bn as i,_n as p,yn as r,x as s,st as w};
