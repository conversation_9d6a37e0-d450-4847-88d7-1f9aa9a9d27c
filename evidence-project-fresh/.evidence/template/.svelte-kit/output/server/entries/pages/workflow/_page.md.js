import{s as M,b as U}from"../../../chunks/utils.js";import{c as q,o as u,s as R,h as n,i as L,v as d,b as o}from"../../../chunks/ssr.js";import"dequal";import{P as F}from"../../../chunks/VennDiagram.svelte_svelte_type_style_lang.js";import{w as N}from"../../../chunks/index2.js";import"clsx";import"chroma-js";import{a as O,Q as G,p as $}from"../../../chunks/Query.js";import{p as Q,h as T}from"../../../chunks/profile.js";import{a as S}from"../../../chunks/index6.js";import{p as z}from"../../../chunks/stores.js";import{i as H,j,C as g}from"../../../chunks/CodeBlock.js";import"ssf";import"@tidyjs/tidy";import"deep-object-diff";import"../../../chunks/index5.js";import"@uwdata/mosaic-sql";import"export-to-csv";import"echarts";import"@evidence-dev/universal-sql/client-duckdb";import"yaml";import"@astronautlabs/jsonpath";import"tua-body-scroll-lock";import"lodash/merge.js";import{Q as W}from"../../../chunks/QueryViewer.js";import"prismjs";import"debounce";import"downloadjs";import"echarts-stat";const P={code:".domo-info.svelte-10vyee7.svelte-10vyee7{margin:20px 0;padding:15px;border:2px solid #00d4aa;border-radius:8px;background-color:#f0fdf9;color:#065f46}.domo-info.svelte-10vyee7 h4.svelte-10vyee7{margin:0 0 10px 0;color:#047857}.dev-info.svelte-10vyee7.svelte-10vyee7{margin:20px 0;padding:15px;border:2px solid #f59e0b;border-radius:8px;background-color:#fffbeb;color:#92400e}.dev-info.svelte-10vyee7 h4.svelte-10vyee7{margin:0 0 10px 0;color:#d97706}.loaded-datasets.svelte-10vyee7.svelte-10vyee7{margin:30px 0;padding:20px;border:1px solid #e5e7eb;border-radius:8px;background-color:#f9fafb}.dataset-card.svelte-10vyee7.svelte-10vyee7{margin:15px 0;padding:15px;border:1px solid #d1d5db;border-radius:6px;background-color:white;box-shadow:0 1px 3px rgba(0, 0, 0, 0.1)}.dataset-card.svelte-10vyee7 h4.svelte-10vyee7{margin:0 0 10px 0;color:#1f2937}.dataset-card.svelte-10vyee7 p.svelte-10vyee7{margin:5px 0;color:#6b7280}.no-datasets.svelte-10vyee7.svelte-10vyee7{margin:30px 0;padding:40px;text-align:center;border:2px dashed #d1d5db;border-radius:8px;color:#6b7280}.no-datasets.svelte-10vyee7 p.svelte-10vyee7{margin:0;font-style:italic}.workflow-picker.svelte-10vyee7.svelte-10vyee7{max-width:800px;margin:0 auto;padding:20px;font-family:-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif}.picker-header.svelte-10vyee7.svelte-10vyee7{text-align:center;margin-bottom:30px}.picker-header.svelte-10vyee7 h3.svelte-10vyee7{color:#1f2937;margin-bottom:8px}.picker-header.svelte-10vyee7 p.svelte-10vyee7{color:#6b7280;margin:0}.workflow-step.svelte-10vyee7.svelte-10vyee7{margin:25px 0;padding:20px;border:1px solid #e5e7eb;border-radius:8px;background-color:#f9fafb}.workflow-step.svelte-10vyee7 label.svelte-10vyee7{display:block;margin-bottom:8px;font-weight:600;color:#374151}.dataset-dropdown.svelte-10vyee7.svelte-10vyee7,.workflow-step.svelte-10vyee7 input.svelte-10vyee7,.workflow-step.svelte-10vyee7 select.svelte-10vyee7{width:100%;padding:10px 12px;border:1px solid #d1d5db;border-radius:6px;font-size:14px;background-color:white}.dataset-dropdown.svelte-10vyee7.svelte-10vyee7:focus,.workflow-step.svelte-10vyee7 input.svelte-10vyee7:focus,.workflow-step.svelte-10vyee7 select.svelte-10vyee7:focus{outline:none;border-color:#3b82f6;box-shadow:0 0 0 3px rgba(59, 130, 246, 0.1)}.dataset-preview.svelte-10vyee7.svelte-10vyee7{margin-top:20px;padding:20px;border:1px solid #e5e7eb;border-radius:8px;background-color:white}.dataset-info.svelte-10vyee7.svelte-10vyee7{display:grid;grid-template-columns:repeat(auto-fit, minmax(200px, 1fr));gap:15px;margin-bottom:20px}.config-grid.svelte-10vyee7.svelte-10vyee7{display:grid;grid-template-columns:repeat(auto-fit, minmax(250px, 1fr));gap:20px}.workflow-actions.svelte-10vyee7.svelte-10vyee7{text-align:center;margin:30px 0}.btn.svelte-10vyee7.svelte-10vyee7{padding:12px 24px;border:none;border-radius:6px;font-size:14px;font-weight:600;cursor:pointer;transition:all 0.2s;margin:0 5px}.btn.svelte-10vyee7.svelte-10vyee7:disabled{opacity:0.6;cursor:not-allowed}.btn-primary.svelte-10vyee7.svelte-10vyee7{background-color:#3b82f6;color:white}.btn-primary.svelte-10vyee7.svelte-10vyee7:hover:not(:disabled){background-color:#2563eb}.btn-secondary.svelte-10vyee7.svelte-10vyee7{background-color:#f3f4f6;color:#374151;border:1px solid #d1d5db}.btn-secondary.svelte-10vyee7.svelte-10vyee7:hover:not(:disabled){background-color:#e5e7eb}.loading-overlay.svelte-10vyee7.svelte-10vyee7{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0, 0, 0, 0.5);display:flex;flex-direction:column;align-items:center;justify-content:center;z-index:1000}.loading-spinner.svelte-10vyee7.svelte-10vyee7{width:40px;height:40px;border:4px solid #f3f3f3;border-top:4px solid #3b82f6;border-radius:50%;animation:svelte-10vyee7-spin 1s linear infinite;margin-bottom:16px}.loading-overlay.svelte-10vyee7 p.svelte-10vyee7{color:white;font-size:16px;margin:0}@keyframes svelte-10vyee7-spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}.preview-actions.svelte-10vyee7.svelte-10vyee7{margin:15px 0}.data-preview.svelte-10vyee7.svelte-10vyee7{margin-top:20px;max-height:300px;overflow:auto}",map:`{"version":3,"file":"+page.md","sources":["+page.md"],"sourcesContent":["\\n<!-- \\n    MDSvex comes in handy here because it takes frontmatter and shoves it into the metadata object.\\n    This means that all we need to do is build out the expected page metadata\\n-->\\n<!-- Show title as h1 if defined, and not hidden -->\\n{#if typeof metadata !== \\"undefined\\" && (metadata.title || metadata.og?.title) && metadata.hide_title !== true}\\n<h1 class=\\"title\\">{metadata.title ?? metadata.og?.title}</h1>\\n{/if}\\n<svelte:head>\\n<!-- Title has a default case; so we need to handle it in a special way -->\\n{#if typeof metadata !== \\"undefined\\" && (metadata.title || metadata.og?.title)}\\n<title>{metadata.title ?? metadata.og?.title}</title>\\n<meta property=\\"og:title\\" content={metadata.og?.title ?? metadata.title} />\\n<meta name=\\"twitter:title\\" content={metadata.og?.title ?? metadata.title} />\\n{:else}\\n<!-- EITHER there is no metadata, or there is no specified style -->\\n<title>Evidence</title>\\n{/if}\\n\\n<!-- default twitter cardtags -->\\n<meta name=\\"twitter:card\\" content=\\"summary_large_image\\" />\\n<meta name=\\"twitter:site\\" content=\\"@evidence_dev\\" />\\n\\n{#if typeof metadata === \\"object\\"}\\n{#if metadata.description || metadata.og?.description}\\n  <meta\\n    name=\\"description\\"\\n    content={metadata.description ?? metadata.og?.description}\\n  />\\n  <meta\\n    property=\\"og:description\\"\\n    content={metadata.og?.description ?? metadata.description}\\n  />\\n  <meta\\n    name=\\"twitter:description\\"\\n    content={metadata.og?.description ?? metadata.description}\\n  />\\n{/if}\\n{#if metadata.og?.image}\\n  <meta property=\\"og:image\\" content={addBasePath(metadata.og?.image)} />\\n  <meta name=\\"twitter:image\\" content={addBasePath(metadata.og?.image)} />\\n{/if}\\n{/if}\\n</svelte:head>\\n<script context=\\"module\\">\\n\\tconst metadata = {\\"title\\":\\"Domo Dataset Workflow\\"};\\n\\tconst { title } = metadata; <\/script>\\n<script>\\nimport { CodeBlock } from '@evidence-dev/core-components'\\nimport { QueryViewer } from '@evidence-dev/core-components'\\nimport { addBasePath } from \\"@evidence-dev/sdk/utils/svelte\\";\\n\\t\\timport { pageHasQueries, routeHash, toasts } from '@evidence-dev/component-utilities/stores';\\nimport { fmt } from '@evidence-dev/component-utilities/formatting';\\nimport { CUSTOM_FORMATTING_SETTINGS_CONTEXT_KEY } from '@evidence-dev/component-utilities/globalContexts';\\nimport { ensureInputContext } from '@evidence-dev/sdk/utils/svelte';\\nimport { profile } from '@evidence-dev/component-utilities/profile';\\nimport { Query, hasUnsetValues } from '@evidence-dev/sdk/usql';\\nimport { setQueryFunction } from '@evidence-dev/component-utilities/buildQuery';\\n\\t\\t\\n        import { page } from '$app/stores';\\n        import { setContext, getContext, beforeUpdate, onDestroy, onMount } from 'svelte';\\n\\t\\timport { writable, get } from 'svelte/store';\\n        \\n        // Functions\\n\\n        \\n        let props;\\n        export { props as data }; // little hack to make the data name not overlap\\n        let { data = {}, customFormattingSettings, __db, inputs } = props;\\n        $: ({ data = {}, customFormattingSettings, __db } = props);\\n\\n        $routeHash = 'fa3a9ba979503cc33be1e8689543296c';\\n\\n\\t\\t\\n\\t\\tlet inputs_store = ensureInputContext(writable(inputs));\\n\\t\\tonDestroy(inputs_store.subscribe((value) => inputs = value));\\n\\n        $: pageHasQueries.set(Object.keys(data).length > 0);\\n\\n        setContext(CUSTOM_FORMATTING_SETTINGS_CONTEXT_KEY, {\\n            getCustomFormats: () => {\\n                return customFormattingSettings.customFormats || [];\\n            }\\n        });\\n\\n\\t\\timport { browser, dev } from \\"$app/environment\\";\\n\\n\\t\\tif (!browser) {\\n\\t\\t\\tonDestroy(() => Query.emptyCache());\\n\\t\\t}\\n\\n\\t\\tconst queryFunc = (query, query_name) => profile(__db.query, query, { query_name });\\n\\t\\tsetQueryFunction(queryFunc);\\n\\n\\t\\tconst scoreNotifier = !dev? () => {} : (info) => {\\n\\t\\t\\ttoasts.add({\\n\\t\\t\\t\\tid: Math.random(),\\n\\t\\t\\t\\ttitle: info.id,\\n\\t\\t\\t\\tmessage: \`Results estimated to use \${\\n\\t\\t\\t\\t\\tIntl.NumberFormat().format(info.score / (1024 * 1024))\\n\\t\\t\\t\\t}mb of memory, performance may be impacted\`,\\n\\t\\t\\t\\tstatus: 'warning'\\n\\t\\t\\t}, 5000);\\n\\t\\t};\\n\\n\\t\\tif (import.meta?.hot) {\\n            if (typeof import.meta.hot.data.hmrHasRun === 'undefined') import.meta.hot.data.hmrHasRun = false\\n\\n\\t\\t\\timport.meta.hot.on(\\"evidence:reset-queries\\", async (payload) => {\\n\\t\\t\\t\\tawait $page.data.__db.updateParquetURLs(JSON.stringify(payload.latestManifest), true);\\n\\t\\t\\t\\tQuery.emptyCache()\\n\\t\\t\\t\\t__my_analysisFactory(__my_analysisText, { noResolve: __my_analysisHasUnresolved });\\n\\t\\t\\t})\\n\\t    }\\n\\t\\t\\n\\t\\tlet params = $page.params;\\n\\t\\t$: params = $page.params;\\n\\t\\t\\n\\t\\tlet _mounted = false;\\n\\t\\tonMount(() => (_mounted = true));\\n\\n        \\n\\t\\t\\n\\t\\t\\n                // Update external queries\\n                if (import.meta?.hot) {\\n\\t\\t\\t\\t\\timport.meta.hot.on(\\"vite:beforeUpdate\\", () => {\\n\\t\\t\\t\\t\\t\\t// remove all prerendered queries\\n\\t\\t\\t\\t\\t\\tprops.data = {}\\n\\t\\t\\t\\t\\t});\\n\\n                    import.meta.hot.on(\\"evidence:queryChange\\", ({queryId, content}) => {\\n                        let errors = []\\n                        if (!queryId) errors.push(\\"Malformed event: Missing queryId\\")\\n                        if (!content) errors.push(\\"Malformed event: Missing content\\")\\n                        if (errors.length) {\\n                            console.warn(\\"Failed to update query on serverside change!\\", errors.join(\\"\\\\n\\"))\\n                            return\\n                        }\\n\\n                        if (queryId === \\"my_analysis\\") {\\n                            __my_analysisText = content\\n                        }\\n                        \\n                    })\\n                }\\n\\n                let my_analysisInitialStates = { initialData: undefined, initialError: undefined }\\n                \\n                // Give initial states for these variables\\n                /** @type {boolean} */\\n                let __my_analysisHasUnresolved = hasUnsetValues\`SELECT * FROM your_table_name LIMIT 10\`;\\n                /** @type {string} */\\n                let __my_analysisText = \`SELECT * FROM your_table_name LIMIT 10\`\\n\\n\\n                if (browser) {\\n                    // Data came from SSR\\n                    if (data.my_analysis_data) {\\n                        // vvv is this still used/possible?\\n                        if (data.my_analysis_data instanceof Error) {\\n                            my_analysisInitialStates.initialError = data.my_analysis_data\\n                        } else {\\n                            my_analysisInitialStates.initialData = data.my_analysis_data\\n                        }\\n                        if (data.my_analysis_columns) {\\n                            my_analysisInitialStates.knownColumns = data.my_analysis_columns\\n                        }\\n                    }\\n                }\\n\\n                /** @type {import(\\"@evidence-dev/sdk/usql\\").QueryValue} */\\n                let my_analysis;\\n\\n                $: __my_analysisHasUnresolved = hasUnsetValues\`SELECT * FROM your_table_name LIMIT 10\`;\\n                $: __my_analysisText = \`SELECT * FROM your_table_name LIMIT 10\`\\n\\n                // keep initial state around until after the query has resolved once\\n                let __my_analysisInitialFactory = false;\\n                $: if (__my_analysisHasUnresolved || !__my_analysisInitialFactory) {    \\n                    if (!__my_analysisHasUnresolved) {\\n                        __my_analysisFactory(__my_analysisText, { noResolve: __my_analysisHasUnresolved, ...my_analysisInitialStates });\\n                        __my_analysisInitialFactory = true;\\n                    }\\n                } else {\\n                    __my_analysisFactory(__my_analysisText, { noResolve: __my_analysisHasUnresolved });\\n                }\\n\\n                const __my_analysisFactory = Query.createReactive(\\n                    { callback: v => {\\n                        my_analysis = v\\n                    }, execFn: queryFunc },\\n                    { id: 'my_analysis', ...my_analysisInitialStates }\\n                )\\n\\n                // Assign a value for the initial run-through\\n                // This is split because chicken / egg\\n                __my_analysisFactory(__my_analysisText, { noResolve: __my_analysisHasUnresolved, ...my_analysisInitialStates })\\n\\n                // Add queries to global scope inside symbols to ease debugging\\n                globalThis[Symbol.for(\\"my_analysis\\")] = { get value() { return my_analysis } }\\n                \\n                \\n            \\n\\t\\t\\n\\t\\tif (!browser) {\\n\\t\\t\\tonDestroy(inputs_store.subscribe((inputs) => {\\n\\t\\t\\t\\t\\n\\t\\t\\t}));\\n\\t\\t}\\n\\t\\t\\n\\t\\t\\n\\t\\t\\n    \\n\\t\\n  let loadedDatasets = [];\\n  let domoEnvironment = false;\\n  let domoUser = null;\\n\\n  // Check Domo environment on component initialization\\n  if (typeof window !== 'undefined') {\\n    domoEnvironment = typeof window.domo !== 'undefined';\\n\\n    if (domoEnvironment && window.domo && window.domo.get) {\\n      // Try to get user info\\n      window.domo.get('/domo/users/v1/me')\\n        .then(user => {\\n          domoUser = user;\\n          console.log('Domo user:', domoUser);\\n        })\\n        .catch(error => {\\n          console.warn('Could not get Domo user info:', error);\\n        });\\n    }\\n  }\\n\\n  function handleDatasetLoaded(event) {\\n    console.log('Dataset loaded:', event);\\n    loadedDatasets = [...loadedDatasets, event];\\n\\n    // Notify Domo of successful data load if in DDX environment\\n    if (domoEnvironment && window.domo && window.domo.publish) {\\n      window.domo.publish('dataset-loaded', event);\\n    }\\n  }\\n\\n  // Initialize Domo workflow picker when page loads\\n  if (typeof window !== 'undefined') {\\n    window.addEventListener('DOMContentLoaded', function() {\\n      // Load the Domo integration script\\n      const script = document.createElement('script');\\n      script.src = '/static/domo-duckdb-integration.js';\\n      script.onload = function() {\\n        console.log('Domo integration script loaded');\\n      };\\n      document.head.appendChild(script);\\n    });\\n  }\\n<\/script>\\n<style>\\n  .domo-info {\\n    margin: 20px 0;\\n    padding: 15px;\\n    border: 2px solid #00d4aa;\\n    border-radius: 8px;\\n    background-color: #f0fdf9;\\n    color: #065f46;\\n  }\\n\\n  .domo-info h4 {\\n    margin: 0 0 10px 0;\\n    color: #047857;\\n  }\\n\\n  .dev-info {\\n    margin: 20px 0;\\n    padding: 15px;\\n    border: 2px solid #f59e0b;\\n    border-radius: 8px;\\n    background-color: #fffbeb;\\n    color: #92400e;\\n  }\\n\\n  .dev-info h4 {\\n    margin: 0 0 10px 0;\\n    color: #d97706;\\n  }\\n\\n  .loaded-datasets {\\n    margin: 30px 0;\\n    padding: 20px;\\n    border: 1px solid #e5e7eb;\\n    border-radius: 8px;\\n    background-color: #f9fafb;\\n  }\\n\\n  .dataset-card {\\n    margin: 15px 0;\\n    padding: 15px;\\n    border: 1px solid #d1d5db;\\n    border-radius: 6px;\\n    background-color: white;\\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  .dataset-card h4 {\\n    margin: 0 0 10px 0;\\n    color: #1f2937;\\n  }\\n\\n  .dataset-card p {\\n    margin: 5px 0;\\n    color: #6b7280;\\n  }\\n\\n  .no-datasets {\\n    margin: 30px 0;\\n    padding: 40px;\\n    text-align: center;\\n    border: 2px dashed #d1d5db;\\n    border-radius: 8px;\\n    color: #6b7280;\\n  }\\n\\n  .no-datasets p {\\n    margin: 0;\\n    font-style: italic;\\n  }\\n\\n  /* Workflow Picker Styles */\\n  .workflow-picker {\\n    max-width: 800px;\\n    margin: 0 auto;\\n    padding: 20px;\\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\\n  }\\n\\n  .picker-header {\\n    text-align: center;\\n    margin-bottom: 30px;\\n  }\\n\\n  .picker-header h3 {\\n    color: #1f2937;\\n    margin-bottom: 8px;\\n  }\\n\\n  .picker-header p {\\n    color: #6b7280;\\n    margin: 0;\\n  }\\n\\n  .workflow-step {\\n    margin: 25px 0;\\n    padding: 20px;\\n    border: 1px solid #e5e7eb;\\n    border-radius: 8px;\\n    background-color: #f9fafb;\\n  }\\n\\n  .workflow-step label {\\n    display: block;\\n    margin-bottom: 8px;\\n    font-weight: 600;\\n    color: #374151;\\n  }\\n\\n  .dataset-dropdown,\\n  .workflow-step input,\\n  .workflow-step select {\\n    width: 100%;\\n    padding: 10px 12px;\\n    border: 1px solid #d1d5db;\\n    border-radius: 6px;\\n    font-size: 14px;\\n    background-color: white;\\n  }\\n\\n  .dataset-dropdown:focus,\\n  .workflow-step input:focus,\\n  .workflow-step select:focus {\\n    outline: none;\\n    border-color: #3b82f6;\\n    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\\n  }\\n\\n  .dataset-preview {\\n    margin-top: 20px;\\n    padding: 20px;\\n    border: 1px solid #e5e7eb;\\n    border-radius: 8px;\\n    background-color: white;\\n  }\\n\\n  .dataset-info {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n    gap: 15px;\\n    margin-bottom: 20px;\\n  }\\n\\n  .info-item {\\n    padding: 10px;\\n    background-color: #f3f4f6;\\n    border-radius: 4px;\\n  }\\n\\n  .schema-table table,\\n  .data-preview table {\\n    width: 100%;\\n    border-collapse: collapse;\\n    margin-top: 10px;\\n  }\\n\\n  .schema-table th,\\n  .schema-table td,\\n  .data-preview th,\\n  .data-preview td {\\n    padding: 8px 12px;\\n    text-align: left;\\n    border: 1px solid #e5e7eb;\\n  }\\n\\n  .schema-table th,\\n  .data-preview th {\\n    background-color: #f3f4f6;\\n    font-weight: 600;\\n  }\\n\\n  .config-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n    gap: 20px;\\n  }\\n\\n  .workflow-actions {\\n    text-align: center;\\n    margin: 30px 0;\\n  }\\n\\n  .btn {\\n    padding: 12px 24px;\\n    border: none;\\n    border-radius: 6px;\\n    font-size: 14px;\\n    font-weight: 600;\\n    cursor: pointer;\\n    transition: all 0.2s;\\n    margin: 0 5px;\\n  }\\n\\n  .btn:disabled {\\n    opacity: 0.6;\\n    cursor: not-allowed;\\n  }\\n\\n  .btn-primary {\\n    background-color: #3b82f6;\\n    color: white;\\n  }\\n\\n  .btn-primary:hover:not(:disabled) {\\n    background-color: #2563eb;\\n  }\\n\\n  .btn-secondary {\\n    background-color: #f3f4f6;\\n    color: #374151;\\n    border: 1px solid #d1d5db;\\n  }\\n\\n  .btn-secondary:hover:not(:disabled) {\\n    background-color: #e5e7eb;\\n  }\\n\\n  .loading-overlay {\\n    position: fixed;\\n    top: 0;\\n    left: 0;\\n    right: 0;\\n    bottom: 0;\\n    background-color: rgba(0, 0, 0, 0.5);\\n    display: flex;\\n    flex-direction: column;\\n    align-items: center;\\n    justify-content: center;\\n    z-index: 1000;\\n  }\\n\\n  .loading-spinner {\\n    width: 40px;\\n    height: 40px;\\n    border: 4px solid #f3f3f3;\\n    border-top: 4px solid #3b82f6;\\n    border-radius: 50%;\\n    animation: spin 1s linear infinite;\\n    margin-bottom: 16px;\\n  }\\n\\n  .loading-overlay p {\\n    color: white;\\n    font-size: 16px;\\n    margin: 0;\\n  }\\n\\n  @keyframes spin {\\n    0% { transform: rotate(0deg); }\\n    100% { transform: rotate(360deg); }\\n  }\\n\\n  .preview-actions {\\n    margin: 15px 0;\\n  }\\n\\n  .data-preview {\\n    margin-top: 20px;\\n    max-height: 300px;\\n    overflow: auto;\\n  }\\n</style>\\n\\n\\n<h1 class=\\"markdown\\" id=\\"domo-dataset-to-duckdb-workflow\\"><a href=\\"#domo-dataset-to-duckdb-workflow\\">Domo Dataset to DuckDB Workflow</a></h1>\\n{#if domoEnvironment}\\n  <div class=\\"domo-info\\">\\n    <h4>🔗 Connected to Domo</h4>\\n    {#if domoUser}\\n      <p>Welcome, {domoUser.displayName || domoUser.name}!</p>\\n    {/if}\\n    <p>This Evidence app is running in your Domo environment with access to your datasets.</p>\\n  </div>\\n{:else}\\n  <div class=\\"dev-info\\">\\n    <h4>🛠️ Development Mode</h4>\\n    <p>Running in development mode with mock Domo data.</p>\\n  </div>\\n{/if}\\n<p\\n  class=\\"markdown\\"\\n>This page allows you to select and load Domo datasets into DuckDB for analysis in Evidence.</p>\\n<h2 class=\\"markdown\\" id=\\"how-it-works\\"><a href=\\"#how-it-works\\">How it Works</a></h2>\\n<ol class=\\"markdown\\">\\n<li class=\\"markdown\\"><strong class=\\"markdown\\">Select a Dataset</strong>: Choose from available Domo datasets in your instance</li>\\n<li class=\\"markdown\\"><strong class=\\"markdown\\">Preview Data</strong>: Review the dataset schema and sample data</li>\\n<li class=\\"markdown\\"><strong class=\\"markdown\\">Configure Loading</strong>: Set table name and refresh mode</li>\\n<li class=\\"markdown\\"><strong class=\\"markdown\\">Load into DuckDB</strong>: Import the data for use in Evidence queries</li>\\n</ol>\\n<!-- Domo Workflow Picker -->\\n<div id=\\"domo-workflow-picker\\" class=\\"workflow-picker\\">\\n  <div class=\\"picker-header\\">\\n    <h3>Domo Dataset Workflow</h3>\\n    <p>Select and load Domo datasets into DuckDB for analysis</p>\\n  </div>\\n  <div class=\\"workflow-step\\">\\n    <label for=\\"dataset-select\\">Select Dataset:</label>\\n    <select id=\\"dataset-select\\" class=\\"dataset-dropdown\\">\\n      <option value=\\"\\">Choose a dataset...</option>\\n    </select>\\n  </div>\\n  <div id=\\"dataset-preview\\" class=\\"dataset-preview\\" style=\\"display: none;\\">\\n    <h4>Dataset Information</h4>\\n    <div id=\\"dataset-info\\" class=\\"dataset-info\\"></div>\\n    <h5>Schema</h5>\\n    <div id=\\"schema-table\\" class=\\"schema-table\\"></div>\\n    <div class=\\"preview-actions\\">\\n      <button id=\\"preview-data-btn\\" class=\\"btn btn-secondary\\">Preview Data</button>\\n    </div>\\n    <div id=\\"data-preview\\" class=\\"data-preview\\" style=\\"display: none;\\"></div>\\n  </div>\\n  <div id=\\"loading-config\\" class=\\"workflow-step\\" style=\\"display: none;\\">\\n    <h4>Loading Configuration</h4>\\n    <div class=\\"config-grid\\">\\n      <div class=\\"config-item\\">\\n        <label for=\\"table-name\\">Table Name in DuckDB:</label>\\n        <input id=\\"table-name\\" type=\\"text\\" placeholder=\\"Enter table name\\" />\\n      </div>\\n      <div class=\\"config-item\\">\\n        <label for=\\"refresh-mode\\">Refresh Mode:</label>\\n        <select id=\\"refresh-mode\\">\\n          <option value=\\"replace\\">Replace existing data</option>\\n          <option value=\\"append\\">Append to existing data</option>\\n        </select>\\n      </div>\\n    </div>\\n  </div>\\n  <div id=\\"workflow-actions\\" class=\\"workflow-actions\\" style=\\"display: none;\\">\\n    <button id=\\"load-dataset-btn\\" class=\\"btn btn-primary\\">Load Dataset into DuckDB</button>\\n  </div>\\n  <div id=\\"loading-overlay\\" class=\\"loading-overlay\\" style=\\"display: none;\\">\\n    <div class=\\"loading-spinner\\"></div>\\n    <p id=\\"loading-message\\">Loading...</p>\\n  </div>\\n</div>\\n<h2 class=\\"markdown\\" id=\\"loaded-datasets\\"><a href=\\"#loaded-datasets\\">Loaded Datasets</a></h2>\\n{#if loadedDatasets.length > 0}\\n  <div class=\\"loaded-datasets\\">\\n    <h3>Successfully Loaded Datasets</h3>\\n    {#each loadedDatasets as dataset}\\n      <div class=\\"dataset-card\\">\\n        <h4>{dataset.datasetName}</h4>\\n        <p><strong>Table Name:</strong> {dataset.tableName}</p>\\n        <p><strong>Rows:</strong> {dataset.rowCount.toLocaleString()}</p>\\n        <p><strong>Dataset ID:</strong> {dataset.datasetId}</p>\\n      </div>\\n    {/each}\\n  </div>\\n{:else}\\n  <div class=\\"no-datasets\\">\\n    <p>No datasets loaded yet. Use the workflow picker above to load your first dataset.</p>\\n  </div>\\n{/if}\\n<h2 class=\\"markdown\\" id=\\"using-your-data\\"><a href=\\"#using-your-data\\">Using Your Data</a></h2>\\n<p\\n  class=\\"markdown\\"\\n>Once you've loaded datasets, you can use them in Evidence pages with SQL queries:</p>\\n\\n        {#if my_analysis }\\n            <QueryViewer\\n                queryID = \\"my_analysis\\"\\n                queryResult = {my_analysis}\\n            /> \\n        {/if}\\n<h3 class=\\"markdown\\" id=\\"example-queries\\"><a href=\\"#example-queries\\">Example Queries</a></h3>\\n<p\\n  class=\\"markdown\\"\\n>Here are some example queries you can run on your loaded datasets:</p>\\n<p class=\\"markdown\\"><strong class=\\"markdown\\">Basic Data Exploration:</strong></p>\\n<CodeBlock source=\\"-- Get row count and basic stats\\nSELECT \\n  COUNT(*) as total_rows,\\n  COUNT(DISTINCT column_name) as unique_values\\nFROM your_table_name;\\" copyToClipboard=true language=\\"sql\\"/>\\n<p class=\\"markdown\\"><strong class=\\"markdown\\">Time Series Analysis:</strong></p>\\n<CodeBlock source=\\"-- Aggregate by date (if you have date columns)\\nSELECT \\n  DATE_TRUNC(&apos;month&apos;, date_column) as month,\\n  SUM(numeric_column) as total\\nFROM your_table_name\\nGROUP BY month\\nORDER BY month;\\" copyToClipboard=true language=\\"sql\\"/>\\n<p class=\\"markdown\\"><strong class=\\"markdown\\">Category Analysis:</strong></p>\\n<CodeBlock source=\\"-- Group by categorical columns\\nSELECT \\n  category_column,\\n  COUNT(*) as count,\\n  AVG(numeric_column) as average\\nFROM your_table_name\\nGROUP BY category_column\\nORDER BY count DESC;\\" copyToClipboard=true language=\\"sql\\"/>\\n<h2 class=\\"markdown\\" id=\\"next-steps\\"><a href=\\"#next-steps\\">Next Steps</a></h2>\\n<ol class=\\"markdown\\">\\n<li class=\\"markdown\\"><strong class=\\"markdown\\">Create Visualizations</strong>: Use Evidence components like <code class=\\"markdown\\">&lt;BarChart&gt;</code>, <code class=\\"markdown\\">&lt;LineChart&gt;</code>, etc.</li>\\n<li class=\\"markdown\\"><strong class=\\"markdown\\">Add Interactivity</strong>: Use <code class=\\"markdown\\">&lt;Dropdown&gt;</code> and other input components</li>\\n<li class=\\"markdown\\"><strong class=\\"markdown\\">Build Dashboards</strong>: Create multiple pages with different analyses</li>\\n<li class=\\"markdown\\"><strong class=\\"markdown\\">Deploy to Domo</strong>: Package your Evidence app for Domo DDX</li>\\n</ol>\\n<h2 class=\\"markdown\\" id=\\"troubleshooting\\"><a href=\\"#troubleshooting\\">Troubleshooting</a></h2>\\n<h3 class=\\"markdown\\" id=\\"common-issues\\"><a href=\\"#common-issues\\">Common Issues</a></h3>\\n<p class=\\"markdown\\"><strong class=\\"markdown\\">Dataset Not Loading:</strong></p>\\n<ul class=\\"markdown\\">\\n<li class=\\"markdown\\">Check your Domo permissions</li>\\n<li class=\\"markdown\\">Verify the dataset exists and is accessible</li>\\n<li class=\\"markdown\\">Try refreshing the page</li>\\n</ul>\\n<p class=\\"markdown\\"><strong class=\\"markdown\\">Table Name Conflicts:</strong></p>\\n<ul class=\\"markdown\\">\\n<li class=\\"markdown\\">Use unique table names</li>\\n<li class=\\"markdown\\">Choose \\"Replace existing data\\" to overwrite</li>\\n</ul>\\n<p class=\\"markdown\\"><strong class=\\"markdown\\">Performance Issues:</strong></p>\\n<ul class=\\"markdown\\">\\n<li class=\\"markdown\\">Large datasets may take time to load</li>\\n<li class=\\"markdown\\">Consider filtering data in Domo before loading</li>\\n<li class=\\"markdown\\">Use appropriate data types for better performance</li>\\n</ul>\\n<h3 class=\\"markdown\\" id=\\"getting-help\\"><a href=\\"#getting-help\\">Getting Help</a></h3>\\n<ul class=\\"markdown\\">\\n<li class=\\"markdown\\">Check the browser console for error messages</li>\\n<li class=\\"markdown\\">Verify your Domo DDX environment is properly configured</li>\\n<li class=\\"markdown\\">Contact your Domo administrator for dataset access issues</li>\\n</ul>\\n\\n"],"names":[],"mappings":"AAqQE,wCAAW,CACT,MAAM,CAAE,IAAI,CAAC,CAAC,CACd,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,aAAa,CAAE,GAAG,CAClB,gBAAgB,CAAE,OAAO,CACzB,KAAK,CAAE,OACT,CAEA,yBAAU,CAAC,iBAAG,CACZ,MAAM,CAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAClB,KAAK,CAAE,OACT,CAEA,uCAAU,CACR,MAAM,CAAE,IAAI,CAAC,CAAC,CACd,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,aAAa,CAAE,GAAG,CAClB,gBAAgB,CAAE,OAAO,CACzB,KAAK,CAAE,OACT,CAEA,wBAAS,CAAC,iBAAG,CACX,MAAM,CAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAClB,KAAK,CAAE,OACT,CAEA,8CAAiB,CACf,MAAM,CAAE,IAAI,CAAC,CAAC,CACd,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,aAAa,CAAE,GAAG,CAClB,gBAAgB,CAAE,OACpB,CAEA,2CAAc,CACZ,MAAM,CAAE,IAAI,CAAC,CAAC,CACd,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,aAAa,CAAE,GAAG,CAClB,gBAAgB,CAAE,KAAK,CACvB,UAAU,CAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CACzC,CAEA,4BAAa,CAAC,iBAAG,CACf,MAAM,CAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAClB,KAAK,CAAE,OACT,CAEA,4BAAa,CAAC,gBAAE,CACd,MAAM,CAAE,GAAG,CAAC,CAAC,CACb,KAAK,CAAE,OACT,CAEA,0CAAa,CACX,MAAM,CAAE,IAAI,CAAC,CAAC,CACd,OAAO,CAAE,IAAI,CACb,UAAU,CAAE,MAAM,CAClB,MAAM,CAAE,GAAG,CAAC,MAAM,CAAC,OAAO,CAC1B,aAAa,CAAE,GAAG,CAClB,KAAK,CAAE,OACT,CAEA,2BAAY,CAAC,gBAAE,CACb,MAAM,CAAE,CAAC,CACT,UAAU,CAAE,MACd,CAGA,8CAAiB,CACf,SAAS,CAAE,KAAK,CAChB,MAAM,CAAE,CAAC,CAAC,IAAI,CACd,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,aAAa,CAAC,CAAC,kBAAkB,CAAC,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,UACtE,CAEA,4CAAe,CACb,UAAU,CAAE,MAAM,CAClB,aAAa,CAAE,IACjB,CAEA,6BAAc,CAAC,iBAAG,CAChB,KAAK,CAAE,OAAO,CACd,aAAa,CAAE,GACjB,CAEA,6BAAc,CAAC,gBAAE,CACf,KAAK,CAAE,OAAO,CACd,MAAM,CAAE,CACV,CAEA,4CAAe,CACb,MAAM,CAAE,IAAI,CAAC,CAAC,CACd,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,aAAa,CAAE,GAAG,CAClB,gBAAgB,CAAE,OACpB,CAEA,6BAAc,CAAC,oBAAM,CACnB,OAAO,CAAE,KAAK,CACd,aAAa,CAAE,GAAG,CAClB,WAAW,CAAE,GAAG,CAChB,KAAK,CAAE,OACT,CAEA,+CAAiB,CACjB,6BAAc,CAAC,oBAAK,CACpB,6BAAc,CAAC,qBAAO,CACpB,KAAK,CAAE,IAAI,CACX,OAAO,CAAE,IAAI,CAAC,IAAI,CAClB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,aAAa,CAAE,GAAG,CAClB,SAAS,CAAE,IAAI,CACf,gBAAgB,CAAE,KACpB,CAEA,+CAAiB,MAAM,CACvB,6BAAc,CAAC,oBAAK,MAAM,CAC1B,6BAAc,CAAC,qBAAM,MAAO,CAC1B,OAAO,CAAE,IAAI,CACb,YAAY,CAAE,OAAO,CACrB,UAAU,CAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAC9C,CAEA,8CAAiB,CACf,UAAU,CAAE,IAAI,CAChB,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,aAAa,CAAE,GAAG,CAClB,gBAAgB,CAAE,KACpB,CAEA,2CAAc,CACZ,OAAO,CAAE,IAAI,CACb,qBAAqB,CAAE,OAAO,QAAQ,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAC3D,GAAG,CAAE,IAAI,CACT,aAAa,CAAE,IACjB,CA8BA,0CAAa,CACX,OAAO,CAAE,IAAI,CACb,qBAAqB,CAAE,OAAO,QAAQ,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAC3D,GAAG,CAAE,IACP,CAEA,+CAAkB,CAChB,UAAU,CAAE,MAAM,CAClB,MAAM,CAAE,IAAI,CAAC,CACf,CAEA,kCAAK,CACH,OAAO,CAAE,IAAI,CAAC,IAAI,CAClB,MAAM,CAAE,IAAI,CACZ,aAAa,CAAE,GAAG,CAClB,SAAS,CAAE,IAAI,CACf,WAAW,CAAE,GAAG,CAChB,MAAM,CAAE,OAAO,CACf,UAAU,CAAE,GAAG,CAAC,IAAI,CACpB,MAAM,CAAE,CAAC,CAAC,GACZ,CAEA,kCAAI,SAAU,CACZ,OAAO,CAAE,GAAG,CACZ,MAAM,CAAE,WACV,CAEA,0CAAa,CACX,gBAAgB,CAAE,OAAO,CACzB,KAAK,CAAE,KACT,CAEA,0CAAY,MAAM,KAAK,SAAS,CAAE,CAChC,gBAAgB,CAAE,OACpB,CAEA,4CAAe,CACb,gBAAgB,CAAE,OAAO,CACzB,KAAK,CAAE,OAAO,CACd,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OACpB,CAEA,4CAAc,MAAM,KAAK,SAAS,CAAE,CAClC,gBAAgB,CAAE,OACpB,CAEA,8CAAiB,CACf,QAAQ,CAAE,KAAK,CACf,GAAG,CAAE,CAAC,CACN,IAAI,CAAE,CAAC,CACP,KAAK,CAAE,CAAC,CACR,MAAM,CAAE,CAAC,CACT,gBAAgB,CAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CACpC,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,OAAO,CAAE,IACX,CAEA,8CAAiB,CACf,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,UAAU,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CAC7B,aAAa,CAAE,GAAG,CAClB,SAAS,CAAE,mBAAI,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAClC,aAAa,CAAE,IACjB,CAEA,+BAAgB,CAAC,gBAAE,CACjB,KAAK,CAAE,KAAK,CACZ,SAAS,CAAE,IAAI,CACf,MAAM,CAAE,CACV,CAEA,WAAW,mBAAK,CACd,EAAG,CAAE,SAAS,CAAE,OAAO,IAAI,CAAG,CAC9B,IAAK,CAAE,SAAS,CAAE,OAAO,MAAM,CAAG,CACpC,CAEA,8CAAiB,CACf,MAAM,CAAE,IAAI,CAAC,CACf,CAEA,2CAAc,CACZ,UAAU,CAAE,IAAI,CAChB,UAAU,CAAE,KAAK,CACjB,QAAQ,CAAE,IACZ"}`},t={title:"Domo Dataset Workflow"},xe=q((a,y,f,V)=>{let C,h,c,w;h=M(z,e=>C=e),w=M(O,e=>c=e);let{data:i}=y,{data:b={},customFormattingSettings:k,__db:E,inputs:x}=i;U(O,c="fa3a9ba979503cc33be1e8689543296c",c);let _=F(N(x));u(_.subscribe(e=>x=e)),R(H,{getCustomFormats:()=>k.customFormats||[]}),u(()=>G.emptyCache());const B=(e,K)=>Q(E.query,e,{query_name:K});j(B),C.params;let m={initialData:void 0,initialError:void 0},s=T`SELECT * FROM your_table_name LIMIT 10`,r="SELECT * FROM your_table_name LIMIT 10",l,I=!1;const p=G.createReactive({callback:e=>{l=e},execFn:B},{id:"my_analysis",...m});p(r,{noResolve:s,...m}),globalThis[Symbol.for("my_analysis")]={get value(){return l}},u(_.subscribe(e=>{}));let D=[],v=!1,A=null;return typeof window<"u"&&(v=typeof window.domo<"u",v&&window.domo&&window.domo.get&&window.domo.get("/domo/users/v1/me").then(e=>{A=e,console.log("Domo user:",A)}).catch(e=>{console.warn("Could not get Domo user info:",e)})),typeof window<"u"&&window.addEventListener("DOMContentLoaded",function(){const e=document.createElement("script");e.src="/static/domo-duckdb-integration.js",e.onload=function(){console.log("Domo integration script loaded")},document.head.appendChild(e)}),y.data===void 0&&f.data&&i!==void 0&&f.data(i),a.css.add(P),{data:b={},customFormattingSettings:k,__db:E}=i,$.set(Object.keys(b).length>0),C.params,s=T`SELECT * FROM your_table_name LIMIT 10`,r="SELECT * FROM your_table_name LIMIT 10",s||!I?s||(p(r,{noResolve:s,...m}),I=!0):p(r,{noResolve:s}),h(),w(),`  ${typeof t<"u"&&t.title&&t.hide_title!==!0?`<h1 class="title">${n(t.title)}</h1>`:""} ${a.head+=`<!-- HEAD_svelte-2igo1p_START -->${typeof t<"u"&&t.title?`${a.title=`<title>${n(t.title)}</title>`,""} <meta property="og:title"${o("content",t.og?.title??t.title,0)}> <meta name="twitter:title"${o("content",t.og?.title??t.title,0)}>`:` ${a.title="<title>Evidence</title>",""}`}<meta name="twitter:card" content="summary_large_image"><meta name="twitter:site" content="@evidence_dev">${typeof t=="object"?`${t.description||t.og?.description?`<meta name="description"${o("content",t.description??t.og?.description,0)}> <meta property="og:description"${o("content",t.og?.description??t.description,0)}> <meta name="twitter:description"${o("content",t.og?.description??t.description,0)}>`:""} ${t.og?.image?`<meta property="og:image"${o("content",S(t.og?.image),0)}> <meta name="twitter:image"${o("content",S(t.og?.image),0)}>`:""}`:""}<!-- HEAD_svelte-2igo1p_END -->`,""}    <h1 class="markdown" id="domo-dataset-to-duckdb-workflow" data-svelte-h="svelte-l4qjn8"><a href="#domo-dataset-to-duckdb-workflow">Domo Dataset to DuckDB Workflow</a></h1> ${v?`<div class="domo-info svelte-10vyee7"><h4 class="svelte-10vyee7" data-svelte-h="svelte-xeif1v">🔗 Connected to Domo</h4> ${A?`<p>Welcome, ${n(A.displayName||A.name)}!</p>`:""} <p data-svelte-h="svelte-fqc5um">This Evidence app is running in your Domo environment with access to your datasets.</p></div>`:'<div class="dev-info svelte-10vyee7" data-svelte-h="svelte-eca4iq"><h4 class="svelte-10vyee7">🛠️ Development Mode</h4> <p>Running in development mode with mock Domo data.</p></div>'} <p class="markdown" data-svelte-h="svelte-1jvntq6">This page allows you to select and load Domo datasets into DuckDB for analysis in Evidence.</p> <h2 class="markdown" id="how-it-works" data-svelte-h="svelte-m9j39v"><a href="#how-it-works">How it Works</a></h2> <ol class="markdown" data-svelte-h="svelte-i541gc"><li class="markdown"><strong class="markdown">Select a Dataset</strong>: Choose from available Domo datasets in your instance</li> <li class="markdown"><strong class="markdown">Preview Data</strong>: Review the dataset schema and sample data</li> <li class="markdown"><strong class="markdown">Configure Loading</strong>: Set table name and refresh mode</li> <li class="markdown"><strong class="markdown">Load into DuckDB</strong>: Import the data for use in Evidence queries</li></ol>  <div id="domo-workflow-picker" class="workflow-picker svelte-10vyee7"><div class="picker-header svelte-10vyee7" data-svelte-h="svelte-1qqhjj8"><h3 class="svelte-10vyee7">Domo Dataset Workflow</h3> <p class="svelte-10vyee7">Select and load Domo datasets into DuckDB for analysis</p></div> <div class="workflow-step svelte-10vyee7"><label for="dataset-select" class="svelte-10vyee7" data-svelte-h="svelte-gxhz7z">Select Dataset:</label> <select id="dataset-select" class="dataset-dropdown svelte-10vyee7"><option value="" data-svelte-h="svelte-59d9xk">Choose a dataset...</option></select></div> <div id="dataset-preview" class="dataset-preview svelte-10vyee7" style="display: none;" data-svelte-h="svelte-1dp1fod"><h4>Dataset Information</h4> <div id="dataset-info" class="dataset-info svelte-10vyee7"></div> <h5>Schema</h5> <div id="schema-table" class="schema-table"></div> <div class="preview-actions svelte-10vyee7"><button id="preview-data-btn" class="btn btn-secondary svelte-10vyee7">Preview Data</button></div> <div id="data-preview" class="data-preview svelte-10vyee7" style="display: none;"></div></div> <div id="loading-config" class="workflow-step svelte-10vyee7" style="display: none;"><h4 data-svelte-h="svelte-1foy07w">Loading Configuration</h4> <div class="config-grid svelte-10vyee7"><div class="config-item" data-svelte-h="svelte-1m428t"><label for="table-name" class="svelte-10vyee7">Table Name in DuckDB:</label> <input id="table-name" type="text" placeholder="Enter table name" class="svelte-10vyee7"></div> <div class="config-item"><label for="refresh-mode" class="svelte-10vyee7" data-svelte-h="svelte-p1qydn">Refresh Mode:</label> <select id="refresh-mode" class="svelte-10vyee7"><option value="replace" data-svelte-h="svelte-qvzdub">Replace existing data</option><option value="append" data-svelte-h="svelte-idsvi6">Append to existing data</option></select></div></div></div> <div id="workflow-actions" class="workflow-actions svelte-10vyee7" style="display: none;" data-svelte-h="svelte-efrb90"><button id="load-dataset-btn" class="btn btn-primary svelte-10vyee7">Load Dataset into DuckDB</button></div> <div id="loading-overlay" class="loading-overlay svelte-10vyee7" style="display: none;" data-svelte-h="svelte-mbg3er"><div class="loading-spinner svelte-10vyee7"></div> <p id="loading-message" class="svelte-10vyee7">Loading...</p></div></div> <h2 class="markdown" id="loaded-datasets" data-svelte-h="svelte-1f0ly50"><a href="#loaded-datasets">Loaded Datasets</a></h2> ${D.length>0?`<div class="loaded-datasets svelte-10vyee7"><h3 data-svelte-h="svelte-1043zgz">Successfully Loaded Datasets</h3> ${L(D,e=>`<div class="dataset-card svelte-10vyee7"><h4 class="svelte-10vyee7">${n(e.datasetName)}</h4> <p class="svelte-10vyee7"><strong data-svelte-h="svelte-1t5jaf3">Table Name:</strong> ${n(e.tableName)}</p> <p class="svelte-10vyee7"><strong data-svelte-h="svelte-wddeo5">Rows:</strong> ${n(e.rowCount.toLocaleString())}</p> <p class="svelte-10vyee7"><strong data-svelte-h="svelte-oldt31">Dataset ID:</strong> ${n(e.datasetId)}</p> </div>`)}</div>`:'<div class="no-datasets svelte-10vyee7" data-svelte-h="svelte-1io82co"><p class="svelte-10vyee7">No datasets loaded yet. Use the workflow picker above to load your first dataset.</p></div>'} <h2 class="markdown" id="using-your-data" data-svelte-h="svelte-d20xgf"><a href="#using-your-data">Using Your Data</a></h2> <p class="markdown" data-svelte-h="svelte-8qpe1e">Once you&#39;ve loaded datasets, you can use them in Evidence pages with SQL queries:</p> ${l?`${d(W,"QueryViewer").$$render(a,{queryID:"my_analysis",queryResult:l},{},{})}`:""} <h3 class="markdown" id="example-queries" data-svelte-h="svelte-1y9nphi"><a href="#example-queries">Example Queries</a></h3> <p class="markdown" data-svelte-h="svelte-1jqarqq">Here are some example queries you can run on your loaded datasets:</p> <p class="markdown" data-svelte-h="svelte-fi6le8"><strong class="markdown">Basic Data Exploration:</strong></p> ${d(g,"CodeBlock").$$render(a,{source:`-- Get row count and basic stats
SELECT 
  COUNT(*) as total_rows,
  COUNT(DISTINCT column_name) as unique_values
FROM your_table_name;`,copyToClipboard:"true",language:"sql"},{},{})} <p class="markdown" data-svelte-h="svelte-8raaax"><strong class="markdown">Time Series Analysis:</strong></p> ${d(g,"CodeBlock").$$render(a,{source:`-- Aggregate by date (if you have date columns)
SELECT 
  DATE_TRUNC('month', date_column) as month,
  SUM(numeric_column) as total
FROM your_table_name
GROUP BY month
ORDER BY month;`,copyToClipboard:"true",language:"sql"},{},{})} <p class="markdown" data-svelte-h="svelte-1m0hm5v"><strong class="markdown">Category Analysis:</strong></p> ${d(g,"CodeBlock").$$render(a,{source:`-- Group by categorical columns
SELECT 
  category_column,
  COUNT(*) as count,
  AVG(numeric_column) as average
FROM your_table_name
GROUP BY category_column
ORDER BY count DESC;`,copyToClipboard:"true",language:"sql"},{},{})} <h2 class="markdown" id="next-steps" data-svelte-h="svelte-b2v2p6"><a href="#next-steps">Next Steps</a></h2> <ol class="markdown" data-svelte-h="svelte-1h5tzx0"><li class="markdown"><strong class="markdown">Create Visualizations</strong>: Use Evidence components like <code class="markdown">&lt;BarChart&gt;</code>, <code class="markdown">&lt;LineChart&gt;</code>, etc.</li> <li class="markdown"><strong class="markdown">Add Interactivity</strong>: Use <code class="markdown">&lt;Dropdown&gt;</code> and other input components</li> <li class="markdown"><strong class="markdown">Build Dashboards</strong>: Create multiple pages with different analyses</li> <li class="markdown"><strong class="markdown">Deploy to Domo</strong>: Package your Evidence app for Domo DDX</li></ol> <h2 class="markdown" id="troubleshooting" data-svelte-h="svelte-1uk16ny"><a href="#troubleshooting">Troubleshooting</a></h2> <h3 class="markdown" id="common-issues" data-svelte-h="svelte-1iqe0ip"><a href="#common-issues">Common Issues</a></h3> <p class="markdown" data-svelte-h="svelte-1syr9k0"><strong class="markdown">Dataset Not Loading:</strong></p> <ul class="markdown" data-svelte-h="svelte-1nuau76"><li class="markdown">Check your Domo permissions</li> <li class="markdown">Verify the dataset exists and is accessible</li> <li class="markdown">Try refreshing the page</li></ul> <p class="markdown" data-svelte-h="svelte-1eb0x8p"><strong class="markdown">Table Name Conflicts:</strong></p> <ul class="markdown" data-svelte-h="svelte-1fulwkk"><li class="markdown">Use unique table names</li> <li class="markdown">Choose &quot;Replace existing data&quot; to overwrite</li></ul> <p class="markdown" data-svelte-h="svelte-ta14c3"><strong class="markdown">Performance Issues:</strong></p> <ul class="markdown" data-svelte-h="svelte-1sdbkgn"><li class="markdown">Large datasets may take time to load</li> <li class="markdown">Consider filtering data in Domo before loading</li> <li class="markdown">Use appropriate data types for better performance</li></ul> <h3 class="markdown" id="getting-help" data-svelte-h="svelte-1f92uqx"><a href="#getting-help">Getting Help</a></h3> <ul class="markdown" data-svelte-h="svelte-1t69tq3"><li class="markdown">Check the browser console for error messages</li> <li class="markdown">Verify your Domo DDX environment is properly configured</li> <li class="markdown">Contact your Domo administrator for dataset access issues</li></ul>`});export{xe as default};
