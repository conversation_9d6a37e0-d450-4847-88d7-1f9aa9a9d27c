import{c as M,s as P,a as J}from"../../chunks/utils.js";import{s as K,g as Q,c as E,a as N,b as O,d as C,v as g,h as w}from"../../chunks/ssr.js";import{p as te}from"../../chunks/stores.js";import"dequal";import{o as ae,m as V,d as re,h as D,b as oe,c as z,k as x,q as ie,a as q,t as le,s as ne}from"../../chunks/VennDiagram.svelte_svelte_type_style_lang.js";import{t as se,g as de,o as ce,j as F,r as ue,c as fe,e as ve,h as me}from"../../chunks/helpers.js";import{w as X,d as he}from"../../chunks/index2.js";import"clsx";import{c as U,I as Y,t as Z,b as Ce}from"../../chunks/index7.js";import{I as $}from"../../chunks/InlineError.js";import{C as be,I as Ae}from"../../chunks/Info.js";import{c as ge}from"../../chunks/checkRequiredProps.js";import"../../chunks/Query.js";import"chroma-js";import"@uwdata/mosaic-sql";import"@evidence-dev/universal-sql/client-duckdb";import"ssf";import"@tidyjs/tidy";import"deep-object-diff";import"../../chunks/index5.js";import"export-to-csv";import"echarts";import"yaml";import"@astronautlabs/jsonpath";import"tua-body-scroll-lock";import"lodash/merge.js";import"prismjs";import"debounce";import"downloadjs";import"echarts-stat";const{name:j,selector:G}=re("accordion"),ye={multiple:!1,disabled:!1,forceVisible:!1},pe=n=>{const e={...ye,...n},t=se(ae(e,"value","onValueChange","defaultValue")),a=de(["root"]),{disabled:r,forceVisible:o}=t,s=e.value??X(e.defaultValue),u=ce(s,e?.onValueChange),c=(i,l)=>l===void 0?!1:typeof l=="string"?l===i:l.includes(i),d=he(u,i=>l=>c(l,i)),f=V(j(),{returned:()=>({"data-melt-id":a.root})}),v=i=>typeof i=="string"?{value:i}:i,b=i=>typeof i=="number"?{level:i}:i,A=V(j("item"),{stores:u,returned:i=>l=>{const{value:m,disabled:h}=v(l);return{"data-state":c(m,i)?"open":"closed","data-disabled":D(h)}}}),p=V(j("trigger"),{stores:[u,r],returned:([i,l])=>m=>{const{value:h,disabled:_}=v(m);return{disabled:D(l||_),"aria-expanded":!!c(h,i),"aria-disabled":!!_,"data-disabled":D(_),"data-value":h,"data-state":c(h,i)?"open":"closed"}},action:i=>({destroy:oe(z(i,"click",()=>{const m=i.dataset.disabled==="true",h=i.dataset.value;m||!h||S(h)}),z(i,"keydown",m=>{if(![x.ARROW_DOWN,x.ARROW_UP,x.HOME,x.END].includes(m.key))return;if(m.preventDefault(),m.key===x.SPACE||m.key===x.ENTER){const B=i.dataset.disabled==="true",L=i.dataset.value;if(B||!L)return;S(L);return}const h=m.target,_=ie(a.root);if(!_||!q(h))return;const T=Array.from(_.querySelectorAll(G("trigger"))).filter(B=>q(B)?B.dataset.disabled!=="true":!1);if(!T.length)return;const k=T.indexOf(h);m.key===x.ARROW_DOWN&&T[(k+1)%T.length].focus(),m.key===x.ARROW_UP&&T[(k-1+T.length)%T.length].focus(),m.key===x.HOME&&T[0].focus(),m.key===x.END&&T[T.length-1].focus()}))})}),I=V(j("content"),{stores:[u,r,o],returned:([i,l,m])=>h=>{const{value:_}=v(h),R=c(_,i)||m;return{"data-state":R?"open":"closed","data-disabled":D(l),"data-value":_,hidden:R?void 0:!0,style:ne({display:R?void 0:"none"})}},action:i=>{le().then(()=>{const l=F(),m=F(),h=document.querySelector(`${G("trigger")}, [data-value="${i.dataset.value}"]`);q(h)&&(i.id=l,h.setAttribute("aria-controls",l),h.id=m)})}}),y=V(j("heading"),{returned:()=>i=>{const{level:l}=b(i);return{role:"heading","aria-level":l,"data-heading-level":l}}});function S(i){u.update(l=>l===void 0?e.multiple?[i]:i:Array.isArray(l)?l.includes(i)?l.filter(m=>m!==i):(l.push(i),l):l===i?void 0:i)}return{ids:a,elements:{root:f,item:A,trigger:p,content:I,heading:y},states:{value:u},helpers:{isSelected:d},options:t}};function W(){return{NAME:"accordion",ITEM_NAME:"accordion-item",PARTS:["root","content","header","item","trigger"]}}function Te(n){const e=pe(ue(n)),{NAME:t,PARTS:a}=W(),r=fe(t,a),o={...e,getAttrs:r,updateOption:ve(e.options)};return K(t,o),o}function H(){const{NAME:n}=W();return Q(n)}function Ee(n){const{ITEM_NAME:e}=W(),t=X(n);return K(e,{propsStore:t}),{...H(),propsStore:t}}function ee(){const{ITEM_NAME:n}=W();return Q(n)}function Ie(){const n=H(),{propsStore:e}=ee();return{...n,propsStore:e}}function _e(){const n=H(),{propsStore:e}=ee();return{...n,props:e}}function xe(n,e){return n.length!==e.length?!1:n.every((t,a)=>t===e[a])}const Pe=E((n,e,t,a)=>{let r,o=M(e,["multiple","value","onValueChange","disabled","asChild","el"]),s,u,{multiple:c=!1}=e,{value:d=void 0}=e,{onValueChange:f=void 0}=e,{disabled:v=!1}=e,{asChild:b=!1}=e,{el:A=void 0}=e;const{elements:{root:p},states:{value:I},updateOption:y,getAttrs:S}=Te({multiple:c,disabled:v,defaultValue:d,onValueChange:({next:l})=>Array.isArray(l)?((!Array.isArray(d)||!xe(d,l))&&(f?.(l),d=l),l):(d!==l&&(f?.(l),d=l),l)});u=P(p,l=>s=l);const i=S("root");return e.multiple===void 0&&t.multiple&&c!==void 0&&t.multiple(c),e.value===void 0&&t.value&&d!==void 0&&t.value(d),e.onValueChange===void 0&&t.onValueChange&&f!==void 0&&t.onValueChange(f),e.disabled===void 0&&t.disabled&&v!==void 0&&t.disabled(v),e.asChild===void 0&&t.asChild&&b!==void 0&&t.asChild(b),e.el===void 0&&t.el&&A!==void 0&&t.el(A),d!==void 0&&I.set(Array.isArray(d)?[...d]:d),y("multiple",c),y("disabled",v),r=s,Object.assign(r,i),u(),`${b?`${a.default?a.default({builder:r}):""}`:`<div${N([C(r),C(o)],{})}${O("this",A,0)}>${a.default?a.default({builder:r}):""}</div>`}`}),Se=E((n,e,t,a)=>{let r,o=M(e,["value","disabled","asChild","el"]),s,u,c,d,{value:f}=e,{disabled:v=void 0}=e,{asChild:b=!1}=e,{el:A=void 0}=e;const{elements:{item:p},propsStore:I,getAttrs:y}=Ee({value:f,disabled:v});d=P(p,i=>c=i),u=P(I,i=>s=i);const S=y("item");return e.value===void 0&&t.value&&f!==void 0&&t.value(f),e.disabled===void 0&&t.disabled&&v!==void 0&&t.disabled(v),e.asChild===void 0&&t.asChild&&b!==void 0&&t.asChild(b),e.el===void 0&&t.el&&A!==void 0&&t.el(A),I.set({value:f,disabled:v}),r=c({...s,disabled:v}),Object.assign(r,S),u(),d(),`${b?`${a.default?a.default({builder:r}):""}`:`<div${N([C(r),C(o)],{})}${O("this",A,0)}>${a.default?a.default({builder:r}):""}</div>`}`}),Ne=E((n,e,t,a)=>{let r,o=M(e,["level","asChild","el"]),s,u,{level:c=3}=e,{asChild:d=!1}=e,{el:f=void 0}=e;const{elements:{heading:v},getAttrs:b}=H();u=P(v,p=>s=p);const A=b("header");return e.level===void 0&&t.level&&c!==void 0&&t.level(c),e.asChild===void 0&&t.asChild&&d!==void 0&&t.asChild(d),e.el===void 0&&t.el&&f!==void 0&&t.el(f),r=s(c),Object.assign(r,A),u(),`${d?`${a.default?a.default({builder:r}):""}`:`<div${N([C(r),C(o)],{})}${O("this",f,0)}>${a.default?a.default({builder:r}):""}</div>`}`}),Oe=E((n,e,t,a)=>{let r,o=M(e,["asChild","el"]),s,u,c,d,{asChild:f=!1}=e,{el:v=void 0}=e;const{elements:{trigger:b},props:A,getAttrs:p}=_e();d=P(b,y=>c=y),u=P(A,y=>s=y),me();const I=p("trigger");return e.asChild===void 0&&t.asChild&&f!==void 0&&t.asChild(f),e.el===void 0&&t.el&&v!==void 0&&t.el(v),r=c({...s}),Object.assign(r,I),u(),d(),`${f?`${a.default?a.default({builder:r}):""}`:`<button${N([C(r),{type:"button"},C(o)],{})}${O("this",v,0)}>${a.default?a.default({builder:r}):""}</button>`}`}),ke=E((n,e,t,a)=>{let r,o=M(e,["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","el"]),s,u,c,d,f,v,{transition:b=void 0}=e,{transitionConfig:A=void 0}=e,{inTransition:p=void 0}=e,{inTransitionConfig:I=void 0}=e,{outTransition:y=void 0}=e,{outTransitionConfig:S=void 0}=e,{asChild:i=!1}=e,{el:l=void 0}=e;const{elements:{content:m},helpers:{isSelected:h},propsStore:_,getAttrs:R}=Ie();d=P(m,k=>c=k),v=P(h,k=>f=k),u=P(_,k=>s=k);const T=R("content");return e.transition===void 0&&t.transition&&b!==void 0&&t.transition(b),e.transitionConfig===void 0&&t.transitionConfig&&A!==void 0&&t.transitionConfig(A),e.inTransition===void 0&&t.inTransition&&p!==void 0&&t.inTransition(p),e.inTransitionConfig===void 0&&t.inTransitionConfig&&I!==void 0&&t.inTransitionConfig(I),e.outTransition===void 0&&t.outTransition&&y!==void 0&&t.outTransition(y),e.outTransitionConfig===void 0&&t.outTransitionConfig&&S!==void 0&&t.outTransitionConfig(S),e.asChild===void 0&&t.asChild&&i!==void 0&&t.asChild(i),e.el===void 0&&t.el&&l!==void 0&&t.el(l),r=c({...s}),Object.assign(r,T),u(),d(),v(),`${i&&f(s.value)?`${a.default?a.default({builder:r}):""}`:`${b&&f(s.value)?`<div${N([C(r),C(o)],{})}${O("this",l,0)}>${a.default?a.default({builder:r}):""}</div>`:`${p&&y&&f(s.value)?`<div${N([C(r),C(o)],{})}${O("this",l,0)}>${a.default?a.default({builder:r}):""}</div>`:`${p&&f(s.value)?`<div${N([C(r),C(o)],{})}${O("this",l,0)}>${a.default?a.default({builder:r}):""}</div>`:`${y&&f(s.value)?`<div${N([C(r),C(o)],{})}${O("this",l,0)}>${a.default?a.default({builder:r}):""}</div>`:`${f(s.value)?`<div${N([C(r),C(o)],{})}${O("this",l,0)}>${a.default?a.default({builder:r}):""}</div>`:""}`}`}`}`}`}`}),Me=E((n,e,t,a)=>{let r=M(e,["class"]),{class:o=void 0}=e;return e.class===void 0&&t.class&&o!==void 0&&t.class(o),`${g(ke,"AccordionPrimitive.Content").$$render(n,Object.assign({},{class:U("overflow-hidden text-sm",o)},r),{},{default:()=>`<div class="pb-4 pt-0">${a.default?a.default({}):""}</div>`})}`}),we=E((n,e,t,a)=>{let r=M(e,["class","value"]),{class:o=void 0}=e,{value:s}=e;return e.class===void 0&&t.class&&o!==void 0&&t.class(o),e.value===void 0&&t.value&&s!==void 0&&t.value(s),`${g(Se,"AccordionPrimitive.Item").$$render(n,Object.assign({},{value:s},{class:U("border-b border-base-300 only-of-type:border-none",o)},r),{},{default:()=>`${a.default?a.default({}):""}`})}`}),Re=E((n,e,t,a)=>{let r=M(e,["class","level"]),{class:o=void 0}=e,{level:s=3}=e;return e.class===void 0&&t.class&&o!==void 0&&t.class(o),e.level===void 0&&t.level&&s!==void 0&&t.level(s),`${g(Ne,"AccordionPrimitive.Header").$$render(n,{level:s,class:"flex"},{},{default:()=>`${g(Oe,"AccordionPrimitive.Trigger").$$render(n,Object.assign({},{class:U("flex flex-1 items-center justify-between py-4 text-sm font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180 rounded-sm focus-visible:ring-base-200 focus-visible:outline-none focus-visible:ring-2",o)},r),{},{default:()=>`${a.default?a.default({}):""} ${g(Y,"Icon").$$render(n,{src:be,class:"h-4 w-4 shrink-0 text-base-content-muted transition-transform duration-200"},{},{})}`})}`})}`}),Ve=Pe,je=E((n,e,t,a)=>{let r=J(a),{single:o=!1}=e,{class:s=void 0}=e;return e.single===void 0&&t.single&&o!==void 0&&t.single(o),e.class===void 0&&t.class&&s!==void 0&&t.class(s),o=Z(o),`${r.default?`${g(Ve,"BaseAccordion.Root").$$render(n,{class:s,multiple:!o},{},{default:()=>`${a.default?a.default({}):""}`})}`:`${g($,"InlineError").$$render(n,{inputType:"Accordion",height:"52",width:"100%",error:["No </AccordionItem> found"]},{},{})}`}`}),Be=E((n,e,t,a)=>{let r=J(a),{title:o=void 0}=e,{compact:s=!1}=e,{description:u=void 0}=e,{class:c=void 0}=e;const d=[];try{if(!r.default)throw new Error("<AccordionItem> requires content to be provided e.g <AccordionItem>Content</AccordionItem>");ge({title:o})}catch(f){d.push(f)}return e.title===void 0&&t.title&&o!==void 0&&t.title(o),e.compact===void 0&&t.compact&&s!==void 0&&t.compact(s),e.description===void 0&&t.description&&u!==void 0&&t.description(u),e.class===void 0&&t.class&&c!==void 0&&t.class(c),s=Z(s),`${d.length>0?`${g($,"InlineError").$$render(n,{inputType:"AccordionItem",height:"52",width:"100%",error:d},{},{})}`:`${g(we,"BaseAccordion.Item").$$render(n,{value:o,class:c},{},{default:()=>`${g(Re,"BaseAccordion.Trigger").$$render(n,{class:s?"py-0":""},{},{default:()=>`<span>${a.title?a.title({}):` ${w(o)} ${u?`${g(Ae,"Info").$$render(n,{description:u},{},{})}`:""} `}</span>`})} ${g(Me,"BaseAccordion.Content").$$render(n,{},{},{default:()=>`${a.default?a.default({}):""}`})}`})}`}`}),De=E((n,e,t,a)=>{let{textToCopy:r=""}=e;return e.textToCopy===void 0&&t.textToCopy&&r!==void 0&&t.textToCopy(r),`<div class="relative"> <button class="bg-base-200 border border-base-300 rounded-sm p-2 hover:bg-base-200/80 active:bg-base-200" title="Copy to Clipboard">${g(Y,"Icon").$$render(n,{src:Ce,class:"w-4 h-4"},{},{})}</button></div>`}),ht=E((n,e,t,a)=>{let r,o,s;s=P(te,c=>o=c);const u=c=>{let d="";return c.stack&&(d+=c.stack),c.cause&&(d+=`

Caused By:
	`,d+=u(c.cause).split(`
`).join(`
	`)),d};return r=u(o.error),s(),`${o.status===404?`<h1 class="mt-0 mb-8 py-0" data-svelte-h="svelte-s9jbdv">Page Not Found</h1> <p><span class="font-mono text-base">${w(o.status)}</span>: The page
		<span class="font-mono text-base bg-base-200">${w(o.url.pathname)}</span> can&#39;t be found in the project.</p>`:`${o.status===500?`<h1 class="mt-0 mb-8 py-0" data-svelte-h="svelte-zh66lr">Application Error</h1> ${o.error.message?`<p class="font-mono text-sm bg-base-200 px-2 py-2"><span class="font-mono text-base">${w(o.status)}</span>:${w(o.error.message)}</p>`:""} ${o.error.stack||o.error.cause?`${g(je,"Accordion").$$render(n,{},{},{default:()=>`${g(Be,"AccordionItem").$$render(n,{title:"Error Details"},{},{default:()=>`<div class="relative"><span class="absolute top-2 right-2">${g(De,"CopyButton").$$render(n,{textToCopy:r},{},{})}</span> <pre class="font-mono text-sm bg-base-200 px-2 py-2 overflow-auto">${w(r)}</pre></div>`})}`})}`:""}`:`<h1 data-svelte-h="svelte-blh3ny">Unknown Error Encountered</h1> <span class="font-mono text-base">HTTP ${w(o.status)}</span>`}`}`});export{ht as default};
