import{c as b,b as v,i as f,v as c,h as u}from"../../../../chunks/ssr.js";import"dequal";import"../../../../chunks/VennDiagram.svelte_svelte_type_style_lang.js";import"clsx";import"chroma-js";import"../../../../chunks/Query.js";import"@uwdata/mosaic-sql";import"@evidence-dev/universal-sql/client-duckdb";import"ssf";import"@tidyjs/tidy";import"deep-object-diff";import"../../../../chunks/index5.js";import{i as g,n as E}from"../../../../chunks/utils.js";import{c as x,I as p,A as y,l as _,m as C,_ as S,n as A,T as N}from"../../../../chunks/index7.js";import"export-to-csv";import"echarts";import"yaml";import"@astronautlabs/jsonpath";import"tua-body-scroll-lock";import"lodash/merge.js";import"prismjs";import"debounce";import"downloadjs";import"echarts-stat";const j=b((s,t,e,h)=>{let{columns:a=[]}=t,{rowClass:i=""}=t,{class:r=""}=t;const n=o=>{switch(o.toUpperCase()){case"INT":case"INTEGER":case"BIGINT":case"SMALLINT":case"TINYINT":case"DOUBLE":return S;case"DATE":case"DATETIME":case"TIMESTAMP":return C;case"BOOLEAN":return _;default:return y}};return t.columns===void 0&&e.columns&&a!==void 0&&e.columns(a),t.rowClass===void 0&&e.rowClass&&i!==void 0&&e.rowClass(i),t.class===void 0&&e.class&&r!==void 0&&e.class(r),`<ul${v("class",x("list-none m-0 flex flex-col gap-1",r),0)}>${f(a,o=>{let l=o.data_type??o.column_type;return` <li${v("class",x("font-mono text-sm rounded-sm flex flex-row hover:bg-base-200 max-w-full",i),0)}><div class="grid grid-cols-[auto,auto,1fr] gap-2 px-2 py-1 w-full lowercase truncate">${c(p,"Icon").$$render(s,{src:n(l),class:"text-base-content-muted w-5 h-5"},{},{})} ${u(l)} <b class="lowercase truncate">${u(o.column_name)}</b></div> </li>`})}</ul>`}),L=b((s,t,e,h)=>{let{data:a}=t,{__db:i}=a;async function r(){return{}}let n="",o="";return t.data===void 0&&e.data&&a!==void 0&&e.data(a),`${function(l){return g(l)?(l.then(null,E),`
	Loading Schema Information...
`):function(w){return` <section><div><ul class="list-none m-0 p-0 flex flex-col gap-1 mb-1">${f(Object.entries(w),([m,T])=>`<li class="font-mono m-0 text-sm"><button class="${["bg-base-200 px-2 py-1 rounded-sm font-bold flex w-full hover:bg-base-300 hover:text-base-content",(o===m?"bg-info":"")+" "+(o===m?"text-info-content":"")].join(" ").trim()}">${c(p,"Icon").$$render(s,{src:A,class:"w-5 h-5 mr-1"},{},{})} ${u(m)} </button></li> ${o===m?`<ul class="list-none m-0 flex flex-col gap-1">${f(Object.entries(T),([I,d])=>`<li class="font-mono m-0 text-sm font-bold ml-3"><button class="${["bg-base-200 px-2 py-1 rounded-sm flex w-full hover:bg-base-300 hover:text-base-content",(n===d?"bg-info":"")+" "+(n===d?"text-info-content":"")].join(" ").trim()}">${c(p,"Icon").$$render(s,{src:N,class:"w-5 h-5 mr-1"},{},{})} ${u(I)} </button></li> ${n===d?`${c(j,"TableView").$$render(s,{columns:d.columns,rowClass:"ml-6 "},{},{})}`:""}`)} </ul>`:""}`)}</ul></div></section> `}(l)}(r())}`}),at=b((s,t,e,h)=>{let{data:a}=t;return t.data===void 0&&e.data&&a!==void 0&&e.data(a),`<h1 class="markdown" data-svelte-h="svelte-15777oi">Project Schema</h1> <p class="markdown" data-svelte-h="svelte-ak948l">This page details the tables and columns that are currently loaded in your project.</p> <h2 class="markdown" data-svelte-h="svelte-9qt1ro">Sources</h2> ${c(L,"SchemaExplorer").$$render(s,{data:a},{},{})}`});export{at as default};
