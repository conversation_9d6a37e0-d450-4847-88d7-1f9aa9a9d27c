import{c as dt,s as G,b as Bo,a as ql,n as Dt,d as Ol}from"../../chunks/utils.js";import{c as F,a as yt,b as q,d as Q,s as Re,g as Ne,o as ye,h as R,v as O,e as ol,i as Jt,f as jl}from"../../chunks/ssr.js";import"dequal";import{o as zl,w as Ql,f as Hl,m as Oe,e as yo,t as ll,n as Qe,p as Vl,s as xo,i as al,d as Yl,r as Xl,b as il,c as He,k as Ve,u as Zl,a as Jl,v as $l,x as Pe,y as ta,z as eo,A as kl,V as Ze,B as ea,I as oa,C as la,D as aa,E as ia,G as ra,H as na,J as sa,K as da,L as Aa,l as de,M as Ll,N as Ml,O as ca,P as ua}from"../../chunks/VennDiagram.svelte_svelte_type_style_lang.js";import{w as Je,d as se}from"../../chunks/index2.js";import"clsx";import"chroma-js";import{r as fa,s as va,l as rl,Q as De,a as nl,p as Ca}from"../../chunks/Query.js";import{h as ke,p as ha}from"../../chunks/profile.js";import{a as sl}from"../../chunks/index6.js";import{p as Oo}from"../../chunks/stores.js";import{b as ma,f as zt,g as Xe,a as Gt,D as wo,C as ya,c as dl,s as xa,d as Qt,e as Ye,E as Dl,Q as ga,h as ba,i as Ea,j as wa,k as Ia,l as pa}from"../../chunks/CodeBlock.js";import{t as z,c as _t,I as $e,f as Sa}from"../../chunks/index7.js";import{M as Ta,b as Al,I as Ba,a as cl,c as Oa}from"../../chunks/Info.js";import{t as ul,o as ka,g as La,d as Ma,u as Da,a as fl,b as _a,c as _l,r as Rl,e as Nl,f as Ra,h as Na}from"../../chunks/helpers.js";import{r as Ga,h as vl,c as Pa,i as Cl,B as Ua}from"../../chunks/button.js";import"deep-object-diff";import"../../chunks/index5.js";import{c as Fa}from"../../chunks/checkRequiredProps.js";import"ssf";import{tidy as ne,summarize as Wa,nDistinct as Ka,groupBy as Io,mutateWithSummary as hl,sum as po,mutate as go,rate as ml,rename as yl,complete as xl,summarizeAt as qa}from"@tidyjs/tidy";import"@uwdata/mosaic-sql";import"export-to-csv";import{throttle as ja}from"echarts";import"@evidence-dev/universal-sql/client-duckdb";import"yaml";import"@astronautlabs/jsonpath";import"tua-body-scroll-lock";import"lodash/merge.js";import za from"prismjs";import"debounce";import"downloadjs";import"echarts-stat";const Qa={positioning:{placement:"bottom"},arrowSize:8,defaultOpen:!1,disableFocusTrap:!1,closeOnEscape:!0,preventScroll:!1,onOpenChange:void 0,closeOnOutsideClick:!0,portal:void 0,forceVisible:!1,openFocus:void 0,closeFocus:void 0,onOutsideClick:void 0},{name:Le}=Yl("popover"),Ha=["trigger","content"];function Va(o){const t={...Qa,...o},e=ul(zl(t,"open","ids")),{positioning:a,arrowSize:l,disableFocusTrap:i,preventScroll:n,closeOnEscape:d,closeOnOutsideClick:C,portal:A,forceVisible:f,openFocus:y,closeFocus:v,onOutsideClick:s}=e,x=t.open??Je(t.defaultOpen),c=ka(x,t?.onOpenChange),u=Ql.writable(null),w=ul({...La(Ha),...t.ids});Hl(()=>{u.set(document.getElementById(w.trigger.get()))});function M(){c.set(!1);const r=document.getElementById(w.trigger.get());vl({prop:v.get(),defaultEl:r})}const I=Ma({open:c,activeTrigger:u,forceVisible:f}),B=Oe(Le("content"),{stores:[I,A,w.content],returned:([r,g,E])=>({hidden:r&&al?void 0:!0,tabindex:-1,style:xo({display:r?void 0:"none"}),id:E,"data-state":r?"open":"closed","data-portal":Vl(g)}),action:r=>{let g=Qe;const E=yo([I,u,a,i,d,C,A],([b,m,U,W,X,P,At])=>{g(),!(!b||!m)&&ll().then(()=>{g(),g=Da(r,{anchorElement:m,open:c,options:{floating:U,focusTrap:W?null:{returnFocusOnDeactivate:!1,clickOutsideDeactivates:P,allowOutsideClick:!0,escapeDeactivates:X},modal:{shouldCloseOnInteractOutside:p,onClose:M,open:b,closeOnInteractOutside:P},escapeKeydown:X?{handler:()=>{M()}}:null,portal:fl(r,At)}}).destroy})});return{destroy(){E(),g()}}}});function L(r){c.update(g=>!g),r&&r!==u.get()&&u.set(r)}function p(r){if(s.get()?.(r),r.defaultPrevented)return!1;const g=r.target,E=document.getElementById(w.trigger.get());return!(E&&Xl(g)&&(g===E||E.contains(g)))}const D=Oe(Le("trigger"),{stores:[I,w.content,w.trigger],returned:([r,g,E])=>({role:"button","aria-haspopup":"dialog","aria-expanded":r?"true":"false","data-state":gl(r),"aria-controls":g,id:E}),action:r=>({destroy:il(He(r,"click",()=>{L(r)}),He(r,"keydown",E=>{E.key!==Ve.ENTER&&E.key!==Ve.SPACE||(E.preventDefault(),L(r))}))})}),T=Oe(Le("overlay"),{stores:[I],returned:([r])=>({hidden:r?void 0:!0,tabindex:-1,style:xo({display:r?void 0:"none"}),"aria-hidden":"true","data-state":gl(r)}),action:r=>{let g=Qe,E=Qe,b=Qe;if(d.get()){const m=Zl(r,{handler:()=>{M()}});m&&m.destroy&&(g=m.destroy)}return E=yo([A],([m])=>{if(b(),m===null)return;const U=fl(r,m);U!==null&&(b=_a(r,U).destroy)}),{destroy(){g(),E(),b()}}}}),S=Oe(Le("arrow"),{stores:l,returned:r=>({"data-arrow":!0,style:xo({position:"absolute",width:`var(--arrow-size, ${r}px)`,height:`var(--arrow-size, ${r}px)`})})}),h=Oe(Le("close"),{returned:()=>({type:"button"}),action:r=>({destroy:il(He(r,"click",E=>{E.defaultPrevented||M()}),He(r,"keydown",E=>{E.defaultPrevented||E.key!==Ve.ENTER&&E.key!==Ve.SPACE||(E.preventDefault(),L())}))})});return yo([c,u,n],([r,g,E])=>{if(!al)return;const b=[];if(r){g||ll().then(()=>{const U=document.getElementById(w.trigger.get());Jl(U)&&u.set(U)}),E&&b.push(Ga());const m=g??document.getElementById(w.trigger.get());vl({prop:y.get(),defaultEl:m})}return()=>{b.forEach(m=>m())}}),{ids:w,elements:{trigger:D,content:B,arrow:S,close:h,overlay:T},states:{open:c},options:e}}function gl(o){return o?"open":"closed"}function Ya(){return{NAME:"separator",PARTS:["root"]}}function Xa(o){const{NAME:t,PARTS:e}=Ya(),a=_l(t,e),l={...Pa(Rl(o)),getAttrs:a};return{...l,updateOption:Nl(l.options)}}const Za=F((o,t,e,a)=>{let l,i=dt(t,["orientation","decorative","asChild","el"]),n,d,{orientation:C="horizontal"}=t,{decorative:A=!0}=t,{asChild:f=!1}=t,{el:y=void 0}=t;const{elements:{root:v},updateOption:s,getAttrs:x}=Xa({orientation:C,decorative:A});d=G(v,u=>n=u);const c=x("root");return t.orientation===void 0&&e.orientation&&C!==void 0&&e.orientation(C),t.decorative===void 0&&e.decorative&&A!==void 0&&e.decorative(A),t.asChild===void 0&&e.asChild&&f!==void 0&&e.asChild(f),t.el===void 0&&e.el&&y!==void 0&&e.el(y),s("orientation",C),s("decorative",A),l=n,Object.assign(l,c),d(),`${f?`${a.default?a.default({builder:l}):""}`:`<div${yt([Q(l),Q(i)],{})}${q("this",y,0)}></div>`}`});function Gl(){return{NAME:"popover",PARTS:["arrow","close","content","trigger"]}}function Ja(o){const{NAME:t,PARTS:e}=Gl(),a=_l(t,e),l={...Va({positioning:{placement:"bottom",gutter:0},...Rl(o),forceVisible:!0}),getAttrs:a};return Re(t,l),{...l,updateOption:Nl(l.options)}}function ko(){const{NAME:o}=Gl();return Ne(o)}function $a(o){const e={...{side:"bottom",align:"center"},...o},{options:{positioning:a}}=ko();Ra(a)(e)}const ti=F((o,t,e,a)=>{let l,i,{disableFocusTrap:n=void 0}=t,{closeOnEscape:d=void 0}=t,{closeOnOutsideClick:C=void 0}=t,{preventScroll:A=void 0}=t,{portal:f=void 0}=t,{open:y=void 0}=t,{onOpenChange:v=void 0}=t,{openFocus:s=void 0}=t,{closeFocus:x=void 0}=t,{onOutsideClick:c=void 0}=t;const{updateOption:u,states:{open:w},ids:M}=Ja({disableFocusTrap:n,closeOnEscape:d,closeOnOutsideClick:C,preventScroll:A,portal:f,defaultOpen:y,openFocus:s,closeFocus:x,onOutsideClick:c,onOpenChange:({next:B})=>(y!==B&&(v?.(B),y=B),B),positioning:{gutter:0,offset:{mainAxis:1}}}),I=se([M.content,M.trigger],([B,L])=>({content:B,trigger:L}));return i=G(I,B=>l=B),t.disableFocusTrap===void 0&&e.disableFocusTrap&&n!==void 0&&e.disableFocusTrap(n),t.closeOnEscape===void 0&&e.closeOnEscape&&d!==void 0&&e.closeOnEscape(d),t.closeOnOutsideClick===void 0&&e.closeOnOutsideClick&&C!==void 0&&e.closeOnOutsideClick(C),t.preventScroll===void 0&&e.preventScroll&&A!==void 0&&e.preventScroll(A),t.portal===void 0&&e.portal&&f!==void 0&&e.portal(f),t.open===void 0&&e.open&&y!==void 0&&e.open(y),t.onOpenChange===void 0&&e.onOpenChange&&v!==void 0&&e.onOpenChange(v),t.openFocus===void 0&&e.openFocus&&s!==void 0&&e.openFocus(s),t.closeFocus===void 0&&e.closeFocus&&x!==void 0&&e.closeFocus(x),t.onOutsideClick===void 0&&e.onOutsideClick&&c!==void 0&&e.onOutsideClick(c),y!==void 0&&w.set(y),u("disableFocusTrap",n),u("closeOnEscape",d),u("closeOnOutsideClick",C),u("preventScroll",A),u("portal",f),u("openFocus",s),u("closeFocus",x),u("onOutsideClick",c),i(),`${a.default?a.default({ids:l}):""}`}),ei=F((o,t,e,a)=>{let l,i=dt(t,["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","id","side","align","sideOffset","alignOffset","collisionPadding","avoidCollisions","collisionBoundary","sameWidth","fitViewport","strategy","overlap","el"]),n,d,C,A,{transition:f=void 0}=t,{transitionConfig:y=void 0}=t,{inTransition:v=void 0}=t,{inTransitionConfig:s=void 0}=t,{outTransition:x=void 0}=t,{outTransitionConfig:c=void 0}=t,{asChild:u=!1}=t,{id:w=void 0}=t,{side:M="bottom"}=t,{align:I="center"}=t,{sideOffset:B=0}=t,{alignOffset:L=0}=t,{collisionPadding:p=8}=t,{avoidCollisions:D=!0}=t,{collisionBoundary:T=void 0}=t,{sameWidth:S=!1}=t,{fitViewport:h=!1}=t,{strategy:r="absolute"}=t,{overlap:g=!1}=t,{el:E=void 0}=t;const{elements:{content:b},states:{open:m},ids:U,getAttrs:W}=ko();A=G(b,P=>C=P),d=G(m,P=>n=P);const X=W("content");return t.transition===void 0&&e.transition&&f!==void 0&&e.transition(f),t.transitionConfig===void 0&&e.transitionConfig&&y!==void 0&&e.transitionConfig(y),t.inTransition===void 0&&e.inTransition&&v!==void 0&&e.inTransition(v),t.inTransitionConfig===void 0&&e.inTransitionConfig&&s!==void 0&&e.inTransitionConfig(s),t.outTransition===void 0&&e.outTransition&&x!==void 0&&e.outTransition(x),t.outTransitionConfig===void 0&&e.outTransitionConfig&&c!==void 0&&e.outTransitionConfig(c),t.asChild===void 0&&e.asChild&&u!==void 0&&e.asChild(u),t.id===void 0&&e.id&&w!==void 0&&e.id(w),t.side===void 0&&e.side&&M!==void 0&&e.side(M),t.align===void 0&&e.align&&I!==void 0&&e.align(I),t.sideOffset===void 0&&e.sideOffset&&B!==void 0&&e.sideOffset(B),t.alignOffset===void 0&&e.alignOffset&&L!==void 0&&e.alignOffset(L),t.collisionPadding===void 0&&e.collisionPadding&&p!==void 0&&e.collisionPadding(p),t.avoidCollisions===void 0&&e.avoidCollisions&&D!==void 0&&e.avoidCollisions(D),t.collisionBoundary===void 0&&e.collisionBoundary&&T!==void 0&&e.collisionBoundary(T),t.sameWidth===void 0&&e.sameWidth&&S!==void 0&&e.sameWidth(S),t.fitViewport===void 0&&e.fitViewport&&h!==void 0&&e.fitViewport(h),t.strategy===void 0&&e.strategy&&r!==void 0&&e.strategy(r),t.overlap===void 0&&e.overlap&&g!==void 0&&e.overlap(g),t.el===void 0&&e.el&&E!==void 0&&e.el(E),w&&U.content.set(w),l=C,Object.assign(l,X),n&&$a({side:M,align:I,sideOffset:B,alignOffset:L,collisionPadding:p,avoidCollisions:D,collisionBoundary:T,sameWidth:S,fitViewport:h,strategy:r,overlap:g}),d(),A(),`${u&&n?`${a.default?a.default({builder:l}):""}`:`${f&&n?`<div${yt([Q(l),Q(i)],{})}${q("this",E,0)}>${a.default?a.default({builder:l}):""}</div>`:`${v&&x&&n?`<div${yt([Q(l),Q(i)],{})}${q("this",E,0)}>${a.default?a.default({builder:l}):""}</div>`:`${v&&n?`<div${yt([Q(l),Q(i)],{})}${q("this",E,0)}>${a.default?a.default({builder:l}):""}</div>`:`${x&&n?`<div${yt([Q(l),Q(i)],{})}${q("this",E,0)}>${a.default?a.default({builder:l}):""}</div>`:`${n?`<div${yt([Q(l),Q(i)],{})}${q("this",E,0)}>${a.default?a.default({builder:l}):""}</div>`:""}`}`}`}`}`}`}),oi=F((o,t,e,a)=>{let l,i,n=dt(t,["asChild","id","el"]),d,C,A,f,{asChild:y=!1}=t,{id:v=void 0}=t,{el:s=void 0}=t;const{elements:{trigger:x},states:{open:c},ids:u,getAttrs:w}=ko();C=G(x,I=>d=I),f=G(c,I=>A=I),Na();const M=w("trigger");return t.asChild===void 0&&e.asChild&&y!==void 0&&e.asChild(y),t.id===void 0&&e.id&&v!==void 0&&e.id(v),t.el===void 0&&e.el&&s!==void 0&&e.el(s),v&&u.trigger.set(v),l={...M,"aria-controls":A?u.content:void 0},i=d,Object.assign(i,l),C(),f(),`${y?`${a.default?a.default({builder:i}):""}`:`<button${yt([Q(i),{type:"button"},Q(n)],{})}${q("this",s,0)}>${a.default?a.default({builder:i}):""}</button>`}`}),So=(o,t={serializeStrings:!0})=>o==null?"null":typeof o=="string"?t.serializeStrings!==!1?`'${o.replaceAll("'","''")}'`:o:typeof o=="number"||typeof o=="bigint"||typeof o=="boolean"?String(o):o instanceof Date?`'${o.toISOString()}'::TIMESTAMP_MS`:Array.isArray(o)?`[${o.map(e=>So(e,t)).join(", ")}]`:JSON.stringify(o),li=F((o,t,e,a)=>{let{enabled:l=!0}=t;return t.enabled===void 0&&e.enabled&&l!==void 0&&e.enabled(l),l=z(l),`<div class="${["contents",l?"print:hidden":""].join(" ").trim()}">${a.default?a.default({}):""}</div>`}),Pl=Symbol("EVIDENCE_DROPDOWN_CTX");let ai=0;const me=F((o,t,e,a)=>{let{value:l}=t,{valueLabel:i=l}=t,{idx:n=-1}=t,{__auto:d=!1}=t;d||(n=ai++);const C=Ne(Pl);return ye(C.registerOption({value:l,label:i,idx:n,__auto:d})),t.value===void 0&&e.value&&l!==void 0&&e.value(l),t.valueLabel===void 0&&e.valueLabel&&i!==void 0&&e.valueLabel(i),t.idx===void 0&&e.idx&&n!==void 0&&e.idx(n),t.__auto===void 0&&e.__auto&&d!==void 0&&e.__auto(d),""});function ii(o){return Object.keys(o).reduce((t,e)=>o[e]===void 0?t:t+`${e}:${o[e]};`,"")}const ri={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"};function to(o,t,e,a){const l=Array.isArray(t)?t:[t];return l.forEach(i=>o.addEventListener(i,e,a)),()=>{l.forEach(i=>o.removeEventListener(i,e,a))}}function Ul(...o){return(...t)=>{for(const e of o)typeof e=="function"&&e(...t)}}const ni=F((o,t,e,a)=>{let l,i=dt(t,["label","shouldFilter","filter","value","onValueChange","loop","onKeydown","state","ids","asChild"]),n,d,{label:C=void 0}=t,{shouldFilter:A=!0}=t,{filter:f=void 0}=t,{value:y=void 0}=t,{onValueChange:v=void 0}=t,{loop:s=void 0}=t,{onKeydown:x=void 0}=t,{state:c=void 0}=t,{ids:u=void 0}=t,{asChild:w=!1}=t;const{commandEl:M,handleRootKeydown:I,ids:B,state:L}=$l({label:C,shouldFilter:A,filter:f,value:y,onValueChange:g=>{g!==y&&(y=g,v?.(g))},loop:s,state:c,ids:u});d=G(L,g=>n=g);function p(g){g&&g!==n.value&&Bo(L,n.value=g,n)}function D(g){return M.set(g),{destroy:Ul(to(g,"keydown",h))}}const T={role:"application",id:B.root,"data-cmdk-root":""},S={"data-cmdk-label":"",for:B.input,id:B.label,style:ii(ri)};function h(g){x?.(g),!g.defaultPrevented&&I(g)}const r={action:D,attrs:T};return t.label===void 0&&e.label&&C!==void 0&&e.label(C),t.shouldFilter===void 0&&e.shouldFilter&&A!==void 0&&e.shouldFilter(A),t.filter===void 0&&e.filter&&f!==void 0&&e.filter(f),t.value===void 0&&e.value&&y!==void 0&&e.value(y),t.onValueChange===void 0&&e.onValueChange&&v!==void 0&&e.onValueChange(v),t.loop===void 0&&e.loop&&s!==void 0&&e.loop(s),t.onKeydown===void 0&&e.onKeydown&&x!==void 0&&e.onKeydown(x),t.state===void 0&&e.state&&c!==void 0&&e.state(c),t.ids===void 0&&e.ids&&u!==void 0&&e.ids(u),t.asChild===void 0&&e.asChild&&w!==void 0&&e.asChild(w),p(y),l={root:r,label:{attrs:S},stateStore:L,state:n},d(),`${w?`${a.default?a.default({...l}):""}`:`<div${yt([Q(T),Q(i)],{})}> <label${yt([Q(S)],{})}>${R(C??"")}</label> ${a.default?a.default({...l}):""}</div>`}`}),si=F((o,t,e,a)=>{dt(t,["asChild"]);let l,i,{asChild:n=!1}=t;const d=Pe();return i=G(d,C=>l=C),t.asChild===void 0&&e.asChild&&n!==void 0&&e.asChild(n),l.filtered.count,i(),""}),di=F((o,t,e,a)=>{let l,i,n,d,C=dt(t,["heading","value","alwaysRender","asChild"]),A,f,{heading:y=void 0}=t,{value:v=""}=t,{alwaysRender:s=!1}=t,{asChild:x=!1}=t;const{id:c}=ta(s),u=eo(),w=Pe(),M=kl(),I=se(w,p=>s||u.filter()===!1||!p.search?!0:p.filtered.groups.has(c));f=G(I,p=>A=p);function B(p){if(v){u.value(c,v),p.setAttribute(Ze,v);return}y?v=y.trim().toLowerCase():p.textContent&&(v=p.textContent.trim().toLowerCase()),u.value(c,v),p.setAttribute(Ze,v)}const L={"data-cmdk-group-heading":"","aria-hidden":!0,id:M};return t.heading===void 0&&e.heading&&y!==void 0&&e.heading(y),t.value===void 0&&e.value&&v!==void 0&&e.value(v),t.alwaysRender===void 0&&e.alwaysRender&&s!==void 0&&e.alwaysRender(s),t.asChild===void 0&&e.asChild&&x!==void 0&&e.asChild(x),l={"data-cmdk-group":"",role:"presentation",hidden:A?void 0:!0,"data-value":v},i={"data-cmdk-group-items":"",role:"group","aria-labelledby":y?M:void 0},n={action:B,attrs:l},d={attrs:i},f(),`${x?`${a.default?a.default({container:n,group:d,heading:{attrs:L}}):""}`:`<div${yt([Q(l),Q(C)],{})}>${y?`<div${yt([Q(L)],{})}>${R(y)}</div>`:""} <div${yt([Q(i)],{})}>${a.default?a.default({container:n,group:d,heading:{attrs:L}}):""}</div></div>`}`});function Ai(o){return new Promise(t=>setTimeout(t,o))}const ci=F((o,t,e,a)=>{let l=dt(t,["autofocus","value","asChild","el"]),i,n,d,C;const{ids:A,commandEl:f}=eo(),y=Pe(),v=se(y,p=>p.search);C=G(v,p=>d=p);const s=se(y,p=>p.value);let{autofocus:x=void 0}=t,{value:c=d}=t,{asChild:u=!1}=t,{el:w=void 0}=t;const M=se([s,f],([p,D])=>ea?D?.querySelector(`${oa}[${Ze}="${p}"]`)?.getAttribute("id"):void 0);n=G(M,p=>i=p);function I(p){y.updateState("search",p)}function B(p){return x&&Ai(10).then(()=>p.focus()),{destroy:to(p,"change",T=>{la(T.target)&&y.updateState("search",T.target.value)})}}let L;return t.autofocus===void 0&&e.autofocus&&x!==void 0&&e.autofocus(x),t.value===void 0&&e.value&&c!==void 0&&e.value(c),t.asChild===void 0&&e.asChild&&u!==void 0&&e.asChild(u),t.el===void 0&&e.el&&w!==void 0&&e.el(w),I(c),L={type:"text","data-cmdk-input":"",autocomplete:"off",autocorrect:"off",spellcheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":A.list,"aria-labelledby":A.label,"aria-activedescendant":i??void 0,id:A.input},n(),C(),`${u?`${a.default?a.default({action:B,attrs:L}):""}`:`<input${yt([Q(L),Q(l)],{})}${q("this",w,0)}${q("value",c,0)}>`}`}),ui=F((o,t,e,a)=>{let l,i=dt(t,["disabled","value","onSelect","alwaysRender","asChild","id"]),n,d,C,{disabled:A=!1}=t,{value:f=""}=t,{onSelect:y=void 0}=t,{alwaysRender:v=!1}=t,{asChild:s=!1}=t,{id:x=kl()}=t;const c=aa(),u=eo(),w=Pe(),M=v??c?.alwaysRender,I=se(w,T=>{if(M||u.filter()===!1||!T.search)return!0;const S=T.filtered.items.get(x);return ia(S)?!1:S>0});C=G(I,T=>T);const B=se(w,T=>T.value===f);d=G(B,T=>n=T);function L(T){!f&&T.textContent&&(f=T.textContent.trim().toLowerCase()),u.value(x,f),T.setAttribute(Ze,f);const S=Ul(to(T,"pointermove",()=>{A||D()}),to(T,"click",()=>{A||p()}));return{destroy(){S()}}}function p(){D(),y?.(f)}function D(){w.updateState("value",f,!0)}return t.disabled===void 0&&e.disabled&&A!==void 0&&e.disabled(A),t.value===void 0&&e.value&&f!==void 0&&e.value(f),t.onSelect===void 0&&e.onSelect&&y!==void 0&&e.onSelect(y),t.alwaysRender===void 0&&e.alwaysRender&&v!==void 0&&e.alwaysRender(v),t.asChild===void 0&&e.asChild&&s!==void 0&&e.asChild(s),t.id===void 0&&e.id&&x!==void 0&&e.id(x),l={"aria-disabled":A?!0:void 0,"aria-selected":n?!0:void 0,"data-disabled":A?!0:void 0,"data-selected":n?!0:void 0,"data-cmdk-item":"","data-value":f,role:"option",id:x},d(),C(),`${`${s?`${a.default?a.default({action:L,attrs:l}):""}`:`<div${yt([Q(l),Q(i)],{})}>${a.default?a.default({action:L,attrs:l}):""}</div>`}`}`}),fi=F((o,t,e,a)=>{let l=dt(t,["el","asChild"]),i;const{ids:n}=eo(),d=Pe();i=G(d,c=>c);let{el:C=void 0}=t,{asChild:A=!1}=t;function f(c){let u;const w=c.closest("[data-cmdk-list]");if(!ra(w))return;const M=new ResizeObserver(()=>{u=requestAnimationFrame(()=>{const I=c.offsetHeight;w.style.setProperty("--cmdk-list-height",I.toFixed(1)+"px")})});return M.observe(c),{destroy(){cancelAnimationFrame(u),M.unobserve(c)}}}const y={"data-cmdk-list":"",role:"listbox","aria-label":"Suggestions",id:n.list,"aria-labelledby":n.input},v={"data-cmdk-list-sizer":""},s={attrs:y},x={attrs:v,action:f};return t.el===void 0&&e.el&&C!==void 0&&e.el(C),t.asChild===void 0&&e.asChild&&A!==void 0&&e.asChild(A),i(),`${A?`${a.default?a.default({list:s,sizer:x}):""}`:`<div${yt([Q(y),Q(l)],{})}${q("this",C,0)}><div${yt([Q(v)],{})}>${a.default?a.default({}):""}</div></div>`}`}),vi=F((o,t,e,a)=>{let l=dt(t,["value","class"]),{value:i=void 0}=t,{class:n=void 0}=t;t.value===void 0&&e.value&&i!==void 0&&e.value(i),t.class===void 0&&e.class&&n!==void 0&&e.class(n);let d,C,A=o.head;do d=!0,o.head=A,C=`${O(ni,"CommandPrimitive.Root").$$render(o,Object.assign({},{class:_t("flex h-full w-full flex-col overflow-hidden rounded-md bg-base-100",n)},l,{value:i}),{value:f=>{i=f,d=!1}},{default:()=>`${a.default?a.default({}):""}`})}`;while(!d);return C}),Ci=F((o,t,e,a)=>{let l=dt(t,["class"]),{class:i=void 0}=t;return t.class===void 0&&e.class&&i!==void 0&&e.class(i),`${O(si,"CommandPrimitive.Empty").$$render(o,Object.assign({},{class:_t("py-6 text-center text-sm",i)},l),{},{default:()=>`${a.default?a.default({}):""}`})}`}),hi=F((o,t,e,a)=>{let l=dt(t,["class"]),{class:i=void 0}=t;return t.class===void 0&&e.class&&i!==void 0&&e.class(i),`${O(di,"CommandPrimitive.Group").$$render(o,Object.assign({},{class:_t("text-foreground overflow-hidden p-1 [&_[data-cmdk-group-heading]]:px-2 [&_[data-cmdk-group-heading]]:py-1.5 [&_[data-cmdk-group-heading]]:text-xs [&_[data-cmdk-group-heading]]:font-medium [&_[data-cmdk-group-heading]]:text-base-content-muted",i)},l),{},{default:()=>`${a.default?a.default({}):""}`})}`}),To=F((o,t,e,a)=>{let l=dt(t,["class"]),{class:i=void 0}=t;return t.class===void 0&&e.class&&i!==void 0&&e.class(i),`${O(ui,"CommandPrimitive.Item").$$render(o,Object.assign({},{class:_t("relative flex cursor-default select-none items-center rounded-xs px-2 py-1.5 text-sm outline-none aria-selected:bg-base-200 data-[disabled]:pointer-events-none data-[disabled]:opacity-50",i)},l),{},{default:()=>`${a.default?a.default({}):""}`})}`}),mi=F((o,t,e,a)=>{let l=dt(t,["class","value"]),{class:i=void 0}=t,{value:n=""}=t;t.class===void 0&&e.class&&i!==void 0&&e.class(i),t.value===void 0&&e.value&&n!==void 0&&e.value(n);let d,C,A=o.head;do d=!0,o.head=A,C=`<div class="flex items-center border-b border-base-300 px-3" data-cmdk-input-wrapper="">${O($e,"Icon").$$render(o,{src:Ta,class:"mr-2 h-4 w-4 shrink-0 text-base-content-muted"},{},{})} ${O(ci,"CommandPrimitive.Input").$$render(o,Object.assign({},{class:_t("flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-base-content-muted disabled:cursor-not-allowed disabled:opacity-50",i)},l,{value:n}),{value:f=>{n=f,d=!1}},{})}</div>`;while(!d);return C}),yi=F((o,t,e,a)=>{let l=dt(t,["class"]),{class:i=void 0}=t;return t.class===void 0&&e.class&&i!==void 0&&e.class(i),`${O(fi,"CommandPrimitive.List").$$render(o,Object.assign({},{class:_t("max-h-[300px] overflow-y-auto overflow-x-hidden",i)},l),{},{default:()=>`${a.default?a.default({}):""}`})}`}),bl=F((o,t,e,a)=>{let{value:l}=t,{valueLabel:i=l}=t,{active:n=!1}=t,{handleSelect:d}=t,{multiple:C}=t;return t.value===void 0&&e.value&&l!==void 0&&e.value(l),t.valueLabel===void 0&&e.valueLabel&&i!==void 0&&e.valueLabel(i),t.active===void 0&&e.active&&n!==void 0&&e.active(n),t.handleSelect===void 0&&e.handleSelect&&d!==void 0&&e.handleSelect(d),t.multiple===void 0&&e.multiple&&C!==void 0&&e.multiple(C),`${O(To,"Command.Item").$$render(o,{value:String(i),onSelect:()=>d({value:l,label:i})},{},{default:()=>`${C?`<div${q("class",_t("mr-2 flex h-4 w-4 items-center justify-center rounded-xs border border-base-content",n?"bg-base-content text-base-100":"opacity-50 [&_svg]:invisible"),0)}>${O($e,"Icon").$$render(o,{src:Al,class:_t("h-4 w-4")},{},{})}</div>`:`<div class="mr-2 flex h-4 w-4 items-center justify-center">${O($e,"Icon").$$render(o,{src:Al,class:_t("h-4 w-4",n?"":"text-transparent")},{},{})}</div>`} <span class="line-clamp-4">${R(i)}</span>`})}`}),xi=F((o,t,e,a)=>{let l=dt(t,["class","transition","transitionConfig","align","sideOffset"]),{class:i=void 0}=t,{transition:n=Sa}=t,{transitionConfig:d=void 0}=t,{align:C="center"}=t,{sideOffset:A=4}=t;return t.class===void 0&&e.class&&i!==void 0&&e.class(i),t.transition===void 0&&e.transition&&n!==void 0&&e.transition(n),t.transitionConfig===void 0&&e.transitionConfig&&d!==void 0&&e.transitionConfig(d),t.align===void 0&&e.align&&C!==void 0&&e.align(C),t.sideOffset===void 0&&e.sideOffset&&A!==void 0&&e.sideOffset(A),`${O(ei,"PopoverPrimitive.Content").$$render(o,Object.assign({},{transition:n},{transitionConfig:d},{align:C},{sideOffset:A},l,{class:_t("z-50 w-72 rounded-md border border-base-300 p-4 shadow-md outline-none bg-base-100",i)}),{},{default:()=>`${a.default?a.default({}):""}`})}`}),gi=ti,bi=oi,El=F((o,t,e,a)=>{let l=dt(t,["class","orientation","decorative"]),{class:i=void 0}=t,{orientation:n="horizontal"}=t,{decorative:d=void 0}=t;return t.class===void 0&&e.class&&i!==void 0&&e.class(i),t.orientation===void 0&&e.orientation&&n!==void 0&&e.orientation(n),t.decorative===void 0&&e.decorative&&d!==void 0&&e.decorative(d),`${O(Za,"SeparatorPrimitive.Root").$$render(o,Object.assign({},{class:_t("shrink-0 bg-base-300",n==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",i)},{orientation:n},{decorative:d},l),{},{})}`}),bo=F((o,t,e,a)=>{let l=dt(t,["class","href","variant"]),{class:i=void 0}=t,{href:n=void 0}=t,{variant:d="default"}=t;return t.class===void 0&&e.class&&i!==void 0&&e.class(i),t.href===void 0&&e.href&&n!==void 0&&e.href(n),t.variant===void 0&&e.variant&&d!==void 0&&e.variant(d),`${(C=>C?`<${n?"a":"span"}${yt([{href:ol(n)},{class:ol(_t(na({variant:d,className:i})))},Q(l)],{})}>${Cl(C)?"":`${a.default?a.default({}):""}`}${Cl(C)?"":`</${C}>`}`:"")(n?"a":"span")}`}),Ei={code:".viewport.svelte-1youqmj{position:relative;overflow-y:auto;-webkit-overflow-scrolling:touch;display:block}.contents.svelte-1youqmj,.row.svelte-1youqmj{display:block}.row.svelte-1youqmj{overflow:hidden}",map:`{"version":3,"file":"Virtual.svelte","sources":["Virtual.svelte"],"sourcesContent":["<script>\\n\\timport { onMount, tick } from 'svelte';\\n\\n\\t// props\\n\\texport let items;\\n\\texport let height = '100%';\\n\\texport let itemHeight = undefined;\\n\\n\\t// read-only, but visible to consumers via bind:start\\n\\texport let start = 0;\\n\\texport let end = 0;\\n\\n\\t// local state\\n\\tlet height_map = [];\\n\\tlet rows;\\n\\tlet viewport;\\n\\tlet contents;\\n\\tlet viewport_height = 0;\\n\\tlet visible;\\n\\tlet mounted;\\n\\n\\tlet top = 0;\\n\\tlet bottom = 0;\\n\\tlet average_height;\\n\\n\\t$: visible = items.slice(start, end).map((data, i) => {\\n\\t\\treturn { index: i + start, data };\\n\\t});\\n\\n\\t// whenever \`items\` changes, invalidate the current heightmap\\n\\t$: if (mounted) refresh(items, viewport_height, itemHeight);\\n\\n\\tasync function refresh(items, viewport_height, itemHeight) {\\n\\t\\tconst { scrollTop } = viewport;\\n\\n\\t\\tawait tick(); // wait until the DOM is up to date\\n\\t\\tif (!mounted) return;\\n\\n\\t\\tlet content_height = top - scrollTop;\\n\\t\\tlet i = start;\\n\\n\\t\\twhile (content_height < viewport_height && i < items.length) {\\n\\t\\t\\tlet row = rows[i - start];\\n\\n\\t\\t\\tif (!row) {\\n\\t\\t\\t\\tend = i + 1;\\n\\t\\t\\t\\tawait tick(); // render the newly visible row\\n\\t\\t\\t\\tif (!mounted) return;\\n\\t\\t\\t\\trow = rows[i - start];\\n\\t\\t\\t}\\n\\n\\t\\t\\tconst row_height = (height_map[i] =\\n\\t\\t\\t\\titemHeight || row?.offsetHeight || Number.MAX_SAFE_INTEGER);\\n\\t\\t\\tcontent_height += row_height;\\n\\t\\t\\ti += 1;\\n\\t\\t}\\n\\n\\t\\tend = i;\\n\\n\\t\\tconst remaining = items.length - end;\\n\\t\\taverage_height = (top + content_height) / end;\\n\\n\\t\\tbottom = remaining * average_height;\\n\\t\\theight_map.length = items.length;\\n\\t}\\n\\n\\tasync function handle_scroll() {\\n\\t\\tconst { scrollTop } = viewport;\\n\\n\\t\\tconst old_start = start;\\n\\n\\t\\tfor (let v = 0; v < rows.length; v += 1) {\\n\\t\\t\\theight_map[start + v] = itemHeight || rows[v]?.offsetHeight || Number.MAX_SAFE_INTEGER;\\n\\t\\t}\\n\\n\\t\\tlet i = 0;\\n\\t\\tlet y = 0;\\n\\n\\t\\twhile (i < items.length) {\\n\\t\\t\\tconst row_height = height_map[i] || average_height;\\n\\t\\t\\tif (y + row_height > scrollTop) {\\n\\t\\t\\t\\tstart = i;\\n\\t\\t\\t\\ttop = y;\\n\\n\\t\\t\\t\\tbreak;\\n\\t\\t\\t}\\n\\n\\t\\t\\ty += row_height;\\n\\t\\t\\ti += 1;\\n\\t\\t}\\n\\n\\t\\twhile (i < items.length) {\\n\\t\\t\\ty += height_map[i] || average_height;\\n\\t\\t\\ti += 1;\\n\\n\\t\\t\\tif (y > scrollTop + viewport_height) break;\\n\\t\\t}\\n\\n\\t\\tend = i;\\n\\n\\t\\tconst remaining = items.length - end;\\n\\t\\taverage_height = y / end;\\n\\n\\t\\twhile (i < items.length) height_map[i++] = average_height;\\n\\t\\tbottom = remaining * average_height;\\n\\n\\t\\t// prevent jumping if we scrolled up into unknown territory\\n\\t\\tif (start < old_start) {\\n\\t\\t\\tawait tick();\\n\\n\\t\\t\\tlet expected_height = 0;\\n\\t\\t\\tlet actual_height = 0;\\n\\n\\t\\t\\tfor (let i = start; i < old_start; i += 1) {\\n\\t\\t\\t\\tif (rows[i - start]) {\\n\\t\\t\\t\\t\\texpected_height += height_map[i];\\n\\t\\t\\t\\t\\tactual_height += itemHeight || rows[i - start]?.offsetHeight || Number.MAX_SAFE_INTEGER;\\n\\t\\t\\t\\t}\\n\\t\\t\\t}\\n\\n\\t\\t\\tconst d = actual_height - expected_height;\\n\\t\\t\\tviewport.scrollTo(0, scrollTop + d);\\n\\t\\t}\\n\\n\\t\\t// TODO if we overestimated the space these\\n\\t\\t// rows would occupy we may need to add some\\n\\t\\t// more. maybe we can just call handle_scroll again?\\n\\t}\\n\\n\\t// trigger initial refresh\\n\\tonMount(() => {\\n\\t\\trows = contents.getElementsByClassName('row');\\n\\t\\tmounted = true;\\n\\t\\treturn () => (mounted = false);\\n\\t});\\n<\/script>\\n\\n<div\\n\\tbind:this={viewport}\\n\\tbind:offsetHeight={viewport_height}\\n\\ton:scroll={handle_scroll}\\n\\tstyle=\\"height: {height};\\"\\n\\tclass=\\"viewport\\"\\n>\\n\\t<div\\n\\t\\tclass=\\"contents\\"\\n\\t\\tbind:this={contents}\\n\\t\\tstyle=\\"padding-top: {top}px; padding-bottom: {bottom}px;\\"\\n\\t>\\n\\t\\t{#each visible as row (row.index)}\\n\\t\\t\\t<div class=\\"row\\">\\n\\t\\t\\t\\t<slot item={row.data}>Missing template</slot>\\n\\t\\t\\t</div>\\n\\t\\t{/each}\\n\\t</div>\\n</div>\\n\\n<style>\\n\\t.viewport {\\n\\t\\tposition: relative;\\n\\t\\toverflow-y: auto;\\n\\t\\t-webkit-overflow-scrolling: touch;\\n\\t\\tdisplay: block;\\n\\t}\\n\\n\\t.contents,\\n\\t.row {\\n\\t\\tdisplay: block;\\n\\t}\\n\\n\\t.row {\\n\\t\\toverflow: hidden;\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AA8JC,wBAAU,CACT,QAAQ,CAAE,QAAQ,CAClB,UAAU,CAAE,IAAI,CAChB,0BAA0B,CAAE,KAAK,CACjC,OAAO,CAAE,KACV,CAEA,wBAAS,CACT,mBAAK,CACJ,OAAO,CAAE,KACV,CAEA,mBAAK,CACJ,QAAQ,CAAE,MACX"}`},wi=F((o,t,e,a)=>{let{items:l}=t,{height:i="100%"}=t,{itemHeight:n=void 0}=t,{start:d=0}=t,{end:C=0}=t,A,f,y,v=0,s=0;return t.items===void 0&&e.items&&l!==void 0&&e.items(l),t.height===void 0&&e.height&&i!==void 0&&e.height(i),t.itemHeight===void 0&&e.itemHeight&&n!==void 0&&e.itemHeight(n),t.start===void 0&&e.start&&d!==void 0&&e.start(d),t.end===void 0&&e.end&&C!==void 0&&e.end(C),o.css.add(Ei),y=l.slice(d,C).map((x,c)=>({index:c+d,data:x})),`<div style="${"height: "+R(i,!0)+";"}" class="viewport svelte-1youqmj"${q("this",A,0)}><div class="contents svelte-1youqmj" style="${"padding-top: "+R(v,!0)+"px; padding-bottom: "+R(s,!0)+"px;"}"${q("this",f,0)}>${Jt(y,x=>`<div class="row svelte-1youqmj">${a.default?a.default({item:x.data}):"Missing template"} </div>`)}</div> </div>`}),wl=5;function Ii(o){return"similarity"in o?o.similarity*-1:o.ordinal??0}const Il=F((o,t,e,a)=>{let l,i=ql(a),n,d=Dt,C=()=>(d(),d=G(l,k=>n=k),l),A,f=Dt,y=()=>(f(),f=G(ht,k=>A=k),ht),v,s,x,c,u,w,M,I,B,L;I=G(Oo,k=>M=k);const p=sa();c=G(p,k=>x=k);let{title:D=void 0}=t,{name:T}=t,{multiple:S=!1}=t,{hideDuringPrint:h=!0}=t,{disableSelectAll:r=!1}=t,{defaultValue:g=[]}=t,{noDefault:E=!1}=t,{selectAllByDefault:b=!1}=t,{description:m=void 0}=t,{value:U="value",data:W,label:X=U,order:P=void 0,where:At=void 0}=t;const{results:pt,update:Pt}=ma({value:U,data:W,label:X,order:P,where:At},`Dropdown-${T}`,M?.data?.data[`Dropdown-${T}_data`]);w=G(pt,k=>u=k);let N=!!W;const kt=T in x&&"rawValues"in x[T]&&Array.isArray(x[T].rawValues)?x[T].rawValues:[],Lt=da({multiselect:S,defaultValues:Array.isArray(g)?g:[g],initialOptions:kt,noDefault:E,selectAllByDefault:z(b)}),{addOptions:ut,removeOptions:ft,options:ct,selectedOptions:Tt,selectAll:xt,deselectAll:Y,toggleSelected:et,pauseSorting:ot,resumeSorting:Z,forceSort:vt,destroy:tt}=Lt;L=G(ct,k=>B=k),s=G(Tt,k=>v=k),ye(tt);const gt=k=>{JSON.stringify(k)!==JSON.stringify(x[T])&&Bo(p,x[T]=k,x)};let bt=[],Et=v.length>0;ye(Tt.subscribe(k=>{if(Et||=k.length>0,k&&Et){const K=k;S?gt({label:K.map($=>$.label).join(", "),value:K.length?`(${K.map($=>So($.value))})`:"(select null where 0)",rawValues:K}):K.length?K.length&&gt({label:K[0].label,value:So(K[0].value,{serializeStrings:!1}),rawValues:K}):gt({label:"",value:null,rawValues:[]})}})),Re(Pl,{registerOption:k=>(ut(k),()=>{ft(k)})});let rt,lt="",Ct=0,ht;const nt=Aa(()=>{if(Ct++,lt&&N){const k=Ct,K=l.search(lt,"label");K.hash!==ht?.hash&&fa(()=>{k===Ct&&(y(ht=K),vt())},K.fetch())}else y(ht=l??W)});let H=[];U||(W?H.push('Missing required prop: "value".'):i.default||H.push('Dropdown requires either "value" and "data" props or <DropdownOption />.')),W&&typeof W!="object"&&(typeof W=="string"?H.push(`'${W}' is not a recognized query result. Data should be provided in the format: data = {'${W.replace("data.","")}'}`):H.push(`'${W}' is not a recognized query result. Data should be an object. e.g data = {QueryName}`));try{Fa({name:T})}catch(k){H.push(k.message)}let mt=!1;t.title===void 0&&e.title&&D!==void 0&&e.title(D),t.name===void 0&&e.name&&T!==void 0&&e.name(T),t.multiple===void 0&&e.multiple&&S!==void 0&&e.multiple(S),t.hideDuringPrint===void 0&&e.hideDuringPrint&&h!==void 0&&e.hideDuringPrint(h),t.disableSelectAll===void 0&&e.disableSelectAll&&r!==void 0&&e.disableSelectAll(r),t.defaultValue===void 0&&e.defaultValue&&g!==void 0&&e.defaultValue(g),t.noDefault===void 0&&e.noDefault&&E!==void 0&&e.noDefault(E),t.selectAllByDefault===void 0&&e.selectAllByDefault&&b!==void 0&&e.selectAllByDefault(b),t.description===void 0&&e.description&&m!==void 0&&e.description(m),t.value===void 0&&e.value&&U!==void 0&&e.value(U),t.data===void 0&&e.data&&W!==void 0&&e.data(W),t.label===void 0&&e.label&&X!==void 0&&e.label(X),t.order===void 0&&e.order&&P!==void 0&&e.order(P),t.where===void 0&&e.where&&At!==void 0&&e.where(At);let st,at,St=o.head;do st=!0,o.head=St,S=z(S),h=z(h),r=z(r),E=z(E),b=z(b),Pt({value:U,data:W,label:X,order:P,where:At}),C({hasQuery:N,query:l}=u),l&&l.fetch(),nt(),rt?ot():Z(),A?.dataLoaded&&(bt=A),n?.error&&N&&!mt&&(H=[...H,n.error],mt=!0),at=`${a.default?a.default({}):""} ${Jt(bt,k=>`${O(me,"DropdownOption").$$render(o,{value:k[U]??k.value,valueLabel:k[X]??k.label,idx:Ii(k),__auto:!0},{},{})}`)} ${O(li,"HiddenInPrint").$$render(o,{enabled:h},{},{default:()=>`<div class="mt-2 mb-4 ml-0 mr-2 inline-block">${H.length>0?`${O(Ba,"InlineError").$$render(o,{inputType:"Dropdown",error:H,height:"32",width:"140"},{},{})} `:`${O(gi,"Popover.Root").$$render(o,{open:rt},{open:k=>{rt=k,st=!1}},{default:()=>`${O(bi,"Popover.Trigger").$$render(o,{asChild:!0},{},{default:({builder:k})=>`${O(Ua,"Button").$$render(o,{builders:[k],variant:"outline",role:"combobox",size:"sm",class:"min-w-5 h-8 border border-base-300","aria-label":D??zt(T)},{},{default:()=>`${D&&!S?`${R(D)} ${m?`${O(cl,"Info").$$render(o,{description:m,className:"pl-1"},{},{})}`:""} ${v.length>0?`${O(El,"Separator").$$render(o,{orientation:"vertical",class:"mx-2 h-4"},{},{})} ${R(v[0].label)}`:""}`:`${v.length>0&&!S?`${R(v[0].label)}`:`${R(D??zt(T))} ${m?`${O(cl,"Info").$$render(o,{description:m,className:"pl-1"},{},{})}`:""}`}`} ${O($e,"Icon").$$render(o,{src:Oa,class:"ml-2 h-4 w-4"},{},{})} ${v.length>0&&S?`${O(El,"Separator").$$render(o,{orientation:"vertical",class:"mx-2 h-4"},{},{})} ${O(bo,"Badge").$$render(o,{variant:"default",class:"rounded-xs px-1 font-normal sm:hidden"},{},{default:()=>`${R(v.length)}`})} <div class="hidden space-x-1 sm:flex">${v.length>3?`${O(bo,"Badge").$$render(o,{variant:"default",class:"rounded-xs px-1 font-normal"},{},{default:()=>`${R(v.length)} Selected`})}`:`${Jt(v,K=>`${O(bo,"Badge").$$render(o,{variant:"default",class:"rounded-xs px-1 font-normal"},{},{default:()=>`${R(K.label)} `})}`)}`}</div>`:""}`})}`})} ${O(xi,"Popover.Content").$$render(o,{class:"w-[200px] p-0",align:"start",side:"bottom"},{},{default:()=>`${O(vi,"Command.Root").$$render(o,{shouldFilter:!1},{},{default:()=>`${O(mi,"Command.Input").$$render(o,{placeholder:D,value:lt},{value:k=>{lt=k,st=!1}},{})} ${O(yi,"Command.List").$$render(o,{},{},{default:()=>`${O(Ci,"Command.Empty").$$render(o,{},{},{default:()=>"No results found."})} ${O(hi,"Command.Group").$$render(o,{},{},{default:()=>`${B.length<=wl?`${Jt(B,(k,K)=>`${O(bl,"DropdownOptionDisplay").$$render(o,{id:K,value:k.value,valueLabel:k.label,handleSelect:({value:$,label:Rt})=>{et({value:$,label:Rt}),S||(rt=!1)},multiple:S,active:v.some($=>$.value===k.value&&$.label===k.label)},{},{})}`)}`:`${O(wi,"VirtualList").$$render(o,{height:`${wl*32}px`,items:B},{},{default:({item:k})=>`${O(bl,"DropdownOptionDisplay").$$render(o,{value:k?.value,valueLabel:k?.label,handleSelect:({value:K,label:$})=>{et({value:K,label:$}),S||(rt=!1)},multiple:S,active:v.some(K=>K.value===k.value&&K.label===k.label)},{},{})}`})}`}`})} ${S?`${r?"":`<div class="-mx-1 h-px bg-base-300"></div> ${O(To,"Command.Item").$$render(o,{class:"justify-center text-center",onSelect:xt},{},{default:()=>"Select all"})}`} <div class="-mx-1 h-px bg-base-300"></div> ${O(To,"Command.Item").$$render(o,{disabled:v.length===0,class:"justify-center text-center",onSelect:Y},{},{default:()=>"Clear selection"})}`:""}`})}`})}`})}`})}`}</div>`})}`;while(!st);return d(),f(),s(),c(),w(),I(),L(),at}),pi={code:"svg.svelte-lqleyo.svelte-lqleyo{display:inline-block;vertical-align:middle;transition:transform 0.15s ease-in}span.svelte-lqleyo.svelte-lqleyo{margin:auto 0 auto 0}[aria-expanded='true'].svelte-lqleyo svg.svelte-lqleyo{transform:rotate(0.25turn)}",map:`{"version":3,"file":"ChevronToggle.svelte","sources":["ChevronToggle.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport { getThemeStores } from '../../themes/themes.js';\\n\\n\\tconst { resolveColor } = getThemeStores();\\n\\n\\texport let toggled = false;\\n\\n\\texport let color = 'base-content';\\n\\t$: colorStore = resolveColor(color);\\n\\n\\texport let size = 10;\\n<\/script>\\n\\n<span aria-expanded={toggled}>\\n\\t<svg viewBox=\\"0 0 16 16\\" width={size} height={size}\\n\\t\\t><path\\n\\t\\t\\tfill={$colorStore}\\n\\t\\t\\tfill-rule=\\"evenodd\\"\\n\\t\\t\\td=\\"M6.22 3.22a.75.75 0 011.06 0l4.25 4.25a.75.75 0 010 1.06l-4.25 4.25a.75.75 0 01-1.06-1.06L9.94 8 6.22 4.28a.75.75 0 010-1.06z\\"\\n\\t\\t/></svg\\n\\t>\\n</span>\\n\\n<style>\\n\\tsvg {\\n\\t\\tdisplay: inline-block;\\n\\t\\tvertical-align: middle;\\n\\t\\ttransition: transform 0.15s ease-in;\\n\\t}\\n\\n\\tspan {\\n\\t\\tmargin: auto 0 auto 0;\\n\\t}\\n\\n\\t[aria-expanded='true'] svg {\\n\\t\\ttransform: rotate(0.25turn);\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AA4BC,+BAAI,CACH,OAAO,CAAE,YAAY,CACrB,cAAc,CAAE,MAAM,CACtB,UAAU,CAAE,SAAS,CAAC,KAAK,CAAC,OAC7B,CAEA,gCAAK,CACJ,MAAM,CAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CACrB,CAEA,CAAC,aAAa,CAAC,MAAM,eAAC,CAAC,iBAAI,CAC1B,SAAS,CAAE,OAAO,QAAQ,CAC3B"}`},pl=F((o,t,e,a)=>{let l,i,n=Dt,d=()=>(n(),n=G(l,v=>i=v),l);const{resolveColor:C}=de();let{toggled:A=!1}=t,{color:f="base-content"}=t,{size:y=10}=t;return t.toggled===void 0&&e.toggled&&A!==void 0&&e.toggled(A),t.color===void 0&&e.color&&f!==void 0&&e.color(f),t.size===void 0&&e.size&&y!==void 0&&e.size(y),o.css.add(pi),d(l=C(f)),n(),`<span${q("aria-expanded",A,0)} class="svelte-lqleyo"><svg viewBox="0 0 16 16"${q("width",y,0)}${q("height",y,0)} class="svelte-lqleyo"><path${q("fill",i,0)} fill-rule="evenodd" d="M6.22 3.22a.75.75 0 011.06 0l4.25 4.25a.75.75 0 010 1.06l-4.25 4.25a.75.75 0 01-1.06-1.06L9.94 8 6.22 4.28a.75.75 0 010-1.06z"></path></svg> </span>`}),Si={code:".marker.svelte-v9l93j{border-left:5px solid transparent;border-right:5px solid transparent;border-top:9px solid var(--base-content-muted);transform:rotate(-90deg);transition:transform 0.2s ease}.rotate-marker.svelte-v9l93j{transform:rotate(0deg)}button.svelte-v9l93j{display:flex;align-items:center;cursor:pointer}",map:`{"version":3,"file":"Details.svelte","sources":["Details.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport { slide } from 'svelte/transition';\\n\\timport { toBoolean } from '../../utils.js';\\n\\n\\texport let title = 'Details';\\n\\texport let open = false;\\n\\t$: open = toBoolean(open);\\n\\n\\texport let printShowAll = true;\\n\\t$: printShowAll = toBoolean(printShowAll);\\n\\tlet printing = false;\\n<\/script>\\n\\n<svelte:window\\n\\ton:beforeprint={() => (printing = true)}\\n\\ton:afterprint={() => (printing = false)}\\n\\ton:export-beforeprint={() => (printing = true)}\\n\\ton:export-afterprint={() => (printing = false)}\\n/>\\n\\n{#if !printing || !printShowAll}\\n\\t<div class=\\"mb-4 mt-2\\">\\n\\t\\t<button\\n\\t\\t\\tclass=\\"text-sm text-base-content-muted cursor-pointer inline-flex gap-2\\"\\n\\t\\t\\ton:click={() => (open = !open)}\\n\\t\\t>\\n\\t\\t\\t<span class={open ? 'marker rotate-marker' : 'marker'} />\\n\\t\\t\\t<span> {title} </span>\\n\\t\\t</button>\\n\\n\\t\\t{#if open}\\n\\t\\t\\t<div class=\\"pl-[calc(0.5rem+10px)] pt-3 mb-6 text-sm\\" transition:slide|local>\\n\\t\\t\\t\\t<slot />\\n\\t\\t\\t</div>\\n\\t\\t{/if}\\n\\t</div>\\n{:else}\\n\\t<div class=\\"mb-4 mt-2 text-base-content-muted\\">\\n\\t\\t<span class=\\"text-sm font-semibold inline-flex\\"> {title} </span>\\n\\t\\t<div class=\\"pt-1 mb-6 text-sm\\">\\n\\t\\t\\t<slot />\\n\\t\\t</div>\\n\\t</div>\\n{/if}\\n\\n<style>\\n\\t.marker {\\n\\t\\tborder-left: 5px solid transparent;\\n\\t\\tborder-right: 5px solid transparent;\\n\\t\\tborder-top: 9px solid var(--base-content-muted);\\n\\t\\ttransform: rotate(-90deg);\\n\\t\\ttransition: transform 0.2s ease;\\n\\t}\\n\\n\\t.rotate-marker {\\n\\t\\ttransform: rotate(0deg);\\n\\t}\\n\\n\\tbutton {\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tcursor: pointer;\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AAkDC,qBAAQ,CACP,WAAW,CAAE,GAAG,CAAC,KAAK,CAAC,WAAW,CAClC,YAAY,CAAE,GAAG,CAAC,KAAK,CAAC,WAAW,CACnC,UAAU,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CAC/C,SAAS,CAAE,OAAO,MAAM,CAAC,CACzB,UAAU,CAAE,SAAS,CAAC,IAAI,CAAC,IAC5B,CAEA,4BAAe,CACd,SAAS,CAAE,OAAO,IAAI,CACvB,CAEA,oBAAO,CACN,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,OACT"}`},Ti=F((o,t,e,a)=>{let{title:l="Details"}=t,{open:i=!1}=t,{printShowAll:n=!0}=t;return t.title===void 0&&e.title&&l!==void 0&&e.title(l),t.open===void 0&&e.open&&i!==void 0&&e.open(i),t.printShowAll===void 0&&e.printShowAll&&n!==void 0&&e.printShowAll(n),o.css.add(Si),i=z(i),n=z(n),` ${`<div class="mb-4 mt-2"><button class="text-sm text-base-content-muted cursor-pointer inline-flex gap-2 svelte-v9l93j"><span class="${R(Ol(i?"marker rotate-marker":"marker"),!0)+" svelte-v9l93j"}"></span> <span>${R(l)}</span></button> ${i?`<div class="pl-[calc(0.5rem+10px)] pt-3 mb-6 text-sm">${a.default?a.default({}):""}</div>`:""}</div>`}`}),Bi={code:"div.pagination.svelte-ghf30y.svelte-ghf30y{padding:0px 5px;align-content:center;border-bottom:1px solid var(--base-200);height:1.25em;background-color:var(--base-100);display:flex;flex-direction:row;justify-content:space-between;align-items:center}.slider.svelte-ghf30y.svelte-ghf30y{-webkit-appearance:none;width:75%;height:10px;margin:0 0;outline:none;border-radius:10px;display:inline-block;cursor:pointer}.slider.svelte-ghf30y.svelte-ghf30y::-webkit-slider-thumb{background-color:var(--color-info);-webkit-appearance:none;appearance:none;width:10px;height:10px;cursor:pointer;border-radius:10px}.slider.svelte-ghf30y.svelte-ghf30y::-moz-range-thumb{background-color:var(--color-info);width:10px;height:10px;cursor:pointer}.slider.svelte-ghf30y.svelte-ghf30y::-moz-range-thumb{background-color:var(--color-info);width:10px;height:10px;cursor:pointer}span.svelte-ghf30y.svelte-ghf30y{font-family:var(--ui-font-family-compact);-webkit-font-smoothing:antialiased;float:right}.scrollbox.svelte-ghf30y.svelte-ghf30y{width:100%;overflow-x:auto;border-bottom:1px solid var(--base-300);background-color:var(--base-100)}.results-pane.svelte-ghf30y .download-button{margin-top:10px}table.svelte-ghf30y.svelte-ghf30y{width:100%;border-collapse:collapse;font-family:var(--ui-font-family);font-variant-numeric:tabular-nums}td.svelte-ghf30y.svelte-ghf30y{padding:2px 8px;overflow:hidden;text-overflow:ellipsis}td.svelte-ghf30y div.svelte-ghf30y{width:100px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.other.svelte-ghf30y.svelte-ghf30y{text-align:left}.string.svelte-ghf30y.svelte-ghf30y{text-align:left}.date.svelte-ghf30y.svelte-ghf30y{text-align:left}.number.svelte-ghf30y.svelte-ghf30y{text-align:right}.boolean.svelte-ghf30y.svelte-ghf30y{text-align:left}.index.svelte-ghf30y.svelte-ghf30y{text-align:left;max-width:min-content}tr.type-indicator.svelte-ghf30y.svelte-ghf30y{border-bottom:1px solid var(--base-300)}.footer.svelte-ghf30y.svelte-ghf30y{display:flex;justify-content:flex-end;font-size:12px}",map:`{"version":3,"file":"QueryDataTable.svelte","sources":["QueryDataTable.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport { blur, slide } from 'svelte/transition';\\n\\timport DownloadData from '../DownloadData.svelte';\\n\\timport getColumnSummary from '@evidence-dev/component-utilities/getColumnSummary';\\n\\timport { formatValue } from '@evidence-dev/component-utilities/formatting';\\n\\timport { throttle } from 'echarts';\\n\\n\\texport let queryID;\\n\\texport let data;\\n\\n\\t$: columnSummary = getColumnSummary(data, 'array');\\n\\t$: columnWidths = 90 / (columnSummary.length + 1);\\n\\n\\t// Slicer\\n\\tlet index = 0;\\n\\tlet size = 5;\\n\\t$: max = Math.max(data.length - size, 0);\\n\\t$: dataPage = data.slice(index, index + size);\\n\\tlet updatedSlice;\\n\\n\\tfunction slice() {\\n\\t\\tupdatedSlice = data.slice(index, index + size);\\n\\t\\tdataPage = updatedSlice;\\n\\t}\\n\\n\\tconst updateIndex = throttle((event) => {\\n\\t\\tindex = Math.min(Math.max(0, index + Math.floor(event.deltaY / Math.abs(event.deltaY))), max);\\n\\t\\tslice();\\n\\t}, 60);\\n\\n\\tfunction handleWheel(event) {\\n\\t\\t// abort if scroll is in x-direction\\n\\t\\tif (Math.abs(event.deltaX) >= Math.abs(event.deltaY)) {\\n\\t\\t\\treturn;\\n\\t\\t}\\n\\n\\t\\tconst hasScrolledToTop = event.deltaY < 0 && index === 0;\\n\\t\\tconst hasScrolledToBottom = event.deltaY > 0 && index === max;\\n\\n\\t\\tif (hasScrolledToTop || hasScrolledToBottom) {\\n\\t\\t\\treturn;\\n\\t\\t}\\n\\n\\t\\tevent.preventDefault();\\n\\t\\tupdateIndex(event);\\n\\t}\\n<\/script>\\n\\n<div class=\\"results-pane py-1\\" transition:slide|local>\\n\\t<div class=\\"scrollbox pretty-scrollbar\\">\\n\\t\\t<table class=\\"text-xs\\" in:blur>\\n\\t\\t\\t<thead>\\n\\t\\t\\t\\t<tr>\\n\\t\\t\\t\\t\\t<th class=\\"py-0 px-2 font-medium index text-base-content-muted\\" style=\\"width:10%\\" />\\n\\t\\t\\t\\t\\t{#each columnSummary as column}\\n\\t\\t\\t\\t\\t\\t<th\\n\\t\\t\\t\\t\\t\\t\\tclass=\\"py-0 px-2 font-medium {column.type}\\"\\n\\t\\t\\t\\t\\t\\t\\tstyle=\\"width:{columnWidths}%\\"\\n\\t\\t\\t\\t\\t\\t\\tevidenceType={column.evidenceColumnType?.evidenceType || 'unavailable'}\\n\\t\\t\\t\\t\\t\\t\\tevidenceTypeFidelity={column.evidenceColumnType?.typeFidelity || 'unavailable'}\\n\\t\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t\\t{column.id}\\n\\t\\t\\t\\t\\t\\t</th>\\n\\t\\t\\t\\t\\t{/each}\\n\\t\\t\\t\\t</tr><tr />\\n\\t\\t\\t\\t<tr class=\\"type-indicator\\">\\n\\t\\t\\t\\t\\t<th\\n\\t\\t\\t\\t\\t\\tclass=\\"py-0 px-2 index type-indicator text-base-content-muted font-normal\\"\\n\\t\\t\\t\\t\\t\\tstyle=\\"width:10%\\"\\n\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t\\t{#each columnSummary as column}\\n\\t\\t\\t\\t\\t\\t<th\\n\\t\\t\\t\\t\\t\\t\\tclass=\\"{column.type} type-indicator text-base-content-muted font-normal py-0 px-2\\"\\n\\t\\t\\t\\t\\t\\t\\tstyle=\\"width:{columnWidths}%\\"\\n\\t\\t\\t\\t\\t\\t\\tevidenceType={column.evidenceColumnType?.evidenceType || 'unavailable'}\\n\\t\\t\\t\\t\\t\\t\\tevidenceTypeFidelity={column.evidenceColumnType?.typeFidelity || 'unavailable'}\\n\\t\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t\\t{column.type}\\n\\t\\t\\t\\t\\t\\t</th>\\n\\t\\t\\t\\t\\t{/each}\\n\\t\\t\\t\\t</tr><tr />\\n\\t\\t\\t</thead>\\n\\t\\t\\t<tbody on:wheel={handleWheel}>\\n\\t\\t\\t\\t{#each dataPage as row, i}\\n\\t\\t\\t\\t\\t<tr>\\n\\t\\t\\t\\t\\t\\t<td class=\\"index text-base-content-muted\\" style=\\"width:10%\\">\\n\\t\\t\\t\\t\\t\\t\\t{#if i === 0}\\n\\t\\t\\t\\t\\t\\t\\t\\t<!-- <input type=\\"number\\" bind:value={index} max={max} min=0 on:input={slice} class=\\"index-key\\" autofocus reversed> -->\\n\\t\\t\\t\\t\\t\\t\\t\\t{(index + i + 1).toLocaleString()}\\n\\t\\t\\t\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t\\t\\t\\t{(index + i + 1).toLocaleString()}\\n\\t\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t{#each columnSummary as column, j}\\n\\t\\t\\t\\t\\t\\t\\t{#if row[column.id] == null}\\n\\t\\t\\t\\t\\t\\t\\t\\t<td\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tclass=\\"text-base-content-muted {columnSummary[j].type}\\"\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tstyle=\\"width:{columnWidths}%\\"\\n\\t\\t\\t\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{'Ø'}\\n\\t\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\t{:else if columnSummary[j].type === 'number'}\\n\\t\\t\\t\\t\\t\\t\\t\\t<td class=\\"number\\" style=\\"width:{columnWidths}%;\\">\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{formatValue(\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\trow[column.id],\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tcolumnSummary[j].format,\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tcolumnSummary[j].columnUnitSummary\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t)}\\n\\t\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\t{:else if columnSummary[j].type === 'date'}\\n\\t\\t\\t\\t\\t\\t\\t\\t<td\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tclass=\\"string\\"\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tstyle=\\"width:{columnWidths}%\\"\\n\\t\\t\\t\\t\\t\\t\\t\\t\\ttitle={formatValue(\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\trow[column.id],\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tcolumnSummary[j].format,\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tcolumnSummary[j].columnUnitSummary\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t)}\\n\\t\\t\\t\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<div>\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{formatValue(\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\trow[column.id],\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tcolumnSummary[j].format,\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tcolumnSummary[j].columnUnitSummary\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t)}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\t{:else if columnSummary[j].type === 'string'}\\n\\t\\t\\t\\t\\t\\t\\t\\t<td class=\\"string\\" style=\\"width:{columnWidths}%\\" title={row[column.id]}>\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<div>\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{row[column.id] || 'Ø'}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\t{:else if columnSummary[j].type === 'boolean'}\\n\\t\\t\\t\\t\\t\\t\\t\\t<td class=\\"boolean\\" style=\\"width:{columnWidths}%\\" title={row[column.id]}>\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<div>\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{row[column.id] ?? 'Ø'}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t\\t\\t\\t<td class=\\"other\\" style=\\"width:{columnWidths}%\\">\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{row[column.id] || 'Ø'}\\n\\t\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t\\t{/each}\\n\\t\\t\\t\\t\\t</tr>\\n\\t\\t\\t\\t{/each}\\n\\t\\t\\t</tbody>\\n\\t\\t</table>\\n\\t</div>\\n\\n\\t{#if max > 0}\\n\\t\\t<div class=\\"pagination\\">\\n\\t\\t\\t<input\\n\\t\\t\\t\\ttype=\\"range\\"\\n\\t\\t\\t\\t{max}\\n\\t\\t\\t\\tstep=\\"1\\"\\n\\t\\t\\t\\tbind:value={index}\\n\\t\\t\\t\\ton:input={slice}\\n\\t\\t\\t\\tclass=\\"slider bg-info/30 hover:bg-info/40 transition-colors\\"\\n\\t\\t\\t/>\\n\\t\\t\\t<span class=\\"text-xs\\">\\n\\t\\t\\t\\t{(index + size).toLocaleString()} of {(max + size).toLocaleString()}\\n\\t\\t\\t</span>\\n\\t\\t</div>\\n\\t{/if}\\n\\n\\t<div class=\\"footer\\">\\n\\t\\t<DownloadData class=\\"download-button\\" {data} {queryID} display />\\n\\t</div>\\n</div>\\n\\n<style>\\n\\tdiv.pagination {\\n\\t\\tpadding: 0px 5px;\\n\\t\\talign-content: center;\\n\\t\\tborder-bottom: 1px solid var(--base-200);\\n\\t\\theight: 1.25em;\\n\\t\\tbackground-color: var(--base-100);\\n\\t\\tdisplay: flex;\\n\\t\\tflex-direction: row;\\n\\t\\tjustify-content: space-between;\\n\\t\\talign-items: center;\\n\\t}\\n\\n\\t.slider {\\n\\t\\t-webkit-appearance: none;\\n\\t\\twidth: 75%;\\n\\t\\theight: 10px;\\n\\t\\tmargin: 0 0;\\n\\t\\toutline: none;\\n\\t\\tborder-radius: 10px;\\n\\t\\tdisplay: inline-block;\\n\\t\\tcursor: pointer;\\n\\t}\\n\\n\\t.slider::-webkit-slider-thumb {\\n\\t\\tbackground-color: var(--color-info);\\n\\t\\t-webkit-appearance: none;\\n\\t\\tappearance: none;\\n\\t\\twidth: 10px;\\n\\t\\theight: 10px;\\n\\t\\tcursor: pointer;\\n\\t\\tborder-radius: 10px;\\n\\t}\\n\\n\\t.slider::-moz-range-thumb {\\n\\t\\tbackground-color: var(--color-info);\\n\\t\\twidth: 10px;\\n\\t\\theight: 10px;\\n\\t\\tcursor: pointer;\\n\\t}\\n\\n\\t.slider::-moz-range-thumb {\\n\\t\\tbackground-color: var(--color-info);\\n\\t\\twidth: 10px;\\n\\t\\theight: 10px;\\n\\t\\tcursor: pointer;\\n\\t}\\n\\n\\tspan {\\n\\t\\tfont-family: var(--ui-font-family-compact);\\n\\t\\t-webkit-font-smoothing: antialiased;\\n\\t\\tfloat: right;\\n\\t}\\n\\n\\t.scrollbox {\\n\\t\\twidth: 100%;\\n\\t\\toverflow-x: auto;\\n\\t\\tborder-bottom: 1px solid var(--base-300);\\n\\t\\tbackground-color: var(--base-100);\\n\\t}\\n\\n\\t.results-pane :global(.download-button) {\\n\\t\\tmargin-top: 10px;\\n\\t}\\n\\n\\ttable {\\n\\t\\twidth: 100%;\\n\\t\\tborder-collapse: collapse;\\n\\t\\tfont-family: var(--ui-font-family);\\n\\t\\tfont-variant-numeric: tabular-nums;\\n\\t}\\n\\n\\ttd {\\n\\t\\tpadding: 2px 8px;\\n\\t\\toverflow: hidden;\\n\\t\\ttext-overflow: ellipsis;\\n\\t}\\n\\n\\ttd div {\\n\\t\\twidth: 100px;\\n\\t\\twhite-space: nowrap;\\n\\t\\toverflow: hidden;\\n\\t\\ttext-overflow: ellipsis;\\n\\t}\\n\\n\\t.other {\\n\\t\\ttext-align: left;\\n\\t}\\n\\n\\t.string {\\n\\t\\ttext-align: left;\\n\\t}\\n\\n\\t.date {\\n\\t\\ttext-align: left;\\n\\t}\\n\\n\\t.number {\\n\\t\\ttext-align: right;\\n\\t}\\n\\n\\t.boolean {\\n\\t\\ttext-align: left;\\n\\t}\\n\\n\\t.index {\\n\\t\\ttext-align: left;\\n\\t\\tmax-width: min-content;\\n\\t}\\n\\n\\ttr.type-indicator {\\n\\t\\tborder-bottom: 1px solid var(--base-300);\\n\\t}\\n\\n\\t.footer {\\n\\t\\tdisplay: flex;\\n\\t\\tjustify-content: flex-end;\\n\\t\\tfont-size: 12px;\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AAiLC,GAAG,uCAAY,CACd,OAAO,CAAE,GAAG,CAAC,GAAG,CAChB,aAAa,CAAE,MAAM,CACrB,aAAa,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACxC,MAAM,CAAE,MAAM,CACd,gBAAgB,CAAE,IAAI,UAAU,CAAC,CACjC,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,GAAG,CACnB,eAAe,CAAE,aAAa,CAC9B,WAAW,CAAE,MACd,CAEA,mCAAQ,CACP,kBAAkB,CAAE,IAAI,CACxB,KAAK,CAAE,GAAG,CACV,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,CAAC,CAAC,CAAC,CACX,OAAO,CAAE,IAAI,CACb,aAAa,CAAE,IAAI,CACnB,OAAO,CAAE,YAAY,CACrB,MAAM,CAAE,OACT,CAEA,mCAAO,sBAAuB,CAC7B,gBAAgB,CAAE,IAAI,YAAY,CAAC,CACnC,kBAAkB,CAAE,IAAI,CACxB,UAAU,CAAE,IAAI,CAChB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,OAAO,CACf,aAAa,CAAE,IAChB,CAEA,mCAAO,kBAAmB,CACzB,gBAAgB,CAAE,IAAI,YAAY,CAAC,CACnC,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,OACT,CAEA,mCAAO,kBAAmB,CACzB,gBAAgB,CAAE,IAAI,YAAY,CAAC,CACnC,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,OACT,CAEA,gCAAK,CACJ,WAAW,CAAE,IAAI,wBAAwB,CAAC,CAC1C,sBAAsB,CAAE,WAAW,CACnC,KAAK,CAAE,KACR,CAEA,sCAAW,CACV,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,IAAI,CAChB,aAAa,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACxC,gBAAgB,CAAE,IAAI,UAAU,CACjC,CAEA,2BAAa,CAAS,gBAAkB,CACvC,UAAU,CAAE,IACb,CAEA,iCAAM,CACL,KAAK,CAAE,IAAI,CACX,eAAe,CAAE,QAAQ,CACzB,WAAW,CAAE,IAAI,gBAAgB,CAAC,CAClC,oBAAoB,CAAE,YACvB,CAEA,8BAAG,CACF,OAAO,CAAE,GAAG,CAAC,GAAG,CAChB,QAAQ,CAAE,MAAM,CAChB,aAAa,CAAE,QAChB,CAEA,gBAAE,CAAC,iBAAI,CACN,KAAK,CAAE,KAAK,CACZ,WAAW,CAAE,MAAM,CACnB,QAAQ,CAAE,MAAM,CAChB,aAAa,CAAE,QAChB,CAEA,kCAAO,CACN,UAAU,CAAE,IACb,CAEA,mCAAQ,CACP,UAAU,CAAE,IACb,CAEA,iCAAM,CACL,UAAU,CAAE,IACb,CAEA,mCAAQ,CACP,UAAU,CAAE,KACb,CAEA,oCAAS,CACR,UAAU,CAAE,IACb,CAEA,kCAAO,CACN,UAAU,CAAE,IAAI,CAChB,SAAS,CAAE,WACZ,CAEA,EAAE,2CAAgB,CACjB,aAAa,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CACxC,CAEA,mCAAQ,CACP,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,QAAQ,CACzB,SAAS,CAAE,IACZ"}`};let Me=5;const Oi=F((o,t,e,a)=>{let l,i,n,d,{queryID:C}=t,{data:A}=t,f=0,y;function v(){y=A.slice(f,f+Me),d=y}return ja(s=>{f=Math.min(Math.max(0,f+Math.floor(s.deltaY/Math.abs(s.deltaY))),n),v()},60),t.queryID===void 0&&e.queryID&&C!==void 0&&e.queryID(C),t.data===void 0&&e.data&&A!==void 0&&e.data(A),o.css.add(Bi),l=Xe(A,"array"),i=90/(l.length+1),n=Math.max(A.length-Me,0),d=A.slice(f,f+Me),`<div class="results-pane py-1 svelte-ghf30y"><div class="scrollbox pretty-scrollbar svelte-ghf30y"><table class="text-xs svelte-ghf30y"><thead><tr><th class="py-0 px-2 font-medium index text-base-content-muted svelte-ghf30y" style="width:10%"></th> ${Jt(l,s=>`<th class="${"py-0 px-2 font-medium "+R(s.type,!0)+" svelte-ghf30y"}" style="${"width:"+R(i,!0)+"%"}"${q("evidencetype",s.evidenceColumnType?.evidenceType||"unavailable",0)}${q("evidencetypefidelity",s.evidenceColumnType?.typeFidelity||"unavailable",0)}>${R(s.id)} </th>`)} </tr><tr></tr> <tr class="type-indicator svelte-ghf30y"><th class="py-0 px-2 index type-indicator text-base-content-muted font-normal svelte-ghf30y" style="width:10%"></th> ${Jt(l,s=>`<th class="${R(s.type,!0)+" type-indicator text-base-content-muted font-normal py-0 px-2 svelte-ghf30y"}" style="${"width:"+R(i,!0)+"%"}"${q("evidencetype",s.evidenceColumnType?.evidenceType||"unavailable",0)}${q("evidencetypefidelity",s.evidenceColumnType?.typeFidelity||"unavailable",0)}>${R(s.type)} </th>`)} </tr><tr></tr></thead> <tbody>${Jt(d,(s,x)=>`<tr><td class="index text-base-content-muted svelte-ghf30y" style="width:10%">${x===0?` ${R((f+x+1).toLocaleString())}`:`${R((f+x+1).toLocaleString())}`}</td> ${Jt(l,(c,u)=>`${s[c.id]==null?`<td class="${"text-base-content-muted "+R(l[u].type,!0)+" svelte-ghf30y"}" style="${"width:"+R(i,!0)+"%"}">${R("Ø")} </td>`:`${l[u].type==="number"?`<td class="number svelte-ghf30y" style="${"width:"+R(i,!0)+"%;"}">${R(Gt(s[c.id],l[u].format,l[u].columnUnitSummary))} </td>`:`${l[u].type==="date"?`<td class="string svelte-ghf30y" style="${"width:"+R(i,!0)+"%"}"${q("title",Gt(s[c.id],l[u].format,l[u].columnUnitSummary),0)}><div class="svelte-ghf30y">${R(Gt(s[c.id],l[u].format,l[u].columnUnitSummary))}</div> </td>`:`${l[u].type==="string"?`<td class="string svelte-ghf30y" style="${"width:"+R(i,!0)+"%"}"${q("title",s[c.id],0)}><div class="svelte-ghf30y">${R(s[c.id]||"Ø")}</div> </td>`:`${l[u].type==="boolean"?`<td class="boolean svelte-ghf30y" style="${"width:"+R(i,!0)+"%"}"${q("title",s[c.id],0)}><div class="svelte-ghf30y">${R(s[c.id]??"Ø")}</div> </td>`:`<td class="other svelte-ghf30y" style="${"width:"+R(i,!0)+"%"}">${R(s[c.id]||"Ø")} </td>`}`}`}`}`}`)} </tr>`)}</tbody></table></div> ${n>0?`<div class="pagination svelte-ghf30y"><input type="range"${q("max",n,0)} step="1" class="slider bg-info/30 hover:bg-info/40 transition-colors svelte-ghf30y"${q("value",f,0)}> <span class="text-xs svelte-ghf30y">${R((f+Me).toLocaleString())} of ${R((n+Me).toLocaleString())}</span></div>`:""} <div class="footer svelte-ghf30y">${O(wo,"DownloadData").$$render(o,{class:"download-button",data:A,queryID:C,display:!0},{},{})}</div> </div>`}),ki={comment:{pattern:/(^|[^\\])(?:\/\*[\s\S]*?\*\/|(?:--|\/\/|#).*)/,lookbehind:!0},variable:[{pattern:/@(["'`])(?:\\[\s\S]|(?!\1)[^\\])+\1/,greedy:!0},/@[\w.$]+/],string:{pattern:/(^|[^@\\])("|')(?:\\[\s\S]|(?!\2)[^\\]|\2\2)*\2/,greedy:!0,lookbehind:!0},identifier:{pattern:/(^|[^@\\])`(?:\\[\s\S]|[^`\\]|``)*`/,greedy:!0,lookbehind:!0,inside:{punctuation:/^`|`$/}},function:/\b(?:AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\s*\()/i,keyword:/\b(?:ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:COL|_INSERT)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|KEYS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:ING|S)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\b/i,boolean:/\b(?:FALSE|NULL|TRUE)\b/i,number:/\b0x[\da-f]+\b|\b\d+(?:\.\d*)?|\B\.\d+\b/i,operator:/[-+*/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\b(?:AND|BETWEEN|DIV|ILIKE|IN|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\b/i,punctuation:/[;[\]()`,.]/},Li={code:"code.svelte-re3fhx{display:block}",map:`{"version":3,"file":"Prismjs.svelte","sources":["Prismjs.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport './prismtheme.css';\\n\\timport Prism from 'prismjs';\\n\\timport { prism_sql } from './prism-sql';\\n\\n\\texport let code = '';\\n<\/script>\\n\\n<pre class=\\"text-xs max-h-56 overflow-auto pretty-scrollbar\\">\\n  <code class=\\"language-sql\\">{@html Prism.highlight(code, prism_sql)}</code>\\n</pre>\\n\\n<style>\\n\\tcode {\\n\\t\\tdisplay: block; /* inline-block has odd behavior when it overflows on webkit mobile */\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AAiBC,kBAAK,CACJ,OAAO,CAAE,KACV"}`},Sl=F((o,t,e,a)=>{let{code:l=""}=t;return t.code===void 0&&e.code&&l!==void 0&&e.code(l),o.css.add(Li),`<pre class="text-xs max-h-56 overflow-auto pretty-scrollbar">  <code class="language-sql svelte-re3fhx"><!-- HTML_TAG_START -->${za.highlight(l,ki)}<!-- HTML_TAG_END --></code>
</pre>`}),Mi={code:"div.toggle.svelte-ska6l4{background-color:var(--base-200);border-left:1px solid var(--base-300);border-right:1px solid var(--base-300);padding:6px 0 10px 12px;font-family:var(--ui-font-family);font-size:10px;user-select:none;-webkit-user-select:none;-moz-user-select:none}button.svelte-ska6l4{padding:2px 4px 2px 4px;border-radius:3px;cursor:pointer;background-color:transparent;font-size:1em;font-weight:600}button.off.svelte-ska6l4{border:1px solid var(--base-300);transition:all 400ms}button.off.svelte-ska6l4:hover{background-color:var(--base-300);transition:all 400ms}",map:`{"version":3,"file":"CompilerToggle.svelte","sources":["CompilerToggle.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport { slide } from 'svelte/transition';\\n\\n\\texport let showCompiled;\\n\\n\\tconst toggleCompiled = function () {\\n\\t\\tshowCompiled = !showCompiled;\\n\\t};\\n<\/script>\\n\\n<div class=\\"toggle\\" transition:slide|local>\\n\\t{#if showCompiled}\\n\\t\\t<button class=\\"text-info bg-info/10 border border-info\\">Compiled</button>\\n\\t\\t<button on:click={toggleCompiled} class=\\"off\\">Written</button>\\n\\t{:else}\\n\\t\\t<button on:click={toggleCompiled} class=\\"off\\">Compiled</button>\\n\\t\\t<button class=\\"text-info bg-info/10 border border-info\\">Written</button>\\n\\t{/if}\\n</div>\\n\\n<style>\\n\\tdiv.toggle {\\n\\t\\tbackground-color: var(--base-200);\\n\\t\\tborder-left: 1px solid var(--base-300);\\n\\t\\tborder-right: 1px solid var(--base-300);\\n\\t\\tpadding: 6px 0 10px 12px;\\n\\t\\tfont-family: var(--ui-font-family);\\n\\t\\tfont-size: 10px;\\n\\t\\tuser-select: none;\\n\\t\\t-webkit-user-select: none;\\n\\t\\t-moz-user-select: none;\\n\\t}\\n\\n\\tbutton {\\n\\t\\tpadding: 2px 4px 2px 4px;\\n\\t\\tborder-radius: 3px;\\n\\t\\tcursor: pointer;\\n\\t\\tbackground-color: transparent;\\n\\t\\tfont-size: 1em;\\n\\t\\tfont-weight: 600;\\n\\t}\\n\\n\\tbutton.off {\\n\\t\\tborder: 1px solid var(--base-300);\\n\\t\\ttransition: all 400ms;\\n\\t}\\n\\n\\tbutton.off:hover {\\n\\t\\tbackground-color: var(--base-300);\\n\\t\\ttransition: all 400ms;\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AAyBC,GAAG,qBAAQ,CACV,gBAAgB,CAAE,IAAI,UAAU,CAAC,CACjC,WAAW,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACtC,YAAY,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACvC,OAAO,CAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CACxB,WAAW,CAAE,IAAI,gBAAgB,CAAC,CAClC,SAAS,CAAE,IAAI,CACf,WAAW,CAAE,IAAI,CACjB,mBAAmB,CAAE,IAAI,CACzB,gBAAgB,CAAE,IACnB,CAEA,oBAAO,CACN,OAAO,CAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CACxB,aAAa,CAAE,GAAG,CAClB,MAAM,CAAE,OAAO,CACf,gBAAgB,CAAE,WAAW,CAC7B,SAAS,CAAE,GAAG,CACd,WAAW,CAAE,GACd,CAEA,MAAM,kBAAK,CACV,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACjC,UAAU,CAAE,GAAG,CAAC,KACjB,CAEA,MAAM,kBAAI,MAAO,CAChB,gBAAgB,CAAE,IAAI,UAAU,CAAC,CACjC,UAAU,CAAE,GAAG,CAAC,KACjB"}`},Di=F((o,t,e,a)=>{let{showCompiled:l}=t;return t.showCompiled===void 0&&e.showCompiled&&l!==void 0&&e.showCompiled(l),o.css.add(Mi),`<div class="toggle svelte-ska6l4">${l?'<button class="text-info bg-info/10 border border-info svelte-ska6l4" data-svelte-h="svelte-wrfleh">Compiled</button> <button class="off svelte-ska6l4" data-svelte-h="svelte-v36xno">Written</button>':'<button class="off svelte-ska6l4" data-svelte-h="svelte-1vzm9jy">Compiled</button> <button class="text-info bg-info/10 border border-info svelte-ska6l4" data-svelte-h="svelte-qu81ez">Written</button>'} </div>`}),_i={code:":root{--scrollbar-track-color:transparent;--scrollbar-color:rgba(0, 0, 0, 0.2);--scrollbar-active-color:rgba(0, 0, 0, 0.4);--scrollbar-size:0.75rem;--scrollbar-minlength:1.5rem}.code-container.svelte-1ursthx{background-color:var(--base-200);border-left:1px solid var(--base-300);border-right:1px solid var(--base-300);overflow-x:auto;overflow-y:hidden;padding-top:0;padding-right:12px;padding-bottom:6px;padding-left:15px;scrollbar-width:thin;scrollbar-color:var(--scrollbar-color) var(--scrollbar-track-color)}.code-container.svelte-1ursthx::-webkit-scrollbar{height:var(--scrollbar-size);width:var(--scrollbar-size)}.code-container.svelte-1ursthx::-webkit-scrollbar-track{background-color:var(--scrollbar-track-color)}.over-container.svelte-1ursthx{overflow-y:hidden;overflow-x:auto}.code-container.svelte-1ursthx::-webkit-scrollbar-thumb{background-color:var(--scrollbar-color);border-radius:7px;background-clip:padding-box}.code-container.svelte-1ursthx::-webkit-scrollbar-thumb:hover{background-color:var(--scrollbar-active-color)}.code-container.svelte-1ursthx::-webkit-scrollbar-thumb:vertical{min-height:var(--scrollbar-minlength);border:3px solid transparent}.code-container.svelte-1ursthx::-webkit-scrollbar-thumb:horizontal{min-width:var(--scrollbar-minlength);border:3px solid transparent}.status-bar.svelte-1ursthx{margin-top:0px;margin-bottom:0px;background-color:var(--base-200);border-left:1px solid var(--base-300);border-right:1px solid var(--base-300);border-bottom:1px solid var(--base-300);overflow-x:auto;overflow-y:hidden;scrollbar-width:thin;scrollbar-color:var(--scrollbar-color) var(--scrollbar-track-color)}.status-bar.svelte-1ursthx::-webkit-scrollbar{height:var(--scrollbar-size);width:var(--scrollbar-size)}.status-bar.svelte-1ursthx::-webkit-scrollbar-track{background-color:var(--scrollbar-track-color)}.status-bar.svelte-1ursthx::-webkit-scrollbar-thumb{background-color:var(--scrollbar-color);border-radius:7px;background-clip:padding-box}.status-bar.svelte-1ursthx::-webkit-scrollbar-thumb:hover{background-color:var(--scrollbar-active-color)}.status-bar.svelte-1ursthx::-webkit-scrollbar-thumb:vertical{min-height:var(--scrollbar-minlength);border:3px solid transparent}.status-bar.svelte-1ursthx::-webkit-scrollbar-thumb:horizontal{min-width:var(--scrollbar-minlength);border:3px solid transparent}.closed.svelte-1ursthx{border-bottom-left-radius:6px;border-bottom-right-radius:6px}.open.svelte-1ursthx{border-bottom-left-radius:0px;border-bottom-right-radius:0px}.status-bar.success.svelte-1ursthx{color:var(--info);cursor:pointer}.status-bar.error.svelte-1ursthx{color:var(--negative);-webkit-user-select:all;-moz-user-select:all;user-select:all;cursor:auto}button.svelte-1ursthx{font-family:var(--ui-font-family-compact);-webkit-font-smoothing:antialiased;font-size:12px;-webkit-user-select:none;user-select:none;white-space:nowrap;text-align:left;width:100%;background-color:var(--base-200);border:none;border-left:1px solid var(--base-300);border-right:1px solid var(--base-300);margin-bottom:0px;cursor:pointer;padding:5px}button.title.svelte-1ursthx{border-top:1px solid var(--base-300);border-top-left-radius:6px;border-top-right-radius:6px}.scrollbox.svelte-1ursthx{display:flex;flex-direction:column}.container-a.svelte-1ursthx{background-color:var(--base-200);border-top-left-radius:6px;border-top-right-radius:6px;box-sizing:border-box;display:flex;flex-direction:column}@media print{.scrollbox.svelte-1ursthx{break-inside:avoid}}",map:`{"version":3,"file":"QueryViewer.svelte","sources":["QueryViewer.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport { slide, blur } from 'svelte/transition';\\n\\timport DataTable from './QueryViewerSupport/QueryDataTable.svelte';\\n\\timport ChevronToggle from './ChevronToggle.svelte';\\n\\timport Prism from './QueryViewerSupport/Prismjs.svelte';\\n\\timport { showQueries, localStorageStore } from '@evidence-dev/component-utilities/stores';\\n\\timport CompilerToggle from './QueryViewerSupport/CompilerToggle.svelte';\\n\\timport { page } from '$app/stores';\\n\\timport { getThemeStores } from '../../themes/themes.js';\\n\\n\\texport let queryID;\\n\\t/** @type {import(\\"@evidence-dev/sdk/usql\\").QueryValue} */\\n\\texport let queryResult;\\n\\n\\t$: pageQueries = $page.data.evidencemeta.queries;\\n\\n\\t// Title & Query Toggle\\n\\tlet showSQL = localStorageStore('showSQL_'.concat(queryID), false);\\n\\t// Query text & Compiler Toggle\\n\\tlet showResults = localStorageStore(\`showResults_\${queryID}\`);\\n\\n\\tconst toggleSQL = function () {\\n\\t\\t$showSQL = !$showSQL;\\n\\t};\\n\\n\\tconst toggleResults = function () {\\n\\t\\tif (!error && $queryResult.length > 0) {\\n\\t\\t\\t$showResults = !$showResults;\\n\\t\\t}\\n\\t};\\n\\n\\tlet inputQuery;\\n\\tlet showCompilerToggle;\\n\\tlet showCompiled = true;\\n\\t/** @type {undefined | Error } */\\n\\tlet error = undefined;\\n\\n\\t// Enter an error state if the queryResult isn't defined\\n\\t$: {\\n\\t\\tif (!$queryResult) error = new Error('queryResult is undefined');\\n\\t\\telse error = $queryResult.error;\\n\\t}\\n\\n\\t$: rowCount = $queryResult?.length ?? 0;\\n\\t$: colCount = $queryResult.columns.length ?? $queryResult?._evidenceColumnTypes.length ?? 0;\\n\\n\\t$: {\\n\\t\\tlet query = pageQueries?.find((d) => d.id === queryID);\\n\\n\\t\\tif (query) {\\n\\t\\t\\tinputQuery = query.inputQueryString;\\n\\t\\t\\tshowCompilerToggle = query.compiled && query.compileError === undefined;\\n\\t\\t}\\n\\t}\\n\\n\\tconst { theme } = getThemeStores();\\n<\/script>\\n\\n<div class=\\"over-container\\" in:blur|local>\\n\\t{#if $showQueries}\\n\\t\\t<!-- Title -->\\n\\t\\t<div class=\\"scrollbox my-3\\" transition:slide|local>\\n\\t\\t\\t<div class=\\"container-a\\">\\n\\t\\t\\t\\t<button type=\\"button\\" aria-label=\\"show-sql\\" on:click={toggleSQL} class=\\"title\\">\\n\\t\\t\\t\\t\\t<ChevronToggle toggled={$showSQL} />\\n\\t\\t\\t\\t\\t{queryID}\\n\\t\\t\\t\\t</button>\\n\\t\\t\\t\\t<!-- Compile Toggle  -->\\n\\t\\t\\t\\t{#if $showSQL && showCompilerToggle}\\n\\t\\t\\t\\t\\t<CompilerToggle bind:showCompiled />\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t<!-- Query Display -->\\n\\t\\t\\t\\t{#if $showSQL}\\n\\t\\t\\t\\t\\t<div class=\\"code-container\\" transition:slide|local>\\n\\t\\t\\t\\t\\t\\t{#if showCompiled}\\n\\t\\t\\t\\t\\t\\t\\t<Prism code={queryResult.originalText} />\\n\\t\\t\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t\\t\\t<Prism code={inputQuery} />\\n\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t</div>\\n\\t\\t\\t<!-- Status -->\\n\\t\\t\\t<button\\n\\t\\t\\t\\ttype=\\"button\\"\\n\\t\\t\\t\\taria-label=\\"view-query\\"\\n\\t\\t\\t\\tclass={'status-bar'}\\n\\t\\t\\t\\tclass:error\\n\\t\\t\\t\\tclass:success={!error}\\n\\t\\t\\t\\tclass:open={$showResults}\\n\\t\\t\\t\\tclass:closed={!$showResults}\\n\\t\\t\\t\\ton:click={toggleResults}\\n\\t\\t\\t>\\n\\t\\t\\t\\t{#if error}\\n\\t\\t\\t\\t\\t{error.message}\\n\\t\\t\\t\\t{:else if rowCount}\\n\\t\\t\\t\\t\\t<ChevronToggle toggled={$showResults} color={$theme.colors['info']} />\\n\\t\\t\\t\\t\\t{rowCount.toLocaleString()}\\n\\t\\t\\t\\t\\t{rowCount > 1 ? 'records' : 'record'} with {colCount.toLocaleString()}\\n\\t\\t\\t\\t\\t{colCount > 1 ? 'properties' : 'property'}\\n\\t\\t\\t\\t{:else if $queryResult.loading}\\n\\t\\t\\t\\t\\tloading...\\n\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\tran successfully but no data was returned\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t<!-- Results -->\\n\\t\\t\\t</button>\\n\\t\\t\\t{#if rowCount > 0 && !error && $showResults}\\n\\t\\t\\t\\t<DataTable data={queryResult} {queryID} />\\n\\t\\t\\t{/if}\\n\\t\\t</div>\\n\\t{/if}\\n</div>\\n\\n<style>\\n\\t:root {\\n\\t\\t--scrollbar-track-color: transparent;\\n\\t\\t--scrollbar-color: rgba(0, 0, 0, 0.2);\\n\\t\\t--scrollbar-active-color: rgba(0, 0, 0, 0.4);\\n\\t\\t--scrollbar-size: 0.75rem;\\n\\t\\t--scrollbar-minlength: 1.5rem; /* Minimum length of scrollbar thumb (width of horizontal, height of vertical) */\\n\\t}\\n\\n\\t.code-container {\\n\\t\\tbackground-color: var(--base-200);\\n\\t\\tborder-left: 1px solid var(--base-300);\\n\\t\\tborder-right: 1px solid var(--base-300);\\n\\t\\toverflow-x: auto;\\n\\t\\toverflow-y: hidden;\\n\\t\\tpadding-top: 0;\\n\\t\\tpadding-right: 12px;\\n\\t\\tpadding-bottom: 6px;\\n\\t\\tpadding-left: 15px;\\n\\t\\tscrollbar-width: thin;\\n\\t\\tscrollbar-color: var(--scrollbar-color) var(--scrollbar-track-color);\\n\\t}\\n\\t.code-container::-webkit-scrollbar {\\n\\t\\theight: var(--scrollbar-size);\\n\\t\\twidth: var(--scrollbar-size);\\n\\t}\\n\\t.code-container::-webkit-scrollbar-track {\\n\\t\\tbackground-color: var(--scrollbar-track-color);\\n\\t}\\n\\n\\t.over-container {\\n\\t\\toverflow-y: hidden;\\n\\t\\toverflow-x: auto;\\n\\t}\\n\\n\\t.code-container::-webkit-scrollbar-thumb {\\n\\t\\tbackground-color: var(--scrollbar-color);\\n\\t\\tborder-radius: 7px;\\n\\t\\tbackground-clip: padding-box;\\n\\t}\\n\\t.code-container::-webkit-scrollbar-thumb:hover {\\n\\t\\tbackground-color: var(--scrollbar-active-color);\\n\\t}\\n\\t.code-container::-webkit-scrollbar-thumb:vertical {\\n\\t\\tmin-height: var(--scrollbar-minlength);\\n\\t\\tborder: 3px solid transparent;\\n\\t}\\n\\t.code-container::-webkit-scrollbar-thumb:horizontal {\\n\\t\\tmin-width: var(--scrollbar-minlength);\\n\\t\\tborder: 3px solid transparent;\\n\\t}\\n\\n\\t.status-bar {\\n\\t\\tmargin-top: 0px;\\n\\t\\tmargin-bottom: 0px;\\n\\t\\tbackground-color: var(--base-200);\\n\\t\\tborder-left: 1px solid var(--base-300);\\n\\t\\tborder-right: 1px solid var(--base-300);\\n\\t\\tborder-bottom: 1px solid var(--base-300);\\n\\t\\toverflow-x: auto;\\n\\t\\toverflow-y: hidden;\\n\\t\\tscrollbar-width: thin;\\n\\t\\tscrollbar-color: var(--scrollbar-color) var(--scrollbar-track-color);\\n\\t}\\n\\n\\t.status-bar::-webkit-scrollbar {\\n\\t\\theight: var(--scrollbar-size);\\n\\t\\twidth: var(--scrollbar-size);\\n\\t}\\n\\t.status-bar::-webkit-scrollbar-track {\\n\\t\\tbackground-color: var(--scrollbar-track-color);\\n\\t}\\n\\t.status-bar::-webkit-scrollbar-thumb {\\n\\t\\tbackground-color: var(--scrollbar-color);\\n\\t\\tborder-radius: 7px;\\n\\t\\tbackground-clip: padding-box;\\n\\t}\\n\\t.status-bar::-webkit-scrollbar-thumb:hover {\\n\\t\\tbackground-color: var(--scrollbar-active-color);\\n\\t}\\n\\t.status-bar::-webkit-scrollbar-thumb:vertical {\\n\\t\\tmin-height: var(--scrollbar-minlength);\\n\\t\\tborder: 3px solid transparent;\\n\\t}\\n\\t.status-bar::-webkit-scrollbar-thumb:horizontal {\\n\\t\\tmin-width: var(--scrollbar-minlength);\\n\\t\\tborder: 3px solid transparent;\\n\\t}\\n\\n\\t.closed {\\n\\t\\tborder-bottom-left-radius: 6px;\\n\\t\\tborder-bottom-right-radius: 6px;\\n\\t}\\n\\n\\t.open {\\n\\t\\tborder-bottom-left-radius: 0px;\\n\\t\\tborder-bottom-right-radius: 0px;\\n\\t}\\n\\n\\t.status-bar.success {\\n\\t\\tcolor: var(--info);\\n\\t\\tcursor: pointer;\\n\\t}\\n\\n\\t.status-bar.error {\\n\\t\\tcolor: var(--negative);\\n\\t\\t-webkit-user-select: all;\\n\\t\\t-moz-user-select: all;\\n\\t\\tuser-select: all;\\n\\t\\tcursor: auto;\\n\\t}\\n\\n\\tbutton {\\n\\t\\tfont-family: var(--ui-font-family-compact);\\n\\t\\t-webkit-font-smoothing: antialiased;\\n\\t\\tfont-size: 12px;\\n\\t\\t-webkit-user-select: none;\\n\\t\\tuser-select: none;\\n\\t\\twhite-space: nowrap;\\n\\t\\ttext-align: left;\\n\\t\\twidth: 100%;\\n\\t\\tbackground-color: var(--base-200);\\n\\t\\tborder: none;\\n\\t\\tborder-left: 1px solid var(--base-300);\\n\\t\\tborder-right: 1px solid var(--base-300);\\n\\t\\tmargin-bottom: 0px;\\n\\t\\tcursor: pointer;\\n\\t\\tpadding: 5px;\\n\\t}\\n\\n\\tbutton.title {\\n\\t\\tborder-top: 1px solid var(--base-300);\\n\\t\\tborder-top-left-radius: 6px;\\n\\t\\tborder-top-right-radius: 6px;\\n\\t}\\n\\n\\t.scrollbox {\\n\\t\\tdisplay: flex;\\n\\t\\tflex-direction: column;\\n\\t}\\n\\n\\t.container-a {\\n\\t\\tbackground-color: var(--base-200);\\n\\t\\tborder-top-left-radius: 6px;\\n\\t\\tborder-top-right-radius: 6px;\\n\\t\\tbox-sizing: border-box;\\n\\t\\tdisplay: flex;\\n\\t\\tflex-direction: column;\\n\\t}\\n\\t/* container-a avoids whitespace appearing in the slide transition */\\n\\n\\t@media print {\\n\\t\\t.scrollbox {\\n\\t\\t\\tbreak-inside: avoid;\\n\\t\\t}\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AAuHC,KAAM,CACL,uBAAuB,CAAE,WAAW,CACpC,iBAAiB,CAAE,kBAAkB,CACrC,wBAAwB,CAAE,kBAAkB,CAC5C,gBAAgB,CAAE,OAAO,CACzB,qBAAqB,CAAE,MACxB,CAEA,8BAAgB,CACf,gBAAgB,CAAE,IAAI,UAAU,CAAC,CACjC,WAAW,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACtC,YAAY,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACvC,UAAU,CAAE,IAAI,CAChB,UAAU,CAAE,MAAM,CAClB,WAAW,CAAE,CAAC,CACd,aAAa,CAAE,IAAI,CACnB,cAAc,CAAE,GAAG,CACnB,YAAY,CAAE,IAAI,CAClB,eAAe,CAAE,IAAI,CACrB,eAAe,CAAE,IAAI,iBAAiB,CAAC,CAAC,IAAI,uBAAuB,CACpE,CACA,8BAAe,mBAAoB,CAClC,MAAM,CAAE,IAAI,gBAAgB,CAAC,CAC7B,KAAK,CAAE,IAAI,gBAAgB,CAC5B,CACA,8BAAe,yBAA0B,CACxC,gBAAgB,CAAE,IAAI,uBAAuB,CAC9C,CAEA,8BAAgB,CACf,UAAU,CAAE,MAAM,CAClB,UAAU,CAAE,IACb,CAEA,8BAAe,yBAA0B,CACxC,gBAAgB,CAAE,IAAI,iBAAiB,CAAC,CACxC,aAAa,CAAE,GAAG,CAClB,eAAe,CAAE,WAClB,CACA,8BAAe,yBAAyB,MAAO,CAC9C,gBAAgB,CAAE,IAAI,wBAAwB,CAC/C,CACA,8BAAe,yBAAyB,SAAU,CACjD,UAAU,CAAE,IAAI,qBAAqB,CAAC,CACtC,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,WACnB,CACA,8BAAe,yBAAyB,WAAY,CACnD,SAAS,CAAE,IAAI,qBAAqB,CAAC,CACrC,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,WACnB,CAEA,0BAAY,CACX,UAAU,CAAE,GAAG,CACf,aAAa,CAAE,GAAG,CAClB,gBAAgB,CAAE,IAAI,UAAU,CAAC,CACjC,WAAW,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACtC,YAAY,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACvC,aAAa,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACxC,UAAU,CAAE,IAAI,CAChB,UAAU,CAAE,MAAM,CAClB,eAAe,CAAE,IAAI,CACrB,eAAe,CAAE,IAAI,iBAAiB,CAAC,CAAC,IAAI,uBAAuB,CACpE,CAEA,0BAAW,mBAAoB,CAC9B,MAAM,CAAE,IAAI,gBAAgB,CAAC,CAC7B,KAAK,CAAE,IAAI,gBAAgB,CAC5B,CACA,0BAAW,yBAA0B,CACpC,gBAAgB,CAAE,IAAI,uBAAuB,CAC9C,CACA,0BAAW,yBAA0B,CACpC,gBAAgB,CAAE,IAAI,iBAAiB,CAAC,CACxC,aAAa,CAAE,GAAG,CAClB,eAAe,CAAE,WAClB,CACA,0BAAW,yBAAyB,MAAO,CAC1C,gBAAgB,CAAE,IAAI,wBAAwB,CAC/C,CACA,0BAAW,yBAAyB,SAAU,CAC7C,UAAU,CAAE,IAAI,qBAAqB,CAAC,CACtC,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,WACnB,CACA,0BAAW,yBAAyB,WAAY,CAC/C,SAAS,CAAE,IAAI,qBAAqB,CAAC,CACrC,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,WACnB,CAEA,sBAAQ,CACP,yBAAyB,CAAE,GAAG,CAC9B,0BAA0B,CAAE,GAC7B,CAEA,oBAAM,CACL,yBAAyB,CAAE,GAAG,CAC9B,0BAA0B,CAAE,GAC7B,CAEA,WAAW,uBAAS,CACnB,KAAK,CAAE,IAAI,MAAM,CAAC,CAClB,MAAM,CAAE,OACT,CAEA,WAAW,qBAAO,CACjB,KAAK,CAAE,IAAI,UAAU,CAAC,CACtB,mBAAmB,CAAE,GAAG,CACxB,gBAAgB,CAAE,GAAG,CACrB,WAAW,CAAE,GAAG,CAChB,MAAM,CAAE,IACT,CAEA,qBAAO,CACN,WAAW,CAAE,IAAI,wBAAwB,CAAC,CAC1C,sBAAsB,CAAE,WAAW,CACnC,SAAS,CAAE,IAAI,CACf,mBAAmB,CAAE,IAAI,CACzB,WAAW,CAAE,IAAI,CACjB,WAAW,CAAE,MAAM,CACnB,UAAU,CAAE,IAAI,CAChB,KAAK,CAAE,IAAI,CACX,gBAAgB,CAAE,IAAI,UAAU,CAAC,CACjC,MAAM,CAAE,IAAI,CACZ,WAAW,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACtC,YAAY,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACvC,aAAa,CAAE,GAAG,CAClB,MAAM,CAAE,OAAO,CACf,OAAO,CAAE,GACV,CAEA,MAAM,qBAAO,CACZ,UAAU,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACrC,sBAAsB,CAAE,GAAG,CAC3B,uBAAuB,CAAE,GAC1B,CAEA,yBAAW,CACV,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MACjB,CAEA,2BAAa,CACZ,gBAAgB,CAAE,IAAI,UAAU,CAAC,CACjC,sBAAsB,CAAE,GAAG,CAC3B,uBAAuB,CAAE,GAAG,CAC5B,UAAU,CAAE,UAAU,CACtB,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MACjB,CAGA,OAAO,KAAM,CACZ,yBAAW,CACV,YAAY,CAAE,KACf,CACD"}`},Tl=F((o,t,e,a)=>{let l,i,n,d,C,A,f,y,v,s,x,c,u,w,M;x=G(Oo,m=>s=m),u=G(va,m=>c=m);let{queryID:I}=t,{queryResult:B}=t;C=G(B,m=>d=m);let L=rl("showSQL_".concat(I),!1);v=G(L,m=>y=m);let p=rl();f=G(p,m=>A=m);let D,T,S=!0,h;const{theme:r}=de();M=G(r,m=>w=m),t.queryID===void 0&&e.queryID&&I!==void 0&&e.queryID(I),t.queryResult===void 0&&e.queryResult&&B!==void 0&&e.queryResult(B),o.css.add(_i);let g,E,b=o.head;do{g=!0,o.head=b,l=s.data.evidencemeta.queries,d?h=d.error:h=new Error("queryResult is undefined"),i=d?.length??0,n=d.columns.length??d?._evidenceColumnTypes.length??0;{let m=l?.find(U=>U.id===I);m&&(D=m.inputQueryString,T=m.compiled&&m.compileError===void 0)}E=`<div class="over-container svelte-1ursthx">${c?` <div class="scrollbox my-3 svelte-1ursthx"><div class="container-a svelte-1ursthx"><button type="button" aria-label="show-sql" class="title svelte-1ursthx">${O(pl,"ChevronToggle").$$render(o,{toggled:y},{},{})} ${R(I)}</button>  ${y&&T?`${O(Di,"CompilerToggle").$$render(o,{showCompiled:S},{showCompiled:m=>{S=m,g=!1}},{})}`:""}  ${y?`<div class="code-container svelte-1ursthx">${S?`${O(Sl,"Prism").$$render(o,{code:B.originalText},{},{})}`:`${O(Sl,"Prism").$$render(o,{code:D},{},{})}`}</div>`:""}</div>  <button type="button" aria-label="view-query" class="${[R(Ol("status-bar"),!0)+" svelte-1ursthx",(h?"error":"")+" "+(h?"":"success")+" "+(A?"open":"")+" "+(A?"":"closed")].join(" ").trim()}">${h?`${R(h.message)}`:`${i?`${O(pl,"ChevronToggle").$$render(o,{toggled:A,color:w.colors.info},{},{})} ${R(i.toLocaleString())} ${R(i>1?"records":"record")} with ${R(n.toLocaleString())} ${R(n>1?"properties":"property")}`:`${d.loading?"loading...":"ran successfully but no data was returned"}`}`} </button> ${i>0&&!h&&A?`${O(Oi,"DataTable").$$render(o,{data:B,queryID:I},{},{})}`:""}</div>`:""} </div>`}while(!g);return C(),f(),v(),x(),u(),M(),E}),Ri=F((o,t,e,a)=>{let l,i,n,d,C,A=Dt,f=()=>(A(),A=G(l,h=>h),l);const{resolveColorsObject:y}=de();let{config:v=void 0}=t,{height:s="291px"}=t,{width:x="100%"}=t,{copying:c=!1}=t,{printing:u=!1}=t,{echartsOptions:w=void 0}=t,{seriesOptions:M=void 0}=t,{seriesColors:I=void 0}=t,{isMap:B=!1}=t,{extraHeight:L=void 0}=t,p=!1,D,T;const S=Ne("gridConfig");return S&&(p=!0,{cols:D,gapWidth:T}=S),t.config===void 0&&e.config&&v!==void 0&&e.config(v),t.height===void 0&&e.height&&s!==void 0&&e.height(s),t.width===void 0&&e.width&&x!==void 0&&e.width(x),t.copying===void 0&&e.copying&&c!==void 0&&e.copying(c),t.printing===void 0&&e.printing&&u!==void 0&&e.printing(u),t.echartsOptions===void 0&&e.echartsOptions&&w!==void 0&&e.echartsOptions(w),t.seriesOptions===void 0&&e.seriesOptions&&M!==void 0&&e.seriesOptions(M),t.seriesColors===void 0&&e.seriesColors&&I!==void 0&&e.seriesColors(I),t.isMap===void 0&&e.isMap&&B!==void 0&&e.isMap(B),t.extraHeight===void 0&&e.extraHeight&&L!==void 0&&e.extraHeight(L),f(l=y(I)),i=Math.min(Number(D),2),n=(650-Number(T)*(i-1))/i,d=Math.min(Number(D),3),C=(841-Number(T)*(d-1))/d,A(),`${c?`<div class="chart" style="${"height: "+R(s,!0)+"; width: "+R(x,!0)+"; margin-left: 0; margin-top: 15px; margin-bottom: 10px; overflow: visible; break-inside: avoid;"}"></div>`:`${u?`${p?`<div class="chart md:hidden" style="${"height: "+R(s,!0)+"; width: "+R(n,!0)+"px; margin-left: 0; margin-top: 15px; margin-bottom: 10px; overflow: visible; break-inside: avoid;"}"></div> <div class="chart hidden md:block" style="${"height: "+R(s,!0)+"; width: "+R(C,!0)+"px; margin-left: 0; margin-top: 15px; margin-bottom: 10px; overflow: visible; break-inside: avoid;"}"></div>`:`<div class="chart md:hidden" style="${"height: "+R(s,!0)+"; width: 650px; margin-left: 0; margin-top: 15px; margin-bottom: 10px; overflow: visible; break-inside: avoid;"}"></div> <div class="chart hidden md:block" style="${"height: "+R(s,!0)+"; width: 841px; margin-left: 0; margin-top: 15px; margin-bottom: 10px; overflow: visible; break-inside: avoid;"}"></div>`}`:""}`}`}),Ni=F((o,t,e,a)=>{let{height:l="231px"}=t;return t.height===void 0&&e.height&&l!==void 0&&e.height(l),`<div role="status" class="animate-pulse"><span class="sr-only" data-svelte-h="svelte-1wtojot">Loading...</span> <div class="bg-base-100 rounded-md max-w-[100%]" style="${"height:"+R(l,!0)+"; margin-top: 15px; margin-bottom: 31px;"}"></div></div>`}),Gi={code:"@media print{.chart.svelte-db4qxn{-moz-column-break-inside:avoid;break-inside:avoid}.chart-container.svelte-db4qxn{padding:0}}.chart.svelte-db4qxn{-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;-o-user-select:none;user-select:none}.chart-footer.svelte-db4qxn{display:flex;justify-content:flex-end;align-items:center;margin:3px 12px;font-size:12px;height:9px}",map:`{"version":3,"file":"ECharts.svelte","sources":["ECharts.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport { browser } from '$app/environment';\\n\\timport echarts from '@evidence-dev/component-utilities/echarts';\\n\\timport echartsCanvasDownload from '@evidence-dev/component-utilities/echartsCanvasDownload';\\n\\timport EChartsCopyTarget from './EChartsCopyTarget.svelte';\\n\\timport DownloadData from '../../ui/DownloadData.svelte';\\n\\timport CodeBlock from '../../ui/CodeBlock.svelte';\\n\\timport ChartLoading from '../../ui/ChartLoading.svelte';\\n\\timport { flush } from 'svelte/internal';\\n\\timport { createEventDispatcher } from 'svelte';\\n\\timport { getThemeStores } from '../../../themes/themes.js';\\n\\n\\tconst { activeAppearance, theme, resolveColorsObject } = getThemeStores();\\n\\n\\texport let config = undefined;\\n\\n\\texport let queryID = undefined;\\n\\texport let evidenceChartTitle = undefined;\\n\\n\\texport let height = '291px';\\n\\texport let width = '100%';\\n\\n\\texport let data;\\n\\n\\texport let renderer = undefined;\\n\\texport let downloadableData = undefined;\\n\\texport let downloadableImage = undefined;\\n\\texport let echartsOptions = undefined;\\n\\texport let seriesOptions = undefined;\\n\\texport let printEchartsConfig; // helper for custom chart development\\n\\n\\texport let seriesColors = undefined;\\n\\t$: seriesColorsStore = resolveColorsObject(seriesColors);\\n\\n\\texport let connectGroup = undefined;\\n\\n\\texport let xAxisLabelOverflow = undefined;\\n\\n\\tconst dispatch = createEventDispatcher();\\n\\n\\tlet downloadChart = false;\\n\\tlet copying = false;\\n\\tlet printing = false;\\n\\tlet hovering = false;\\n<\/script>\\n\\n<svelte:window\\n\\ton:copy={() => {\\n\\t\\tcopying = true;\\n\\t\\tflush();\\n\\t\\tsetTimeout(() => {\\n\\t\\t\\tcopying = false;\\n\\t\\t}, 0);\\n\\t}}\\n\\ton:beforeprint={() => (printing = true)}\\n\\ton:afterprint={() => (printing = false)}\\n\\ton:export-beforeprint={() => (printing = true)}\\n\\ton:export-afterprint={() => (printing = false)}\\n/>\\n\\n<div\\n\\trole=\\"none\\"\\n\\tclass=\\"chart-container mt-2 mb-3\\"\\n\\ton:mouseenter={() => (hovering = true)}\\n\\ton:mouseleave={() => (hovering = false)}\\n>\\n\\t{#if !printing}\\n\\t\\t{#if !browser}\\n\\t\\t\\t<ChartLoading {height} />\\n\\t\\t{:else}\\n\\t\\t\\t<div\\n\\t\\t\\t\\tclass=\\"chart\\"\\n\\t\\t\\t\\tstyle=\\"\\n\\t\\t\\t\\theight: {height};\\n\\t\\t\\t\\twidth: {width};\\n\\t\\t\\t\\toverflow: visible;\\n\\t\\t\\t\\tdisplay: {copying ? 'none' : 'inherit'}\\n\\t\\t\\t\\"\\n\\t\\t\\t\\tuse:echarts={{\\n\\t\\t\\t\\t\\tconfig,\\n\\t\\t\\t\\t\\t...$$restProps,\\n\\t\\t\\t\\t\\techartsOptions,\\n\\t\\t\\t\\t\\tseriesOptions,\\n\\t\\t\\t\\t\\tdispatch,\\n\\t\\t\\t\\t\\trenderer,\\n\\t\\t\\t\\t\\tconnectGroup,\\n\\t\\t\\t\\t\\txAxisLabelOverflow,\\n\\t\\t\\t\\t\\tseriesColors: $seriesColorsStore,\\n\\t\\t\\t\\t\\ttheme: $activeAppearance\\n\\t\\t\\t\\t}}\\n\\t\\t\\t/>\\n\\t\\t{/if}\\n\\t{/if}\\n\\n\\t<EChartsCopyTarget\\n\\t\\t{config}\\n\\t\\t{height}\\n\\t\\t{width}\\n\\t\\t{copying}\\n\\t\\t{printing}\\n\\t\\t{echartsOptions}\\n\\t\\t{seriesOptions}\\n\\t\\tseriesColors={seriesColorsStore}\\n\\t/>\\n\\n\\t{#if downloadableData || downloadableImage}\\n\\t\\t<div class=\\"chart-footer\\">\\n\\t\\t\\t{#if downloadableImage}\\n\\t\\t\\t\\t<DownloadData\\n\\t\\t\\t\\t\\ttext=\\"Save Image\\"\\n\\t\\t\\t\\t\\tclass=\\"download-button\\"\\n\\t\\t\\t\\t\\tdownloadData={() => {\\n\\t\\t\\t\\t\\t\\tdownloadChart = true;\\n\\t\\t\\t\\t\\t\\tsetTimeout(() => {\\n\\t\\t\\t\\t\\t\\t\\tdownloadChart = false;\\n\\t\\t\\t\\t\\t\\t}, 0);\\n\\t\\t\\t\\t\\t}}\\n\\t\\t\\t\\t\\tdisplay={hovering}\\n\\t\\t\\t\\t\\t{queryID}\\n\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t<svg\\n\\t\\t\\t\\t\\t\\txmlns=\\"http://www.w3.org/2000/svg\\"\\n\\t\\t\\t\\t\\t\\twidth=\\"12\\"\\n\\t\\t\\t\\t\\t\\theight=\\"12\\"\\n\\t\\t\\t\\t\\t\\tviewBox=\\"0 0 24 24\\"\\n\\t\\t\\t\\t\\t\\tfill=\\"none\\"\\n\\t\\t\\t\\t\\t\\tstroke=\\"#000\\"\\n\\t\\t\\t\\t\\t\\tstroke-width=\\"2\\"\\n\\t\\t\\t\\t\\t\\tstroke-linecap=\\"round\\"\\n\\t\\t\\t\\t\\t\\tstroke-linejoin=\\"round\\"\\n\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t<rect x=\\"3\\" y=\\"3\\" width=\\"18\\" height=\\"18\\" rx=\\"2\\" />\\n\\t\\t\\t\\t\\t\\t<circle cx=\\"8.5\\" cy=\\"8.5\\" r=\\"1.5\\" />\\n\\t\\t\\t\\t\\t\\t<path d=\\"M20.4 14.5L16 10 4 20\\" />\\n\\t\\t\\t\\t\\t</svg>\\n\\t\\t\\t\\t</DownloadData>\\n\\t\\t\\t{/if}\\n\\t\\t\\t{#if data && downloadableData}\\n\\t\\t\\t\\t<DownloadData\\n\\t\\t\\t\\t\\ttext=\\"Download Data\\"\\n\\t\\t\\t\\t\\t{data}\\n\\t\\t\\t\\t\\t{queryID}\\n\\t\\t\\t\\t\\tclass=\\"download-button\\"\\n\\t\\t\\t\\t\\tdisplay={hovering}\\n\\t\\t\\t\\t/>\\n\\t\\t\\t{/if}\\n\\t\\t</div>\\n\\t{/if}\\n\\n\\t{#if printEchartsConfig && !printing}\\n\\t\\t<CodeBlock source={JSON.stringify(config, undefined, 3)} copyToClipboard={true}>\\n\\t\\t\\t{JSON.stringify(config, undefined, 3)}\\n\\t\\t</CodeBlock>\\n\\t{/if}\\n</div>\\n\\n{#if downloadChart}\\n\\t<div\\n\\t\\tclass=\\"chart\\"\\n\\t\\tstyle=\\"\\n        display: none;\\n        visibility: visible;\\n        height: {height};\\n        width: 666px;\\n        margin-left: 0;\\n        margin-top: 15px;\\n        margin-bottom: 15px;\\n        overflow: visible;\\n    \\"\\n\\t\\tuse:echartsCanvasDownload={{\\n\\t\\t\\tconfig,\\n\\t\\t\\t...$$restProps,\\n\\t\\t\\techartsOptions,\\n\\t\\t\\tseriesOptions,\\n\\t\\t\\tseriesColors: $seriesColorsStore,\\n\\t\\t\\tqueryID,\\n\\t\\t\\tevidenceChartTitle,\\n\\t\\t\\ttheme: $activeAppearance,\\n\\t\\t\\tbackgroundColor: $theme.colors['base-100']\\n\\t\\t}}\\n\\t/>\\n{/if}\\n\\n<style>\\n\\t@media print {\\n\\t\\t.chart {\\n\\t\\t\\t-moz-column-break-inside: avoid;\\n\\t\\t\\tbreak-inside: avoid;\\n\\t\\t}\\n\\n\\t\\t.chart-container {\\n\\t\\t\\tpadding: 0;\\n\\t\\t}\\n\\t}\\n\\n\\t.chart {\\n\\t\\t-moz-user-select: none;\\n\\t\\t-webkit-user-select: none;\\n\\t\\t-ms-user-select: none;\\n\\t\\t-o-user-select: none;\\n\\t\\tuser-select: none;\\n\\t}\\n\\n\\t.chart-footer {\\n\\t\\tdisplay: flex;\\n\\t\\tjustify-content: flex-end;\\n\\t\\talign-items: center;\\n\\t\\tmargin: 3px 12px;\\n\\t\\tfont-size: 12px;\\n\\t\\theight: 9px;\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AA4LC,OAAO,KAAM,CACZ,oBAAO,CACN,wBAAwB,CAAE,KAAK,CAC/B,YAAY,CAAE,KACf,CAEA,8BAAiB,CAChB,OAAO,CAAE,CACV,CACD,CAEA,oBAAO,CACN,gBAAgB,CAAE,IAAI,CACtB,mBAAmB,CAAE,IAAI,CACzB,eAAe,CAAE,IAAI,CACrB,cAAc,CAAE,IAAI,CACpB,WAAW,CAAE,IACd,CAEA,2BAAc,CACb,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,QAAQ,CACzB,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,GAAG,CAAC,IAAI,CAChB,SAAS,CAAE,IAAI,CACf,MAAM,CAAE,GACT"}`},Pi=F((o,t,e,a)=>{let l;dt(t,["config","queryID","evidenceChartTitle","height","width","data","renderer","downloadableData","downloadableImage","echartsOptions","seriesOptions","printEchartsConfig","seriesColors","connectGroup","xAxisLabelOverflow"]);let i=Dt,n=()=>(i(),i=G(l,m=>m),l),d,C;const{activeAppearance:A,theme:f,resolveColorsObject:y}=de();d=G(A,m=>m),C=G(f,m=>m);let{config:v=void 0}=t,{queryID:s=void 0}=t,{evidenceChartTitle:x=void 0}=t,{height:c="291px"}=t,{width:u="100%"}=t,{data:w}=t,{renderer:M=void 0}=t,{downloadableData:I=void 0}=t,{downloadableImage:B=void 0}=t,{echartsOptions:L=void 0}=t,{seriesOptions:p=void 0}=t,{printEchartsConfig:D}=t,{seriesColors:T=void 0}=t,{connectGroup:S=void 0}=t,{xAxisLabelOverflow:h=void 0}=t;jl();let r=!1,g=!1,E=!1,b=!1;return t.config===void 0&&e.config&&v!==void 0&&e.config(v),t.queryID===void 0&&e.queryID&&s!==void 0&&e.queryID(s),t.evidenceChartTitle===void 0&&e.evidenceChartTitle&&x!==void 0&&e.evidenceChartTitle(x),t.height===void 0&&e.height&&c!==void 0&&e.height(c),t.width===void 0&&e.width&&u!==void 0&&e.width(u),t.data===void 0&&e.data&&w!==void 0&&e.data(w),t.renderer===void 0&&e.renderer&&M!==void 0&&e.renderer(M),t.downloadableData===void 0&&e.downloadableData&&I!==void 0&&e.downloadableData(I),t.downloadableImage===void 0&&e.downloadableImage&&B!==void 0&&e.downloadableImage(B),t.echartsOptions===void 0&&e.echartsOptions&&L!==void 0&&e.echartsOptions(L),t.seriesOptions===void 0&&e.seriesOptions&&p!==void 0&&e.seriesOptions(p),t.printEchartsConfig===void 0&&e.printEchartsConfig&&D!==void 0&&e.printEchartsConfig(D),t.seriesColors===void 0&&e.seriesColors&&T!==void 0&&e.seriesColors(T),t.connectGroup===void 0&&e.connectGroup&&S!==void 0&&e.connectGroup(S),t.xAxisLabelOverflow===void 0&&e.xAxisLabelOverflow&&h!==void 0&&e.xAxisLabelOverflow(h),o.css.add(Gi),n(l=y(T)),i(),d(),C(),` <div role="none" class="chart-container mt-2 mb-3 svelte-db4qxn">${`${`${O(Ni,"ChartLoading").$$render(o,{height:c},{},{})}`}`} ${O(Ri,"EChartsCopyTarget").$$render(o,{config:v,height:c,width:u,copying:g,printing:E,echartsOptions:L,seriesOptions:p,seriesColors:l},{},{})} ${I||B?`<div class="chart-footer svelte-db4qxn">${B?`${O(wo,"DownloadData").$$render(o,{text:"Save Image",class:"download-button",downloadData:()=>{r=!0,setTimeout(()=>{r=!1},0)},display:b,queryID:s},{},{default:()=>'<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><path d="M20.4 14.5L16 10 4 20"></path></svg>'})}`:""} ${w&&I?`${O(wo,"DownloadData").$$render(o,{text:"Download Data",data:w,queryID:s,class:"download-button",display:b},{},{})}`:""}</div>`:""} ${D?`${O(ya,"CodeBlock").$$render(o,{source:JSON.stringify(v,void 0,3),copyToClipboard:!0},{},{default:()=>`${R(JSON.stringify(v,void 0,3))}`})}`:""}</div> ${r?`<div class="chart svelte-db4qxn" style="${"display: none; visibility: visible; height: "+R(c,!0)+"; width: 666px; margin-left: 0; margin-top: 15px; margin-bottom: 15px; overflow: visible;"}"></div>`:""}`});function Ge(o,t){const e=new Set(o.map(a=>a[t]));return Array.from(e)}function Ui(o,t){return ne(o,Wa({count:Ka(t)}))[0].count}function Fi(o,t,e){let a;if(typeof e!="object")a=ne(o,Io(t,hl({xTotal:po(e)})),go({percentOfX:ml(e,"xTotal")}),yl({percentOfX:e+"_pct"}));else{a=ne(o,go({valueSum:0}));for(let l=0;l<a.length;l++){a[l].valueSum=0;for(let i=0;i<e.length;i++)a[l].valueSum=a[l].valueSum+a[l][e[i]]}a=ne(a,Io(t,hl({xTotal:po("valueSum")})));for(let l=0;l<e.length;l++)a=ne(a,go({percentOfX:ml(e[l],"xTotal")}),yl({percentOfX:e[l]+"_pct"}))}return a}function _e(o,t,e){return[...o].sort((a,l)=>(a[t]<l[t]?-1:1)*(e?1:-1))}function Fl(o,t,e){const a=t+e;return o%a<t?0:1}const Wi=F((o,t,e,a)=>{let l,i,n,d,C,A=Dt,f=()=>(A(),A=G(n,It=>C=It),n),y,v,s,x=Dt,c=()=>(x(),x=G(i,It=>s=It),i),u,w=Dt,M=()=>(w(),w=G(l,It=>u=It),l),I,B,L=Je({}),p=Je({});B=G(p,It=>I=It);const{theme:D,resolveColor:T,resolveColorsObject:S,resolveColorPalette:h}=de();v=G(D,It=>y=It);let{data:r=void 0}=t,{queryID:g=void 0}=t,{x:E=void 0}=t,{y:b=void 0}=t,{y2:m=void 0}=t,{series:U=void 0}=t,{size:W=void 0}=t,{tooltipTitle:X=void 0}=t,{showAllXAxisLabels:P=void 0}=t,{printEchartsConfig:At=!1}=t,pt=!!b,Pt=!!E,{swapXY:N=!1}=t,{title:kt=void 0}=t,{subtitle:Lt=void 0}=t,{chartType:ut="Chart"}=t,{bubble:ft=!1}=t,{hist:ct=!1}=t,{boxplot:Tt=!1}=t,xt,{xType:Y=void 0}=t,{xAxisTitle:et="false"}=t,{xBaseline:ot=!0}=t,{xTickMarks:Z=!1}=t,{xGridlines:vt=!1}=t,{xAxisLabels:tt=!0}=t,{sort:gt=!0}=t,{xFmt:bt=void 0}=t,{xMin:Et=void 0}=t,{xMax:rt=void 0}=t,{yLog:lt=!1}=t,{yType:Ct=lt===!0?"log":"value"}=t,{yLogBase:ht=10}=t,{yAxisTitle:nt="false"}=t,{yBaseline:H=!1}=t,{yTickMarks:mt=!1}=t,{yGridlines:st=!0}=t,{yAxisLabels:at=!0}=t,{yMin:St=void 0}=t,{yMax:k=void 0}=t,{yScale:K=!1}=t,{yFmt:$=void 0}=t,{yAxisColor:Rt="true"}=t,{y2AxisTitle:wt="false"}=t,{y2Baseline:j=!1}=t,{y2TickMarks:it=!1}=t,{y2Gridlines:Mt=!0}=t,{y2AxisLabels:Nt=!0}=t,{y2Min:Ht=void 0}=t,{y2Max:$t=void 0}=t,{y2Scale:Ft=!1}=t,{y2Fmt:Wt=void 0}=t,{y2AxisColor:te="true"}=t,{sizeFmt:Vt=void 0}=t,{colorPalette:ee="default"}=t,{legend:Bt=void 0}=t,{echartsOptions:oe=void 0}=t,{seriesOptions:le=void 0}=t,{seriesColors:ae=void 0}=t,{stackType:ie=void 0}=t,{stacked100:Kt=!1}=t,{chartAreaHeight:Ut}=t,{renderer:oo=void 0}=t,{downloadableData:xe=!0}=t,{downloadableImage:ge=!0}=t,{connectGroup:lo=void 0}=t,{leftPadding:ao=void 0}=t,{rightPadding:io=void 0}=t,{xLabelWrap:be=!1}=t;const Kl=be?"break":"truncate";let V,ro,no=[],Ee=[],Lo,Ue,qt,Mo,jt,Ot,Yt,so,Do,Fe,Ao,_o,co,we,Ro,No,We,Ie,Go,Po,Uo,Fo,Wo,Ko,qo,jo,zo,Qo,Ho,pe,Ke,qe,uo,fo,Vo,Yo,Se,Xo,Zo,vo,Jo,Co,Ae=[],Te=!0,Xt=[],je=[],Zt,ce,$o,ue;t.data===void 0&&e.data&&r!==void 0&&e.data(r),t.queryID===void 0&&e.queryID&&g!==void 0&&e.queryID(g),t.x===void 0&&e.x&&E!==void 0&&e.x(E),t.y===void 0&&e.y&&b!==void 0&&e.y(b),t.y2===void 0&&e.y2&&m!==void 0&&e.y2(m),t.series===void 0&&e.series&&U!==void 0&&e.series(U),t.size===void 0&&e.size&&W!==void 0&&e.size(W),t.tooltipTitle===void 0&&e.tooltipTitle&&X!==void 0&&e.tooltipTitle(X),t.showAllXAxisLabels===void 0&&e.showAllXAxisLabels&&P!==void 0&&e.showAllXAxisLabels(P),t.printEchartsConfig===void 0&&e.printEchartsConfig&&At!==void 0&&e.printEchartsConfig(At),t.swapXY===void 0&&e.swapXY&&N!==void 0&&e.swapXY(N),t.title===void 0&&e.title&&kt!==void 0&&e.title(kt),t.subtitle===void 0&&e.subtitle&&Lt!==void 0&&e.subtitle(Lt),t.chartType===void 0&&e.chartType&&ut!==void 0&&e.chartType(ut),t.bubble===void 0&&e.bubble&&ft!==void 0&&e.bubble(ft),t.hist===void 0&&e.hist&&ct!==void 0&&e.hist(ct),t.boxplot===void 0&&e.boxplot&&Tt!==void 0&&e.boxplot(Tt),t.xType===void 0&&e.xType&&Y!==void 0&&e.xType(Y),t.xAxisTitle===void 0&&e.xAxisTitle&&et!==void 0&&e.xAxisTitle(et),t.xBaseline===void 0&&e.xBaseline&&ot!==void 0&&e.xBaseline(ot),t.xTickMarks===void 0&&e.xTickMarks&&Z!==void 0&&e.xTickMarks(Z),t.xGridlines===void 0&&e.xGridlines&&vt!==void 0&&e.xGridlines(vt),t.xAxisLabels===void 0&&e.xAxisLabels&&tt!==void 0&&e.xAxisLabels(tt),t.sort===void 0&&e.sort&&gt!==void 0&&e.sort(gt),t.xFmt===void 0&&e.xFmt&&bt!==void 0&&e.xFmt(bt),t.xMin===void 0&&e.xMin&&Et!==void 0&&e.xMin(Et),t.xMax===void 0&&e.xMax&&rt!==void 0&&e.xMax(rt),t.yLog===void 0&&e.yLog&&lt!==void 0&&e.yLog(lt),t.yType===void 0&&e.yType&&Ct!==void 0&&e.yType(Ct),t.yLogBase===void 0&&e.yLogBase&&ht!==void 0&&e.yLogBase(ht),t.yAxisTitle===void 0&&e.yAxisTitle&&nt!==void 0&&e.yAxisTitle(nt),t.yBaseline===void 0&&e.yBaseline&&H!==void 0&&e.yBaseline(H),t.yTickMarks===void 0&&e.yTickMarks&&mt!==void 0&&e.yTickMarks(mt),t.yGridlines===void 0&&e.yGridlines&&st!==void 0&&e.yGridlines(st),t.yAxisLabels===void 0&&e.yAxisLabels&&at!==void 0&&e.yAxisLabels(at),t.yMin===void 0&&e.yMin&&St!==void 0&&e.yMin(St),t.yMax===void 0&&e.yMax&&k!==void 0&&e.yMax(k),t.yScale===void 0&&e.yScale&&K!==void 0&&e.yScale(K),t.yFmt===void 0&&e.yFmt&&$!==void 0&&e.yFmt($),t.yAxisColor===void 0&&e.yAxisColor&&Rt!==void 0&&e.yAxisColor(Rt),t.y2AxisTitle===void 0&&e.y2AxisTitle&&wt!==void 0&&e.y2AxisTitle(wt),t.y2Baseline===void 0&&e.y2Baseline&&j!==void 0&&e.y2Baseline(j),t.y2TickMarks===void 0&&e.y2TickMarks&&it!==void 0&&e.y2TickMarks(it),t.y2Gridlines===void 0&&e.y2Gridlines&&Mt!==void 0&&e.y2Gridlines(Mt),t.y2AxisLabels===void 0&&e.y2AxisLabels&&Nt!==void 0&&e.y2AxisLabels(Nt),t.y2Min===void 0&&e.y2Min&&Ht!==void 0&&e.y2Min(Ht),t.y2Max===void 0&&e.y2Max&&$t!==void 0&&e.y2Max($t),t.y2Scale===void 0&&e.y2Scale&&Ft!==void 0&&e.y2Scale(Ft),t.y2Fmt===void 0&&e.y2Fmt&&Wt!==void 0&&e.y2Fmt(Wt),t.y2AxisColor===void 0&&e.y2AxisColor&&te!==void 0&&e.y2AxisColor(te),t.sizeFmt===void 0&&e.sizeFmt&&Vt!==void 0&&e.sizeFmt(Vt),t.colorPalette===void 0&&e.colorPalette&&ee!==void 0&&e.colorPalette(ee),t.legend===void 0&&e.legend&&Bt!==void 0&&e.legend(Bt),t.echartsOptions===void 0&&e.echartsOptions&&oe!==void 0&&e.echartsOptions(oe),t.seriesOptions===void 0&&e.seriesOptions&&le!==void 0&&e.seriesOptions(le),t.seriesColors===void 0&&e.seriesColors&&ae!==void 0&&e.seriesColors(ae),t.stackType===void 0&&e.stackType&&ie!==void 0&&e.stackType(ie),t.stacked100===void 0&&e.stacked100&&Kt!==void 0&&e.stacked100(Kt),t.chartAreaHeight===void 0&&e.chartAreaHeight&&Ut!==void 0&&e.chartAreaHeight(Ut),t.renderer===void 0&&e.renderer&&oo!==void 0&&e.renderer(oo),t.downloadableData===void 0&&e.downloadableData&&xe!==void 0&&e.downloadableData(xe),t.downloadableImage===void 0&&e.downloadableImage&&ge!==void 0&&e.downloadableImage(ge),t.connectGroup===void 0&&e.connectGroup&&lo!==void 0&&e.connectGroup(lo),t.leftPadding===void 0&&e.leftPadding&&ao!==void 0&&e.leftPadding(ao),t.rightPadding===void 0&&e.rightPadding&&io!==void 0&&e.rightPadding(io),t.xLabelWrap===void 0&&e.xLabelWrap&&be!==void 0&&e.xLabelWrap(be),Re(Ll,L),Re(Ml,p),At=z(At),N=z(N),ot=z(ot),Z=z(Z),vt=z(vt),tt=z(tt),gt=z(gt),lt=z(lt),H=z(H),mt=z(mt),st=z(st),at=z(at),K=z(K),M(l=T(Rt)),j=z(j),it=z(it),Mt=z(Mt),Nt=z(Nt),Ft=z(Ft),c(i=T(te)),f(n=h(ee)),d=S(ae),xe=z(xe),ge=z(ge),be=z(be);try{if(ce=void 0,Ae=[],Ee=[],Xt=[],je=[],Ue=[],pt=!!b,Pt=!!E,dl(r),V=Xe(r),ro=Object.keys(V),Pt||(E=ro[0]),!pt){no=ro.filter(function(_){return![E,U,W].includes(_)});for(let _=0;_<no.length;_++)Ue=no[_],Lo=V[Ue].type,Lo==="number"&&Ee.push(Ue);b=Ee.length>1?Ee:Ee[0]}ft?xt={x:E,y:b,size:W}:ct?xt={x:E}:Tt?xt={}:xt={x:E,y:b};for(let _ in xt)xt[_]==null&&Ae.push(_);if(Ae.length===1)throw Error(new Intl.ListFormat().format(Ae)+" is required");if(Ae.length>1)throw Error(new Intl.ListFormat().format(Ae)+" are required");if(Kt===!0&&b.includes("_pct")&&Te===!1)if(typeof b=="object"){for(let _=0;_<b.length;_++)b[_]=b[_].replace("_pct","");Te=!1}else b=b.replace("_pct",""),Te=!1;if(E&&Xt.push(E),b)if(typeof b=="object")for(Zt=0;Zt<b.length;Zt++)Xt.push(b[Zt]);else Xt.push(b);if(m)if(typeof m=="object")for(Zt=0;Zt<m.length;Zt++)Xt.push(m[Zt]);else Xt.push(m);if(W&&Xt.push(W),U&&je.push(U),X&&je.push(X),dl(r,Xt,je),Kt===!0){if(r=Fi(r,E,b),typeof b=="object"){for(let _=0;_<b.length;_++)b[_]=b[_]+"_pct";Te=!1}else b=b+"_pct",Te=!1;V=Xe(r)}switch(qt=V[E].type,qt){case"number":qt="value";break;case"string":qt="category";break;case"date":qt="time";break;default:break}if(Y=Y==="category"?"category":qt,P?P=P==="true"||P===!0:P=Y==="category",N&&Y!=="category")throw Error("Horizontal charts do not support a value or time-based x-axis. You can either change your SQL query to output string values or set swapXY=false.");if(N&&m)throw Error("Horizontal charts do not support a secondary y-axis. You can either set swapXY=false or remove the y2 prop from your chart.");if(N&&(Y="category"),Mo=qt==="value"&&Y==="category",r=gt?qt==="category"?_e(r,b,!1):_e(r,E,!0):r,qt==="time"&&(r=_e(r,E,!0)),$o=Xe(r,"array"),ue=$o.filter(_=>_.type==="date"),ue=ue.map(_=>_.id),ue.length>0)for(let _=0;_<ue.length;_++)r=xa(r,ue[_]);bt?jt=Qt(bt,V[E].format?.valueType):jt=V[E].format,b?$?typeof b=="object"?Ot=Qt($,V[b[0]].format?.valueType):Ot=Qt($,V[b].format?.valueType):typeof b=="object"?Ot=V[b[0]].format:Ot=V[b].format:Ot="str",m&&(Wt?typeof m=="object"?Yt=Qt(Wt,V[m[0]].format?.valueType):Yt=Qt(Wt,V[m].format?.valueType):typeof m=="object"?Yt=V[m[0]].format:Yt=V[m].format),W&&(Vt?so=Qt(Vt,V[W].format?.valueType):so=V[W].format),Do=V[E].columnUnitSummary,b&&(typeof b=="object"?Fe=V[b[0]].columnUnitSummary:Fe=V[b].columnUnitSummary),m&&(typeof m=="object"?Ao=V[m[0]].columnUnitSummary:Ao=V[m].columnUnitSummary),et=et==="true"?zt(E,jt):et==="false"?"":et,nt=nt==="true"?typeof b=="object"?"":zt(b,Ot):nt==="false"?"":nt,wt=wt==="true"?typeof m=="object"?"":zt(m,Yt):wt==="false"?"":wt;let It=typeof b=="object"?b.length:1,tl=U?Ui(r,U):1,Be=It*tl,ho=typeof m=="object"?m.length:m?1:0,mo=Be+ho;if(Bt!==void 0&&(Bt=Bt==="true"||Bt===!0),Bt=Bt??mo>1,Kt===!0&&lt===!0)throw Error("Log axis cannot be used in a 100% stacked chart");if(ie==="stacked"&&mo>1&&lt===!0)throw Error("Log axis cannot be used in a stacked chart");let fe;if(typeof b=="object"){fe=V[b[0]].columnUnitSummary.min;for(let _=0;_<b.length;_++)V[b[_]].columnUnitSummary.min<fe&&(fe=V[b[_]].columnUnitSummary.min)}else b&&(fe=V[b].columnUnitSummary.min);if(lt===!0&&fe<=0&&fe!==null)throw Error("Log axis cannot display values less than or equal to zero");L.update(_=>({..._,data:r,x:E,y:b,y2:m,series:U,swapXY:N,sort:gt,xType:Y,xFormat:jt,yFormat:Ot,y2Format:Yt,sizeFormat:so,xMismatch:Mo,size:W,yMin:St,y2Min:Ht,columnSummary:V,xAxisTitle:et,yAxisTitle:nt,y2AxisTitle:wt,tooltipTitle:X,chartAreaHeight:Ut,chartType:ut,yCount:It,y2Count:ho})),_o=Ge(r,E);let el;if(N?co={type:Ct,logBase:ht,position:"top",axisLabel:{show:at,hideOverlap:!0,showMaxLabel:!0,formatter(_){return Ye(_,Ot,Fe)},margin:4},min:St,max:k,scale:K,splitLine:{show:st},axisLine:{show:H,onZero:!1},axisTick:{show:mt},boundaryGap:!1,z:2}:co={type:Y,min:Et,max:rt,tooltip:{show:!0,position:"inside",formatter(_){if(_.isTruncated())return _.name}},splitLine:{show:vt},axisLine:{show:ot},axisTick:{show:Z},axisLabel:{show:tt,hideOverlap:!0,showMaxLabel:Y==="category"||Y==="value",formatter:Y==="time"||Y==="category"?!1:function(_){return Ye(_,jt,Do)},margin:6},scale:!0,z:2},N?we={type:Y,inverse:"true",splitLine:{show:vt},axisLine:{show:ot},axisTick:{show:Z},axisLabel:{show:tt,hideOverlap:!0},scale:!0,min:Et,max:rt,z:2}:(we={type:Ct,logBase:ht,splitLine:{show:st},axisLine:{show:H,onZero:!1},axisTick:{show:mt},axisLabel:{show:at,hideOverlap:!0,margin:4,formatter(_){return Ye(_,Ot,Fe)},color:m?u==="true"?C[0]:u!=="false"?u:void 0:void 0},name:nt,nameLocation:"end",nameTextStyle:{align:"left",verticalAlign:"top",padding:[0,5,0,0],color:m?u==="true"?C[0]:u!=="false"?u:void 0:void 0},nameGap:6,min:St,max:k,scale:K,boundaryGap:["0%","1%"],z:2},el={type:"value",show:!1,alignTicks:!0,splitLine:{show:Mt},axisLine:{show:j,onZero:!1},axisTick:{show:it},axisLabel:{show:Nt,hideOverlap:!0,margin:4,formatter(_){return Ye(_,Yt,Ao)},color:s==="true"?C[Be]:s!=="false"?s:void 0},name:wt,nameLocation:"end",nameTextStyle:{align:"right",verticalAlign:"top",padding:[0,0,0,5],color:s==="true"?C[Be]:s!=="false"?s:void 0},nameGap:6,min:Ht,max:$t,scale:Ft,boundaryGap:["0%","1%"],z:2},we=[we,el]),Ut){if(Ut=Number(Ut),isNaN(Ut))throw Error("chartAreaHeight must be a number");if(Ut<=0)throw Error("chartAreaHeight must be a positive number")}else Ut=180;We=!!kt,Ie=!!Lt,Go=Bt*(U!==null||typeof b=="object"&&b.length>1),Po=nt!==""&&N,Uo=et!==""&&!N,Fo=15,Wo=13,Ko=6*Ie,qo=We*Fo+Ie*Wo+Ko*Math.max(We,Ie),jo=10,zo=10,Qo=14,Ho=14,pe=15,pe=pe*Go,Ke=7,Ke=Ke*Math.max(We,Ie),qe=qo+Ke,uo=qe+pe+Ho*Po+jo,fo=Uo*Qo+zo,Xo=8,vo=1,N&&(Zo=_o.length,vo=Math.max(1,Zo/Xo)),Vo=Ut*vo+uo+fo,Yo=qe+pe+7,Jo=Vo+"px",Co="100%",Se=N?nt:et,Se!==""&&(Se=Se+" →"),Ro={id:"horiz-axis-title",type:"text",style:{text:Se,textAlign:"right",fill:y.colors["base-content-muted"]},cursor:"auto",right:N?"2%":"3%",top:N?Yo:null,bottom:N?null:"2%"},No={title:{text:kt,subtext:Lt,subtextStyle:{width:Co}},tooltip:{trigger:"axis",show:!0,formatter(_){let ve,Ce,he,ze;if(mo>1){Ce=_[0].value[N?1:0],ve=`<span id="tooltip" style='font-weight: 600;'>${Gt(Ce,jt)}</span>`;for(let re=_.length-1;re>=0;re--)_[re].seriesName!=="stackTotal"&&(he=_[re].value[N?0:1],ve=ve+`<br> <span style='font-size: 11px;'>${_[re].marker} ${_[re].seriesName}<span/><span style='float:right; margin-left: 10px; font-size: 12px;'>${Gt(he,Fl(_[re].componentIndex,It,ho)===0?Ot:Yt)}</span>`)}else Y==="value"?(Ce=_[0].value[N?1:0],he=_[0].value[N?0:1],ze=_[0].seriesName,ve=`<span id="tooltip" style='font-weight: 600;'>${zt(E,jt)}: </span><span style='float:right; margin-left: 10px;'>${Gt(Ce,jt)}</span><br/><span style='font-weight: 600;'>${zt(ze,Ot)}: </span><span style='float:right; margin-left: 10px;'>${Gt(he,Ot)}</span>`):(Ce=_[0].value[N?1:0],he=_[0].value[N?0:1],ze=_[0].seriesName,ve=`<span id="tooltip" style='font-weight: 600;'>${Gt(Ce,jt)}</span><br/><span>${zt(ze,Ot)}: </span><span style='float:right; margin-left: 10px;'>${Gt(he,Ot)}</span>`);return ve},confine:!0,axisPointer:{type:"shadow"},extraCssText:'box-shadow: 0 3px 6px rgba(0,0,0,.15); box-shadow: 0 2px 4px rgba(0,0,0,.12); z-index: 1; font-feature-settings: "cv02", "tnum";',order:"valueDesc"},legend:{show:Bt,type:"scroll",top:qe,padding:[0,0,0,0],data:[]},grid:{left:ao??(N?"1%":"0.8%"),right:io??(N?"4%":"3%"),bottom:fo,top:uo,containLabel:!0},xAxis:co,yAxis:we,series:[],animation:!0,graphic:Ro,color:C},p.update(()=>No)}catch(It){if(ce=It.message,console.error("\x1B[31m%s\x1B[0m",`Error in ${ut}: ${It.message}`),ca)throw ce;L.update(Be=>({...Be,error:ce}))}return A(),v(),x(),w(),B(),`${ce?`${O(Dl,"ErrorChart").$$render(o,{error:ce,title:ut},{},{})}`:`${a.default?a.default({}):""} ${O(Pi,"ECharts").$$render(o,{config:I,height:Jo,width:Co,data:r,queryID:g,evidenceChartTitle:kt,showAllXAxisLabels:P,swapXY:N,echartsOptions:oe,seriesOptions:le,printEchartsConfig:At,renderer:oo,downloadableData:xe,downloadableImage:ge,connectGroup:lo,xAxisLabelOverflow:Kl,seriesColors:d},{},{})}`}`}),{Object:Ki}=ba,qi=F((o,t,e,a)=>{let l,{data:i}=t;const n=De.isQuery(i)?i.hash:void 0;let d=i?.hash===n,{emptySet:C=void 0}=t,{emptyMessage:A=void 0}=t,{height:f=200}=t,y=i?.id;return t.data===void 0&&e.data&&i!==void 0&&e.data(i),t.emptySet===void 0&&e.emptySet&&C!==void 0&&e.emptySet(C),t.emptyMessage===void 0&&e.emptyMessage&&A!==void 0&&e.emptyMessage(A),t.height===void 0&&e.height&&f!==void 0&&e.height(f),d=i?.hash===n,l={...Object.fromEntries(Object.entries(t).filter(([,v])=>v!==void 0))},` ${O(ga,"QueryLoad").$$render(o,{data:i,height:f},{},{error:({loaded:v})=>`${O(Dl,"ErrorChart").$$render(o,{slot:"error",title:l.chartType,error:v.error.message},{},{})}`,empty:()=>`${O(Ea,"EmptyChart").$$render(o,{slot:"empty",emptyMessage:A,emptySet:C,chartType:l.chartType,isInitial:d},{},{})}`,default:({loaded:v})=>`${O(Wi,"Chart").$$render(o,Ki.assign({},l,{data:De.isQuery(v)?Array.from(v):v},{queryID:y}),{},{default:()=>`${a.default?a.default({}):""}`})}`})}`});function ji(o,t,e,a,l,i,n,d,C,A,f=void 0,y=void 0,v=void 0,s=void 0){function x(h,r,g,E){let b={name:r,data:h,yAxisIndex:g};return b={...E,...b},b}let c,u,w,M=[],I,B,L,p,D;function T(h,r){const g=[];function E(m){return typeof m>"u"}function b(m,U){E(m)||(Array.isArray(m)?m.forEach(W=>g.push([W,U])):g.push([m,U]))}return b(h,0),b(r,1),g}let S=T(e,v);if(a!=null&&S.length===1)for(p=Ge(o,a),c=0;c<p.length;c++){if(B=o.filter(h=>h[a]===p[c]),l?I=B.map(h=>[h[S[0][0]],d?h[t].toString():h[t]]):I=B.map(h=>[d?h[t].toString():h[t],h[S[0][0]]]),f){let h=B.map(r=>r[f]);I.forEach((r,g)=>r.push(h[g]))}if(y){let h=B.map(r=>r[y]);I.forEach((r,g)=>r.push(h[g]))}L=p[c]??"null",D=S[0][1],w=x(I,L,D,i),M.push(w)}if(a!=null&&S.length>1)for(p=Ge(o,a),c=0;c<p.length;c++)for(B=o.filter(h=>h[a]===p[c]),u=0;u<S.length;u++){if(l?I=B.map(h=>[h[S[u][0]],d?h[t].toString():h[t]]):I=B.map(h=>[d?h[t].toString():h[t],h[S[u][0]]]),f){let h=B.map(r=>r[f]);I.forEach((r,g)=>r.push(h[g]))}if(y){let h=B.map(r=>r[y]);I.forEach((r,g)=>r.push(h[g]))}L=(p[c]??"null")+" - "+C[S[u][0]].title,D=S[u][1],w=x(I,L,D,i),M.push(w)}if(a==null&&S.length>1)for(c=0;c<S.length;c++){if(l?I=o.map(h=>[h[S[c][0]],d?h[t].toString():h[t]]):I=o.map(h=>[d?h[t].toString():h[t],h[S[c][0]]]),f){let h=o.map(r=>r[f]);I.forEach((r,g)=>r.push(h[g]))}if(y){let h=o.map(r=>r[y]);I.forEach((r,g)=>r.push(h[g]))}L=C[S[c][0]].title,D=S[c][1],w=x(I,L,D,i),M.push(w)}if(a==null&&S.length===1){if(l?I=o.map(h=>[h[S[0][0]],d?h[t].toString():h[t]]):I=o.map(h=>[d?h[t].toString():h[t],h[S[0][0]]]),f){let h=o.map(r=>r[f]);I.forEach((r,g)=>r.push(h[g]))}if(y){let h=o.map(r=>r[y]);I.forEach((r,g)=>r.push(h[g]))}L=C[S[0][0]].title,D=S[0][1],w=x(I,L,D,i),M.push(w)}return A&&M.sort((h,r)=>A.indexOf(h.name)-A.indexOf(r.name)),s&&M.forEach(h=>{h.name=wa(h.name,s)}),M}function zi(o){let t=[];for(let e=1;e<o.length;e++)t.push(o[e]-o[e-1]);return t}function Wl(o,t){return(typeof o!="number"||isNaN(o))&&(o=0),(typeof t!="number"||isNaN(t))&&(t=0),o=Math.abs(o),t=Math.abs(t),t<=.01?o:Wl(t,o%t)}function Qi(o,t){if(!Array.isArray(o))throw new TypeError("Cannot calculate extent of non-array value.");let e,a;for(const l of o)typeof l=="number"&&(e===void 0?l>=l&&(e=a=l):(e>l&&(e=l),a<l&&(a=l)));return[e,a]}function Hi(o,t){let[e,a]=Qi(o);const l=[];let i=e;for(;i<=a;)l.push(Math.round((i+Number.EPSILON)*1e8)/1e8),i+=t;return l}function Vi(o){if(o.length<=1)return;o.sort(function(e,a){return e-a}),o=o.map(function(e){return e*1e8}),o=zi(o);let t=o.reduce((e,a)=>Wl(e,a))/1e8;return t=Math.round((t+Number.EPSILON)*1e8)/1e8,t}function Eo(o,t,e,a,l=!1,i=!1){let n=!1;const d=o.map(s=>Object.assign({},s,{[t]:s[t]instanceof Date?(n=!0,s[t].toISOString()):s[t]})).filter(s=>s[t]!==void 0&&s[t]!==null),C=Array.from(d).reduce((s,x)=>(x[t]instanceof Date&&(x[t]=x[t].toISOString(),n=!0),a?(s[x[a]??"null"]||(s[x[a]??"null"]=[]),s[x[a]??"null"].push(x)):(s.default||(s.default=[]),s.default.push(x)),s),{}),A={};let f;const y=d.find(s=>s&&s[t]!==null&&s[t]!==void 0)?.[t]??null;switch(typeof y){case"object":throw y===null?new Error(`Column '${t}' is entirely null. Column must contain at least one non-null value.`):new Error("Unexpected object property, expected string, date, or number");case"number":if(f=Ge(d,t),i){const s=Vi(f);A[t]=Hi(f,s)}break;case"string":f=Ge(d,t),A[t]=f;break}const v=[];for(const s of Object.values(C)){const x=a?{[a]:null}:{};if(l)if(e instanceof Array)for(let u=0;u<e.length;u++)x[e[u]]=0;else x[e]=0;else if(e instanceof Array)for(let u=0;u<e.length;u++)x[e[u]]=null;else x[e]=null;a&&(A[a]=a);const c=[];Object.keys(A).length===0?c.push(xl([t],x)):c.push(xl(A,x)),v.push(ne(s,...c))}return n?v.flat().map(s=>({...s,[t]:new Date(s[t])})):v.flat()}function Bl(o,t,e){let a=ne(o,Io(t,[qa(e,po)]));if(typeof e=="object")for(let l=0;l<a.length;l++){a[l].stackTotal=0;for(let i=0;i<e.length;i++)a[l].stackTotal=a[l].stackTotal+a[l][e[i]]}return a}let Yi=60;const Xi=F((o,t,e,a)=>{let l,i,n,d,C,A,f,y,v,s,x,c,u,w,M,I,B,L,p,D=Dt,T=()=>(D(),D=G(d,j=>p=j),d),S,h=Dt,r=()=>(h(),h=G(n,j=>S=j),n),g,E=Dt,b=()=>(E(),E=G(C,j=>g=j),C),m,U=Dt,W=()=>(U(),U=G(l,j=>m=j),l);const{resolveColor:X}=de();let{y:P=void 0}=t;const At=!!P;let{y2:pt=void 0}=t;const Pt=!!pt;let{series:N=void 0}=t;const kt=!!N;let{options:Lt=void 0}=t,{name:ut=void 0}=t,{type:ft="stacked"}=t,{stackName:ct=void 0}=t,{fillColor:Tt=void 0}=t,{fillOpacity:xt=void 0}=t,{outlineColor:Y=void 0}=t,{outlineWidth:et=void 0}=t,{labels:ot=!1}=t,{seriesLabels:Z=!0}=t,{labelSize:vt=11}=t,{labelPosition:tt=void 0}=t,{labelColor:gt=void 0}=t,{labelFmt:bt=void 0}=t,Et;bt&&(Et=Qt(bt));let{yLabelFmt:rt=void 0}=t,lt;rt&&(lt=Qt(rt));let{y2LabelFmt:Ct=void 0}=t,ht;Ct&&(ht=Qt(Ct));let{y2SeriesType:nt="bar"}=t,{stackTotalLabel:H=!0}=t,{showAllLabels:mt=!1}=t,{seriesOrder:st=void 0}=t,at,St,k,K;const $={outside:"top",inside:"inside"},Rt={outside:"right",inside:"inside"};let{seriesLabelFmt:wt=void 0}=t;return t.y===void 0&&e.y&&P!==void 0&&e.y(P),t.y2===void 0&&e.y2&&pt!==void 0&&e.y2(pt),t.series===void 0&&e.series&&N!==void 0&&e.series(N),t.options===void 0&&e.options&&Lt!==void 0&&e.options(Lt),t.name===void 0&&e.name&&ut!==void 0&&e.name(ut),t.type===void 0&&e.type&&ft!==void 0&&e.type(ft),t.stackName===void 0&&e.stackName&&ct!==void 0&&e.stackName(ct),t.fillColor===void 0&&e.fillColor&&Tt!==void 0&&e.fillColor(Tt),t.fillOpacity===void 0&&e.fillOpacity&&xt!==void 0&&e.fillOpacity(xt),t.outlineColor===void 0&&e.outlineColor&&Y!==void 0&&e.outlineColor(Y),t.outlineWidth===void 0&&e.outlineWidth&&et!==void 0&&e.outlineWidth(et),t.labels===void 0&&e.labels&&ot!==void 0&&e.labels(ot),t.seriesLabels===void 0&&e.seriesLabels&&Z!==void 0&&e.seriesLabels(Z),t.labelSize===void 0&&e.labelSize&&vt!==void 0&&e.labelSize(vt),t.labelPosition===void 0&&e.labelPosition&&tt!==void 0&&e.labelPosition(tt),t.labelColor===void 0&&e.labelColor&&gt!==void 0&&e.labelColor(gt),t.labelFmt===void 0&&e.labelFmt&&bt!==void 0&&e.labelFmt(bt),t.yLabelFmt===void 0&&e.yLabelFmt&&rt!==void 0&&e.yLabelFmt(rt),t.y2LabelFmt===void 0&&e.y2LabelFmt&&Ct!==void 0&&e.y2LabelFmt(Ct),t.y2SeriesType===void 0&&e.y2SeriesType&&nt!==void 0&&e.y2SeriesType(nt),t.stackTotalLabel===void 0&&e.stackTotalLabel&&H!==void 0&&e.stackTotalLabel(H),t.showAllLabels===void 0&&e.showAllLabels&&mt!==void 0&&e.showAllLabels(mt),t.seriesOrder===void 0&&e.seriesOrder&&st!==void 0&&e.seriesOrder(st),t.seriesLabelFmt===void 0&&e.seriesLabelFmt&&wt!==void 0&&e.seriesLabelFmt(wt),W(l=Ne(Ll)),i=Ne(Ml),r(n=X(Tt)),T(d=X(Y)),ot=ot==="true"||ot===!0,Z=Z==="true"||Z===!0,b(C=X(gt)),H=H==="true"||H===!0,A=m.data,f=m.x,P=At?P:m.y,pt=Pt?pt:m.y2,y=m.yFormat,v=m.y2Format,s=m.yCount,x=m.y2Count,c=m.swapXY,u=m.xType,w=m.xMismatch,M=m.columnSummary,I=m.sort,N=kt?N:m.series,!N&&typeof P!="object"?(ut=ut??zt(P,M[P].title),c&&u!=="category"&&(A=Eo(A,f,P,N,!0,u!=="time"),u="category"),ct="stack1",k=c?"right":"top"):(I===!0&&u==="category"&&(at=Bl(A,f,P),typeof P=="object"?at=_e(at,"stackTotal",!1):at=_e(at,P,!1),St=at.map(j=>j[f]),A=[...A].sort(function(j,it){return St.indexOf(j[f])-St.indexOf(it[f])})),c||(u==="value"||u==="category")&&ft.includes("stacked")?(A=Eo(A,f,P,N,!0,u==="value"),u="category"):u==="time"&&ft.includes("stacked")&&(A=Eo(A,f,P,N,!0,!0)),ft.includes("stacked")?(ct=ct??"stack1",k="inside"):(ct=void 0,k=c?"right":"top")),ft==="stacked"&&(K=Bl(A,f,P)),tt=(c?Rt[tt]:$[tt])??k,B={type:"bar",stack:ct,label:{show:ot&&Z,formatter(j){return j.value[c?0:1]===0?"":Gt(j.value[c?0:1],[lt??Et??y,ht??Et??v][Fl(j.componentIndex,s,x)])},position:tt,fontSize:vt,color:g},labelLayout:{hideOverlap:!mt},emphasis:{focus:"series"},barMaxWidth:Yi,itemStyle:{color:S,opacity:xt,borderColor:p,borderWidth:et}},L=ji(A,f,P,N,c,B,ut,w,M,st,void 0,void 0,pt,wt),i.update(j=>(j.series.push(...L),j.legend.data.push(...L.map(it=>it.name.toString())),ot===!0&&ft==="stacked"&&typeof P=="object"|N!==void 0&&H===!0&&N!==f&&(j.series.push({type:"bar",stack:ct,name:"stackTotal",color:"none",data:K.map(it=>[c?0:w?it[f].toString():it[f],c?w?it[f].toString():it[f]:0]),label:{show:!0,position:c?"right":"top",formatter(it){let Mt=0;return L.forEach(Nt=>{Mt+=Nt.data[it.dataIndex][c?0:1]}),Mt===0?"":Gt(Mt,Et??y)},fontWeight:"bold",fontSize:vt,padding:c?[0,0,0,5]:void 0}}),j.legend.selectedMode=!1),j)),D(),h(),E(),U(),""}),Zi=F((o,t,e,a)=>{let l,i,n,d,C,A,f;const{resolveColor:y,resolveColorsObject:v,resolveColorPalette:s}=de();let{data:x=void 0}=t,{x:c=void 0}=t,{y:u=void 0}=t,{y2:w=void 0}=t,{series:M=void 0}=t,{xType:I=void 0}=t,{yLog:B=void 0}=t,{yLogBase:L=void 0}=t,{y2SeriesType:p=void 0}=t,{yFmt:D=void 0}=t,{y2Fmt:T=void 0}=t,{xFmt:S=void 0}=t,{title:h=void 0}=t,{subtitle:r=void 0}=t,{legend:g=void 0}=t,{xAxisTitle:E=void 0}=t,{yAxisTitle:b=w?"true":void 0}=t,{y2AxisTitle:m=w?"true":void 0}=t,{xGridlines:U=void 0}=t,{yGridlines:W=void 0}=t,{y2Gridlines:X=void 0}=t,{xAxisLabels:P=void 0}=t,{yAxisLabels:At=void 0}=t,{y2AxisLabels:pt=void 0}=t,{xBaseline:Pt=void 0}=t,{yBaseline:N=void 0}=t,{y2Baseline:kt=void 0}=t,{xTickMarks:Lt=void 0}=t,{yTickMarks:ut=void 0}=t,{y2TickMarks:ft=void 0}=t,{yMin:ct=void 0}=t,{yMax:Tt=void 0}=t,{yScale:xt=void 0}=t,{y2Min:Y=void 0}=t,{y2Max:et=void 0}=t,{y2Scale:ot=void 0}=t,{swapXY:Z=!1}=t,{showAllXAxisLabels:vt}=t,{type:tt="stacked"}=t,gt=tt==="stacked100",{fillColor:bt=void 0}=t,{fillOpacity:Et=void 0}=t,{outlineColor:rt=void 0}=t,{outlineWidth:lt=void 0}=t,{chartAreaHeight:Ct=void 0}=t,{sort:ht=void 0}=t,{colorPalette:nt="default"}=t,{labels:H=void 0}=t,{labelSize:mt=void 0}=t,{labelPosition:st=void 0}=t,{labelColor:at=void 0}=t,{labelFmt:St=void 0}=t,{yLabelFmt:k=void 0}=t,{y2LabelFmt:K=void 0}=t,{stackTotalLabel:$=void 0}=t,{seriesLabels:Rt=void 0}=t,{showAllLabels:wt=void 0}=t,{yAxisColor:j=void 0}=t,{y2AxisColor:it=void 0}=t,{echartsOptions:Mt=void 0}=t,{seriesOptions:Nt=void 0}=t,{printEchartsConfig:Ht=!1}=t,{emptySet:$t=void 0}=t,{emptyMessage:Ft=void 0}=t,{renderer:Wt=void 0}=t,{downloadableData:te=void 0}=t,{downloadableImage:Vt=void 0}=t,{seriesColors:ee=void 0}=t,{seriesOrder:Bt=void 0}=t,{connectGroup:oe=void 0}=t,{seriesLabelFmt:le=void 0}=t,{leftPadding:ae=void 0}=t,{rightPadding:ie=void 0}=t,{xLabelWrap:Kt=void 0}=t;return t.data===void 0&&e.data&&x!==void 0&&e.data(x),t.x===void 0&&e.x&&c!==void 0&&e.x(c),t.y===void 0&&e.y&&u!==void 0&&e.y(u),t.y2===void 0&&e.y2&&w!==void 0&&e.y2(w),t.series===void 0&&e.series&&M!==void 0&&e.series(M),t.xType===void 0&&e.xType&&I!==void 0&&e.xType(I),t.yLog===void 0&&e.yLog&&B!==void 0&&e.yLog(B),t.yLogBase===void 0&&e.yLogBase&&L!==void 0&&e.yLogBase(L),t.y2SeriesType===void 0&&e.y2SeriesType&&p!==void 0&&e.y2SeriesType(p),t.yFmt===void 0&&e.yFmt&&D!==void 0&&e.yFmt(D),t.y2Fmt===void 0&&e.y2Fmt&&T!==void 0&&e.y2Fmt(T),t.xFmt===void 0&&e.xFmt&&S!==void 0&&e.xFmt(S),t.title===void 0&&e.title&&h!==void 0&&e.title(h),t.subtitle===void 0&&e.subtitle&&r!==void 0&&e.subtitle(r),t.legend===void 0&&e.legend&&g!==void 0&&e.legend(g),t.xAxisTitle===void 0&&e.xAxisTitle&&E!==void 0&&e.xAxisTitle(E),t.yAxisTitle===void 0&&e.yAxisTitle&&b!==void 0&&e.yAxisTitle(b),t.y2AxisTitle===void 0&&e.y2AxisTitle&&m!==void 0&&e.y2AxisTitle(m),t.xGridlines===void 0&&e.xGridlines&&U!==void 0&&e.xGridlines(U),t.yGridlines===void 0&&e.yGridlines&&W!==void 0&&e.yGridlines(W),t.y2Gridlines===void 0&&e.y2Gridlines&&X!==void 0&&e.y2Gridlines(X),t.xAxisLabels===void 0&&e.xAxisLabels&&P!==void 0&&e.xAxisLabels(P),t.yAxisLabels===void 0&&e.yAxisLabels&&At!==void 0&&e.yAxisLabels(At),t.y2AxisLabels===void 0&&e.y2AxisLabels&&pt!==void 0&&e.y2AxisLabels(pt),t.xBaseline===void 0&&e.xBaseline&&Pt!==void 0&&e.xBaseline(Pt),t.yBaseline===void 0&&e.yBaseline&&N!==void 0&&e.yBaseline(N),t.y2Baseline===void 0&&e.y2Baseline&&kt!==void 0&&e.y2Baseline(kt),t.xTickMarks===void 0&&e.xTickMarks&&Lt!==void 0&&e.xTickMarks(Lt),t.yTickMarks===void 0&&e.yTickMarks&&ut!==void 0&&e.yTickMarks(ut),t.y2TickMarks===void 0&&e.y2TickMarks&&ft!==void 0&&e.y2TickMarks(ft),t.yMin===void 0&&e.yMin&&ct!==void 0&&e.yMin(ct),t.yMax===void 0&&e.yMax&&Tt!==void 0&&e.yMax(Tt),t.yScale===void 0&&e.yScale&&xt!==void 0&&e.yScale(xt),t.y2Min===void 0&&e.y2Min&&Y!==void 0&&e.y2Min(Y),t.y2Max===void 0&&e.y2Max&&et!==void 0&&e.y2Max(et),t.y2Scale===void 0&&e.y2Scale&&ot!==void 0&&e.y2Scale(ot),t.swapXY===void 0&&e.swapXY&&Z!==void 0&&e.swapXY(Z),t.showAllXAxisLabels===void 0&&e.showAllXAxisLabels&&vt!==void 0&&e.showAllXAxisLabels(vt),t.type===void 0&&e.type&&tt!==void 0&&e.type(tt),t.fillColor===void 0&&e.fillColor&&bt!==void 0&&e.fillColor(bt),t.fillOpacity===void 0&&e.fillOpacity&&Et!==void 0&&e.fillOpacity(Et),t.outlineColor===void 0&&e.outlineColor&&rt!==void 0&&e.outlineColor(rt),t.outlineWidth===void 0&&e.outlineWidth&&lt!==void 0&&e.outlineWidth(lt),t.chartAreaHeight===void 0&&e.chartAreaHeight&&Ct!==void 0&&e.chartAreaHeight(Ct),t.sort===void 0&&e.sort&&ht!==void 0&&e.sort(ht),t.colorPalette===void 0&&e.colorPalette&&nt!==void 0&&e.colorPalette(nt),t.labels===void 0&&e.labels&&H!==void 0&&e.labels(H),t.labelSize===void 0&&e.labelSize&&mt!==void 0&&e.labelSize(mt),t.labelPosition===void 0&&e.labelPosition&&st!==void 0&&e.labelPosition(st),t.labelColor===void 0&&e.labelColor&&at!==void 0&&e.labelColor(at),t.labelFmt===void 0&&e.labelFmt&&St!==void 0&&e.labelFmt(St),t.yLabelFmt===void 0&&e.yLabelFmt&&k!==void 0&&e.yLabelFmt(k),t.y2LabelFmt===void 0&&e.y2LabelFmt&&K!==void 0&&e.y2LabelFmt(K),t.stackTotalLabel===void 0&&e.stackTotalLabel&&$!==void 0&&e.stackTotalLabel($),t.seriesLabels===void 0&&e.seriesLabels&&Rt!==void 0&&e.seriesLabels(Rt),t.showAllLabels===void 0&&e.showAllLabels&&wt!==void 0&&e.showAllLabels(wt),t.yAxisColor===void 0&&e.yAxisColor&&j!==void 0&&e.yAxisColor(j),t.y2AxisColor===void 0&&e.y2AxisColor&&it!==void 0&&e.y2AxisColor(it),t.echartsOptions===void 0&&e.echartsOptions&&Mt!==void 0&&e.echartsOptions(Mt),t.seriesOptions===void 0&&e.seriesOptions&&Nt!==void 0&&e.seriesOptions(Nt),t.printEchartsConfig===void 0&&e.printEchartsConfig&&Ht!==void 0&&e.printEchartsConfig(Ht),t.emptySet===void 0&&e.emptySet&&$t!==void 0&&e.emptySet($t),t.emptyMessage===void 0&&e.emptyMessage&&Ft!==void 0&&e.emptyMessage(Ft),t.renderer===void 0&&e.renderer&&Wt!==void 0&&e.renderer(Wt),t.downloadableData===void 0&&e.downloadableData&&te!==void 0&&e.downloadableData(te),t.downloadableImage===void 0&&e.downloadableImage&&Vt!==void 0&&e.downloadableImage(Vt),t.seriesColors===void 0&&e.seriesColors&&ee!==void 0&&e.seriesColors(ee),t.seriesOrder===void 0&&e.seriesOrder&&Bt!==void 0&&e.seriesOrder(Bt),t.connectGroup===void 0&&e.connectGroup&&oe!==void 0&&e.connectGroup(oe),t.seriesLabelFmt===void 0&&e.seriesLabelFmt&&le!==void 0&&e.seriesLabelFmt(le),t.leftPadding===void 0&&e.leftPadding&&ae!==void 0&&e.leftPadding(ae),t.rightPadding===void 0&&e.rightPadding&&ie!==void 0&&e.rightPadding(ie),t.xLabelWrap===void 0&&e.xLabelWrap&&Kt!==void 0&&e.xLabelWrap(Kt),Z==="true"||Z===!0?Z=!0:Z=!1,l=y(bt),i=y(rt),n=s(nt),d=y(at),C=y(j),A=y(it),f=v(ee),`${O(qi,"Chart").$$render(o,{data:x,x:c,y:u,y2:w,xFmt:S,yFmt:D,y2Fmt:T,series:M,xType:I,yLog:B,yLogBase:L,legend:g,xAxisTitle:E,yAxisTitle:b,y2AxisTitle:m,xGridlines:U,yGridlines:W,y2Gridlines:X,xAxisLabels:P,yAxisLabels:At,y2AxisLabels:pt,xBaseline:Pt,yBaseline:N,y2Baseline:kt,xTickMarks:Lt,yTickMarks:ut,y2TickMarks:ft,yAxisColor:C,y2AxisColor:A,yMin:ct,yMax:Tt,yScale:xt,y2Min:Y,y2Max:et,y2Scale:ot,swapXY:Z,title:h,subtitle:r,chartType:"Bar Chart",stackType:tt,sort:ht,stacked100:gt,chartAreaHeight:Ct,showAllXAxisLabels:vt,colorPalette:n,echartsOptions:Mt,seriesOptions:Nt,printEchartsConfig:Ht,emptySet:$t,emptyMessage:Ft,renderer:Wt,downloadableData:te,downloadableImage:Vt,connectGroup:oe,xLabelWrap:Kt,seriesColors:f,leftPadding:ae,rightPadding:ie},{},{default:()=>`${O(Xi,"Bar").$$render(o,{type:tt,fillColor:l,fillOpacity:Et,outlineColor:i,outlineWidth:lt,labels:H,labelSize:mt,labelPosition:st,labelColor:d,labelFmt:St,yLabelFmt:k,y2LabelFmt:K,stackTotalLabel:$,seriesLabels:Rt,showAllLabels:wt,y2SeriesType:p,seriesOrder:Bt,seriesLabelFmt:le},{},{})} ${a.default?a.default({}):""}`})}`}),J={title:"Welcome to Evidence"},Lr=F((o,t,e,a)=>{let l,i,n,d;i=G(Oo,r=>l=r),d=G(nl,r=>n=r);let{data:C}=t,{data:A={},customFormattingSettings:f,__db:y,inputs:v}=C;Bo(nl,n="6666cd76f96956469e7be39d750cc7d9",n);let s=ua(Je(v));ye(s.subscribe(r=>v=r)),Re(Ia,{getCustomFormats:()=>f.customFormats||[]}),ye(()=>De.emptyCache());const x=(r,g)=>ha(y.query,r,{query_name:g});pa(x),l.params;let c={initialData:void 0,initialError:void 0},u=ke`select
      category
  from needful_things.orders
  group by category`,w=`select
      category
  from needful_things.orders
  group by category`,M,I=!1;const B=De.createReactive({callback:r=>{M=r},execFn:x},{id:"categories",...c});B(w,{noResolve:u,...c}),globalThis[Symbol.for("categories")]={get value(){return M}};let L={initialData:void 0,initialError:void 0},p=ke`select 
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${v.category.value}'
  and date_part('year', order_datetime) like '${v.year.value}'
  group by all
  order by sales_usd desc`,D=`select 
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${v.category.value}'
  and date_part('year', order_datetime) like '${v.year.value}'
  group by all
  order by sales_usd desc`,T,S=!1;const h=De.createReactive({callback:r=>{T=r},execFn:x},{id:"orders_by_category",...L});return h(D,{noResolve:p,...L}),globalThis[Symbol.for("orders_by_category")]={get value(){return T}},ye(s.subscribe(r=>{p=ke`select 
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${r.category.value}'
  and date_part('year', order_datetime) like '${r.year.value}'
  group by all
  order by sales_usd desc`,D=`select 
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${r.category.value}'
  and date_part('year', order_datetime) like '${r.year.value}'
  group by all
  order by sales_usd desc`,h(D,{noResolve:p})})),t.data===void 0&&e.data&&C!==void 0&&e.data(C),{data:A={},customFormattingSettings:f,__db:y}=C,Ca.set(Object.keys(A).length>0),l.params,u=ke`select
      category
  from needful_things.orders
  group by category`,w=`select
      category
  from needful_things.orders
  group by category`,u||!I?u||(B(w,{noResolve:u,...c}),I=!0):B(w,{noResolve:u}),p=ke`select 
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${v.category.value}'
  and date_part('year', order_datetime) like '${v.year.value}'
  group by all
  order by sales_usd desc`,D=`select 
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${v.category.value}'
  and date_part('year', order_datetime) like '${v.year.value}'
  group by all
  order by sales_usd desc`,p||!S?p||(h(D,{noResolve:p,...L}),S=!0):h(D,{noResolve:p}),i(),d(),`  ${typeof J<"u"&&J.title&&J.hide_title!==!0?`<h1 class="title">${R(J.title)}</h1>`:""} ${o.head+=`<!-- HEAD_svelte-2igo1p_START -->${typeof J<"u"&&J.title?`${o.title=`<title>${R(J.title)}</title>`,""} <meta property="og:title"${q("content",J.og?.title??J.title,0)}> <meta name="twitter:title"${q("content",J.og?.title??J.title,0)}>`:` ${o.title="<title>Evidence</title>",""}`}<meta name="twitter:card" content="summary_large_image"><meta name="twitter:site" content="@evidence_dev">${typeof J=="object"?`${J.description||J.og?.description?`<meta name="description"${q("content",J.description??J.og?.description,0)}> <meta property="og:description"${q("content",J.og?.description??J.description,0)}> <meta name="twitter:description"${q("content",J.og?.description??J.description,0)}>`:""} ${J.og?.image?`<meta property="og:image"${q("content",sl(J.og?.image),0)}> <meta name="twitter:image"${q("content",sl(J.og?.image),0)}>`:""}`:""}<!-- HEAD_svelte-2igo1p_END -->`,""}   ${O(Ti,"Details").$$render(o,{title:"How to edit this page"},{},{default:()=>'<p class="markdown" data-svelte-h="svelte-pu0hug">This page can be found in your project at <code class="markdown">/pages/index.md</code>. Make a change to the markdown file and save it to see the change take effect in your browser.</p>'})} ${M?`${O(Tl,"QueryViewer").$$render(o,{queryID:"categories",queryResult:M},{},{})}`:""} ${O(Il,"Dropdown").$$render(o,{data:M,name:"category",value:"category"},{},{default:()=>`${O(me,"DropdownOption").$$render(o,{value:"%",valueLabel:"All Categories"},{},{})}`})} ${O(Il,"Dropdown").$$render(o,{name:"year"},{},{default:()=>`${O(me,"DropdownOption").$$render(o,{value:"%",valueLabel:"All Years"},{},{})} ${O(me,"DropdownOption").$$render(o,{value:"2019"},{},{})} ${O(me,"DropdownOption").$$render(o,{value:"2020"},{},{})} ${O(me,"DropdownOption").$$render(o,{value:"2021"},{},{})}`})} ${T?`${O(Tl,"QueryViewer").$$render(o,{queryID:"orders_by_category",queryResult:T},{},{})}`:""} ${O(Zi,"BarChart").$$render(o,{data:T,title:"Sales by Month, "+v.category.label,x:"month",y:"sales_usd",series:"category"},{},{})} <h2 class="markdown" id="whats-next" data-svelte-h="svelte-fy128a"><a href="#whats-next">What&#39;s Next?</a></h2> <ul class="markdown" data-svelte-h="svelte-14v8ajg"><li class="markdown"><a href="settings" class="markdown">Connect your data sources</a></li> <li class="markdown">Edit/add markdown files in the <code class="markdown">pages</code> folder</li> <li class="markdown">Deploy your project with <a href="https://evidence.dev/cloud" rel="nofollow" class="markdown">Evidence Cloud</a></li></ul> <h2 class="markdown" id="get-support" data-svelte-h="svelte-1o2veuf"><a href="#get-support">Get Support</a></h2> <ul class="markdown" data-svelte-h="svelte-1xbsjxs"><li class="markdown">Message us on <a href="https://slack.evidence.dev/" rel="nofollow" class="markdown">Slack</a></li> <li class="markdown">Read the <a href="https://docs.evidence.dev/" rel="nofollow" class="markdown">Docs</a></li> <li class="markdown">Open an issue on <a href="https://github.com/evidence-dev/evidence" rel="nofollow" class="markdown">Github</a></li></ul>`});export{Lr as default};
