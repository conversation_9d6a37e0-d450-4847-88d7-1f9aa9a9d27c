import{c as de,s as z,b as ka,a as Go,n as <PERSON><PERSON>,d as jo}from"../../chunks/utils.js";import{c as N,a as Ce,b as le,d as H,s as Bt,g as <PERSON>,o as ht,h as R,v as D,e as Za,i as Qt,f as zo}from"../../chunks/ssr.js";import"dequal";import{o as No,w as Uo,f as Ko,m as Et,e as ma,t as $a,n as Ht,p as Ho,s as ha,i as eo,d as Ro,r as Vo,b as to,c as Rt,k as Vt,u as Wo,a as Qo,v as Xo,x as Ft,y as Yo,z as $t,A as po,V as Xt,B as Jo,I as Zo,C as $o,D as ei,E as ti,G as ai,H as oi,J as ii,K as li,L as ri,l as Pt,M as ko,N as So,O as ni,P as si}from"../../chunks/VennDiagram.svelte_svelte_type_style_lang.js";import{w as Yt,d as nt}from"../../chunks/index2.js";import"clsx";import"chroma-js";import{r as di,Q as Dt,a as ao,p as ci}from"../../chunks/Query.js";import{h as Ot,p as ui}from"../../chunks/profile.js";import{a as oo}from"../../chunks/index6.js";import{p as Eo}from"../../chunks/stores.js";import{b as fi,f as He,D as io,C as vi,c as lo,g as ya,s as Ai,a as Re,d as Wt,e as Je,h as mi,i as hi,j as yi}from"../../chunks/CodeBlock.js";import{t as K,c as Le,I as Jt,f as Ci}from"../../chunks/index7.js";import{M as gi,a as ro,I as no,b as xi}from"../../chunks/Info.js";import{t as so,o as wi,g as _i,d as bi,u as pi,a as co,b as ki,c as Oo,r as To,e as Do,f as Si,h as Ei}from"../../chunks/helpers.js";import{r as Oi,h as uo,c as Ti,i as fo,B as Di}from"../../chunks/button.js";import"deep-object-diff";import"../../chunks/index5.js";import{I as Ii}from"../../chunks/InlineError.js";import{c as Bi}from"../../chunks/checkRequiredProps.js";import"ssf";import{tidy as rt,summarize as Li,nDistinct as Mi,groupBy as wa,mutateWithSummary as vo,sum as _a,mutate as Ca,rate as Ao,rename as mo,complete as ho,summarizeAt as Fi}from"@tidyjs/tidy";import"@uwdata/mosaic-sql";import"export-to-csv";import"echarts";import"@evidence-dev/universal-sql/client-duckdb";import"yaml";import"@astronautlabs/jsonpath";import"tua-body-scroll-lock";import"lodash/merge.js";import{Q as yo}from"../../chunks/QueryViewer.js";import"prismjs";import"debounce";import"downloadjs";import{E as Io,Q as Pi,g as qi,a as Gi}from"../../chunks/EmptyChart.js";import"echarts-stat";const ji={positioning:{placement:"bottom"},arrowSize:8,defaultOpen:!1,disableFocusTrap:!1,closeOnEscape:!0,preventScroll:!1,onOpenChange:void 0,closeOnOutsideClick:!0,portal:void 0,forceVisible:!1,openFocus:void 0,closeFocus:void 0,onOutsideClick:void 0},{name:Tt}=Ro("popover"),zi=["trigger","content"];function Ni(a){const e={...ji,...a},t=so(No(e,"open","ids")),{positioning:i,arrowSize:o,disableFocusTrap:l,preventScroll:n,closeOnEscape:s,closeOnOutsideClick:v,portal:A,forceVisible:h,openFocus:g,closeFocus:c,onOutsideClick:m}=t,C=e.open??Yt(e.defaultOpen),d=wi(C,e?.onOpenChange),f=Uo.writable(null),b=so({..._i(zi),...e.ids});Ko(()=>{f.set(document.getElementById(b.trigger.get()))});function B(){d.set(!1);const r=document.getElementById(b.trigger.get());uo({prop:c.get(),defaultEl:r})}const k=bi({open:d,activeTrigger:f,forceVisible:h}),T=Et(Tt("content"),{stores:[k,A,b.content],returned:([r,u,_])=>({hidden:r&&eo?void 0:!0,tabindex:-1,style:ha({display:r?void 0:"none"}),id:_,"data-state":r?"open":"closed","data-portal":Ho(u)}),action:r=>{let u=Ht;const _=ma([k,f,i,l,s,v,A],([x,w,q,G,X,P,ce])=>{u(),!(!x||!w)&&$a().then(()=>{u(),u=pi(r,{anchorElement:w,open:d,options:{floating:q,focusTrap:G?null:{returnFocusOnDeactivate:!1,clickOutsideDeactivates:P,allowOutsideClick:!0,escapeDeactivates:X},modal:{shouldCloseOnInteractOutside:p,onClose:B,open:x,closeOnInteractOutside:P},escapeKeydown:X?{handler:()=>{B()}}:null,portal:co(r,ce)}}).destroy})});return{destroy(){_(),u()}}}});function I(r){d.update(u=>!u),r&&r!==f.get()&&f.set(r)}function p(r){if(m.get()?.(r),r.defaultPrevented)return!1;const u=r.target,_=document.getElementById(b.trigger.get());return!(_&&Vo(u)&&(u===_||_.contains(u)))}const M=Et(Tt("trigger"),{stores:[k,b.content,b.trigger],returned:([r,u,_])=>({role:"button","aria-haspopup":"dialog","aria-expanded":r?"true":"false","data-state":Co(r),"aria-controls":u,id:_}),action:r=>({destroy:to(Rt(r,"click",()=>{I(r)}),Rt(r,"keydown",_=>{_.key!==Vt.ENTER&&_.key!==Vt.SPACE||(_.preventDefault(),I(r))}))})}),E=Et(Tt("overlay"),{stores:[k],returned:([r])=>({hidden:r?void 0:!0,tabindex:-1,style:ha({display:r?void 0:"none"}),"aria-hidden":"true","data-state":Co(r)}),action:r=>{let u=Ht,_=Ht,x=Ht;if(s.get()){const w=Wo(r,{handler:()=>{B()}});w&&w.destroy&&(u=w.destroy)}return _=ma([A],([w])=>{if(x(),w===null)return;const q=co(r,w);q!==null&&(x=ki(r,q).destroy)}),{destroy(){u(),_(),x()}}}}),S=Et(Tt("arrow"),{stores:o,returned:r=>({"data-arrow":!0,style:ha({position:"absolute",width:`var(--arrow-size, ${r}px)`,height:`var(--arrow-size, ${r}px)`})})}),y=Et(Tt("close"),{returned:()=>({type:"button"}),action:r=>({destroy:to(Rt(r,"click",_=>{_.defaultPrevented||B()}),Rt(r,"keydown",_=>{_.defaultPrevented||_.key!==Vt.ENTER&&_.key!==Vt.SPACE||(_.preventDefault(),I())}))})});return ma([d,f,n],([r,u,_])=>{if(!eo)return;const x=[];if(r){u||$a().then(()=>{const q=document.getElementById(b.trigger.get());Qo(q)&&f.set(q)}),_&&x.push(Oi());const w=u??document.getElementById(b.trigger.get());uo({prop:g.get(),defaultEl:w})}return()=>{x.forEach(w=>w())}}),{ids:b,elements:{trigger:M,content:T,arrow:S,close:y,overlay:E},states:{open:d},options:t}}function Co(a){return a?"open":"closed"}function Ui(){return{NAME:"separator",PARTS:["root"]}}function Ki(a){const{NAME:e,PARTS:t}=Ui(),i=Oo(e,t),o={...Ti(To(a)),getAttrs:i};return{...o,updateOption:Do(o.options)}}const Hi=N((a,e,t,i)=>{let o,l=de(e,["orientation","decorative","asChild","el"]),n,s,{orientation:v="horizontal"}=e,{decorative:A=!0}=e,{asChild:h=!1}=e,{el:g=void 0}=e;const{elements:{root:c},updateOption:m,getAttrs:C}=Ki({orientation:v,decorative:A});s=z(c,f=>n=f);const d=C("root");return e.orientation===void 0&&t.orientation&&v!==void 0&&t.orientation(v),e.decorative===void 0&&t.decorative&&A!==void 0&&t.decorative(A),e.asChild===void 0&&t.asChild&&h!==void 0&&t.asChild(h),e.el===void 0&&t.el&&g!==void 0&&t.el(g),m("orientation",v),m("decorative",A),o=n,Object.assign(o,d),s(),`${h?`${i.default?i.default({builder:o}):""}`:`<div${Ce([H(o),H(l)],{})}${le("this",g,0)}></div>`}`});function Bo(){return{NAME:"popover",PARTS:["arrow","close","content","trigger"]}}function Ri(a){const{NAME:e,PARTS:t}=Bo(),i=Oo(e,t),o={...Ni({positioning:{placement:"bottom",gutter:0},...To(a),forceVisible:!0}),getAttrs:i};return Bt(e,o),{...o,updateOption:Do(o.options)}}function Sa(){const{NAME:a}=Bo();return Lt(a)}function Vi(a){const t={...{side:"bottom",align:"center"},...a},{options:{positioning:i}}=Sa();Si(i)(t)}const Wi=N((a,e,t,i)=>{let o,l,{disableFocusTrap:n=void 0}=e,{closeOnEscape:s=void 0}=e,{closeOnOutsideClick:v=void 0}=e,{preventScroll:A=void 0}=e,{portal:h=void 0}=e,{open:g=void 0}=e,{onOpenChange:c=void 0}=e,{openFocus:m=void 0}=e,{closeFocus:C=void 0}=e,{onOutsideClick:d=void 0}=e;const{updateOption:f,states:{open:b},ids:B}=Ri({disableFocusTrap:n,closeOnEscape:s,closeOnOutsideClick:v,preventScroll:A,portal:h,defaultOpen:g,openFocus:m,closeFocus:C,onOutsideClick:d,onOpenChange:({next:T})=>(g!==T&&(c?.(T),g=T),T),positioning:{gutter:0,offset:{mainAxis:1}}}),k=nt([B.content,B.trigger],([T,I])=>({content:T,trigger:I}));return l=z(k,T=>o=T),e.disableFocusTrap===void 0&&t.disableFocusTrap&&n!==void 0&&t.disableFocusTrap(n),e.closeOnEscape===void 0&&t.closeOnEscape&&s!==void 0&&t.closeOnEscape(s),e.closeOnOutsideClick===void 0&&t.closeOnOutsideClick&&v!==void 0&&t.closeOnOutsideClick(v),e.preventScroll===void 0&&t.preventScroll&&A!==void 0&&t.preventScroll(A),e.portal===void 0&&t.portal&&h!==void 0&&t.portal(h),e.open===void 0&&t.open&&g!==void 0&&t.open(g),e.onOpenChange===void 0&&t.onOpenChange&&c!==void 0&&t.onOpenChange(c),e.openFocus===void 0&&t.openFocus&&m!==void 0&&t.openFocus(m),e.closeFocus===void 0&&t.closeFocus&&C!==void 0&&t.closeFocus(C),e.onOutsideClick===void 0&&t.onOutsideClick&&d!==void 0&&t.onOutsideClick(d),g!==void 0&&b.set(g),f("disableFocusTrap",n),f("closeOnEscape",s),f("closeOnOutsideClick",v),f("preventScroll",A),f("portal",h),f("openFocus",m),f("closeFocus",C),f("onOutsideClick",d),l(),`${i.default?i.default({ids:o}):""}`}),Qi=N((a,e,t,i)=>{let o,l=de(e,["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","id","side","align","sideOffset","alignOffset","collisionPadding","avoidCollisions","collisionBoundary","sameWidth","fitViewport","strategy","overlap","el"]),n,s,v,A,{transition:h=void 0}=e,{transitionConfig:g=void 0}=e,{inTransition:c=void 0}=e,{inTransitionConfig:m=void 0}=e,{outTransition:C=void 0}=e,{outTransitionConfig:d=void 0}=e,{asChild:f=!1}=e,{id:b=void 0}=e,{side:B="bottom"}=e,{align:k="center"}=e,{sideOffset:T=0}=e,{alignOffset:I=0}=e,{collisionPadding:p=8}=e,{avoidCollisions:M=!0}=e,{collisionBoundary:E=void 0}=e,{sameWidth:S=!1}=e,{fitViewport:y=!1}=e,{strategy:r="absolute"}=e,{overlap:u=!1}=e,{el:_=void 0}=e;const{elements:{content:x},states:{open:w},ids:q,getAttrs:G}=Sa();A=z(x,P=>v=P),s=z(w,P=>n=P);const X=G("content");return e.transition===void 0&&t.transition&&h!==void 0&&t.transition(h),e.transitionConfig===void 0&&t.transitionConfig&&g!==void 0&&t.transitionConfig(g),e.inTransition===void 0&&t.inTransition&&c!==void 0&&t.inTransition(c),e.inTransitionConfig===void 0&&t.inTransitionConfig&&m!==void 0&&t.inTransitionConfig(m),e.outTransition===void 0&&t.outTransition&&C!==void 0&&t.outTransition(C),e.outTransitionConfig===void 0&&t.outTransitionConfig&&d!==void 0&&t.outTransitionConfig(d),e.asChild===void 0&&t.asChild&&f!==void 0&&t.asChild(f),e.id===void 0&&t.id&&b!==void 0&&t.id(b),e.side===void 0&&t.side&&B!==void 0&&t.side(B),e.align===void 0&&t.align&&k!==void 0&&t.align(k),e.sideOffset===void 0&&t.sideOffset&&T!==void 0&&t.sideOffset(T),e.alignOffset===void 0&&t.alignOffset&&I!==void 0&&t.alignOffset(I),e.collisionPadding===void 0&&t.collisionPadding&&p!==void 0&&t.collisionPadding(p),e.avoidCollisions===void 0&&t.avoidCollisions&&M!==void 0&&t.avoidCollisions(M),e.collisionBoundary===void 0&&t.collisionBoundary&&E!==void 0&&t.collisionBoundary(E),e.sameWidth===void 0&&t.sameWidth&&S!==void 0&&t.sameWidth(S),e.fitViewport===void 0&&t.fitViewport&&y!==void 0&&t.fitViewport(y),e.strategy===void 0&&t.strategy&&r!==void 0&&t.strategy(r),e.overlap===void 0&&t.overlap&&u!==void 0&&t.overlap(u),e.el===void 0&&t.el&&_!==void 0&&t.el(_),b&&q.content.set(b),o=v,Object.assign(o,X),n&&Vi({side:B,align:k,sideOffset:T,alignOffset:I,collisionPadding:p,avoidCollisions:M,collisionBoundary:E,sameWidth:S,fitViewport:y,strategy:r,overlap:u}),s(),A(),`${f&&n?`${i.default?i.default({builder:o}):""}`:`${h&&n?`<div${Ce([H(o),H(l)],{})}${le("this",_,0)}>${i.default?i.default({builder:o}):""}</div>`:`${c&&C&&n?`<div${Ce([H(o),H(l)],{})}${le("this",_,0)}>${i.default?i.default({builder:o}):""}</div>`:`${c&&n?`<div${Ce([H(o),H(l)],{})}${le("this",_,0)}>${i.default?i.default({builder:o}):""}</div>`:`${C&&n?`<div${Ce([H(o),H(l)],{})}${le("this",_,0)}>${i.default?i.default({builder:o}):""}</div>`:`${n?`<div${Ce([H(o),H(l)],{})}${le("this",_,0)}>${i.default?i.default({builder:o}):""}</div>`:""}`}`}`}`}`}`}),Xi=N((a,e,t,i)=>{let o,l,n=de(e,["asChild","id","el"]),s,v,A,h,{asChild:g=!1}=e,{id:c=void 0}=e,{el:m=void 0}=e;const{elements:{trigger:C},states:{open:d},ids:f,getAttrs:b}=Sa();v=z(C,k=>s=k),h=z(d,k=>A=k),Ei();const B=b("trigger");return e.asChild===void 0&&t.asChild&&g!==void 0&&t.asChild(g),e.id===void 0&&t.id&&c!==void 0&&t.id(c),e.el===void 0&&t.el&&m!==void 0&&t.el(m),c&&f.trigger.set(c),o={...B,"aria-controls":A?f.content:void 0},l=s,Object.assign(l,o),v(),h(),`${g?`${i.default?i.default({builder:l}):""}`:`<button${Ce([H(l),{type:"button"},H(n)],{})}${le("this",m,0)}>${i.default?i.default({builder:l}):""}</button>`}`}),ba=(a,e={serializeStrings:!0})=>a==null?"null":typeof a=="string"?e.serializeStrings!==!1?`'${a.replaceAll("'","''")}'`:a:typeof a=="number"||typeof a=="bigint"||typeof a=="boolean"?String(a):a instanceof Date?`'${a.toISOString()}'::TIMESTAMP_MS`:Array.isArray(a)?`[${a.map(t=>ba(t,e)).join(", ")}]`:JSON.stringify(a),Yi=N((a,e,t,i)=>{let{enabled:o=!0}=e;return e.enabled===void 0&&t.enabled&&o!==void 0&&t.enabled(o),o=K(o),`<div class="${["contents",o?"print:hidden":""].join(" ").trim()}">${i.default?i.default({}):""}</div>`}),Lo=Symbol("EVIDENCE_DROPDOWN_CTX");let Ji=0;const mt=N((a,e,t,i)=>{let{value:o}=e,{valueLabel:l=o}=e,{idx:n=-1}=e,{__auto:s=!1}=e;s||(n=Ji++);const v=Lt(Lo);return ht(v.registerOption({value:o,label:l,idx:n,__auto:s})),e.value===void 0&&t.value&&o!==void 0&&t.value(o),e.valueLabel===void 0&&t.valueLabel&&l!==void 0&&t.valueLabel(l),e.idx===void 0&&t.idx&&n!==void 0&&t.idx(n),e.__auto===void 0&&t.__auto&&s!==void 0&&t.__auto(s),""});function Zi(a){return Object.keys(a).reduce((e,t)=>a[t]===void 0?e:e+`${t}:${a[t]};`,"")}const $i={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"};function Zt(a,e,t,i){const o=Array.isArray(e)?e:[e];return o.forEach(l=>a.addEventListener(l,t,i)),()=>{o.forEach(l=>a.removeEventListener(l,t,i))}}function Mo(...a){return(...e)=>{for(const t of a)typeof t=="function"&&t(...e)}}const el=N((a,e,t,i)=>{let o,l=de(e,["label","shouldFilter","filter","value","onValueChange","loop","onKeydown","state","ids","asChild"]),n,s,{label:v=void 0}=e,{shouldFilter:A=!0}=e,{filter:h=void 0}=e,{value:g=void 0}=e,{onValueChange:c=void 0}=e,{loop:m=void 0}=e,{onKeydown:C=void 0}=e,{state:d=void 0}=e,{ids:f=void 0}=e,{asChild:b=!1}=e;const{commandEl:B,handleRootKeydown:k,ids:T,state:I}=Xo({label:v,shouldFilter:A,filter:h,value:g,onValueChange:u=>{u!==g&&(g=u,c?.(u))},loop:m,state:d,ids:f});s=z(I,u=>n=u);function p(u){u&&u!==n.value&&ka(I,n.value=u,n)}function M(u){return B.set(u),{destroy:Mo(Zt(u,"keydown",y))}}const E={role:"application",id:T.root,"data-cmdk-root":""},S={"data-cmdk-label":"",for:T.input,id:T.label,style:Zi($i)};function y(u){C?.(u),!u.defaultPrevented&&k(u)}const r={action:M,attrs:E};return e.label===void 0&&t.label&&v!==void 0&&t.label(v),e.shouldFilter===void 0&&t.shouldFilter&&A!==void 0&&t.shouldFilter(A),e.filter===void 0&&t.filter&&h!==void 0&&t.filter(h),e.value===void 0&&t.value&&g!==void 0&&t.value(g),e.onValueChange===void 0&&t.onValueChange&&c!==void 0&&t.onValueChange(c),e.loop===void 0&&t.loop&&m!==void 0&&t.loop(m),e.onKeydown===void 0&&t.onKeydown&&C!==void 0&&t.onKeydown(C),e.state===void 0&&t.state&&d!==void 0&&t.state(d),e.ids===void 0&&t.ids&&f!==void 0&&t.ids(f),e.asChild===void 0&&t.asChild&&b!==void 0&&t.asChild(b),p(g),o={root:r,label:{attrs:S},stateStore:I,state:n},s(),`${b?`${i.default?i.default({...o}):""}`:`<div${Ce([H(E),H(l)],{})}> <label${Ce([H(S)],{})}>${R(v??"")}</label> ${i.default?i.default({...o}):""}</div>`}`}),tl=N((a,e,t,i)=>{de(e,["asChild"]);let o,l,{asChild:n=!1}=e;const s=Ft();return l=z(s,v=>o=v),e.asChild===void 0&&t.asChild&&n!==void 0&&t.asChild(n),o.filtered.count,l(),""}),al=N((a,e,t,i)=>{let o,l,n,s,v=de(e,["heading","value","alwaysRender","asChild"]),A,h,{heading:g=void 0}=e,{value:c=""}=e,{alwaysRender:m=!1}=e,{asChild:C=!1}=e;const{id:d}=Yo(m),f=$t(),b=Ft(),B=po(),k=nt(b,p=>m||f.filter()===!1||!p.search?!0:p.filtered.groups.has(d));h=z(k,p=>A=p);function T(p){if(c){f.value(d,c),p.setAttribute(Xt,c);return}g?c=g.trim().toLowerCase():p.textContent&&(c=p.textContent.trim().toLowerCase()),f.value(d,c),p.setAttribute(Xt,c)}const I={"data-cmdk-group-heading":"","aria-hidden":!0,id:B};return e.heading===void 0&&t.heading&&g!==void 0&&t.heading(g),e.value===void 0&&t.value&&c!==void 0&&t.value(c),e.alwaysRender===void 0&&t.alwaysRender&&m!==void 0&&t.alwaysRender(m),e.asChild===void 0&&t.asChild&&C!==void 0&&t.asChild(C),o={"data-cmdk-group":"",role:"presentation",hidden:A?void 0:!0,"data-value":c},l={"data-cmdk-group-items":"",role:"group","aria-labelledby":g?B:void 0},n={action:T,attrs:o},s={attrs:l},h(),`${C?`${i.default?i.default({container:n,group:s,heading:{attrs:I}}):""}`:`<div${Ce([H(o),H(v)],{})}>${g?`<div${Ce([H(I)],{})}>${R(g)}</div>`:""} <div${Ce([H(l)],{})}>${i.default?i.default({container:n,group:s,heading:{attrs:I}}):""}</div></div>`}`});function ol(a){return new Promise(e=>setTimeout(e,a))}const il=N((a,e,t,i)=>{let o=de(e,["autofocus","value","asChild","el"]),l,n,s,v;const{ids:A,commandEl:h}=$t(),g=Ft(),c=nt(g,p=>p.search);v=z(c,p=>s=p);const m=nt(g,p=>p.value);let{autofocus:C=void 0}=e,{value:d=s}=e,{asChild:f=!1}=e,{el:b=void 0}=e;const B=nt([m,h],([p,M])=>Jo?M?.querySelector(`${Zo}[${Xt}="${p}"]`)?.getAttribute("id"):void 0);n=z(B,p=>l=p);function k(p){g.updateState("search",p)}function T(p){return C&&ol(10).then(()=>p.focus()),{destroy:Zt(p,"change",E=>{$o(E.target)&&g.updateState("search",E.target.value)})}}let I;return e.autofocus===void 0&&t.autofocus&&C!==void 0&&t.autofocus(C),e.value===void 0&&t.value&&d!==void 0&&t.value(d),e.asChild===void 0&&t.asChild&&f!==void 0&&t.asChild(f),e.el===void 0&&t.el&&b!==void 0&&t.el(b),k(d),I={type:"text","data-cmdk-input":"",autocomplete:"off",autocorrect:"off",spellcheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":A.list,"aria-labelledby":A.label,"aria-activedescendant":l??void 0,id:A.input},n(),v(),`${f?`${i.default?i.default({action:T,attrs:I}):""}`:`<input${Ce([H(I),H(o)],{})}${le("this",b,0)}${le("value",d,0)}>`}`}),ll=N((a,e,t,i)=>{let o,l=de(e,["disabled","value","onSelect","alwaysRender","asChild","id"]),n,s,v,{disabled:A=!1}=e,{value:h=""}=e,{onSelect:g=void 0}=e,{alwaysRender:c=!1}=e,{asChild:m=!1}=e,{id:C=po()}=e;const d=ei(),f=$t(),b=Ft(),B=c??d?.alwaysRender,k=nt(b,E=>{if(B||f.filter()===!1||!E.search)return!0;const S=E.filtered.items.get(C);return ti(S)?!1:S>0});v=z(k,E=>E);const T=nt(b,E=>E.value===h);s=z(T,E=>n=E);function I(E){!h&&E.textContent&&(h=E.textContent.trim().toLowerCase()),f.value(C,h),E.setAttribute(Xt,h);const S=Mo(Zt(E,"pointermove",()=>{A||M()}),Zt(E,"click",()=>{A||p()}));return{destroy(){S()}}}function p(){M(),g?.(h)}function M(){b.updateState("value",h,!0)}return e.disabled===void 0&&t.disabled&&A!==void 0&&t.disabled(A),e.value===void 0&&t.value&&h!==void 0&&t.value(h),e.onSelect===void 0&&t.onSelect&&g!==void 0&&t.onSelect(g),e.alwaysRender===void 0&&t.alwaysRender&&c!==void 0&&t.alwaysRender(c),e.asChild===void 0&&t.asChild&&m!==void 0&&t.asChild(m),e.id===void 0&&t.id&&C!==void 0&&t.id(C),o={"aria-disabled":A?!0:void 0,"aria-selected":n?!0:void 0,"data-disabled":A?!0:void 0,"data-selected":n?!0:void 0,"data-cmdk-item":"","data-value":h,role:"option",id:C},s(),v(),`${`${m?`${i.default?i.default({action:I,attrs:o}):""}`:`<div${Ce([H(o),H(l)],{})}>${i.default?i.default({action:I,attrs:o}):""}</div>`}`}`}),rl=N((a,e,t,i)=>{let o=de(e,["el","asChild"]),l;const{ids:n}=$t(),s=Ft();l=z(s,d=>d);let{el:v=void 0}=e,{asChild:A=!1}=e;function h(d){let f;const b=d.closest("[data-cmdk-list]");if(!ai(b))return;const B=new ResizeObserver(()=>{f=requestAnimationFrame(()=>{const k=d.offsetHeight;b.style.setProperty("--cmdk-list-height",k.toFixed(1)+"px")})});return B.observe(d),{destroy(){cancelAnimationFrame(f),B.unobserve(d)}}}const g={"data-cmdk-list":"",role:"listbox","aria-label":"Suggestions",id:n.list,"aria-labelledby":n.input},c={"data-cmdk-list-sizer":""},m={attrs:g},C={attrs:c,action:h};return e.el===void 0&&t.el&&v!==void 0&&t.el(v),e.asChild===void 0&&t.asChild&&A!==void 0&&t.asChild(A),l(),`${A?`${i.default?i.default({list:m,sizer:C}):""}`:`<div${Ce([H(g),H(o)],{})}${le("this",v,0)}><div${Ce([H(c)],{})}>${i.default?i.default({}):""}</div></div>`}`}),nl=N((a,e,t,i)=>{let o=de(e,["value","class"]),{value:l=void 0}=e,{class:n=void 0}=e;e.value===void 0&&t.value&&l!==void 0&&t.value(l),e.class===void 0&&t.class&&n!==void 0&&t.class(n);let s,v,A=a.head;do s=!0,a.head=A,v=`${D(el,"CommandPrimitive.Root").$$render(a,Object.assign({},{class:Le("flex h-full w-full flex-col overflow-hidden rounded-md bg-base-100",n)},o,{value:l}),{value:h=>{l=h,s=!1}},{default:()=>`${i.default?i.default({}):""}`})}`;while(!s);return v}),sl=N((a,e,t,i)=>{let o=de(e,["class"]),{class:l=void 0}=e;return e.class===void 0&&t.class&&l!==void 0&&t.class(l),`${D(tl,"CommandPrimitive.Empty").$$render(a,Object.assign({},{class:Le("py-6 text-center text-sm",l)},o),{},{default:()=>`${i.default?i.default({}):""}`})}`}),dl=N((a,e,t,i)=>{let o=de(e,["class"]),{class:l=void 0}=e;return e.class===void 0&&t.class&&l!==void 0&&t.class(l),`${D(al,"CommandPrimitive.Group").$$render(a,Object.assign({},{class:Le("text-foreground overflow-hidden p-1 [&_[data-cmdk-group-heading]]:px-2 [&_[data-cmdk-group-heading]]:py-1.5 [&_[data-cmdk-group-heading]]:text-xs [&_[data-cmdk-group-heading]]:font-medium [&_[data-cmdk-group-heading]]:text-base-content-muted",l)},o),{},{default:()=>`${i.default?i.default({}):""}`})}`}),pa=N((a,e,t,i)=>{let o=de(e,["class"]),{class:l=void 0}=e;return e.class===void 0&&t.class&&l!==void 0&&t.class(l),`${D(ll,"CommandPrimitive.Item").$$render(a,Object.assign({},{class:Le("relative flex cursor-default select-none items-center rounded-xs px-2 py-1.5 text-sm outline-none aria-selected:bg-base-200 data-[disabled]:pointer-events-none data-[disabled]:opacity-50",l)},o),{},{default:()=>`${i.default?i.default({}):""}`})}`}),cl=N((a,e,t,i)=>{let o=de(e,["class","value"]),{class:l=void 0}=e,{value:n=""}=e;e.class===void 0&&t.class&&l!==void 0&&t.class(l),e.value===void 0&&t.value&&n!==void 0&&t.value(n);let s,v,A=a.head;do s=!0,a.head=A,v=`<div class="flex items-center border-b border-base-300 px-3" data-cmdk-input-wrapper="">${D(Jt,"Icon").$$render(a,{src:gi,class:"mr-2 h-4 w-4 shrink-0 text-base-content-muted"},{},{})} ${D(il,"CommandPrimitive.Input").$$render(a,Object.assign({},{class:Le("flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-base-content-muted disabled:cursor-not-allowed disabled:opacity-50",l)},o,{value:n}),{value:h=>{n=h,s=!1}},{})}</div>`;while(!s);return v}),ul=N((a,e,t,i)=>{let o=de(e,["class"]),{class:l=void 0}=e;return e.class===void 0&&t.class&&l!==void 0&&t.class(l),`${D(rl,"CommandPrimitive.List").$$render(a,Object.assign({},{class:Le("max-h-[300px] overflow-y-auto overflow-x-hidden",l)},o),{},{default:()=>`${i.default?i.default({}):""}`})}`}),go=N((a,e,t,i)=>{let{value:o}=e,{valueLabel:l=o}=e,{active:n=!1}=e,{handleSelect:s}=e,{multiple:v}=e;return e.value===void 0&&t.value&&o!==void 0&&t.value(o),e.valueLabel===void 0&&t.valueLabel&&l!==void 0&&t.valueLabel(l),e.active===void 0&&t.active&&n!==void 0&&t.active(n),e.handleSelect===void 0&&t.handleSelect&&s!==void 0&&t.handleSelect(s),e.multiple===void 0&&t.multiple&&v!==void 0&&t.multiple(v),`${D(pa,"Command.Item").$$render(a,{value:String(l),onSelect:()=>s({value:o,label:l})},{},{default:()=>`${v?`<div${le("class",Le("mr-2 flex h-4 w-4 items-center justify-center rounded-xs border border-base-content",n?"bg-base-content text-base-100":"opacity-50 [&_svg]:invisible"),0)}>${D(Jt,"Icon").$$render(a,{src:ro,class:Le("h-4 w-4")},{},{})}</div>`:`<div class="mr-2 flex h-4 w-4 items-center justify-center">${D(Jt,"Icon").$$render(a,{src:ro,class:Le("h-4 w-4",n?"":"text-transparent")},{},{})}</div>`} <span class="line-clamp-4">${R(l)}</span>`})}`}),fl=N((a,e,t,i)=>{let o=de(e,["class","transition","transitionConfig","align","sideOffset"]),{class:l=void 0}=e,{transition:n=Ci}=e,{transitionConfig:s=void 0}=e,{align:v="center"}=e,{sideOffset:A=4}=e;return e.class===void 0&&t.class&&l!==void 0&&t.class(l),e.transition===void 0&&t.transition&&n!==void 0&&t.transition(n),e.transitionConfig===void 0&&t.transitionConfig&&s!==void 0&&t.transitionConfig(s),e.align===void 0&&t.align&&v!==void 0&&t.align(v),e.sideOffset===void 0&&t.sideOffset&&A!==void 0&&t.sideOffset(A),`${D(Qi,"PopoverPrimitive.Content").$$render(a,Object.assign({},{transition:n},{transitionConfig:s},{align:v},{sideOffset:A},o,{class:Le("z-50 w-72 rounded-md border border-base-300 p-4 shadow-md outline-none bg-base-100",l)}),{},{default:()=>`${i.default?i.default({}):""}`})}`}),vl=Wi,Al=Xi,xo=N((a,e,t,i)=>{let o=de(e,["class","orientation","decorative"]),{class:l=void 0}=e,{orientation:n="horizontal"}=e,{decorative:s=void 0}=e;return e.class===void 0&&t.class&&l!==void 0&&t.class(l),e.orientation===void 0&&t.orientation&&n!==void 0&&t.orientation(n),e.decorative===void 0&&t.decorative&&s!==void 0&&t.decorative(s),`${D(Hi,"SeparatorPrimitive.Root").$$render(a,Object.assign({},{class:Le("shrink-0 bg-base-300",n==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",l)},{orientation:n},{decorative:s},o),{},{})}`}),ga=N((a,e,t,i)=>{let o=de(e,["class","href","variant"]),{class:l=void 0}=e,{href:n=void 0}=e,{variant:s="default"}=e;return e.class===void 0&&t.class&&l!==void 0&&t.class(l),e.href===void 0&&t.href&&n!==void 0&&t.href(n),e.variant===void 0&&t.variant&&s!==void 0&&t.variant(s),`${(v=>v?`<${n?"a":"span"}${Ce([{href:Za(n)},{class:Za(Le(oi({variant:s,className:l})))},H(o)],{})}>${fo(v)?"":`${i.default?i.default({}):""}`}${fo(v)?"":`</${v}>`}`:"")(n?"a":"span")}`}),ml={code:".viewport.svelte-1youqmj{position:relative;overflow-y:auto;-webkit-overflow-scrolling:touch;display:block}.contents.svelte-1youqmj,.row.svelte-1youqmj{display:block}.row.svelte-1youqmj{overflow:hidden}",map:`{"version":3,"file":"Virtual.svelte","sources":["Virtual.svelte"],"sourcesContent":["<script>\\n\\timport { onMount, tick } from 'svelte';\\n\\n\\t// props\\n\\texport let items;\\n\\texport let height = '100%';\\n\\texport let itemHeight = undefined;\\n\\n\\t// read-only, but visible to consumers via bind:start\\n\\texport let start = 0;\\n\\texport let end = 0;\\n\\n\\t// local state\\n\\tlet height_map = [];\\n\\tlet rows;\\n\\tlet viewport;\\n\\tlet contents;\\n\\tlet viewport_height = 0;\\n\\tlet visible;\\n\\tlet mounted;\\n\\n\\tlet top = 0;\\n\\tlet bottom = 0;\\n\\tlet average_height;\\n\\n\\t$: visible = items.slice(start, end).map((data, i) => {\\n\\t\\treturn { index: i + start, data };\\n\\t});\\n\\n\\t// whenever \`items\` changes, invalidate the current heightmap\\n\\t$: if (mounted) refresh(items, viewport_height, itemHeight);\\n\\n\\tasync function refresh(items, viewport_height, itemHeight) {\\n\\t\\tconst { scrollTop } = viewport;\\n\\n\\t\\tawait tick(); // wait until the DOM is up to date\\n\\t\\tif (!mounted) return;\\n\\n\\t\\tlet content_height = top - scrollTop;\\n\\t\\tlet i = start;\\n\\n\\t\\twhile (content_height < viewport_height && i < items.length) {\\n\\t\\t\\tlet row = rows[i - start];\\n\\n\\t\\t\\tif (!row) {\\n\\t\\t\\t\\tend = i + 1;\\n\\t\\t\\t\\tawait tick(); // render the newly visible row\\n\\t\\t\\t\\tif (!mounted) return;\\n\\t\\t\\t\\trow = rows[i - start];\\n\\t\\t\\t}\\n\\n\\t\\t\\tconst row_height = (height_map[i] =\\n\\t\\t\\t\\titemHeight || row?.offsetHeight || Number.MAX_SAFE_INTEGER);\\n\\t\\t\\tcontent_height += row_height;\\n\\t\\t\\ti += 1;\\n\\t\\t}\\n\\n\\t\\tend = i;\\n\\n\\t\\tconst remaining = items.length - end;\\n\\t\\taverage_height = (top + content_height) / end;\\n\\n\\t\\tbottom = remaining * average_height;\\n\\t\\theight_map.length = items.length;\\n\\t}\\n\\n\\tasync function handle_scroll() {\\n\\t\\tconst { scrollTop } = viewport;\\n\\n\\t\\tconst old_start = start;\\n\\n\\t\\tfor (let v = 0; v < rows.length; v += 1) {\\n\\t\\t\\theight_map[start + v] = itemHeight || rows[v]?.offsetHeight || Number.MAX_SAFE_INTEGER;\\n\\t\\t}\\n\\n\\t\\tlet i = 0;\\n\\t\\tlet y = 0;\\n\\n\\t\\twhile (i < items.length) {\\n\\t\\t\\tconst row_height = height_map[i] || average_height;\\n\\t\\t\\tif (y + row_height > scrollTop) {\\n\\t\\t\\t\\tstart = i;\\n\\t\\t\\t\\ttop = y;\\n\\n\\t\\t\\t\\tbreak;\\n\\t\\t\\t}\\n\\n\\t\\t\\ty += row_height;\\n\\t\\t\\ti += 1;\\n\\t\\t}\\n\\n\\t\\twhile (i < items.length) {\\n\\t\\t\\ty += height_map[i] || average_height;\\n\\t\\t\\ti += 1;\\n\\n\\t\\t\\tif (y > scrollTop + viewport_height) break;\\n\\t\\t}\\n\\n\\t\\tend = i;\\n\\n\\t\\tconst remaining = items.length - end;\\n\\t\\taverage_height = y / end;\\n\\n\\t\\twhile (i < items.length) height_map[i++] = average_height;\\n\\t\\tbottom = remaining * average_height;\\n\\n\\t\\t// prevent jumping if we scrolled up into unknown territory\\n\\t\\tif (start < old_start) {\\n\\t\\t\\tawait tick();\\n\\n\\t\\t\\tlet expected_height = 0;\\n\\t\\t\\tlet actual_height = 0;\\n\\n\\t\\t\\tfor (let i = start; i < old_start; i += 1) {\\n\\t\\t\\t\\tif (rows[i - start]) {\\n\\t\\t\\t\\t\\texpected_height += height_map[i];\\n\\t\\t\\t\\t\\tactual_height += itemHeight || rows[i - start]?.offsetHeight || Number.MAX_SAFE_INTEGER;\\n\\t\\t\\t\\t}\\n\\t\\t\\t}\\n\\n\\t\\t\\tconst d = actual_height - expected_height;\\n\\t\\t\\tviewport.scrollTo(0, scrollTop + d);\\n\\t\\t}\\n\\n\\t\\t// TODO if we overestimated the space these\\n\\t\\t// rows would occupy we may need to add some\\n\\t\\t// more. maybe we can just call handle_scroll again?\\n\\t}\\n\\n\\t// trigger initial refresh\\n\\tonMount(() => {\\n\\t\\trows = contents.getElementsByClassName('row');\\n\\t\\tmounted = true;\\n\\t\\treturn () => (mounted = false);\\n\\t});\\n<\/script>\\n\\n<div\\n\\tbind:this={viewport}\\n\\tbind:offsetHeight={viewport_height}\\n\\ton:scroll={handle_scroll}\\n\\tstyle=\\"height: {height};\\"\\n\\tclass=\\"viewport\\"\\n>\\n\\t<div\\n\\t\\tclass=\\"contents\\"\\n\\t\\tbind:this={contents}\\n\\t\\tstyle=\\"padding-top: {top}px; padding-bottom: {bottom}px;\\"\\n\\t>\\n\\t\\t{#each visible as row (row.index)}\\n\\t\\t\\t<div class=\\"row\\">\\n\\t\\t\\t\\t<slot item={row.data}>Missing template</slot>\\n\\t\\t\\t</div>\\n\\t\\t{/each}\\n\\t</div>\\n</div>\\n\\n<style>\\n\\t.viewport {\\n\\t\\tposition: relative;\\n\\t\\toverflow-y: auto;\\n\\t\\t-webkit-overflow-scrolling: touch;\\n\\t\\tdisplay: block;\\n\\t}\\n\\n\\t.contents,\\n\\t.row {\\n\\t\\tdisplay: block;\\n\\t}\\n\\n\\t.row {\\n\\t\\toverflow: hidden;\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AA8JC,wBAAU,CACT,QAAQ,CAAE,QAAQ,CAClB,UAAU,CAAE,IAAI,CAChB,0BAA0B,CAAE,KAAK,CACjC,OAAO,CAAE,KACV,CAEA,wBAAS,CACT,mBAAK,CACJ,OAAO,CAAE,KACV,CAEA,mBAAK,CACJ,QAAQ,CAAE,MACX"}`},hl=N((a,e,t,i)=>{let{items:o}=e,{height:l="100%"}=e,{itemHeight:n=void 0}=e,{start:s=0}=e,{end:v=0}=e,A,h,g,c=0,m=0;return e.items===void 0&&t.items&&o!==void 0&&t.items(o),e.height===void 0&&t.height&&l!==void 0&&t.height(l),e.itemHeight===void 0&&t.itemHeight&&n!==void 0&&t.itemHeight(n),e.start===void 0&&t.start&&s!==void 0&&t.start(s),e.end===void 0&&t.end&&v!==void 0&&t.end(v),a.css.add(ml),g=o.slice(s,v).map((C,d)=>({index:d+s,data:C})),`<div style="${"height: "+R(l,!0)+";"}" class="viewport svelte-1youqmj"${le("this",A,0)}><div class="contents svelte-1youqmj" style="${"padding-top: "+R(c,!0)+"px; padding-bottom: "+R(m,!0)+"px;"}"${le("this",h,0)}>${Qt(g,C=>`<div class="row svelte-1youqmj">${i.default?i.default({item:C.data}):"Missing template"} </div>`)}</div> </div>`}),wo=5;function yl(a){return"similarity"in a?a.similarity*-1:a.ordinal??0}const _o=N((a,e,t,i)=>{let o,l=Go(i),n,s=Pe,v=()=>(s(),s=z(o,O=>n=O),o),A,h=Pe,g=()=>(h(),h=z(he,O=>A=O),he),c,m,C,d,f,b,B,k,T,I;k=z(Eo,O=>B=O);const p=ii();d=z(p,O=>C=O);let{title:M=void 0}=e,{name:E}=e,{multiple:S=!1}=e,{hideDuringPrint:y=!0}=e,{disableSelectAll:r=!1}=e,{defaultValue:u=[]}=e,{noDefault:_=!1}=e,{selectAllByDefault:x=!1}=e,{description:w=void 0}=e,{value:q="value",data:G,label:X=q,order:P=void 0,where:ce=void 0}=e;const{results:ke,update:qe}=fi({value:q,data:G,label:X,order:P,where:ce},`Dropdown-${E}`,B?.data?.data[`Dropdown-${E}_data`]);b=z(ke,O=>f=O);let F=!!G;const De=E in C&&"rawValues"in C[E]&&Array.isArray(C[E].rawValues)?C[E].rawValues:[],Ie=li({multiselect:S,defaultValues:Array.isArray(u)?u:[u],initialOptions:De,noDefault:_,selectAllByDefault:K(x)}),{addOptions:fe,removeOptions:ve,options:ue,selectedOptions:Ee,selectAll:ge,deselectAll:Q,toggleSelected:ee,pauseSorting:te,resumeSorting:Y,forceSort:Ae,destroy:$}=Ie;I=z(ue,O=>T=O),m=z(Ee,O=>c=O),ht($);const xe=O=>{JSON.stringify(O)!==JSON.stringify(C[E])&&ka(p,C[E]=O,C)};let we=[],_e=c.length>0;ht(Ee.subscribe(O=>{if(_e||=O.length>0,O&&_e){const j=O;S?xe({label:j.map(Z=>Z.label).join(", "),value:j.length?`(${j.map(Z=>ba(Z.value))})`:"(select null where 0)",rawValues:j}):j.length?j.length&&xe({label:j[0].label,value:ba(j[0].value,{serializeStrings:!1}),rawValues:j}):xe({label:"",value:null,rawValues:[]})}})),Bt(Lo,{registerOption:O=>(fe(O),()=>{ve(O)})});let re,ae="",me=0,he;const ne=ri(()=>{if(me++,ae&&F){const O=me,j=o.search(ae,"label");j.hash!==he?.hash&&di(()=>{O===me&&(g(he=j),Ae())},j.fetch())}else g(he=o??G)});let V=[];q||(G?V.push('Missing required prop: "value".'):l.default||V.push('Dropdown requires either "value" and "data" props or <DropdownOption />.')),G&&typeof G!="object"&&(typeof G=="string"?V.push(`'${G}' is not a recognized query result. Data should be provided in the format: data = {'${G.replace("data.","")}'}`):V.push(`'${G}' is not a recognized query result. Data should be an object. e.g data = {QueryName}`));try{Bi({name:E})}catch(O){V.push(O.message)}let ye=!1;e.title===void 0&&t.title&&M!==void 0&&t.title(M),e.name===void 0&&t.name&&E!==void 0&&t.name(E),e.multiple===void 0&&t.multiple&&S!==void 0&&t.multiple(S),e.hideDuringPrint===void 0&&t.hideDuringPrint&&y!==void 0&&t.hideDuringPrint(y),e.disableSelectAll===void 0&&t.disableSelectAll&&r!==void 0&&t.disableSelectAll(r),e.defaultValue===void 0&&t.defaultValue&&u!==void 0&&t.defaultValue(u),e.noDefault===void 0&&t.noDefault&&_!==void 0&&t.noDefault(_),e.selectAllByDefault===void 0&&t.selectAllByDefault&&x!==void 0&&t.selectAllByDefault(x),e.description===void 0&&t.description&&w!==void 0&&t.description(w),e.value===void 0&&t.value&&q!==void 0&&t.value(q),e.data===void 0&&t.data&&G!==void 0&&t.data(G),e.label===void 0&&t.label&&X!==void 0&&t.label(X),e.order===void 0&&t.order&&P!==void 0&&t.order(P),e.where===void 0&&t.where&&ce!==void 0&&t.where(ce);let se,oe,Se=a.head;do se=!0,a.head=Se,S=K(S),y=K(y),r=K(r),_=K(_),x=K(x),qe({value:q,data:G,label:X,order:P,where:ce}),v({hasQuery:F,query:o}=f),o&&o.fetch(),ne(),re?te():Y(),A?.dataLoaded&&(we=A),n?.error&&F&&!ye&&(V=[...V,n.error],ye=!0),oe=`${i.default?i.default({}):""} ${Qt(we,O=>`${D(mt,"DropdownOption").$$render(a,{value:O[q]??O.value,valueLabel:O[X]??O.label,idx:yl(O),__auto:!0},{},{})}`)} ${D(Yi,"HiddenInPrint").$$render(a,{enabled:y},{},{default:()=>`<div class="mt-2 mb-4 ml-0 mr-2 inline-block">${V.length>0?`${D(Ii,"InlineError").$$render(a,{inputType:"Dropdown",error:V,height:"32",width:"140"},{},{})} `:`${D(vl,"Popover.Root").$$render(a,{open:re},{open:O=>{re=O,se=!1}},{default:()=>`${D(Al,"Popover.Trigger").$$render(a,{asChild:!0},{},{default:({builder:O})=>`${D(Di,"Button").$$render(a,{builders:[O],variant:"outline",role:"combobox",size:"sm",class:"min-w-5 h-8 border border-base-300","aria-label":M??He(E)},{},{default:()=>`${M&&!S?`${R(M)} ${w?`${D(no,"Info").$$render(a,{description:w,className:"pl-1"},{},{})}`:""} ${c.length>0?`${D(xo,"Separator").$$render(a,{orientation:"vertical",class:"mx-2 h-4"},{},{})} ${R(c[0].label)}`:""}`:`${c.length>0&&!S?`${R(c[0].label)}`:`${R(M??He(E))} ${w?`${D(no,"Info").$$render(a,{description:w,className:"pl-1"},{},{})}`:""}`}`} ${D(Jt,"Icon").$$render(a,{src:xi,class:"ml-2 h-4 w-4"},{},{})} ${c.length>0&&S?`${D(xo,"Separator").$$render(a,{orientation:"vertical",class:"mx-2 h-4"},{},{})} ${D(ga,"Badge").$$render(a,{variant:"default",class:"rounded-xs px-1 font-normal sm:hidden"},{},{default:()=>`${R(c.length)}`})} <div class="hidden space-x-1 sm:flex">${c.length>3?`${D(ga,"Badge").$$render(a,{variant:"default",class:"rounded-xs px-1 font-normal"},{},{default:()=>`${R(c.length)} Selected`})}`:`${Qt(c,j=>`${D(ga,"Badge").$$render(a,{variant:"default",class:"rounded-xs px-1 font-normal"},{},{default:()=>`${R(j.label)} `})}`)}`}</div>`:""}`})}`})} ${D(fl,"Popover.Content").$$render(a,{class:"w-[200px] p-0",align:"start",side:"bottom"},{},{default:()=>`${D(nl,"Command.Root").$$render(a,{shouldFilter:!1},{},{default:()=>`${D(cl,"Command.Input").$$render(a,{placeholder:M,value:ae},{value:O=>{ae=O,se=!1}},{})} ${D(ul,"Command.List").$$render(a,{},{},{default:()=>`${D(sl,"Command.Empty").$$render(a,{},{},{default:()=>"No results found."})} ${D(dl,"Command.Group").$$render(a,{},{},{default:()=>`${T.length<=wo?`${Qt(T,(O,j)=>`${D(go,"DropdownOptionDisplay").$$render(a,{id:j,value:O.value,valueLabel:O.label,handleSelect:({value:Z,label:Me})=>{ee({value:Z,label:Me}),S||(re=!1)},multiple:S,active:c.some(Z=>Z.value===O.value&&Z.label===O.label)},{},{})}`)}`:`${D(hl,"VirtualList").$$render(a,{height:`${wo*32}px`,items:T},{},{default:({item:O})=>`${D(go,"DropdownOptionDisplay").$$render(a,{value:O?.value,valueLabel:O?.label,handleSelect:({value:j,label:Z})=>{ee({value:j,label:Z}),S||(re=!1)},multiple:S,active:c.some(j=>j.value===O.value&&j.label===O.label)},{},{})}`})}`}`})} ${S?`${r?"":`<div class="-mx-1 h-px bg-base-300"></div> ${D(pa,"Command.Item").$$render(a,{class:"justify-center text-center",onSelect:ge},{},{default:()=>"Select all"})}`} <div class="-mx-1 h-px bg-base-300"></div> ${D(pa,"Command.Item").$$render(a,{disabled:c.length===0,class:"justify-center text-center",onSelect:Q},{},{default:()=>"Clear selection"})}`:""}`})}`})}`})}`})}`}</div>`})}`;while(!se);return s(),h(),m(),d(),b(),k(),I(),oe}),Cl={code:".marker.svelte-v9l93j{border-left:5px solid transparent;border-right:5px solid transparent;border-top:9px solid var(--base-content-muted);transform:rotate(-90deg);transition:transform 0.2s ease}.rotate-marker.svelte-v9l93j{transform:rotate(0deg)}button.svelte-v9l93j{display:flex;align-items:center;cursor:pointer}",map:`{"version":3,"file":"Details.svelte","sources":["Details.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport { slide } from 'svelte/transition';\\n\\timport { toBoolean } from '../../utils.js';\\n\\n\\texport let title = 'Details';\\n\\texport let open = false;\\n\\t$: open = toBoolean(open);\\n\\n\\texport let printShowAll = true;\\n\\t$: printShowAll = toBoolean(printShowAll);\\n\\tlet printing = false;\\n<\/script>\\n\\n<svelte:window\\n\\ton:beforeprint={() => (printing = true)}\\n\\ton:afterprint={() => (printing = false)}\\n\\ton:export-beforeprint={() => (printing = true)}\\n\\ton:export-afterprint={() => (printing = false)}\\n/>\\n\\n{#if !printing || !printShowAll}\\n\\t<div class=\\"mb-4 mt-2\\">\\n\\t\\t<button\\n\\t\\t\\tclass=\\"text-sm text-base-content-muted cursor-pointer inline-flex gap-2\\"\\n\\t\\t\\ton:click={() => (open = !open)}\\n\\t\\t>\\n\\t\\t\\t<span class={open ? 'marker rotate-marker' : 'marker'} />\\n\\t\\t\\t<span> {title} </span>\\n\\t\\t</button>\\n\\n\\t\\t{#if open}\\n\\t\\t\\t<div class=\\"pl-[calc(0.5rem+10px)] pt-3 mb-6 text-sm\\" transition:slide|local>\\n\\t\\t\\t\\t<slot />\\n\\t\\t\\t</div>\\n\\t\\t{/if}\\n\\t</div>\\n{:else}\\n\\t<div class=\\"mb-4 mt-2 text-base-content-muted\\">\\n\\t\\t<span class=\\"text-sm font-semibold inline-flex\\"> {title} </span>\\n\\t\\t<div class=\\"pt-1 mb-6 text-sm\\">\\n\\t\\t\\t<slot />\\n\\t\\t</div>\\n\\t</div>\\n{/if}\\n\\n<style>\\n\\t.marker {\\n\\t\\tborder-left: 5px solid transparent;\\n\\t\\tborder-right: 5px solid transparent;\\n\\t\\tborder-top: 9px solid var(--base-content-muted);\\n\\t\\ttransform: rotate(-90deg);\\n\\t\\ttransition: transform 0.2s ease;\\n\\t}\\n\\n\\t.rotate-marker {\\n\\t\\ttransform: rotate(0deg);\\n\\t}\\n\\n\\tbutton {\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tcursor: pointer;\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AAkDC,qBAAQ,CACP,WAAW,CAAE,GAAG,CAAC,KAAK,CAAC,WAAW,CAClC,YAAY,CAAE,GAAG,CAAC,KAAK,CAAC,WAAW,CACnC,UAAU,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CAC/C,SAAS,CAAE,OAAO,MAAM,CAAC,CACzB,UAAU,CAAE,SAAS,CAAC,IAAI,CAAC,IAC5B,CAEA,4BAAe,CACd,SAAS,CAAE,OAAO,IAAI,CACvB,CAEA,oBAAO,CACN,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,OACT"}`},gl=N((a,e,t,i)=>{let{title:o="Details"}=e,{open:l=!1}=e,{printShowAll:n=!0}=e;return e.title===void 0&&t.title&&o!==void 0&&t.title(o),e.open===void 0&&t.open&&l!==void 0&&t.open(l),e.printShowAll===void 0&&t.printShowAll&&n!==void 0&&t.printShowAll(n),a.css.add(Cl),l=K(l),n=K(n),` ${`<div class="mb-4 mt-2"><button class="text-sm text-base-content-muted cursor-pointer inline-flex gap-2 svelte-v9l93j"><span class="${R(jo(l?"marker rotate-marker":"marker"),!0)+" svelte-v9l93j"}"></span> <span>${R(o)}</span></button> ${l?`<div class="pl-[calc(0.5rem+10px)] pt-3 mb-6 text-sm">${i.default?i.default({}):""}</div>`:""}</div>`}`}),xl=N((a,e,t,i)=>{let o,l,n,s,v,A=Pe,h=()=>(A(),A=z(o,y=>y),o);const{resolveColorsObject:g}=Pt();let{config:c=void 0}=e,{height:m="291px"}=e,{width:C="100%"}=e,{copying:d=!1}=e,{printing:f=!1}=e,{echartsOptions:b=void 0}=e,{seriesOptions:B=void 0}=e,{seriesColors:k=void 0}=e,{isMap:T=!1}=e,{extraHeight:I=void 0}=e,p=!1,M,E;const S=Lt("gridConfig");return S&&(p=!0,{cols:M,gapWidth:E}=S),e.config===void 0&&t.config&&c!==void 0&&t.config(c),e.height===void 0&&t.height&&m!==void 0&&t.height(m),e.width===void 0&&t.width&&C!==void 0&&t.width(C),e.copying===void 0&&t.copying&&d!==void 0&&t.copying(d),e.printing===void 0&&t.printing&&f!==void 0&&t.printing(f),e.echartsOptions===void 0&&t.echartsOptions&&b!==void 0&&t.echartsOptions(b),e.seriesOptions===void 0&&t.seriesOptions&&B!==void 0&&t.seriesOptions(B),e.seriesColors===void 0&&t.seriesColors&&k!==void 0&&t.seriesColors(k),e.isMap===void 0&&t.isMap&&T!==void 0&&t.isMap(T),e.extraHeight===void 0&&t.extraHeight&&I!==void 0&&t.extraHeight(I),h(o=g(k)),l=Math.min(Number(M),2),n=(650-Number(E)*(l-1))/l,s=Math.min(Number(M),3),v=(841-Number(E)*(s-1))/s,A(),`${d?`<div class="chart" style="${"height: "+R(m,!0)+"; width: "+R(C,!0)+"; margin-left: 0; margin-top: 15px; margin-bottom: 10px; overflow: visible; break-inside: avoid;"}"></div>`:`${f?`${p?`<div class="chart md:hidden" style="${"height: "+R(m,!0)+"; width: "+R(n,!0)+"px; margin-left: 0; margin-top: 15px; margin-bottom: 10px; overflow: visible; break-inside: avoid;"}"></div> <div class="chart hidden md:block" style="${"height: "+R(m,!0)+"; width: "+R(v,!0)+"px; margin-left: 0; margin-top: 15px; margin-bottom: 10px; overflow: visible; break-inside: avoid;"}"></div>`:`<div class="chart md:hidden" style="${"height: "+R(m,!0)+"; width: 650px; margin-left: 0; margin-top: 15px; margin-bottom: 10px; overflow: visible; break-inside: avoid;"}"></div> <div class="chart hidden md:block" style="${"height: "+R(m,!0)+"; width: 841px; margin-left: 0; margin-top: 15px; margin-bottom: 10px; overflow: visible; break-inside: avoid;"}"></div>`}`:""}`}`}),wl=N((a,e,t,i)=>{let{height:o="231px"}=e;return e.height===void 0&&t.height&&o!==void 0&&t.height(o),`<div role="status" class="animate-pulse"><span class="sr-only" data-svelte-h="svelte-1wtojot">Loading...</span> <div class="bg-base-100 rounded-md max-w-[100%]" style="${"height:"+R(o,!0)+"; margin-top: 15px; margin-bottom: 31px;"}"></div></div>`}),_l={code:"@media print{.chart.svelte-db4qxn{-moz-column-break-inside:avoid;break-inside:avoid}.chart-container.svelte-db4qxn{padding:0}}.chart.svelte-db4qxn{-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;-o-user-select:none;user-select:none}.chart-footer.svelte-db4qxn{display:flex;justify-content:flex-end;align-items:center;margin:3px 12px;font-size:12px;height:9px}",map:`{"version":3,"file":"ECharts.svelte","sources":["ECharts.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport { browser } from '$app/environment';\\n\\timport echarts from '@evidence-dev/component-utilities/echarts';\\n\\timport echartsCanvasDownload from '@evidence-dev/component-utilities/echartsCanvasDownload';\\n\\timport EChartsCopyTarget from './EChartsCopyTarget.svelte';\\n\\timport DownloadData from '../../ui/DownloadData.svelte';\\n\\timport CodeBlock from '../../ui/CodeBlock.svelte';\\n\\timport ChartLoading from '../../ui/ChartLoading.svelte';\\n\\timport { flush } from 'svelte/internal';\\n\\timport { createEventDispatcher } from 'svelte';\\n\\timport { getThemeStores } from '../../../themes/themes.js';\\n\\n\\tconst { activeAppearance, theme, resolveColorsObject } = getThemeStores();\\n\\n\\texport let config = undefined;\\n\\n\\texport let queryID = undefined;\\n\\texport let evidenceChartTitle = undefined;\\n\\n\\texport let height = '291px';\\n\\texport let width = '100%';\\n\\n\\texport let data;\\n\\n\\texport let renderer = undefined;\\n\\texport let downloadableData = undefined;\\n\\texport let downloadableImage = undefined;\\n\\texport let echartsOptions = undefined;\\n\\texport let seriesOptions = undefined;\\n\\texport let printEchartsConfig; // helper for custom chart development\\n\\n\\texport let seriesColors = undefined;\\n\\t$: seriesColorsStore = resolveColorsObject(seriesColors);\\n\\n\\texport let connectGroup = undefined;\\n\\n\\texport let xAxisLabelOverflow = undefined;\\n\\n\\tconst dispatch = createEventDispatcher();\\n\\n\\tlet downloadChart = false;\\n\\tlet copying = false;\\n\\tlet printing = false;\\n\\tlet hovering = false;\\n<\/script>\\n\\n<svelte:window\\n\\ton:copy={() => {\\n\\t\\tcopying = true;\\n\\t\\tflush();\\n\\t\\tsetTimeout(() => {\\n\\t\\t\\tcopying = false;\\n\\t\\t}, 0);\\n\\t}}\\n\\ton:beforeprint={() => (printing = true)}\\n\\ton:afterprint={() => (printing = false)}\\n\\ton:export-beforeprint={() => (printing = true)}\\n\\ton:export-afterprint={() => (printing = false)}\\n/>\\n\\n<div\\n\\trole=\\"none\\"\\n\\tclass=\\"chart-container mt-2 mb-3\\"\\n\\ton:mouseenter={() => (hovering = true)}\\n\\ton:mouseleave={() => (hovering = false)}\\n>\\n\\t{#if !printing}\\n\\t\\t{#if !browser}\\n\\t\\t\\t<ChartLoading {height} />\\n\\t\\t{:else}\\n\\t\\t\\t<div\\n\\t\\t\\t\\tclass=\\"chart\\"\\n\\t\\t\\t\\tstyle=\\"\\n\\t\\t\\t\\theight: {height};\\n\\t\\t\\t\\twidth: {width};\\n\\t\\t\\t\\toverflow: visible;\\n\\t\\t\\t\\tdisplay: {copying ? 'none' : 'inherit'}\\n\\t\\t\\t\\"\\n\\t\\t\\t\\tuse:echarts={{\\n\\t\\t\\t\\t\\tconfig,\\n\\t\\t\\t\\t\\t...$$restProps,\\n\\t\\t\\t\\t\\techartsOptions,\\n\\t\\t\\t\\t\\tseriesOptions,\\n\\t\\t\\t\\t\\tdispatch,\\n\\t\\t\\t\\t\\trenderer,\\n\\t\\t\\t\\t\\tconnectGroup,\\n\\t\\t\\t\\t\\txAxisLabelOverflow,\\n\\t\\t\\t\\t\\tseriesColors: $seriesColorsStore,\\n\\t\\t\\t\\t\\ttheme: $activeAppearance\\n\\t\\t\\t\\t}}\\n\\t\\t\\t/>\\n\\t\\t{/if}\\n\\t{/if}\\n\\n\\t<EChartsCopyTarget\\n\\t\\t{config}\\n\\t\\t{height}\\n\\t\\t{width}\\n\\t\\t{copying}\\n\\t\\t{printing}\\n\\t\\t{echartsOptions}\\n\\t\\t{seriesOptions}\\n\\t\\tseriesColors={seriesColorsStore}\\n\\t/>\\n\\n\\t{#if downloadableData || downloadableImage}\\n\\t\\t<div class=\\"chart-footer\\">\\n\\t\\t\\t{#if downloadableImage}\\n\\t\\t\\t\\t<DownloadData\\n\\t\\t\\t\\t\\ttext=\\"Save Image\\"\\n\\t\\t\\t\\t\\tclass=\\"download-button\\"\\n\\t\\t\\t\\t\\tdownloadData={() => {\\n\\t\\t\\t\\t\\t\\tdownloadChart = true;\\n\\t\\t\\t\\t\\t\\tsetTimeout(() => {\\n\\t\\t\\t\\t\\t\\t\\tdownloadChart = false;\\n\\t\\t\\t\\t\\t\\t}, 0);\\n\\t\\t\\t\\t\\t}}\\n\\t\\t\\t\\t\\tdisplay={hovering}\\n\\t\\t\\t\\t\\t{queryID}\\n\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t<svg\\n\\t\\t\\t\\t\\t\\txmlns=\\"http://www.w3.org/2000/svg\\"\\n\\t\\t\\t\\t\\t\\twidth=\\"12\\"\\n\\t\\t\\t\\t\\t\\theight=\\"12\\"\\n\\t\\t\\t\\t\\t\\tviewBox=\\"0 0 24 24\\"\\n\\t\\t\\t\\t\\t\\tfill=\\"none\\"\\n\\t\\t\\t\\t\\t\\tstroke=\\"#000\\"\\n\\t\\t\\t\\t\\t\\tstroke-width=\\"2\\"\\n\\t\\t\\t\\t\\t\\tstroke-linecap=\\"round\\"\\n\\t\\t\\t\\t\\t\\tstroke-linejoin=\\"round\\"\\n\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t<rect x=\\"3\\" y=\\"3\\" width=\\"18\\" height=\\"18\\" rx=\\"2\\" />\\n\\t\\t\\t\\t\\t\\t<circle cx=\\"8.5\\" cy=\\"8.5\\" r=\\"1.5\\" />\\n\\t\\t\\t\\t\\t\\t<path d=\\"M20.4 14.5L16 10 4 20\\" />\\n\\t\\t\\t\\t\\t</svg>\\n\\t\\t\\t\\t</DownloadData>\\n\\t\\t\\t{/if}\\n\\t\\t\\t{#if data && downloadableData}\\n\\t\\t\\t\\t<DownloadData\\n\\t\\t\\t\\t\\ttext=\\"Download Data\\"\\n\\t\\t\\t\\t\\t{data}\\n\\t\\t\\t\\t\\t{queryID}\\n\\t\\t\\t\\t\\tclass=\\"download-button\\"\\n\\t\\t\\t\\t\\tdisplay={hovering}\\n\\t\\t\\t\\t/>\\n\\t\\t\\t{/if}\\n\\t\\t</div>\\n\\t{/if}\\n\\n\\t{#if printEchartsConfig && !printing}\\n\\t\\t<CodeBlock source={JSON.stringify(config, undefined, 3)} copyToClipboard={true}>\\n\\t\\t\\t{JSON.stringify(config, undefined, 3)}\\n\\t\\t</CodeBlock>\\n\\t{/if}\\n</div>\\n\\n{#if downloadChart}\\n\\t<div\\n\\t\\tclass=\\"chart\\"\\n\\t\\tstyle=\\"\\n        display: none;\\n        visibility: visible;\\n        height: {height};\\n        width: 666px;\\n        margin-left: 0;\\n        margin-top: 15px;\\n        margin-bottom: 15px;\\n        overflow: visible;\\n    \\"\\n\\t\\tuse:echartsCanvasDownload={{\\n\\t\\t\\tconfig,\\n\\t\\t\\t...$$restProps,\\n\\t\\t\\techartsOptions,\\n\\t\\t\\tseriesOptions,\\n\\t\\t\\tseriesColors: $seriesColorsStore,\\n\\t\\t\\tqueryID,\\n\\t\\t\\tevidenceChartTitle,\\n\\t\\t\\ttheme: $activeAppearance,\\n\\t\\t\\tbackgroundColor: $theme.colors['base-100']\\n\\t\\t}}\\n\\t/>\\n{/if}\\n\\n<style>\\n\\t@media print {\\n\\t\\t.chart {\\n\\t\\t\\t-moz-column-break-inside: avoid;\\n\\t\\t\\tbreak-inside: avoid;\\n\\t\\t}\\n\\n\\t\\t.chart-container {\\n\\t\\t\\tpadding: 0;\\n\\t\\t}\\n\\t}\\n\\n\\t.chart {\\n\\t\\t-moz-user-select: none;\\n\\t\\t-webkit-user-select: none;\\n\\t\\t-ms-user-select: none;\\n\\t\\t-o-user-select: none;\\n\\t\\tuser-select: none;\\n\\t}\\n\\n\\t.chart-footer {\\n\\t\\tdisplay: flex;\\n\\t\\tjustify-content: flex-end;\\n\\t\\talign-items: center;\\n\\t\\tmargin: 3px 12px;\\n\\t\\tfont-size: 12px;\\n\\t\\theight: 9px;\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AA4LC,OAAO,KAAM,CACZ,oBAAO,CACN,wBAAwB,CAAE,KAAK,CAC/B,YAAY,CAAE,KACf,CAEA,8BAAiB,CAChB,OAAO,CAAE,CACV,CACD,CAEA,oBAAO,CACN,gBAAgB,CAAE,IAAI,CACtB,mBAAmB,CAAE,IAAI,CACzB,eAAe,CAAE,IAAI,CACrB,cAAc,CAAE,IAAI,CACpB,WAAW,CAAE,IACd,CAEA,2BAAc,CACb,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,QAAQ,CACzB,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,GAAG,CAAC,IAAI,CAChB,SAAS,CAAE,IAAI,CACf,MAAM,CAAE,GACT"}`},bl=N((a,e,t,i)=>{let o;de(e,["config","queryID","evidenceChartTitle","height","width","data","renderer","downloadableData","downloadableImage","echartsOptions","seriesOptions","printEchartsConfig","seriesColors","connectGroup","xAxisLabelOverflow"]);let l=Pe,n=()=>(l(),l=z(o,w=>w),o),s,v;const{activeAppearance:A,theme:h,resolveColorsObject:g}=Pt();s=z(A,w=>w),v=z(h,w=>w);let{config:c=void 0}=e,{queryID:m=void 0}=e,{evidenceChartTitle:C=void 0}=e,{height:d="291px"}=e,{width:f="100%"}=e,{data:b}=e,{renderer:B=void 0}=e,{downloadableData:k=void 0}=e,{downloadableImage:T=void 0}=e,{echartsOptions:I=void 0}=e,{seriesOptions:p=void 0}=e,{printEchartsConfig:M}=e,{seriesColors:E=void 0}=e,{connectGroup:S=void 0}=e,{xAxisLabelOverflow:y=void 0}=e;zo();let r=!1,u=!1,_=!1,x=!1;return e.config===void 0&&t.config&&c!==void 0&&t.config(c),e.queryID===void 0&&t.queryID&&m!==void 0&&t.queryID(m),e.evidenceChartTitle===void 0&&t.evidenceChartTitle&&C!==void 0&&t.evidenceChartTitle(C),e.height===void 0&&t.height&&d!==void 0&&t.height(d),e.width===void 0&&t.width&&f!==void 0&&t.width(f),e.data===void 0&&t.data&&b!==void 0&&t.data(b),e.renderer===void 0&&t.renderer&&B!==void 0&&t.renderer(B),e.downloadableData===void 0&&t.downloadableData&&k!==void 0&&t.downloadableData(k),e.downloadableImage===void 0&&t.downloadableImage&&T!==void 0&&t.downloadableImage(T),e.echartsOptions===void 0&&t.echartsOptions&&I!==void 0&&t.echartsOptions(I),e.seriesOptions===void 0&&t.seriesOptions&&p!==void 0&&t.seriesOptions(p),e.printEchartsConfig===void 0&&t.printEchartsConfig&&M!==void 0&&t.printEchartsConfig(M),e.seriesColors===void 0&&t.seriesColors&&E!==void 0&&t.seriesColors(E),e.connectGroup===void 0&&t.connectGroup&&S!==void 0&&t.connectGroup(S),e.xAxisLabelOverflow===void 0&&t.xAxisLabelOverflow&&y!==void 0&&t.xAxisLabelOverflow(y),a.css.add(_l),n(o=g(E)),l(),s(),v(),` <div role="none" class="chart-container mt-2 mb-3 svelte-db4qxn">${`${`${D(wl,"ChartLoading").$$render(a,{height:d},{},{})}`}`} ${D(xl,"EChartsCopyTarget").$$render(a,{config:c,height:d,width:f,copying:u,printing:_,echartsOptions:I,seriesOptions:p,seriesColors:o},{},{})} ${k||T?`<div class="chart-footer svelte-db4qxn">${T?`${D(io,"DownloadData").$$render(a,{text:"Save Image",class:"download-button",downloadData:()=>{r=!0,setTimeout(()=>{r=!1},0)},display:x,queryID:m},{},{default:()=>'<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="#000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><path d="M20.4 14.5L16 10 4 20"></path></svg>'})}`:""} ${b&&k?`${D(io,"DownloadData").$$render(a,{text:"Download Data",data:b,queryID:m,class:"download-button",display:x},{},{})}`:""}</div>`:""} ${M?`${D(vi,"CodeBlock").$$render(a,{source:JSON.stringify(c,void 0,3),copyToClipboard:!0},{},{default:()=>`${R(JSON.stringify(c,void 0,3))}`})}`:""}</div> ${r?`<div class="chart svelte-db4qxn" style="${"display: none; visibility: visible; height: "+R(d,!0)+"; width: 666px; margin-left: 0; margin-top: 15px; margin-bottom: 15px; overflow: visible;"}"></div>`:""}`});function Mt(a,e){const t=new Set(a.map(i=>i[e]));return Array.from(t)}function pl(a,e){return rt(a,Li({count:Mi(e)}))[0].count}function kl(a,e,t){let i;if(typeof t!="object")i=rt(a,wa(e,vo({xTotal:_a(t)})),Ca({percentOfX:Ao(t,"xTotal")}),mo({percentOfX:t+"_pct"}));else{i=rt(a,Ca({valueSum:0}));for(let o=0;o<i.length;o++){i[o].valueSum=0;for(let l=0;l<t.length;l++)i[o].valueSum=i[o].valueSum+i[o][t[l]]}i=rt(i,wa(e,vo({xTotal:_a("valueSum")})));for(let o=0;o<t.length;o++)i=rt(i,Ca({percentOfX:Ao(t[o],"xTotal")}),mo({percentOfX:t[o]+"_pct"}))}return i}function It(a,e,t){return[...a].sort((i,o)=>(i[e]<o[e]?-1:1)*(t?1:-1))}function Fo(a,e,t){const i=e+t;return a%i<e?0:1}const Sl=N((a,e,t,i)=>{let o,l,n,s,v,A=Pe,h=()=>(A(),A=z(n,pe=>v=pe),n),g,c,m,C=Pe,d=()=>(C(),C=z(l,pe=>m=pe),l),f,b=Pe,B=()=>(b(),b=z(o,pe=>f=pe),o),k,T,I=Yt({}),p=Yt({});T=z(p,pe=>k=pe);const{theme:M,resolveColor:E,resolveColorsObject:S,resolveColorPalette:y}=Pt();c=z(M,pe=>g=pe);let{data:r=void 0}=e,{queryID:u=void 0}=e,{x:_=void 0}=e,{y:x=void 0}=e,{y2:w=void 0}=e,{series:q=void 0}=e,{size:G=void 0}=e,{tooltipTitle:X=void 0}=e,{showAllXAxisLabels:P=void 0}=e,{printEchartsConfig:ce=!1}=e,ke=!!x,qe=!!_,{swapXY:F=!1}=e,{title:De=void 0}=e,{subtitle:Ie=void 0}=e,{chartType:fe="Chart"}=e,{bubble:ve=!1}=e,{hist:ue=!1}=e,{boxplot:Ee=!1}=e,ge,{xType:Q=void 0}=e,{xAxisTitle:ee="false"}=e,{xBaseline:te=!0}=e,{xTickMarks:Y=!1}=e,{xGridlines:Ae=!1}=e,{xAxisLabels:$=!0}=e,{sort:xe=!0}=e,{xFmt:we=void 0}=e,{xMin:_e=void 0}=e,{xMax:re=void 0}=e,{yLog:ae=!1}=e,{yType:me=ae===!0?"log":"value"}=e,{yLogBase:he=10}=e,{yAxisTitle:ne="false"}=e,{yBaseline:V=!1}=e,{yTickMarks:ye=!1}=e,{yGridlines:se=!0}=e,{yAxisLabels:oe=!0}=e,{yMin:Se=void 0}=e,{yMax:O=void 0}=e,{yScale:j=!1}=e,{yFmt:Z=void 0}=e,{yAxisColor:Me="true"}=e,{y2AxisTitle:be="false"}=e,{y2Baseline:U=!1}=e,{y2TickMarks:ie=!1}=e,{y2Gridlines:Be=!0}=e,{y2AxisLabels:Fe=!0}=e,{y2Min:Ve=void 0}=e,{y2Max:Ze=void 0}=e,{y2Scale:je=!1}=e,{y2Fmt:ze=void 0}=e,{y2AxisColor:$e="true"}=e,{sizeFmt:We=void 0}=e,{colorPalette:et="default"}=e,{legend:Oe=void 0}=e,{echartsOptions:tt=void 0}=e,{seriesOptions:at=void 0}=e,{seriesColors:ot=void 0}=e,{stackType:it=void 0}=e,{stacked100:Ne=!1}=e,{chartAreaHeight:Ge}=e,{renderer:ea=void 0}=e,{downloadableData:yt=!0}=e,{downloadableImage:Ct=!0}=e,{connectGroup:ta=void 0}=e,{leftPadding:aa=void 0}=e,{rightPadding:oa=void 0}=e,{xLabelWrap:gt=!1}=e;const qo=gt?"break":"truncate";let W,ia,la=[],xt=[],Ea,qt,Ue,Oa,Ke,Te,Qe,ra,Ta,Gt,na,Da,sa,wt,Ia,Ba,jt,_t,La,Ma,Fa,Pa,qa,Ga,ja,za,Na,Ua,Ka,bt,zt,Nt,da,ca,Ha,Ra,pt,Va,Wa,ua,Qa,fa,st=[],kt=!0,Xe=[],Ut=[],Ye,dt,Xa,ct;e.data===void 0&&t.data&&r!==void 0&&t.data(r),e.queryID===void 0&&t.queryID&&u!==void 0&&t.queryID(u),e.x===void 0&&t.x&&_!==void 0&&t.x(_),e.y===void 0&&t.y&&x!==void 0&&t.y(x),e.y2===void 0&&t.y2&&w!==void 0&&t.y2(w),e.series===void 0&&t.series&&q!==void 0&&t.series(q),e.size===void 0&&t.size&&G!==void 0&&t.size(G),e.tooltipTitle===void 0&&t.tooltipTitle&&X!==void 0&&t.tooltipTitle(X),e.showAllXAxisLabels===void 0&&t.showAllXAxisLabels&&P!==void 0&&t.showAllXAxisLabels(P),e.printEchartsConfig===void 0&&t.printEchartsConfig&&ce!==void 0&&t.printEchartsConfig(ce),e.swapXY===void 0&&t.swapXY&&F!==void 0&&t.swapXY(F),e.title===void 0&&t.title&&De!==void 0&&t.title(De),e.subtitle===void 0&&t.subtitle&&Ie!==void 0&&t.subtitle(Ie),e.chartType===void 0&&t.chartType&&fe!==void 0&&t.chartType(fe),e.bubble===void 0&&t.bubble&&ve!==void 0&&t.bubble(ve),e.hist===void 0&&t.hist&&ue!==void 0&&t.hist(ue),e.boxplot===void 0&&t.boxplot&&Ee!==void 0&&t.boxplot(Ee),e.xType===void 0&&t.xType&&Q!==void 0&&t.xType(Q),e.xAxisTitle===void 0&&t.xAxisTitle&&ee!==void 0&&t.xAxisTitle(ee),e.xBaseline===void 0&&t.xBaseline&&te!==void 0&&t.xBaseline(te),e.xTickMarks===void 0&&t.xTickMarks&&Y!==void 0&&t.xTickMarks(Y),e.xGridlines===void 0&&t.xGridlines&&Ae!==void 0&&t.xGridlines(Ae),e.xAxisLabels===void 0&&t.xAxisLabels&&$!==void 0&&t.xAxisLabels($),e.sort===void 0&&t.sort&&xe!==void 0&&t.sort(xe),e.xFmt===void 0&&t.xFmt&&we!==void 0&&t.xFmt(we),e.xMin===void 0&&t.xMin&&_e!==void 0&&t.xMin(_e),e.xMax===void 0&&t.xMax&&re!==void 0&&t.xMax(re),e.yLog===void 0&&t.yLog&&ae!==void 0&&t.yLog(ae),e.yType===void 0&&t.yType&&me!==void 0&&t.yType(me),e.yLogBase===void 0&&t.yLogBase&&he!==void 0&&t.yLogBase(he),e.yAxisTitle===void 0&&t.yAxisTitle&&ne!==void 0&&t.yAxisTitle(ne),e.yBaseline===void 0&&t.yBaseline&&V!==void 0&&t.yBaseline(V),e.yTickMarks===void 0&&t.yTickMarks&&ye!==void 0&&t.yTickMarks(ye),e.yGridlines===void 0&&t.yGridlines&&se!==void 0&&t.yGridlines(se),e.yAxisLabels===void 0&&t.yAxisLabels&&oe!==void 0&&t.yAxisLabels(oe),e.yMin===void 0&&t.yMin&&Se!==void 0&&t.yMin(Se),e.yMax===void 0&&t.yMax&&O!==void 0&&t.yMax(O),e.yScale===void 0&&t.yScale&&j!==void 0&&t.yScale(j),e.yFmt===void 0&&t.yFmt&&Z!==void 0&&t.yFmt(Z),e.yAxisColor===void 0&&t.yAxisColor&&Me!==void 0&&t.yAxisColor(Me),e.y2AxisTitle===void 0&&t.y2AxisTitle&&be!==void 0&&t.y2AxisTitle(be),e.y2Baseline===void 0&&t.y2Baseline&&U!==void 0&&t.y2Baseline(U),e.y2TickMarks===void 0&&t.y2TickMarks&&ie!==void 0&&t.y2TickMarks(ie),e.y2Gridlines===void 0&&t.y2Gridlines&&Be!==void 0&&t.y2Gridlines(Be),e.y2AxisLabels===void 0&&t.y2AxisLabels&&Fe!==void 0&&t.y2AxisLabels(Fe),e.y2Min===void 0&&t.y2Min&&Ve!==void 0&&t.y2Min(Ve),e.y2Max===void 0&&t.y2Max&&Ze!==void 0&&t.y2Max(Ze),e.y2Scale===void 0&&t.y2Scale&&je!==void 0&&t.y2Scale(je),e.y2Fmt===void 0&&t.y2Fmt&&ze!==void 0&&t.y2Fmt(ze),e.y2AxisColor===void 0&&t.y2AxisColor&&$e!==void 0&&t.y2AxisColor($e),e.sizeFmt===void 0&&t.sizeFmt&&We!==void 0&&t.sizeFmt(We),e.colorPalette===void 0&&t.colorPalette&&et!==void 0&&t.colorPalette(et),e.legend===void 0&&t.legend&&Oe!==void 0&&t.legend(Oe),e.echartsOptions===void 0&&t.echartsOptions&&tt!==void 0&&t.echartsOptions(tt),e.seriesOptions===void 0&&t.seriesOptions&&at!==void 0&&t.seriesOptions(at),e.seriesColors===void 0&&t.seriesColors&&ot!==void 0&&t.seriesColors(ot),e.stackType===void 0&&t.stackType&&it!==void 0&&t.stackType(it),e.stacked100===void 0&&t.stacked100&&Ne!==void 0&&t.stacked100(Ne),e.chartAreaHeight===void 0&&t.chartAreaHeight&&Ge!==void 0&&t.chartAreaHeight(Ge),e.renderer===void 0&&t.renderer&&ea!==void 0&&t.renderer(ea),e.downloadableData===void 0&&t.downloadableData&&yt!==void 0&&t.downloadableData(yt),e.downloadableImage===void 0&&t.downloadableImage&&Ct!==void 0&&t.downloadableImage(Ct),e.connectGroup===void 0&&t.connectGroup&&ta!==void 0&&t.connectGroup(ta),e.leftPadding===void 0&&t.leftPadding&&aa!==void 0&&t.leftPadding(aa),e.rightPadding===void 0&&t.rightPadding&&oa!==void 0&&t.rightPadding(oa),e.xLabelWrap===void 0&&t.xLabelWrap&&gt!==void 0&&t.xLabelWrap(gt),Bt(ko,I),Bt(So,p),ce=K(ce),F=K(F),te=K(te),Y=K(Y),Ae=K(Ae),$=K($),xe=K(xe),ae=K(ae),V=K(V),ye=K(ye),se=K(se),oe=K(oe),j=K(j),B(o=E(Me)),U=K(U),ie=K(ie),Be=K(Be),Fe=K(Fe),je=K(je),d(l=E($e)),h(n=y(et)),s=S(ot),yt=K(yt),Ct=K(Ct),gt=K(gt);try{if(dt=void 0,st=[],xt=[],Xe=[],Ut=[],qt=[],ke=!!x,qe=!!_,lo(r),W=ya(r),ia=Object.keys(W),qe||(_=ia[0]),!ke){la=ia.filter(function(L){return![_,q,G].includes(L)});for(let L=0;L<la.length;L++)qt=la[L],Ea=W[qt].type,Ea==="number"&&xt.push(qt);x=xt.length>1?xt:xt[0]}ve?ge={x:_,y:x,size:G}:ue?ge={x:_}:Ee?ge={}:ge={x:_,y:x};for(let L in ge)ge[L]==null&&st.push(L);if(st.length===1)throw Error(new Intl.ListFormat().format(st)+" is required");if(st.length>1)throw Error(new Intl.ListFormat().format(st)+" are required");if(Ne===!0&&x.includes("_pct")&&kt===!1)if(typeof x=="object"){for(let L=0;L<x.length;L++)x[L]=x[L].replace("_pct","");kt=!1}else x=x.replace("_pct",""),kt=!1;if(_&&Xe.push(_),x)if(typeof x=="object")for(Ye=0;Ye<x.length;Ye++)Xe.push(x[Ye]);else Xe.push(x);if(w)if(typeof w=="object")for(Ye=0;Ye<w.length;Ye++)Xe.push(w[Ye]);else Xe.push(w);if(G&&Xe.push(G),q&&Ut.push(q),X&&Ut.push(X),lo(r,Xe,Ut),Ne===!0){if(r=kl(r,_,x),typeof x=="object"){for(let L=0;L<x.length;L++)x[L]=x[L]+"_pct";kt=!1}else x=x+"_pct",kt=!1;W=ya(r)}switch(Ue=W[_].type,Ue){case"number":Ue="value";break;case"string":Ue="category";break;case"date":Ue="time";break;default:break}if(Q=Q==="category"?"category":Ue,P?P=P==="true"||P===!0:P=Q==="category",F&&Q!=="category")throw Error("Horizontal charts do not support a value or time-based x-axis. You can either change your SQL query to output string values or set swapXY=false.");if(F&&w)throw Error("Horizontal charts do not support a secondary y-axis. You can either set swapXY=false or remove the y2 prop from your chart.");if(F&&(Q="category"),Oa=Ue==="value"&&Q==="category",r=xe?Ue==="category"?It(r,x,!1):It(r,_,!0):r,Ue==="time"&&(r=It(r,_,!0)),Xa=ya(r,"array"),ct=Xa.filter(L=>L.type==="date"),ct=ct.map(L=>L.id),ct.length>0)for(let L=0;L<ct.length;L++)r=Ai(r,ct[L]);we?Ke=Re(we,W[_].format?.valueType):Ke=W[_].format,x?Z?typeof x=="object"?Te=Re(Z,W[x[0]].format?.valueType):Te=Re(Z,W[x].format?.valueType):typeof x=="object"?Te=W[x[0]].format:Te=W[x].format:Te="str",w&&(ze?typeof w=="object"?Qe=Re(ze,W[w[0]].format?.valueType):Qe=Re(ze,W[w].format?.valueType):typeof w=="object"?Qe=W[w[0]].format:Qe=W[w].format),G&&(We?ra=Re(We,W[G].format?.valueType):ra=W[G].format),Ta=W[_].columnUnitSummary,x&&(typeof x=="object"?Gt=W[x[0]].columnUnitSummary:Gt=W[x].columnUnitSummary),w&&(typeof w=="object"?na=W[w[0]].columnUnitSummary:na=W[w].columnUnitSummary),ee=ee==="true"?He(_,Ke):ee==="false"?"":ee,ne=ne==="true"?typeof x=="object"?"":He(x,Te):ne==="false"?"":ne,be=be==="true"?typeof w=="object"?"":He(w,Qe):be==="false"?"":be;let pe=typeof x=="object"?x.length:1,Ya=q?pl(r,q):1,St=pe*Ya,va=typeof w=="object"?w.length:w?1:0,Aa=St+va;if(Oe!==void 0&&(Oe=Oe==="true"||Oe===!0),Oe=Oe??Aa>1,Ne===!0&&ae===!0)throw Error("Log axis cannot be used in a 100% stacked chart");if(it==="stacked"&&Aa>1&&ae===!0)throw Error("Log axis cannot be used in a stacked chart");let ut;if(typeof x=="object"){ut=W[x[0]].columnUnitSummary.min;for(let L=0;L<x.length;L++)W[x[L]].columnUnitSummary.min<ut&&(ut=W[x[L]].columnUnitSummary.min)}else x&&(ut=W[x].columnUnitSummary.min);if(ae===!0&&ut<=0&&ut!==null)throw Error("Log axis cannot display values less than or equal to zero");I.update(L=>({...L,data:r,x:_,y:x,y2:w,series:q,swapXY:F,sort:xe,xType:Q,xFormat:Ke,yFormat:Te,y2Format:Qe,sizeFormat:ra,xMismatch:Oa,size:G,yMin:Se,y2Min:Ve,columnSummary:W,xAxisTitle:ee,yAxisTitle:ne,y2AxisTitle:be,tooltipTitle:X,chartAreaHeight:Ge,chartType:fe,yCount:pe,y2Count:va})),Da=Mt(r,_);let Ja;if(F?sa={type:me,logBase:he,position:"top",axisLabel:{show:oe,hideOverlap:!0,showMaxLabel:!0,formatter(L){return Wt(L,Te,Gt)},margin:4},min:Se,max:O,scale:j,splitLine:{show:se},axisLine:{show:V,onZero:!1},axisTick:{show:ye},boundaryGap:!1,z:2}:sa={type:Q,min:_e,max:re,tooltip:{show:!0,position:"inside",formatter(L){if(L.isTruncated())return L.name}},splitLine:{show:Ae},axisLine:{show:te},axisTick:{show:Y},axisLabel:{show:$,hideOverlap:!0,showMaxLabel:Q==="category"||Q==="value",formatter:Q==="time"||Q==="category"?!1:function(L){return Wt(L,Ke,Ta)},margin:6},scale:!0,z:2},F?wt={type:Q,inverse:"true",splitLine:{show:Ae},axisLine:{show:te},axisTick:{show:Y},axisLabel:{show:$,hideOverlap:!0},scale:!0,min:_e,max:re,z:2}:(wt={type:me,logBase:he,splitLine:{show:se},axisLine:{show:V,onZero:!1},axisTick:{show:ye},axisLabel:{show:oe,hideOverlap:!0,margin:4,formatter(L){return Wt(L,Te,Gt)},color:w?f==="true"?v[0]:f!=="false"?f:void 0:void 0},name:ne,nameLocation:"end",nameTextStyle:{align:"left",verticalAlign:"top",padding:[0,5,0,0],color:w?f==="true"?v[0]:f!=="false"?f:void 0:void 0},nameGap:6,min:Se,max:O,scale:j,boundaryGap:["0%","1%"],z:2},Ja={type:"value",show:!1,alignTicks:!0,splitLine:{show:Be},axisLine:{show:U,onZero:!1},axisTick:{show:ie},axisLabel:{show:Fe,hideOverlap:!0,margin:4,formatter(L){return Wt(L,Qe,na)},color:m==="true"?v[St]:m!=="false"?m:void 0},name:be,nameLocation:"end",nameTextStyle:{align:"right",verticalAlign:"top",padding:[0,0,0,5],color:m==="true"?v[St]:m!=="false"?m:void 0},nameGap:6,min:Ve,max:Ze,scale:je,boundaryGap:["0%","1%"],z:2},wt=[wt,Ja]),Ge){if(Ge=Number(Ge),isNaN(Ge))throw Error("chartAreaHeight must be a number");if(Ge<=0)throw Error("chartAreaHeight must be a positive number")}else Ge=180;jt=!!De,_t=!!Ie,La=Oe*(q!==null||typeof x=="object"&&x.length>1),Ma=ne!==""&&F,Fa=ee!==""&&!F,Pa=15,qa=13,Ga=6*_t,ja=jt*Pa+_t*qa+Ga*Math.max(jt,_t),za=10,Na=10,Ua=14,Ka=14,bt=15,bt=bt*La,zt=7,zt=zt*Math.max(jt,_t),Nt=ja+zt,da=Nt+bt+Ka*Ma+za,ca=Fa*Ua+Na,Va=8,ua=1,F&&(Wa=Da.length,ua=Math.max(1,Wa/Va)),Ha=Ge*ua+da+ca,Ra=Nt+bt+7,Qa=Ha+"px",fa="100%",pt=F?ne:ee,pt!==""&&(pt=pt+" →"),Ia={id:"horiz-axis-title",type:"text",style:{text:pt,textAlign:"right",fill:g.colors["base-content-muted"]},cursor:"auto",right:F?"2%":"3%",top:F?Ra:null,bottom:F?null:"2%"},Ba={title:{text:De,subtext:Ie,subtextStyle:{width:fa}},tooltip:{trigger:"axis",show:!0,formatter(L){let ft,vt,At,Kt;if(Aa>1){vt=L[0].value[F?1:0],ft=`<span id="tooltip" style='font-weight: 600;'>${Je(vt,Ke)}</span>`;for(let lt=L.length-1;lt>=0;lt--)L[lt].seriesName!=="stackTotal"&&(At=L[lt].value[F?0:1],ft=ft+`<br> <span style='font-size: 11px;'>${L[lt].marker} ${L[lt].seriesName}<span/><span style='float:right; margin-left: 10px; font-size: 12px;'>${Je(At,Fo(L[lt].componentIndex,pe,va)===0?Te:Qe)}</span>`)}else Q==="value"?(vt=L[0].value[F?1:0],At=L[0].value[F?0:1],Kt=L[0].seriesName,ft=`<span id="tooltip" style='font-weight: 600;'>${He(_,Ke)}: </span><span style='float:right; margin-left: 10px;'>${Je(vt,Ke)}</span><br/><span style='font-weight: 600;'>${He(Kt,Te)}: </span><span style='float:right; margin-left: 10px;'>${Je(At,Te)}</span>`):(vt=L[0].value[F?1:0],At=L[0].value[F?0:1],Kt=L[0].seriesName,ft=`<span id="tooltip" style='font-weight: 600;'>${Je(vt,Ke)}</span><br/><span>${He(Kt,Te)}: </span><span style='float:right; margin-left: 10px;'>${Je(At,Te)}</span>`);return ft},confine:!0,axisPointer:{type:"shadow"},extraCssText:'box-shadow: 0 3px 6px rgba(0,0,0,.15); box-shadow: 0 2px 4px rgba(0,0,0,.12); z-index: 1; font-feature-settings: "cv02", "tnum";',order:"valueDesc"},legend:{show:Oe,type:"scroll",top:Nt,padding:[0,0,0,0],data:[]},grid:{left:aa??(F?"1%":"0.8%"),right:oa??(F?"4%":"3%"),bottom:ca,top:da,containLabel:!0},xAxis:sa,yAxis:wt,series:[],animation:!0,graphic:Ia,color:v},p.update(()=>Ba)}catch(pe){if(dt=pe.message,console.error("\x1B[31m%s\x1B[0m",`Error in ${fe}: ${pe.message}`),ni)throw dt;I.update(St=>({...St,error:dt}))}return A(),c(),C(),b(),T(),`${dt?`${D(Io,"ErrorChart").$$render(a,{error:dt,title:fe},{},{})}`:`${i.default?i.default({}):""} ${D(bl,"ECharts").$$render(a,{config:k,height:Qa,width:fa,data:r,queryID:u,evidenceChartTitle:De,showAllXAxisLabels:P,swapXY:F,echartsOptions:tt,seriesOptions:at,printEchartsConfig:ce,renderer:ea,downloadableData:yt,downloadableImage:Ct,connectGroup:ta,xAxisLabelOverflow:qo,seriesColors:s},{},{})}`}`}),{Object:El}=qi,Ol=N((a,e,t,i)=>{let o,{data:l}=e;const n=Dt.isQuery(l)?l.hash:void 0;let s=l?.hash===n,{emptySet:v=void 0}=e,{emptyMessage:A=void 0}=e,{height:h=200}=e,g=l?.id;return e.data===void 0&&t.data&&l!==void 0&&t.data(l),e.emptySet===void 0&&t.emptySet&&v!==void 0&&t.emptySet(v),e.emptyMessage===void 0&&t.emptyMessage&&A!==void 0&&t.emptyMessage(A),e.height===void 0&&t.height&&h!==void 0&&t.height(h),s=l?.hash===n,o={...Object.fromEntries(Object.entries(e).filter(([,c])=>c!==void 0))},` ${D(Pi,"QueryLoad").$$render(a,{data:l,height:h},{},{error:({loaded:c})=>`${D(Io,"ErrorChart").$$render(a,{slot:"error",title:o.chartType,error:c.error.message},{},{})}`,empty:()=>`${D(Gi,"EmptyChart").$$render(a,{slot:"empty",emptyMessage:A,emptySet:v,chartType:o.chartType,isInitial:s},{},{})}`,default:({loaded:c})=>`${D(Sl,"Chart").$$render(a,El.assign({},o,{data:Dt.isQuery(c)?Array.from(c):c},{queryID:g}),{},{default:()=>`${i.default?i.default({}):""}`})}`})}`});function Tl(a,e,t,i,o,l,n,s,v,A,h=void 0,g=void 0,c=void 0,m=void 0){function C(y,r,u,_){let x={name:r,data:y,yAxisIndex:u};return x={..._,...x},x}let d,f,b,B=[],k,T,I,p,M;function E(y,r){const u=[];function _(w){return typeof w>"u"}function x(w,q){_(w)||(Array.isArray(w)?w.forEach(G=>u.push([G,q])):u.push([w,q]))}return x(y,0),x(r,1),u}let S=E(t,c);if(i!=null&&S.length===1)for(p=Mt(a,i),d=0;d<p.length;d++){if(T=a.filter(y=>y[i]===p[d]),o?k=T.map(y=>[y[S[0][0]],s?y[e].toString():y[e]]):k=T.map(y=>[s?y[e].toString():y[e],y[S[0][0]]]),h){let y=T.map(r=>r[h]);k.forEach((r,u)=>r.push(y[u]))}if(g){let y=T.map(r=>r[g]);k.forEach((r,u)=>r.push(y[u]))}I=p[d]??"null",M=S[0][1],b=C(k,I,M,l),B.push(b)}if(i!=null&&S.length>1)for(p=Mt(a,i),d=0;d<p.length;d++)for(T=a.filter(y=>y[i]===p[d]),f=0;f<S.length;f++){if(o?k=T.map(y=>[y[S[f][0]],s?y[e].toString():y[e]]):k=T.map(y=>[s?y[e].toString():y[e],y[S[f][0]]]),h){let y=T.map(r=>r[h]);k.forEach((r,u)=>r.push(y[u]))}if(g){let y=T.map(r=>r[g]);k.forEach((r,u)=>r.push(y[u]))}I=(p[d]??"null")+" - "+v[S[f][0]].title,M=S[f][1],b=C(k,I,M,l),B.push(b)}if(i==null&&S.length>1)for(d=0;d<S.length;d++){if(o?k=a.map(y=>[y[S[d][0]],s?y[e].toString():y[e]]):k=a.map(y=>[s?y[e].toString():y[e],y[S[d][0]]]),h){let y=a.map(r=>r[h]);k.forEach((r,u)=>r.push(y[u]))}if(g){let y=a.map(r=>r[g]);k.forEach((r,u)=>r.push(y[u]))}I=v[S[d][0]].title,M=S[d][1],b=C(k,I,M,l),B.push(b)}if(i==null&&S.length===1){if(o?k=a.map(y=>[y[S[0][0]],s?y[e].toString():y[e]]):k=a.map(y=>[s?y[e].toString():y[e],y[S[0][0]]]),h){let y=a.map(r=>r[h]);k.forEach((r,u)=>r.push(y[u]))}if(g){let y=a.map(r=>r[g]);k.forEach((r,u)=>r.push(y[u]))}I=v[S[0][0]].title,M=S[0][1],b=C(k,I,M,l),B.push(b)}return A&&B.sort((y,r)=>A.indexOf(y.name)-A.indexOf(r.name)),m&&B.forEach(y=>{y.name=mi(y.name,m)}),B}function Dl(a){let e=[];for(let t=1;t<a.length;t++)e.push(a[t]-a[t-1]);return e}function Po(a,e){return(typeof a!="number"||isNaN(a))&&(a=0),(typeof e!="number"||isNaN(e))&&(e=0),a=Math.abs(a),e=Math.abs(e),e<=.01?a:Po(e,a%e)}function Il(a,e){if(!Array.isArray(a))throw new TypeError("Cannot calculate extent of non-array value.");let t,i;for(const o of a)typeof o=="number"&&(t===void 0?o>=o&&(t=i=o):(t>o&&(t=o),i<o&&(i=o)));return[t,i]}function Bl(a,e){let[t,i]=Il(a);const o=[];let l=t;for(;l<=i;)o.push(Math.round((l+Number.EPSILON)*1e8)/1e8),l+=e;return o}function Ll(a){if(a.length<=1)return;a.sort(function(t,i){return t-i}),a=a.map(function(t){return t*1e8}),a=Dl(a);let e=a.reduce((t,i)=>Po(t,i))/1e8;return e=Math.round((e+Number.EPSILON)*1e8)/1e8,e}function xa(a,e,t,i,o=!1,l=!1){let n=!1;const s=a.map(m=>Object.assign({},m,{[e]:m[e]instanceof Date?(n=!0,m[e].toISOString()):m[e]})).filter(m=>m[e]!==void 0&&m[e]!==null),v=Array.from(s).reduce((m,C)=>(C[e]instanceof Date&&(C[e]=C[e].toISOString(),n=!0),i?(m[C[i]??"null"]||(m[C[i]??"null"]=[]),m[C[i]??"null"].push(C)):(m.default||(m.default=[]),m.default.push(C)),m),{}),A={};let h;const g=s.find(m=>m&&m[e]!==null&&m[e]!==void 0)?.[e]??null;switch(typeof g){case"object":throw g===null?new Error(`Column '${e}' is entirely null. Column must contain at least one non-null value.`):new Error("Unexpected object property, expected string, date, or number");case"number":if(h=Mt(s,e),l){const m=Ll(h);A[e]=Bl(h,m)}break;case"string":h=Mt(s,e),A[e]=h;break}const c=[];for(const m of Object.values(v)){const C=i?{[i]:null}:{};if(o)if(t instanceof Array)for(let f=0;f<t.length;f++)C[t[f]]=0;else C[t]=0;else if(t instanceof Array)for(let f=0;f<t.length;f++)C[t[f]]=null;else C[t]=null;i&&(A[i]=i);const d=[];Object.keys(A).length===0?d.push(ho([e],C)):d.push(ho(A,C)),c.push(rt(m,...d))}return n?c.flat().map(m=>({...m,[e]:new Date(m[e])})):c.flat()}function bo(a,e,t){let i=rt(a,wa(e,[Fi(t,_a)]));if(typeof t=="object")for(let o=0;o<i.length;o++){i[o].stackTotal=0;for(let l=0;l<t.length;l++)i[o].stackTotal=i[o].stackTotal+i[o][t[l]]}return i}let Ml=60;const Fl=N((a,e,t,i)=>{let o,l,n,s,v,A,h,g,c,m,C,d,f,b,B,k,T,I,p,M=Pe,E=()=>(M(),M=z(s,U=>p=U),s),S,y=Pe,r=()=>(y(),y=z(n,U=>S=U),n),u,_=Pe,x=()=>(_(),_=z(v,U=>u=U),v),w,q=Pe,G=()=>(q(),q=z(o,U=>w=U),o);const{resolveColor:X}=Pt();let{y:P=void 0}=e;const ce=!!P;let{y2:ke=void 0}=e;const qe=!!ke;let{series:F=void 0}=e;const De=!!F;let{options:Ie=void 0}=e,{name:fe=void 0}=e,{type:ve="stacked"}=e,{stackName:ue=void 0}=e,{fillColor:Ee=void 0}=e,{fillOpacity:ge=void 0}=e,{outlineColor:Q=void 0}=e,{outlineWidth:ee=void 0}=e,{labels:te=!1}=e,{seriesLabels:Y=!0}=e,{labelSize:Ae=11}=e,{labelPosition:$=void 0}=e,{labelColor:xe=void 0}=e,{labelFmt:we=void 0}=e,_e;we&&(_e=Re(we));let{yLabelFmt:re=void 0}=e,ae;re&&(ae=Re(re));let{y2LabelFmt:me=void 0}=e,he;me&&(he=Re(me));let{y2SeriesType:ne="bar"}=e,{stackTotalLabel:V=!0}=e,{showAllLabels:ye=!1}=e,{seriesOrder:se=void 0}=e,oe,Se,O,j;const Z={outside:"top",inside:"inside"},Me={outside:"right",inside:"inside"};let{seriesLabelFmt:be=void 0}=e;return e.y===void 0&&t.y&&P!==void 0&&t.y(P),e.y2===void 0&&t.y2&&ke!==void 0&&t.y2(ke),e.series===void 0&&t.series&&F!==void 0&&t.series(F),e.options===void 0&&t.options&&Ie!==void 0&&t.options(Ie),e.name===void 0&&t.name&&fe!==void 0&&t.name(fe),e.type===void 0&&t.type&&ve!==void 0&&t.type(ve),e.stackName===void 0&&t.stackName&&ue!==void 0&&t.stackName(ue),e.fillColor===void 0&&t.fillColor&&Ee!==void 0&&t.fillColor(Ee),e.fillOpacity===void 0&&t.fillOpacity&&ge!==void 0&&t.fillOpacity(ge),e.outlineColor===void 0&&t.outlineColor&&Q!==void 0&&t.outlineColor(Q),e.outlineWidth===void 0&&t.outlineWidth&&ee!==void 0&&t.outlineWidth(ee),e.labels===void 0&&t.labels&&te!==void 0&&t.labels(te),e.seriesLabels===void 0&&t.seriesLabels&&Y!==void 0&&t.seriesLabels(Y),e.labelSize===void 0&&t.labelSize&&Ae!==void 0&&t.labelSize(Ae),e.labelPosition===void 0&&t.labelPosition&&$!==void 0&&t.labelPosition($),e.labelColor===void 0&&t.labelColor&&xe!==void 0&&t.labelColor(xe),e.labelFmt===void 0&&t.labelFmt&&we!==void 0&&t.labelFmt(we),e.yLabelFmt===void 0&&t.yLabelFmt&&re!==void 0&&t.yLabelFmt(re),e.y2LabelFmt===void 0&&t.y2LabelFmt&&me!==void 0&&t.y2LabelFmt(me),e.y2SeriesType===void 0&&t.y2SeriesType&&ne!==void 0&&t.y2SeriesType(ne),e.stackTotalLabel===void 0&&t.stackTotalLabel&&V!==void 0&&t.stackTotalLabel(V),e.showAllLabels===void 0&&t.showAllLabels&&ye!==void 0&&t.showAllLabels(ye),e.seriesOrder===void 0&&t.seriesOrder&&se!==void 0&&t.seriesOrder(se),e.seriesLabelFmt===void 0&&t.seriesLabelFmt&&be!==void 0&&t.seriesLabelFmt(be),G(o=Lt(ko)),l=Lt(So),r(n=X(Ee)),E(s=X(Q)),te=te==="true"||te===!0,Y=Y==="true"||Y===!0,x(v=X(xe)),V=V==="true"||V===!0,A=w.data,h=w.x,P=ce?P:w.y,ke=qe?ke:w.y2,g=w.yFormat,c=w.y2Format,m=w.yCount,C=w.y2Count,d=w.swapXY,f=w.xType,b=w.xMismatch,B=w.columnSummary,k=w.sort,F=De?F:w.series,!F&&typeof P!="object"?(fe=fe??He(P,B[P].title),d&&f!=="category"&&(A=xa(A,h,P,F,!0,f!=="time"),f="category"),ue="stack1",O=d?"right":"top"):(k===!0&&f==="category"&&(oe=bo(A,h,P),typeof P=="object"?oe=It(oe,"stackTotal",!1):oe=It(oe,P,!1),Se=oe.map(U=>U[h]),A=[...A].sort(function(U,ie){return Se.indexOf(U[h])-Se.indexOf(ie[h])})),d||(f==="value"||f==="category")&&ve.includes("stacked")?(A=xa(A,h,P,F,!0,f==="value"),f="category"):f==="time"&&ve.includes("stacked")&&(A=xa(A,h,P,F,!0,!0)),ve.includes("stacked")?(ue=ue??"stack1",O="inside"):(ue=void 0,O=d?"right":"top")),ve==="stacked"&&(j=bo(A,h,P)),$=(d?Me[$]:Z[$])??O,T={type:"bar",stack:ue,label:{show:te&&Y,formatter(U){return U.value[d?0:1]===0?"":Je(U.value[d?0:1],[ae??_e??g,he??_e??c][Fo(U.componentIndex,m,C)])},position:$,fontSize:Ae,color:u},labelLayout:{hideOverlap:!ye},emphasis:{focus:"series"},barMaxWidth:Ml,itemStyle:{color:S,opacity:ge,borderColor:p,borderWidth:ee}},I=Tl(A,h,P,F,d,T,fe,b,B,se,void 0,void 0,ke,be),l.update(U=>(U.series.push(...I),U.legend.data.push(...I.map(ie=>ie.name.toString())),te===!0&&ve==="stacked"&&typeof P=="object"|F!==void 0&&V===!0&&F!==h&&(U.series.push({type:"bar",stack:ue,name:"stackTotal",color:"none",data:j.map(ie=>[d?0:b?ie[h].toString():ie[h],d?b?ie[h].toString():ie[h]:0]),label:{show:!0,position:d?"right":"top",formatter(ie){let Be=0;return I.forEach(Fe=>{Be+=Fe.data[ie.dataIndex][d?0:1]}),Be===0?"":Je(Be,_e??g)},fontWeight:"bold",fontSize:Ae,padding:d?[0,0,0,5]:void 0}}),U.legend.selectedMode=!1),U)),M(),y(),_(),q(),""}),Pl=N((a,e,t,i)=>{let o,l,n,s,v,A,h;const{resolveColor:g,resolveColorsObject:c,resolveColorPalette:m}=Pt();let{data:C=void 0}=e,{x:d=void 0}=e,{y:f=void 0}=e,{y2:b=void 0}=e,{series:B=void 0}=e,{xType:k=void 0}=e,{yLog:T=void 0}=e,{yLogBase:I=void 0}=e,{y2SeriesType:p=void 0}=e,{yFmt:M=void 0}=e,{y2Fmt:E=void 0}=e,{xFmt:S=void 0}=e,{title:y=void 0}=e,{subtitle:r=void 0}=e,{legend:u=void 0}=e,{xAxisTitle:_=void 0}=e,{yAxisTitle:x=b?"true":void 0}=e,{y2AxisTitle:w=b?"true":void 0}=e,{xGridlines:q=void 0}=e,{yGridlines:G=void 0}=e,{y2Gridlines:X=void 0}=e,{xAxisLabels:P=void 0}=e,{yAxisLabels:ce=void 0}=e,{y2AxisLabels:ke=void 0}=e,{xBaseline:qe=void 0}=e,{yBaseline:F=void 0}=e,{y2Baseline:De=void 0}=e,{xTickMarks:Ie=void 0}=e,{yTickMarks:fe=void 0}=e,{y2TickMarks:ve=void 0}=e,{yMin:ue=void 0}=e,{yMax:Ee=void 0}=e,{yScale:ge=void 0}=e,{y2Min:Q=void 0}=e,{y2Max:ee=void 0}=e,{y2Scale:te=void 0}=e,{swapXY:Y=!1}=e,{showAllXAxisLabels:Ae}=e,{type:$="stacked"}=e,xe=$==="stacked100",{fillColor:we=void 0}=e,{fillOpacity:_e=void 0}=e,{outlineColor:re=void 0}=e,{outlineWidth:ae=void 0}=e,{chartAreaHeight:me=void 0}=e,{sort:he=void 0}=e,{colorPalette:ne="default"}=e,{labels:V=void 0}=e,{labelSize:ye=void 0}=e,{labelPosition:se=void 0}=e,{labelColor:oe=void 0}=e,{labelFmt:Se=void 0}=e,{yLabelFmt:O=void 0}=e,{y2LabelFmt:j=void 0}=e,{stackTotalLabel:Z=void 0}=e,{seriesLabels:Me=void 0}=e,{showAllLabels:be=void 0}=e,{yAxisColor:U=void 0}=e,{y2AxisColor:ie=void 0}=e,{echartsOptions:Be=void 0}=e,{seriesOptions:Fe=void 0}=e,{printEchartsConfig:Ve=!1}=e,{emptySet:Ze=void 0}=e,{emptyMessage:je=void 0}=e,{renderer:ze=void 0}=e,{downloadableData:$e=void 0}=e,{downloadableImage:We=void 0}=e,{seriesColors:et=void 0}=e,{seriesOrder:Oe=void 0}=e,{connectGroup:tt=void 0}=e,{seriesLabelFmt:at=void 0}=e,{leftPadding:ot=void 0}=e,{rightPadding:it=void 0}=e,{xLabelWrap:Ne=void 0}=e;return e.data===void 0&&t.data&&C!==void 0&&t.data(C),e.x===void 0&&t.x&&d!==void 0&&t.x(d),e.y===void 0&&t.y&&f!==void 0&&t.y(f),e.y2===void 0&&t.y2&&b!==void 0&&t.y2(b),e.series===void 0&&t.series&&B!==void 0&&t.series(B),e.xType===void 0&&t.xType&&k!==void 0&&t.xType(k),e.yLog===void 0&&t.yLog&&T!==void 0&&t.yLog(T),e.yLogBase===void 0&&t.yLogBase&&I!==void 0&&t.yLogBase(I),e.y2SeriesType===void 0&&t.y2SeriesType&&p!==void 0&&t.y2SeriesType(p),e.yFmt===void 0&&t.yFmt&&M!==void 0&&t.yFmt(M),e.y2Fmt===void 0&&t.y2Fmt&&E!==void 0&&t.y2Fmt(E),e.xFmt===void 0&&t.xFmt&&S!==void 0&&t.xFmt(S),e.title===void 0&&t.title&&y!==void 0&&t.title(y),e.subtitle===void 0&&t.subtitle&&r!==void 0&&t.subtitle(r),e.legend===void 0&&t.legend&&u!==void 0&&t.legend(u),e.xAxisTitle===void 0&&t.xAxisTitle&&_!==void 0&&t.xAxisTitle(_),e.yAxisTitle===void 0&&t.yAxisTitle&&x!==void 0&&t.yAxisTitle(x),e.y2AxisTitle===void 0&&t.y2AxisTitle&&w!==void 0&&t.y2AxisTitle(w),e.xGridlines===void 0&&t.xGridlines&&q!==void 0&&t.xGridlines(q),e.yGridlines===void 0&&t.yGridlines&&G!==void 0&&t.yGridlines(G),e.y2Gridlines===void 0&&t.y2Gridlines&&X!==void 0&&t.y2Gridlines(X),e.xAxisLabels===void 0&&t.xAxisLabels&&P!==void 0&&t.xAxisLabels(P),e.yAxisLabels===void 0&&t.yAxisLabels&&ce!==void 0&&t.yAxisLabels(ce),e.y2AxisLabels===void 0&&t.y2AxisLabels&&ke!==void 0&&t.y2AxisLabels(ke),e.xBaseline===void 0&&t.xBaseline&&qe!==void 0&&t.xBaseline(qe),e.yBaseline===void 0&&t.yBaseline&&F!==void 0&&t.yBaseline(F),e.y2Baseline===void 0&&t.y2Baseline&&De!==void 0&&t.y2Baseline(De),e.xTickMarks===void 0&&t.xTickMarks&&Ie!==void 0&&t.xTickMarks(Ie),e.yTickMarks===void 0&&t.yTickMarks&&fe!==void 0&&t.yTickMarks(fe),e.y2TickMarks===void 0&&t.y2TickMarks&&ve!==void 0&&t.y2TickMarks(ve),e.yMin===void 0&&t.yMin&&ue!==void 0&&t.yMin(ue),e.yMax===void 0&&t.yMax&&Ee!==void 0&&t.yMax(Ee),e.yScale===void 0&&t.yScale&&ge!==void 0&&t.yScale(ge),e.y2Min===void 0&&t.y2Min&&Q!==void 0&&t.y2Min(Q),e.y2Max===void 0&&t.y2Max&&ee!==void 0&&t.y2Max(ee),e.y2Scale===void 0&&t.y2Scale&&te!==void 0&&t.y2Scale(te),e.swapXY===void 0&&t.swapXY&&Y!==void 0&&t.swapXY(Y),e.showAllXAxisLabels===void 0&&t.showAllXAxisLabels&&Ae!==void 0&&t.showAllXAxisLabels(Ae),e.type===void 0&&t.type&&$!==void 0&&t.type($),e.fillColor===void 0&&t.fillColor&&we!==void 0&&t.fillColor(we),e.fillOpacity===void 0&&t.fillOpacity&&_e!==void 0&&t.fillOpacity(_e),e.outlineColor===void 0&&t.outlineColor&&re!==void 0&&t.outlineColor(re),e.outlineWidth===void 0&&t.outlineWidth&&ae!==void 0&&t.outlineWidth(ae),e.chartAreaHeight===void 0&&t.chartAreaHeight&&me!==void 0&&t.chartAreaHeight(me),e.sort===void 0&&t.sort&&he!==void 0&&t.sort(he),e.colorPalette===void 0&&t.colorPalette&&ne!==void 0&&t.colorPalette(ne),e.labels===void 0&&t.labels&&V!==void 0&&t.labels(V),e.labelSize===void 0&&t.labelSize&&ye!==void 0&&t.labelSize(ye),e.labelPosition===void 0&&t.labelPosition&&se!==void 0&&t.labelPosition(se),e.labelColor===void 0&&t.labelColor&&oe!==void 0&&t.labelColor(oe),e.labelFmt===void 0&&t.labelFmt&&Se!==void 0&&t.labelFmt(Se),e.yLabelFmt===void 0&&t.yLabelFmt&&O!==void 0&&t.yLabelFmt(O),e.y2LabelFmt===void 0&&t.y2LabelFmt&&j!==void 0&&t.y2LabelFmt(j),e.stackTotalLabel===void 0&&t.stackTotalLabel&&Z!==void 0&&t.stackTotalLabel(Z),e.seriesLabels===void 0&&t.seriesLabels&&Me!==void 0&&t.seriesLabels(Me),e.showAllLabels===void 0&&t.showAllLabels&&be!==void 0&&t.showAllLabels(be),e.yAxisColor===void 0&&t.yAxisColor&&U!==void 0&&t.yAxisColor(U),e.y2AxisColor===void 0&&t.y2AxisColor&&ie!==void 0&&t.y2AxisColor(ie),e.echartsOptions===void 0&&t.echartsOptions&&Be!==void 0&&t.echartsOptions(Be),e.seriesOptions===void 0&&t.seriesOptions&&Fe!==void 0&&t.seriesOptions(Fe),e.printEchartsConfig===void 0&&t.printEchartsConfig&&Ve!==void 0&&t.printEchartsConfig(Ve),e.emptySet===void 0&&t.emptySet&&Ze!==void 0&&t.emptySet(Ze),e.emptyMessage===void 0&&t.emptyMessage&&je!==void 0&&t.emptyMessage(je),e.renderer===void 0&&t.renderer&&ze!==void 0&&t.renderer(ze),e.downloadableData===void 0&&t.downloadableData&&$e!==void 0&&t.downloadableData($e),e.downloadableImage===void 0&&t.downloadableImage&&We!==void 0&&t.downloadableImage(We),e.seriesColors===void 0&&t.seriesColors&&et!==void 0&&t.seriesColors(et),e.seriesOrder===void 0&&t.seriesOrder&&Oe!==void 0&&t.seriesOrder(Oe),e.connectGroup===void 0&&t.connectGroup&&tt!==void 0&&t.connectGroup(tt),e.seriesLabelFmt===void 0&&t.seriesLabelFmt&&at!==void 0&&t.seriesLabelFmt(at),e.leftPadding===void 0&&t.leftPadding&&ot!==void 0&&t.leftPadding(ot),e.rightPadding===void 0&&t.rightPadding&&it!==void 0&&t.rightPadding(it),e.xLabelWrap===void 0&&t.xLabelWrap&&Ne!==void 0&&t.xLabelWrap(Ne),Y==="true"||Y===!0?Y=!0:Y=!1,o=g(we),l=g(re),n=m(ne),s=g(oe),v=g(U),A=g(ie),h=c(et),`${D(Ol,"Chart").$$render(a,{data:C,x:d,y:f,y2:b,xFmt:S,yFmt:M,y2Fmt:E,series:B,xType:k,yLog:T,yLogBase:I,legend:u,xAxisTitle:_,yAxisTitle:x,y2AxisTitle:w,xGridlines:q,yGridlines:G,y2Gridlines:X,xAxisLabels:P,yAxisLabels:ce,y2AxisLabels:ke,xBaseline:qe,yBaseline:F,y2Baseline:De,xTickMarks:Ie,yTickMarks:fe,y2TickMarks:ve,yAxisColor:v,y2AxisColor:A,yMin:ue,yMax:Ee,yScale:ge,y2Min:Q,y2Max:ee,y2Scale:te,swapXY:Y,title:y,subtitle:r,chartType:"Bar Chart",stackType:$,sort:he,stacked100:xe,chartAreaHeight:me,showAllXAxisLabels:Ae,colorPalette:n,echartsOptions:Be,seriesOptions:Fe,printEchartsConfig:Ve,emptySet:Ze,emptyMessage:je,renderer:ze,downloadableData:$e,downloadableImage:We,connectGroup:tt,xLabelWrap:Ne,seriesColors:h,leftPadding:ot,rightPadding:it},{},{default:()=>`${D(Fl,"Bar").$$render(a,{type:$,fillColor:o,fillOpacity:_e,outlineColor:l,outlineWidth:ae,labels:V,labelSize:ye,labelPosition:se,labelColor:s,labelFmt:Se,yLabelFmt:O,y2LabelFmt:j,stackTotalLabel:Z,seriesLabels:Me,showAllLabels:be,y2SeriesType:p,seriesOrder:Oe,seriesLabelFmt:at},{},{})} ${i.default?i.default({}):""}`})}`}),ql={code:".domo-banner.svelte-styow2.svelte-styow2{margin:20px 0;padding:20px;border:2px solid #00d4aa;border-radius:12px;background:linear-gradient(135deg, #f0fdf9 0%, #ecfdf5 100%);color:#065f46;text-align:center}.domo-banner.svelte-styow2 h3.svelte-styow2{margin:0 0 10px 0;color:#047857}.dev-banner.svelte-styow2.svelte-styow2{margin:20px 0;padding:20px;border:2px solid #f59e0b;border-radius:12px;background:linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);color:#92400e;text-align:center}.dev-banner.svelte-styow2 h3.svelte-styow2{margin:0 0 10px 0;color:#d97706}.quick-start-grid.svelte-styow2.svelte-styow2{display:grid;grid-template-columns:repeat(auto-fit, minmax(300px, 1fr));gap:20px;margin:30px 0}.quick-start-card.svelte-styow2.svelte-styow2{padding:25px;border:1px solid #e5e7eb;border-radius:12px;background-color:white;box-shadow:0 4px 6px rgba(0, 0, 0, 0.05);transition:transform 0.2s, box-shadow 0.2s}.quick-start-card.svelte-styow2.svelte-styow2:hover{transform:translateY(-2px);box-shadow:0 8px 15px rgba(0, 0, 0, 0.1)}.quick-start-card.svelte-styow2 h4.svelte-styow2{margin:0 0 15px 0;color:#1f2937;font-size:18px}.quick-start-card.svelte-styow2 p.svelte-styow2{margin:0 0 20px 0;color:#6b7280;line-height:1.5}.btn-primary.svelte-styow2.svelte-styow2,.btn-secondary.svelte-styow2.svelte-styow2{display:inline-block;padding:12px 24px;border:none;border-radius:8px;text-decoration:none;font-weight:600;font-size:14px;cursor:pointer;transition:all 0.2s}.btn-primary.svelte-styow2.svelte-styow2{background-color:#2563eb;color:white}.btn-primary.svelte-styow2.svelte-styow2:hover{background-color:#1d4ed8;text-decoration:none;color:white}.btn-secondary.svelte-styow2.svelte-styow2{background-color:#f3f4f6;color:#374151;border:1px solid #d1d5db}.btn-secondary.svelte-styow2.svelte-styow2:hover{background-color:#e5e7eb;text-decoration:none;color:#374151}.next-steps.svelte-styow2.svelte-styow2{display:grid;grid-template-columns:repeat(auto-fit, minmax(250px, 1fr));gap:20px;margin:30px 0}.step.svelte-styow2.svelte-styow2{padding:20px;border:1px solid #e5e7eb;border-radius:8px;background-color:#fafafa}.step.svelte-styow2 h4.svelte-styow2{margin:0 0 10px 0;color:#1f2937;font-size:16px}.step.svelte-styow2 p.svelte-styow2{margin:0;color:#6b7280;line-height:1.5}.step.svelte-styow2 a.svelte-styow2{color:#2563eb;text-decoration:none}.step.svelte-styow2 a.svelte-styow2:hover{text-decoration:underline}",map:`{"version":3,"file":"+page.md","sources":["+page.md"],"sourcesContent":["\\n<!-- \\n    MDSvex comes in handy here because it takes frontmatter and shoves it into the metadata object.\\n    This means that all we need to do is build out the expected page metadata\\n-->\\n<!-- Show title as h1 if defined, and not hidden -->\\n{#if typeof metadata !== \\"undefined\\" && (metadata.title || metadata.og?.title) && metadata.hide_title !== true}\\n<h1 class=\\"title\\">{metadata.title ?? metadata.og?.title}</h1>\\n{/if}\\n<svelte:head>\\n<!-- Title has a default case; so we need to handle it in a special way -->\\n{#if typeof metadata !== \\"undefined\\" && (metadata.title || metadata.og?.title)}\\n<title>{metadata.title ?? metadata.og?.title}</title>\\n<meta property=\\"og:title\\" content={metadata.og?.title ?? metadata.title} />\\n<meta name=\\"twitter:title\\" content={metadata.og?.title ?? metadata.title} />\\n{:else}\\n<!-- EITHER there is no metadata, or there is no specified style -->\\n<title>Evidence</title>\\n{/if}\\n\\n<!-- default twitter cardtags -->\\n<meta name=\\"twitter:card\\" content=\\"summary_large_image\\" />\\n<meta name=\\"twitter:site\\" content=\\"@evidence_dev\\" />\\n\\n{#if typeof metadata === \\"object\\"}\\n{#if metadata.description || metadata.og?.description}\\n  <meta\\n    name=\\"description\\"\\n    content={metadata.description ?? metadata.og?.description}\\n  />\\n  <meta\\n    property=\\"og:description\\"\\n    content={metadata.og?.description ?? metadata.description}\\n  />\\n  <meta\\n    name=\\"twitter:description\\"\\n    content={metadata.og?.description ?? metadata.description}\\n  />\\n{/if}\\n{#if metadata.og?.image}\\n  <meta property=\\"og:image\\" content={addBasePath(metadata.og?.image)} />\\n  <meta name=\\"twitter:image\\" content={addBasePath(metadata.og?.image)} />\\n{/if}\\n{/if}\\n</svelte:head>\\n<script context=\\"module\\">\\n\\tconst metadata = {\\"title\\":\\"Evidence Dashboard for Domo\\"};\\n\\tconst { title } = metadata; <\/script>\\n<script>\\nimport { Dropdown } from '@evidence-dev/core-components'\\nimport { DropdownOption } from '@evidence-dev/core-components'\\nimport { Details } from '@evidence-dev/core-components'\\nimport { QueryViewer } from '@evidence-dev/core-components'\\nimport { BarChart } from '@evidence-dev/core-components'\\nimport { addBasePath } from \\"@evidence-dev/sdk/utils/svelte\\";\\n\\t\\timport { pageHasQueries, routeHash, toasts } from '@evidence-dev/component-utilities/stores';\\nimport { fmt } from '@evidence-dev/component-utilities/formatting';\\nimport { CUSTOM_FORMATTING_SETTINGS_CONTEXT_KEY } from '@evidence-dev/component-utilities/globalContexts';\\nimport { ensureInputContext } from '@evidence-dev/sdk/utils/svelte';\\nimport { profile } from '@evidence-dev/component-utilities/profile';\\nimport { Query, hasUnsetValues } from '@evidence-dev/sdk/usql';\\nimport { setQueryFunction } from '@evidence-dev/component-utilities/buildQuery';\\n\\t\\t\\n        import { page } from '$app/stores';\\n        import { setContext, getContext, beforeUpdate, onDestroy, onMount } from 'svelte';\\n\\t\\timport { writable, get } from 'svelte/store';\\n        \\n        // Functions\\n\\n        \\n        let props;\\n        export { props as data }; // little hack to make the data name not overlap\\n        let { data = {}, customFormattingSettings, __db, inputs } = props;\\n        $: ({ data = {}, customFormattingSettings, __db } = props);\\n\\n        $routeHash = '6666cd76f96956469e7be39d750cc7d9';\\n\\n\\t\\t\\n\\t\\tlet inputs_store = ensureInputContext(writable(inputs));\\n\\t\\tonDestroy(inputs_store.subscribe((value) => inputs = value));\\n\\n        $: pageHasQueries.set(Object.keys(data).length > 0);\\n\\n        setContext(CUSTOM_FORMATTING_SETTINGS_CONTEXT_KEY, {\\n            getCustomFormats: () => {\\n                return customFormattingSettings.customFormats || [];\\n            }\\n        });\\n\\n\\t\\timport { browser, dev } from \\"$app/environment\\";\\n\\n\\t\\tif (!browser) {\\n\\t\\t\\tonDestroy(() => Query.emptyCache());\\n\\t\\t}\\n\\n\\t\\tconst queryFunc = (query, query_name) => profile(__db.query, query, { query_name });\\n\\t\\tsetQueryFunction(queryFunc);\\n\\n\\t\\tconst scoreNotifier = !dev? () => {} : (info) => {\\n\\t\\t\\ttoasts.add({\\n\\t\\t\\t\\tid: Math.random(),\\n\\t\\t\\t\\ttitle: info.id,\\n\\t\\t\\t\\tmessage: \`Results estimated to use \${\\n\\t\\t\\t\\t\\tIntl.NumberFormat().format(info.score / (1024 * 1024))\\n\\t\\t\\t\\t}mb of memory, performance may be impacted\`,\\n\\t\\t\\t\\tstatus: 'warning'\\n\\t\\t\\t}, 5000);\\n\\t\\t};\\n\\n\\t\\tif (import.meta?.hot) {\\n            if (typeof import.meta.hot.data.hmrHasRun === 'undefined') import.meta.hot.data.hmrHasRun = false\\n\\n\\t\\t\\timport.meta.hot.on(\\"evidence:reset-queries\\", async (payload) => {\\n\\t\\t\\t\\tawait $page.data.__db.updateParquetURLs(JSON.stringify(payload.latestManifest), true);\\n\\t\\t\\t\\tQuery.emptyCache()\\n\\t\\t\\t\\t__categoriesFactory(__categoriesText, { noResolve: __categoriesHasUnresolved });\\n__orders_by_categoryFactory(__orders_by_categoryText, { noResolve: __orders_by_categoryHasUnresolved });\\n\\t\\t\\t})\\n\\t    }\\n\\t\\t\\n\\t\\tlet params = $page.params;\\n\\t\\t$: params = $page.params;\\n\\t\\t\\n\\t\\tlet _mounted = false;\\n\\t\\tonMount(() => (_mounted = true));\\n\\n        \\n\\t\\t\\n\\t\\t\\n                // Update external queries\\n                if (import.meta?.hot) {\\n\\t\\t\\t\\t\\timport.meta.hot.on(\\"vite:beforeUpdate\\", () => {\\n\\t\\t\\t\\t\\t\\t// remove all prerendered queries\\n\\t\\t\\t\\t\\t\\tprops.data = {}\\n\\t\\t\\t\\t\\t});\\n\\n                    import.meta.hot.on(\\"evidence:queryChange\\", ({queryId, content}) => {\\n                        let errors = []\\n                        if (!queryId) errors.push(\\"Malformed event: Missing queryId\\")\\n                        if (!content) errors.push(\\"Malformed event: Missing content\\")\\n                        if (errors.length) {\\n                            console.warn(\\"Failed to update query on serverside change!\\", errors.join(\\"\\\\n\\"))\\n                            return\\n                        }\\n\\n                        if (queryId === \\"categories\\") {\\n                            __categoriesText = content\\n                        }\\n                        \\n                    })\\n                }\\n\\n                let categoriesInitialStates = { initialData: undefined, initialError: undefined }\\n                \\n                // Give initial states for these variables\\n                /** @type {boolean} */\\n                let __categoriesHasUnresolved = hasUnsetValues\`select\\n      category\\n  from needful_things.orders\\n  group by category\`;\\n                /** @type {string} */\\n                let __categoriesText = \`select\\n      category\\n  from needful_things.orders\\n  group by category\`\\n\\n\\n                if (browser) {\\n                    // Data came from SSR\\n                    if (data.categories_data) {\\n                        // vvv is this still used/possible?\\n                        if (data.categories_data instanceof Error) {\\n                            categoriesInitialStates.initialError = data.categories_data\\n                        } else {\\n                            categoriesInitialStates.initialData = data.categories_data\\n                        }\\n                        if (data.categories_columns) {\\n                            categoriesInitialStates.knownColumns = data.categories_columns\\n                        }\\n                    }\\n                }\\n\\n                /** @type {import(\\"@evidence-dev/sdk/usql\\").QueryValue} */\\n                let categories;\\n\\n                $: __categoriesHasUnresolved = hasUnsetValues\`select\\n      category\\n  from needful_things.orders\\n  group by category\`;\\n                $: __categoriesText = \`select\\n      category\\n  from needful_things.orders\\n  group by category\`\\n\\n                // keep initial state around until after the query has resolved once\\n                let __categoriesInitialFactory = false;\\n                $: if (__categoriesHasUnresolved || !__categoriesInitialFactory) {    \\n                    if (!__categoriesHasUnresolved) {\\n                        __categoriesFactory(__categoriesText, { noResolve: __categoriesHasUnresolved, ...categoriesInitialStates });\\n                        __categoriesInitialFactory = true;\\n                    }\\n                } else {\\n                    __categoriesFactory(__categoriesText, { noResolve: __categoriesHasUnresolved });\\n                }\\n\\n                const __categoriesFactory = Query.createReactive(\\n                    { callback: v => {\\n                        categories = v\\n                    }, execFn: queryFunc },\\n                    { id: 'categories', ...categoriesInitialStates }\\n                )\\n\\n                // Assign a value for the initial run-through\\n                // This is split because chicken / egg\\n                __categoriesFactory(__categoriesText, { noResolve: __categoriesHasUnresolved, ...categoriesInitialStates })\\n\\n                // Add queries to global scope inside symbols to ease debugging\\n                globalThis[Symbol.for(\\"categories\\")] = { get value() { return categories } }\\n                \\n                \\n            \\n\\n                // Update external queries\\n                if (import.meta?.hot) {\\n\\t\\t\\t\\t\\timport.meta.hot.on(\\"vite:beforeUpdate\\", () => {\\n\\t\\t\\t\\t\\t\\t// remove all prerendered queries\\n\\t\\t\\t\\t\\t\\tprops.data = {}\\n\\t\\t\\t\\t\\t});\\n\\n                    import.meta.hot.on(\\"evidence:queryChange\\", ({queryId, content}) => {\\n                        let errors = []\\n                        if (!queryId) errors.push(\\"Malformed event: Missing queryId\\")\\n                        if (!content) errors.push(\\"Malformed event: Missing content\\")\\n                        if (errors.length) {\\n                            console.warn(\\"Failed to update query on serverside change!\\", errors.join(\\"\\\\n\\"))\\n                            return\\n                        }\\n\\n                        if (queryId === \\"orders_by_category\\") {\\n                            __orders_by_categoryText = content\\n                        }\\n                        \\n                    })\\n                }\\n\\n                let orders_by_categoryInitialStates = { initialData: undefined, initialError: undefined }\\n                \\n                // Give initial states for these variables\\n                /** @type {boolean} */\\n                let __orders_by_categoryHasUnresolved = hasUnsetValues\`select\\n      date_trunc('month', order_datetime) as month,\\n      sum(sales) as sales_usd,\\n      category\\n  from needful_things.orders\\n  where category like '\${inputs.category.value}'\\n  and date_part('year', order_datetime) like '\${inputs.year.value}'\\n  group by all\\n  order by sales_usd desc\`;\\n                /** @type {string} */\\n                let __orders_by_categoryText = \`select\\n      date_trunc('month', order_datetime) as month,\\n      sum(sales) as sales_usd,\\n      category\\n  from needful_things.orders\\n  where category like '\${inputs.category.value}'\\n  and date_part('year', order_datetime) like '\${inputs.year.value}'\\n  group by all\\n  order by sales_usd desc\`\\n\\n\\n                if (browser) {\\n                    // Data came from SSR\\n                    if (data.orders_by_category_data) {\\n                        // vvv is this still used/possible?\\n                        if (data.orders_by_category_data instanceof Error) {\\n                            orders_by_categoryInitialStates.initialError = data.orders_by_category_data\\n                        } else {\\n                            orders_by_categoryInitialStates.initialData = data.orders_by_category_data\\n                        }\\n                        if (data.orders_by_category_columns) {\\n                            orders_by_categoryInitialStates.knownColumns = data.orders_by_category_columns\\n                        }\\n                    }\\n                }\\n\\n                /** @type {import(\\"@evidence-dev/sdk/usql\\").QueryValue} */\\n                let orders_by_category;\\n\\n                $: __orders_by_categoryHasUnresolved = hasUnsetValues\`select\\n      date_trunc('month', order_datetime) as month,\\n      sum(sales) as sales_usd,\\n      category\\n  from needful_things.orders\\n  where category like '\${inputs.category.value}'\\n  and date_part('year', order_datetime) like '\${inputs.year.value}'\\n  group by all\\n  order by sales_usd desc\`;\\n                $: __orders_by_categoryText = \`select\\n      date_trunc('month', order_datetime) as month,\\n      sum(sales) as sales_usd,\\n      category\\n  from needful_things.orders\\n  where category like '\${inputs.category.value}'\\n  and date_part('year', order_datetime) like '\${inputs.year.value}'\\n  group by all\\n  order by sales_usd desc\`\\n\\n                // keep initial state around until after the query has resolved once\\n                let __orders_by_categoryInitialFactory = false;\\n                $: if (__orders_by_categoryHasUnresolved || !__orders_by_categoryInitialFactory) {    \\n                    if (!__orders_by_categoryHasUnresolved) {\\n                        __orders_by_categoryFactory(__orders_by_categoryText, { noResolve: __orders_by_categoryHasUnresolved, ...orders_by_categoryInitialStates });\\n                        __orders_by_categoryInitialFactory = true;\\n                    }\\n                } else {\\n                    __orders_by_categoryFactory(__orders_by_categoryText, { noResolve: __orders_by_categoryHasUnresolved });\\n                }\\n\\n                const __orders_by_categoryFactory = Query.createReactive(\\n                    { callback: v => {\\n                        orders_by_category = v\\n                    }, execFn: queryFunc },\\n                    { id: 'orders_by_category', ...orders_by_categoryInitialStates }\\n                )\\n\\n                // Assign a value for the initial run-through\\n                // This is split because chicken / egg\\n                __orders_by_categoryFactory(__orders_by_categoryText, { noResolve: __orders_by_categoryHasUnresolved, ...orders_by_categoryInitialStates })\\n\\n                // Add queries to global scope inside symbols to ease debugging\\n                globalThis[Symbol.for(\\"orders_by_category\\")] = { get value() { return orders_by_category } }\\n                \\n                \\n            \\n\\t\\t\\n\\t\\tif (!browser) {\\n\\t\\t\\tonDestroy(inputs_store.subscribe((inputs) => {\\n\\t\\t\\t\\t\\n\\t\\t\\t\\t\\t\\t__orders_by_categoryHasUnresolved = hasUnsetValues\`select\\n      date_trunc('month', order_datetime) as month,\\n      sum(sales) as sales_usd,\\n      category\\n  from needful_things.orders\\n  where category like '\${inputs.category.value}'\\n  and date_part('year', order_datetime) like '\${inputs.year.value}'\\n  group by all\\n  order by sales_usd desc\`;\\n\\t\\t\\t\\t\\t\\t__orders_by_categoryText = \`select\\n      date_trunc('month', order_datetime) as month,\\n      sum(sales) as sales_usd,\\n      category\\n  from needful_things.orders\\n  where category like '\${inputs.category.value}'\\n  and date_part('year', order_datetime) like '\${inputs.year.value}'\\n  group by all\\n  order by sales_usd desc\`;\\n\\t\\t\\t\\t\\t\\t__orders_by_categoryFactory(__orders_by_categoryText, { noResolve: __orders_by_categoryHasUnresolved });\\n\\t\\t\\t\\t\\n\\t\\t\\t}));\\n\\t\\t}\\n\\t\\t\\n\\t\\t\\n\\t\\t\\n    \\n\\t\\n  let isDomoEnvironment = false;\\n\\n  // Check Domo environment\\n  if (typeof window !== 'undefined') {\\n    isDomoEnvironment = typeof window.domo !== 'undefined';\\n  }\\n<\/script>\\n<style>\\n  .domo-banner {\\n    margin: 20px 0;\\n    padding: 20px;\\n    border: 2px solid #00d4aa;\\n    border-radius: 12px;\\n    background: linear-gradient(135deg, #f0fdf9 0%, #ecfdf5 100%);\\n    color: #065f46;\\n    text-align: center;\\n  }\\n\\n  .domo-banner h3 {\\n    margin: 0 0 10px 0;\\n    color: #047857;\\n  }\\n\\n  .dev-banner {\\n    margin: 20px 0;\\n    padding: 20px;\\n    border: 2px solid #f59e0b;\\n    border-radius: 12px;\\n    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);\\n    color: #92400e;\\n    text-align: center;\\n  }\\n\\n  .dev-banner h3 {\\n    margin: 0 0 10px 0;\\n    color: #d97706;\\n  }\\n\\n  .quick-start-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n    gap: 20px;\\n    margin: 30px 0;\\n  }\\n\\n  .quick-start-card {\\n    padding: 25px;\\n    border: 1px solid #e5e7eb;\\n    border-radius: 12px;\\n    background-color: white;\\n    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\\n    transition: transform 0.2s, box-shadow 0.2s;\\n  }\\n\\n  .quick-start-card:hover {\\n    transform: translateY(-2px);\\n    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);\\n  }\\n\\n  .quick-start-card h4 {\\n    margin: 0 0 15px 0;\\n    color: #1f2937;\\n    font-size: 18px;\\n  }\\n\\n  .quick-start-card p {\\n    margin: 0 0 20px 0;\\n    color: #6b7280;\\n    line-height: 1.5;\\n  }\\n\\n  .btn-primary, .btn-secondary {\\n    display: inline-block;\\n    padding: 12px 24px;\\n    border: none;\\n    border-radius: 8px;\\n    text-decoration: none;\\n    font-weight: 600;\\n    font-size: 14px;\\n    cursor: pointer;\\n    transition: all 0.2s;\\n  }\\n\\n  .btn-primary {\\n    background-color: #2563eb;\\n    color: white;\\n  }\\n\\n  .btn-primary:hover {\\n    background-color: #1d4ed8;\\n    text-decoration: none;\\n    color: white;\\n  }\\n\\n  .btn-secondary {\\n    background-color: #f3f4f6;\\n    color: #374151;\\n    border: 1px solid #d1d5db;\\n  }\\n\\n  .btn-secondary:hover {\\n    background-color: #e5e7eb;\\n    text-decoration: none;\\n    color: #374151;\\n  }\\n\\n\\n\\n  .next-steps {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n    gap: 20px;\\n    margin: 30px 0;\\n  }\\n\\n  .step {\\n    padding: 20px;\\n    border: 1px solid #e5e7eb;\\n    border-radius: 8px;\\n    background-color: #fafafa;\\n  }\\n\\n  .step h4 {\\n    margin: 0 0 10px 0;\\n    color: #1f2937;\\n    font-size: 16px;\\n  }\\n\\n  .step p {\\n    margin: 0;\\n    color: #6b7280;\\n    line-height: 1.5;\\n  }\\n\\n  .step a {\\n    color: #2563eb;\\n    text-decoration: none;\\n  }\\n\\n  .step a:hover {\\n    text-decoration: underline;\\n  }\\n</style>\\n\\n\\n<h1 class=\\"markdown\\" id=\\"evidence-dashboard-for-domo-ddx\\"><a href=\\"#evidence-dashboard-for-domo-ddx\\">Evidence Dashboard for Domo DDX</a></h1>\\n{#if isDomoEnvironment}\\n  <div class=\\"domo-banner\\">\\n    <h3>🚀 Running in Domo DDX Environment</h3>\\n    <p>This Evidence dashboard is connected to your Domo instance and ready to analyze your data!</p>\\n  </div>\\n{:else}\\n  <div class=\\"dev-banner\\">\\n    <h3>🛠️ Development Mode</h3>\\n    <p>This is a preview of your Evidence dashboard. Deploy to Domo DDX to access real datasets.</p>\\n  </div>\\n{/if}\\n<p\\n  class=\\"markdown\\"\\n>Welcome to your Evidence dashboard! This application combines the power of Evidence's analytics framework with Domo's data platform, allowing you to create interactive dashboards and reports using your Domo datasets.</p>\\n<h2 class=\\"markdown\\" id=\\"quick-start\\"><a href=\\"#quick-start\\">Quick Start</a></h2>\\n<div class=\\"quick-start-grid\\">\\n  <div class=\\"quick-start-card\\">\\n    <h4>📊 Load Domo Data</h4>\\n    <p>Use the workflow picker to select and load Domo datasets into DuckDB for analysis.</p>\\n    <a href=\\"/workflow\\" class=\\"btn-primary\\">Open Workflow Picker</a>\\n  </div>\\n  <div class=\\"quick-start-card\\">\\n    <h4>📈 View Sample Analysis</h4>\\n    <p>See how Evidence works with the sample data below.</p>\\n    <a href=\\"#sample-analysis\\" class=\\"btn-secondary\\">View Sample</a>\\n  </div>\\n  <div class=\\"quick-start-card\\">\\n    <h4>📚 Learn More</h4>\\n    <p>Explore Evidence documentation and best practices.</p>\\n    <a href=\\"https://docs.evidence.dev\\" target=\\"_blank\\" class=\\"btn-secondary\\">Documentation</a>\\n  </div>\\n</div>\\n<h2 class=\\"markdown\\" id=\\"sample-analysis\\"><a href=\\"#sample-analysis\\">Sample Analysis</a></h2>\\n<div id=\\"sample-analysis\\"></div>\\n<Details title='How Evidence Works with Your Data'>\\n  This example shows how Evidence creates interactive dashboards from SQL queries. Once you load your Domo datasets, you can create similar analyses with your own data.\\n</Details>\\n\\n        {#if categories }\\n            <QueryViewer\\n                queryID = \\"categories\\"\\n                queryResult = {categories}\\n            /> \\n        {/if}\\n<Dropdown data={categories} name=category value=category>\\n    <DropdownOption value=\\"%\\" valueLabel=\\"All Categories\\"/>\\n</Dropdown>\\n<Dropdown name=year>\\n    <DropdownOption value=% valueLabel=\\"All Years\\"/>\\n    <DropdownOption value=2019/>\\n    <DropdownOption value=2020/>\\n    <DropdownOption value=2021/>\\n</Dropdown>\\n\\n        {#if orders_by_category }\\n            <QueryViewer\\n                queryID = \\"orders_by_category\\"\\n                queryResult = {orders_by_category}\\n            /> \\n        {/if}\\n<BarChart\\n    data={orders_by_category}\\n    title=\\"Sales by Month, {inputs.category.label}\\"\\n    x=month\\n    y=sales_usd\\n    series=category\\n/>\\n<h2 class=\\"markdown\\" id=\\"whats-next\\"><a href=\\"#whats-next\\">What's Next?</a></h2>\\n<div class=\\"next-steps\\">\\n  <div class=\\"step\\">\\n    <h4>1. Load Your Data</h4>\\n    <p>Use the <a href=\\"/workflow\\">workflow picker</a> to select and load Domo datasets into DuckDB</p>\\n  </div>\\n  <div class=\\"step\\">\\n    <h4>2. Create Queries</h4>\\n    <p>Write SQL queries against your loaded data using Evidence's query blocks</p>\\n  </div>\\n  <div class=\\"step\\">\\n    <h4>3. Build Visualizations</h4>\\n    <p>Use Evidence components like BarChart, LineChart, and DataTable to create interactive dashboards</p>\\n  </div>\\n  <div class=\\"step\\">\\n    <h4>4. Deploy to Domo</h4>\\n    <p>Package your Evidence app and deploy it to Domo DDX for your team to use</p>\\n  </div>\\n</div>\\n\\n"],"names":[],"mappings":"AAqXE,wCAAa,CACX,MAAM,CAAE,IAAI,CAAC,CAAC,CACd,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,aAAa,CAAE,IAAI,CACnB,UAAU,CAAE,gBAAgB,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAC7D,KAAK,CAAE,OAAO,CACd,UAAU,CAAE,MACd,CAEA,0BAAY,CAAC,gBAAG,CACd,MAAM,CAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAClB,KAAK,CAAE,OACT,CAEA,uCAAY,CACV,MAAM,CAAE,IAAI,CAAC,CAAC,CACd,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,aAAa,CAAE,IAAI,CACnB,UAAU,CAAE,gBAAgB,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAC7D,KAAK,CAAE,OAAO,CACd,UAAU,CAAE,MACd,CAEA,yBAAW,CAAC,gBAAG,CACb,MAAM,CAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAClB,KAAK,CAAE,OACT,CAEA,6CAAkB,CAChB,OAAO,CAAE,IAAI,CACb,qBAAqB,CAAE,OAAO,QAAQ,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAC3D,GAAG,CAAE,IAAI,CACT,MAAM,CAAE,IAAI,CAAC,CACf,CAEA,6CAAkB,CAChB,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,aAAa,CAAE,IAAI,CACnB,gBAAgB,CAAE,KAAK,CACvB,UAAU,CAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CACzC,UAAU,CAAE,SAAS,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,IACzC,CAEA,6CAAiB,MAAO,CACtB,SAAS,CAAE,WAAW,IAAI,CAAC,CAC3B,UAAU,CAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAC1C,CAEA,+BAAiB,CAAC,gBAAG,CACnB,MAAM,CAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAClB,KAAK,CAAE,OAAO,CACd,SAAS,CAAE,IACb,CAEA,+BAAiB,CAAC,eAAE,CAClB,MAAM,CAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAClB,KAAK,CAAE,OAAO,CACd,WAAW,CAAE,GACf,CAEA,wCAAY,CAAE,0CAAe,CAC3B,OAAO,CAAE,YAAY,CACrB,OAAO,CAAE,IAAI,CAAC,IAAI,CAClB,MAAM,CAAE,IAAI,CACZ,aAAa,CAAE,GAAG,CAClB,eAAe,CAAE,IAAI,CACrB,WAAW,CAAE,GAAG,CAChB,SAAS,CAAE,IAAI,CACf,MAAM,CAAE,OAAO,CACf,UAAU,CAAE,GAAG,CAAC,IAClB,CAEA,wCAAa,CACX,gBAAgB,CAAE,OAAO,CACzB,KAAK,CAAE,KACT,CAEA,wCAAY,MAAO,CACjB,gBAAgB,CAAE,OAAO,CACzB,eAAe,CAAE,IAAI,CACrB,KAAK,CAAE,KACT,CAEA,0CAAe,CACb,gBAAgB,CAAE,OAAO,CACzB,KAAK,CAAE,OAAO,CACd,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OACpB,CAEA,0CAAc,MAAO,CACnB,gBAAgB,CAAE,OAAO,CACzB,eAAe,CAAE,IAAI,CACrB,KAAK,CAAE,OACT,CAIA,uCAAY,CACV,OAAO,CAAE,IAAI,CACb,qBAAqB,CAAE,OAAO,QAAQ,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAC3D,GAAG,CAAE,IAAI,CACT,MAAM,CAAE,IAAI,CAAC,CACf,CAEA,iCAAM,CACJ,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,aAAa,CAAE,GAAG,CAClB,gBAAgB,CAAE,OACpB,CAEA,mBAAK,CAAC,gBAAG,CACP,MAAM,CAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAClB,KAAK,CAAE,OAAO,CACd,SAAS,CAAE,IACb,CAEA,mBAAK,CAAC,eAAE,CACN,MAAM,CAAE,CAAC,CACT,KAAK,CAAE,OAAO,CACd,WAAW,CAAE,GACf,CAEA,mBAAK,CAAC,eAAE,CACN,KAAK,CAAE,OAAO,CACd,eAAe,CAAE,IACnB,CAEA,mBAAK,CAAC,eAAC,MAAO,CACZ,eAAe,CAAE,SACnB"}`},J={title:"Evidence Dashboard for Domo"},xr=N((a,e,t,i)=>{let o,l,n,s;l=z(Eo,u=>o=u),s=z(ao,u=>n=u);let{data:v}=e,{data:A={},customFormattingSettings:h,__db:g,inputs:c}=v;ka(ao,n="6666cd76f96956469e7be39d750cc7d9",n);let m=si(Yt(c));ht(m.subscribe(u=>c=u)),Bt(hi,{getCustomFormats:()=>h.customFormats||[]}),ht(()=>Dt.emptyCache());const C=(u,_)=>ui(g.query,u,{query_name:_});yi(C),o.params;let d={initialData:void 0,initialError:void 0},f=Ot`select
      category
  from needful_things.orders
  group by category`,b=`select
      category
  from needful_things.orders
  group by category`,B,k=!1;const T=Dt.createReactive({callback:u=>{B=u},execFn:C},{id:"categories",...d});T(b,{noResolve:f,...d}),globalThis[Symbol.for("categories")]={get value(){return B}};let I={initialData:void 0,initialError:void 0},p=Ot`select
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${c.category.value}'
  and date_part('year', order_datetime) like '${c.year.value}'
  group by all
  order by sales_usd desc`,M=`select
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${c.category.value}'
  and date_part('year', order_datetime) like '${c.year.value}'
  group by all
  order by sales_usd desc`,E,S=!1;const y=Dt.createReactive({callback:u=>{E=u},execFn:C},{id:"orders_by_category",...I});y(M,{noResolve:p,...I}),globalThis[Symbol.for("orders_by_category")]={get value(){return E}},ht(m.subscribe(u=>{p=Ot`select
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${u.category.value}'
  and date_part('year', order_datetime) like '${u.year.value}'
  group by all
  order by sales_usd desc`,M=`select
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${u.category.value}'
  and date_part('year', order_datetime) like '${u.year.value}'
  group by all
  order by sales_usd desc`,y(M,{noResolve:p})}));let r=!1;return typeof window<"u"&&(r=typeof window.domo<"u"),e.data===void 0&&t.data&&v!==void 0&&t.data(v),a.css.add(ql),{data:A={},customFormattingSettings:h,__db:g}=v,ci.set(Object.keys(A).length>0),o.params,f=Ot`select
      category
  from needful_things.orders
  group by category`,b=`select
      category
  from needful_things.orders
  group by category`,f||!k?f||(T(b,{noResolve:f,...d}),k=!0):T(b,{noResolve:f}),p=Ot`select
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${c.category.value}'
  and date_part('year', order_datetime) like '${c.year.value}'
  group by all
  order by sales_usd desc`,M=`select
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${c.category.value}'
  and date_part('year', order_datetime) like '${c.year.value}'
  group by all
  order by sales_usd desc`,p||!S?p||(y(M,{noResolve:p,...I}),S=!0):y(M,{noResolve:p}),l(),s(),`  ${typeof J<"u"&&J.title&&J.hide_title!==!0?`<h1 class="title">${R(J.title)}</h1>`:""} ${a.head+=`<!-- HEAD_svelte-2igo1p_START -->${typeof J<"u"&&J.title?`${a.title=`<title>${R(J.title)}</title>`,""} <meta property="og:title"${le("content",J.og?.title??J.title,0)}> <meta name="twitter:title"${le("content",J.og?.title??J.title,0)}>`:` ${a.title="<title>Evidence</title>",""}`}<meta name="twitter:card" content="summary_large_image"><meta name="twitter:site" content="@evidence_dev">${typeof J=="object"?`${J.description||J.og?.description?`<meta name="description"${le("content",J.description??J.og?.description,0)}> <meta property="og:description"${le("content",J.og?.description??J.description,0)}> <meta name="twitter:description"${le("content",J.og?.description??J.description,0)}>`:""} ${J.og?.image?`<meta property="og:image"${le("content",oo(J.og?.image),0)}> <meta name="twitter:image"${le("content",oo(J.og?.image),0)}>`:""}`:""}<!-- HEAD_svelte-2igo1p_END -->`,""}    <h1 class="markdown" id="evidence-dashboard-for-domo-ddx" data-svelte-h="svelte-dwxo2v"><a href="#evidence-dashboard-for-domo-ddx">Evidence Dashboard for Domo DDX</a></h1> ${r?'<div class="domo-banner svelte-styow2" data-svelte-h="svelte-1uzwljv"><h3 class="svelte-styow2">🚀 Running in Domo DDX Environment</h3> <p>This Evidence dashboard is connected to your Domo instance and ready to analyze your data!</p></div>':'<div class="dev-banner svelte-styow2" data-svelte-h="svelte-1b532x0"><h3 class="svelte-styow2">🛠️ Development Mode</h3> <p>This is a preview of your Evidence dashboard. Deploy to Domo DDX to access real datasets.</p></div>'} <p class="markdown" data-svelte-h="svelte-17kjwqn">Welcome to your Evidence dashboard! This application combines the power of Evidence&#39;s analytics framework with Domo&#39;s data platform, allowing you to create interactive dashboards and reports using your Domo datasets.</p> <h2 class="markdown" id="quick-start" data-svelte-h="svelte-e7fbvj"><a href="#quick-start">Quick Start</a></h2> <div class="quick-start-grid svelte-styow2" data-svelte-h="svelte-1ynrehd"><div class="quick-start-card svelte-styow2"><h4 class="svelte-styow2">📊 Load Domo Data</h4> <p class="svelte-styow2">Use the workflow picker to select and load Domo datasets into DuckDB for analysis.</p> <a href="/workflow" class="btn-primary svelte-styow2">Open Workflow Picker</a></div> <div class="quick-start-card svelte-styow2"><h4 class="svelte-styow2">📈 View Sample Analysis</h4> <p class="svelte-styow2">See how Evidence works with the sample data below.</p> <a href="#sample-analysis" class="btn-secondary svelte-styow2">View Sample</a></div> <div class="quick-start-card svelte-styow2"><h4 class="svelte-styow2">📚 Learn More</h4> <p class="svelte-styow2">Explore Evidence documentation and best practices.</p> <a href="https://docs.evidence.dev" target="_blank" class="btn-secondary svelte-styow2">Documentation</a></div></div> <h2 class="markdown" id="sample-analysis" data-svelte-h="svelte-1941s2m"><a href="#sample-analysis">Sample Analysis</a></h2> <div id="sample-analysis"></div> ${D(gl,"Details").$$render(a,{title:"How Evidence Works with Your Data"},{},{default:()=>"This example shows how Evidence creates interactive dashboards from SQL queries. Once you load your Domo datasets, you can create similar analyses with your own data."})} ${B?`${D(yo,"QueryViewer").$$render(a,{queryID:"categories",queryResult:B},{},{})}`:""} ${D(_o,"Dropdown").$$render(a,{data:B,name:"category",value:"category"},{},{default:()=>`${D(mt,"DropdownOption").$$render(a,{value:"%",valueLabel:"All Categories"},{},{})}`})} ${D(_o,"Dropdown").$$render(a,{name:"year"},{},{default:()=>`${D(mt,"DropdownOption").$$render(a,{value:"%",valueLabel:"All Years"},{},{})} ${D(mt,"DropdownOption").$$render(a,{value:"2019"},{},{})} ${D(mt,"DropdownOption").$$render(a,{value:"2020"},{},{})} ${D(mt,"DropdownOption").$$render(a,{value:"2021"},{},{})}`})} ${E?`${D(yo,"QueryViewer").$$render(a,{queryID:"orders_by_category",queryResult:E},{},{})}`:""} ${D(Pl,"BarChart").$$render(a,{data:E,title:"Sales by Month, "+c.category.label,x:"month",y:"sales_usd",series:"category"},{},{})} <h2 class="markdown" id="whats-next" data-svelte-h="svelte-fy128a"><a href="#whats-next">What&#39;s Next?</a></h2> <div class="next-steps svelte-styow2" data-svelte-h="svelte-zkky8f"><div class="step svelte-styow2"><h4 class="svelte-styow2">1. Load Your Data</h4> <p class="svelte-styow2">Use the <a href="/workflow" class="svelte-styow2">workflow picker</a> to select and load Domo datasets into DuckDB</p></div> <div class="step svelte-styow2"><h4 class="svelte-styow2">2. Create Queries</h4> <p class="svelte-styow2">Write SQL queries against your loaded data using Evidence&#39;s query blocks</p></div> <div class="step svelte-styow2"><h4 class="svelte-styow2">3. Build Visualizations</h4> <p class="svelte-styow2">Use Evidence components like BarChart, LineChart, and DataTable to create interactive dashboards</p></div> <div class="step svelte-styow2"><h4 class="svelte-styow2">4. Deploy to Domo</h4> <p class="svelte-styow2">Package your Evidence app and deploy it to Domo DDX for your team to use</p></div></div>`});export{xr as default};
