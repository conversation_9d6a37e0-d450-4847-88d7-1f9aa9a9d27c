import{s as Q,g as U,c as T,v as G,b as F,h as f}from"./ssr.js";import{Query as R,sql as u}from"@uwdata/mosaic-sql";import{r as j,Q as p}from"./Query.js";import{query as q}from"@evidence-dev/universal-sql/client-duckdb";import{w as K,d as L}from"./index2.js";import{tidy as m,mutate as I,summarize as h,sum as V,mean as z,median as N,max as H,min as W,nDistinct as v,n as b}from"@tidyjs/tidy";import Y from"ssf";import{W as x,X as Z,Y as X,Z as J,_ as P,$,a0 as tt,a1 as et,a2 as nt}from"./VennDiagram.svelte_svelte_type_style_lang.js";import{d as rt}from"./utils.js";import{ExportToCsv as ot}from"export-to-csv";import{I as at}from"./InlineError.js";import{t as st}from"./index7.js";const it="___usql_query";let D=q;const kt=t=>{Q(it,t),D=t},Qt=(t,e,n)=>{const r=K(w(t,e,n));let o;return{results:L(r,a=>a),update:async a=>{const{hasQuery:s,query:i}=w(a,e);s?j(()=>{i.hash!==o?.hash&&(o=i,r.set({hasQuery:s,query:i}))},i.fetch()):r.set({hasQuery:!1})}}},w=({value:t,label:e,select:n,data:r,where:o,order:a},s,i)=>{if(!r||!(t||n))return{hasQuery:!1};let A=!1;const l=new R().distinct();if(t&&l.select({value:u`${t}`}),e?l.select({label:u`${e}`}):l.select({label:u`${t}`}),n&&l.select(n),typeof r=="string")r.trim().match(/^[\w]+$/)?l.from(r.trim()):l.from(u(r.trim()));else if(p.isQuery(r))l.from(u`(${r.text})`),A=r.opts.noResolve??!1;else return{hasQuery:!1};o&&l.where(o),a&&(l.orderby(u`${a}`),l.select({ordinal:u`row_number() over (ORDER BY ${a})`}));const c=lt(l.toString(),s,i,{noResolve:A});return c.fetch(),{hasQuery:!0,query:c}},lt=(t,e,n,r)=>p.create(t,D,e,{...r,initialData:n}),At="customFormattingSettings";function C(t){if(t&&typeof t=="string"){let e=t.split(" ");t.includes(":")||(t=t+"T00:00:00"),e.length>2&&(t=e[0]+" "+e[1]);const n=/\.([^\s]+)/;t=t.replace(n,""),t=t.replace("Z",""),t=t.replace(" ","T")}return t}function Ut(t,e){return t=m(t,I({[e]:n=>n[e]?new Date(C(n[e])):null})),t}function Gt(t,e){return t=m(t,I({[e]:n=>C(n[e])})),t}const O="axis",d="value",B=()=>{try{return U(At)?.getCustomFormats()||[]}catch{return[]}},ut=(t,e,n)=>{let r=mt(t);if(e.evidenceType==="string")return;if(r){let a=B(),s=[...x,...a].find(i=>i.formatTag?.toLowerCase()===r?.toLowerCase?.());if(s)return s}let o=Z(t,e,n);if(o)return o};function ct(t,e=void 0){let n=t,r=B(),o=[...x,...r].find(s=>s.formatTag?.toLowerCase()===n?.toLowerCase?.()),a={};return o||(a={formatTag:"custom",formatCode:n},e&&(a.valueType=e),a)}const ft=(t,e=void 0,n=void 0)=>{try{return S(t,e,n,d)}catch(r){return console.warn(`Unexpected error calling applyFormatting(${t}, ${e}, ${d}, ${n}). Error=${r}`),t}},Ft=(t,e=void 0,n=void 0)=>{try{return S(t,e,n,O)}catch{}return t},dt=(t,e)=>{let n=t;if(t&&e?.formatTag){let r=t.toLowerCase().lastIndexOf(`_${e.formatTag.toLowerCase()}`),o="";r>0&&(typeof e?.titleTagReplacement=="string"&&(o=e.titleTagReplacement),n=t.substring(0,r)+o)}return n};function S(t,e=void 0,n=void 0,r=d){if(t==null)return"-";let o;if(e)try{let a=pt(e,r),s;try{e.valueType==="date"&&typeof t=="string"?s=new Date(C(t)):t instanceof Date?s=new Date(t.toISOString().slice(0,-1)):e.valueType==="number"&&typeof t!="number"&&!Number.isNaN(t)?s=Number(t):s=t}catch{s=t}if(J(e,a))try{o=P(s,e,n)}catch(i){console.warn(`Unexpected error applying auto formatting. Error=${i}`)}else o=Y.format(a,s)}catch(a){console.warn(`Unexpected error applying formatting ${a}`)}return o===void 0&&(o=$(t)),o}function pt(t,e=d){return typeof t=="string"?t:e===O&&t?.axisFormatCode?t.axisFormatCode:t?.formatCode}function mt(t){let e=t.toLowerCase(),n=e.lastIndexOf("_");if(n>0)return e.substr(n).replace("_","")}function Rt(t,e){let n=ct(e),r=X(t);return n.valueType=r,ft(t,n)}function Ct(t,e){let n=dt(t,e),r=["id","gdp"],o=["of","the","and","in","on"];function a(s){return s.replace(/\S*/g,function(i){return!r.includes(i)&&!o.includes(i)?i.charAt(0).toUpperCase()+i.substr(1).toLowerCase():r.includes(i)?i.toUpperCase():i.toLowerCase()})}return n=a(t.replace(/"/g,"").replace(/_/g," ")),n}function yt(t,e,n){let r=[];if(t===void 0)throw Error("No data provided");if(typeof t!="object")throw Error("'"+t+"' is not a recognized query result. Data should be provided in the format: data = {"+t.replace("data.","")+"}");if(t[0]===void 0||t.length===0)throw Error("Dataset is empty: query ran successfully, but no data was returned from the database");if(t[0]?.error_object?.error!=null)throw Error("SQL Error: "+t[0]?.error_object?.error?.message);if(e!=null){if(!(e instanceof Array))throw Error("reqCols must be passed in as an array");for(var o=0;o<e.length;o++){if(e[o]==null)throw Error(`Missing required column(s): ${e[o]} not found in data set.`);if(e[o]=="")throw Error("Missing required column(s): A Empty string was provided for one of your props.")}if(p.isQuery(t))if(!t.columnsLoaded&&t.dataLoaded){const s=Object.keys(t[0]);for(const i of s)r.push(i)}else for(const s of t.columns)r.push(s.column_name);else for(const s of Object.keys(t[0]))r.push(s);let a;for(o=0;o<e.length;o++)if(a=e[o],!r.includes(a))throw Error("'"+a+"' is not a column in the dataset");if(n!=null&&n.some(s=>s!=null)){for(o=0;o<n.length;o++)if(a=n[o],a!=null&&!r.includes(a))throw Error("'"+a+"' is not a column in the dataset")}}}function E(t,e,n=!0){const r=m(t,n?h({count:b(e),countDistinct:v(e),min:W(e),max:H(e),median:N(e),mean:z(e),sum:V(e)}):h({count:b(e),countDistinct:v(e)}))[0],{maxDecimals:o,unitType:a}=gt(t.map(s=>s[e]));return{min:r.min,max:r.max,median:r.median,mean:r.mean,count:r.count,countDistinct:r.countDistinct,sum:r.sum,maxDecimals:o,unitType:a}}function gt(t){if(t==null||t.length===0)return{maxDecimals:0,unitType:"unknown"};{let e=0;for(const n of t){const r=n?.toString().split(".")[1]?.length;r>e&&(e=r)}return{maxDecimals:e,unitType:"number"}}}function jt(t,e="object"){const n={},r=tt(t);for(const o of Object.keys(t[0])){const a=r.find(l=>l.name?.toLowerCase()===o?.toLowerCase())??{name:o,evidenceType:nt.NUMBER,typeFidelity:et.INFERRED},s=a.evidenceType;let i=a.evidenceType==="number"?E(t,o,!0):E(t,o,!1);a.evidenceType!=="number"&&(i.maxDecimals=0,i.unitType=a.evidenceType);const A=ut(o,a,i);n[o]={title:Ct(o,A),type:s,evidenceColumnType:a,format:A,columnUnitSummary:i}}return e!=="object"?Object.entries(n).map(([o,a])=>({id:o,...a})):n}const ht={code:"button.svelte-1uc1g2y svg{stroke:var(--base-content);margin-top:auto;margin-bottom:auto;transition:stroke 200ms}button.svelte-1uc1g2y{display:flex;cursor:pointer;font-family:var(--ui-font-family);font-size:1em;color:var(--base-content);opacity:0.5;justify-items:flex-end;align-items:baseline;background-color:transparent;border:none;padding:0;margin:0 5px;gap:3px;transition:all 200ms;-moz-user-select:none;-webkit-user-select:none;-o-user-select:none;user-select:none}button.svelte-1uc1g2y:hover{opacity:1;color:var(--primary);transition:all 200ms}button.svelte-1uc1g2y:hover svg{stroke:var(--primary);transition:all 200ms}@media(max-width: 600px){button.svelte-1uc1g2y{display:none}}@media print{button.svelte-1uc1g2y{display:none}}",map:`{"version":3,"file":"DownloadData.svelte","sources":["DownloadData.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport { ExportToCsv } from 'export-to-csv';\\n\\timport { fade } from 'svelte/transition';\\n\\timport checkInputs from '@evidence-dev/component-utilities/checkInputs';\\n\\timport InlineError from '../../atoms/inputs/InlineError.svelte';\\n\\timport { toBoolean } from '../../utils.js';\\n\\n\\texport let data = undefined;\\n\\texport let queryID = undefined;\\n\\texport let text = 'Download';\\n\\texport let display = true;\\n\\t$: display = toBoolean(display);\\n\\tlet errors = [];\\n\\n\\tconst date = new Date();\\n\\tconst localISOTime = new Date(date.getTime() - date.getTimezoneOffset() * 60000)\\n\\t\\t.toISOString()\\n\\t\\t.slice(0, 19)\\n\\t\\t.replaceAll(':', '-');\\n\\n\\texport let downloadData = (data) => {\\n\\t\\ttry {\\n\\t\\t\\tcheckInputs(data);\\n\\t\\t} catch (e) {\\n\\t\\t\\terrors = [...errors, e.message];\\n\\t\\t\\treturn;\\n\\t\\t}\\n\\t\\tconst options = {\\n\\t\\t\\tfieldSeparator: ',',\\n\\t\\t\\tquoteStrings: '\\"',\\n\\t\\t\\tdecimalSeparator: '.',\\n\\t\\t\\tshowLabels: true,\\n\\t\\t\\tshowTitle: false,\\n\\t\\t\\tfilename: (queryID ?? 'evidence_download') + \` \${localISOTime}\`,\\n\\t\\t\\tuseTextFile: false,\\n\\t\\t\\tuseBom: true,\\n\\t\\t\\tuseKeysAsHeaders: true\\n\\t\\t};\\n\\n\\t\\tconst data_copy = JSON.parse(JSON.stringify(Array.from(data)));\\n\\n\\t\\tconst csvExporter = new ExportToCsv(options);\\n\\n\\t\\tcsvExporter.generateCsv(data_copy);\\n\\t};\\n<\/script>\\n\\n{#if errors.length > 0}\\n\\t<InlineError inputType=\\"DownloadData\\" height=\\"32\\" width=\\"160\\" error={errors} />\\n{:else if display}\\n\\t<div transition:fade|local={errors.length > 0 ? { duration: 0 } : { duration: 200 }}>\\n\\t\\t<button type=\\"button\\" aria-label={text} class={$$props.class} on:click={downloadData(data)}>\\n\\t\\t\\t<span>{text}</span>\\n\\t\\t\\t<slot>\\n\\t\\t\\t\\t<svg\\n\\t\\t\\t\\t\\twidth=\\"12\\"\\n\\t\\t\\t\\t\\theight=\\"12\\"\\n\\t\\t\\t\\t\\tviewBox=\\"0 0 24 24\\"\\n\\t\\t\\t\\t\\tfill=\\"none\\"\\n\\t\\t\\t\\t\\tstroke-width=\\"2\\"\\n\\t\\t\\t\\t\\tstroke-linecap=\\"round\\"\\n\\t\\t\\t\\t\\tstroke-linejoin=\\"round\\"\\n\\t\\t\\t\\t\\t><path d=\\"M3 15v4c0 1.1.9 2 2 2h14a2 2 0 0 0 2-2v-4M17 9l-5 5-5-5M12 12.8V2.5\\" /></svg\\n\\t\\t\\t\\t>\\n\\t\\t\\t</slot>\\n\\t\\t</button>\\n\\t</div>\\n{/if}\\n\\n<style>\\n\\tbutton :global(svg) {\\n\\t\\tstroke: var(--base-content);\\n\\t\\tmargin-top: auto;\\n\\t\\tmargin-bottom: auto;\\n\\t\\ttransition: stroke 200ms;\\n\\t}\\n\\n\\tbutton {\\n\\t\\tdisplay: flex;\\n\\t\\tcursor: pointer;\\n\\t\\tfont-family: var(--ui-font-family);\\n\\t\\tfont-size: 1em;\\n\\t\\tcolor: var(--base-content);\\n\\t\\topacity: 0.5;\\n\\t\\tjustify-items: flex-end;\\n\\t\\talign-items: baseline;\\n\\t\\tbackground-color: transparent;\\n\\t\\tborder: none;\\n\\t\\tpadding: 0;\\n\\t\\tmargin: 0 5px;\\n\\t\\tgap: 3px;\\n\\t\\ttransition: all 200ms;\\n\\t\\t-moz-user-select: none;\\n\\t\\t-webkit-user-select: none;\\n\\t\\t-o-user-select: none;\\n\\t\\tuser-select: none;\\n\\t}\\n\\n\\tbutton:hover {\\n\\t\\topacity: 1;\\n\\t\\tcolor: var(--primary);\\n\\t\\ttransition: all 200ms;\\n\\t}\\n\\n\\tbutton:hover :global(svg) {\\n\\t\\tstroke: var(--primary);\\n\\t\\ttransition: all 200ms;\\n\\t}\\n\\n\\t@media (max-width: 600px) {\\n\\t\\tbutton {\\n\\t\\t\\tdisplay: none;\\n\\t\\t}\\n\\t}\\n\\n\\t@media print {\\n\\t\\tbutton {\\n\\t\\t\\tdisplay: none;\\n\\t\\t}\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AA0EC,qBAAM,CAAS,GAAK,CACnB,MAAM,CAAE,IAAI,cAAc,CAAC,CAC3B,UAAU,CAAE,IAAI,CAChB,aAAa,CAAE,IAAI,CACnB,UAAU,CAAE,MAAM,CAAC,KACpB,CAEA,qBAAO,CACN,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,OAAO,CACf,WAAW,CAAE,IAAI,gBAAgB,CAAC,CAClC,SAAS,CAAE,GAAG,CACd,KAAK,CAAE,IAAI,cAAc,CAAC,CAC1B,OAAO,CAAE,GAAG,CACZ,aAAa,CAAE,QAAQ,CACvB,WAAW,CAAE,QAAQ,CACrB,gBAAgB,CAAE,WAAW,CAC7B,MAAM,CAAE,IAAI,CACZ,OAAO,CAAE,CAAC,CACV,MAAM,CAAE,CAAC,CAAC,GAAG,CACb,GAAG,CAAE,GAAG,CACR,UAAU,CAAE,GAAG,CAAC,KAAK,CACrB,gBAAgB,CAAE,IAAI,CACtB,mBAAmB,CAAE,IAAI,CACzB,cAAc,CAAE,IAAI,CACpB,WAAW,CAAE,IACd,CAEA,qBAAM,MAAO,CACZ,OAAO,CAAE,CAAC,CACV,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,UAAU,CAAE,GAAG,CAAC,KACjB,CAEA,qBAAM,MAAM,CAAS,GAAK,CACzB,MAAM,CAAE,IAAI,SAAS,CAAC,CACtB,UAAU,CAAE,GAAG,CAAC,KACjB,CAEA,MAAO,YAAY,KAAK,CAAE,CACzB,qBAAO,CACN,OAAO,CAAE,IACV,CACD,CAEA,OAAO,KAAM,CACZ,qBAAO,CACN,OAAO,CAAE,IACV,CACD"}`},qt=T((t,e,n,r)=>{let{data:o=void 0}=e,{queryID:a=void 0}=e,{text:s="Download"}=e,{display:i=!0}=e,A=[];const l=new Date,c=new Date(l.getTime()-l.getTimezoneOffset()*6e4).toISOString().slice(0,19).replaceAll(":","-");let{downloadData:y=g=>{try{yt(g)}catch(k){A=[...A,k.message];return}const M={fieldSeparator:",",quoteStrings:'"',decimalSeparator:".",showLabels:!0,showTitle:!1,filename:(a??"evidence_download")+` ${c}`,useTextFile:!1,useBom:!0,useKeysAsHeaders:!0},_=JSON.parse(JSON.stringify(Array.from(g)));new ot(M).generateCsv(_)}}=e;return e.data===void 0&&n.data&&o!==void 0&&n.data(o),e.queryID===void 0&&n.queryID&&a!==void 0&&n.queryID(a),e.text===void 0&&n.text&&s!==void 0&&n.text(s),e.display===void 0&&n.display&&i!==void 0&&n.display(i),e.downloadData===void 0&&n.downloadData&&y!==void 0&&n.downloadData(y),t.css.add(ht),i=st(i),`${A.length>0?`${G(at,"InlineError").$$render(t,{inputType:"DownloadData",height:"32",width:"160",error:A},{},{})}`:`${i?`<div><button type="button"${F("aria-label",s,0)} class="${f(rt(e.class),!0)+" svelte-1uc1g2y"}"><span>${f(s)}</span> ${r.default?r.default({}):' <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 15v4c0 1.1.9 2 2 2h14a2 2 0 0 0 2-2v-4M17 9l-5 5-5-5M12 12.8V2.5"></path></svg> '}</button></div>`:""}`}`}),Kt=T((t,e,n,r)=>{let{source:o}=e,{copyToClipboard:a=!1}=e,{language:s=void 0}=e;return e.source===void 0&&n.source&&o!==void 0&&n.source(o),e.copyToClipboard===void 0&&n.copyToClipboard&&a!==void 0&&n.copyToClipboard(a),e.language===void 0&&n.language&&s!==void 0&&n.language(s),`<div class="mt-2 mb-4 bg-base-200 border border-base-300 rounded-md px-3 py-2 relative group">${a?'<button class="absolute opacity-0 rounded p-1 group-hover:opacity-100 hover:bg-base-300/30 top-2 right-2 h-6 w-6 z-10 transition-all duration-200 ease-in-out text-base-content-muted active:bg-base-300/50"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" fill="currentColor" width="100%" height="100%" preserveAspectRatio="xMidYMid meet"><path d="M28,10V28H10V10H28m0-2H10a2,2,0,0,0-2,2V28a2,2,0,0,0,2,2H28a2,2,0,0,0,2-2V10a2,2,0,0,0-2-2Z"></path><path d="M4,18H2V4A2,2,0,0,1,4,2H18V4H4Z"></path></svg></button>':""} <pre class="overflow-auto pretty-scrollbar"><code class="${"language-"+f(s,!0)+" text-sm"}">${o?`${f(o)}`:`${r.default?r.default({}):""}`}</code></pre></div>`});export{Kt as C,qt as D,ct as a,Qt as b,yt as c,Ft as d,ft as e,Ct as f,jt as g,Rt as h,At as i,kt as j,Ut as k,lt as l,Gt as s};
