import{s as R,c as b,n as de}from"./utils.js";import{s as ce,g as fe,c as U,a as N,b as M,d as S,v as A,j as ue,h as $}from"./ssr.js";import{f as ve,c as Ce,I as me}from"./index7.js";import{w as j,o as Oe,m as q,b as ee,c as H,r as ge,T as he,U as G,d as ye,e as J,t as we,a as Q,n as te,p as Te,s as oe,i as ie,f as Se,l as ke}from"./VennDiagram.svelte_svelte_type_style_lang.js";import Ee from"chroma-js";import"dequal";import{w as X,d as xe}from"./index2.js";import{o as De,t as le,g as He,d as Le,u as Pe,a as _e,s as Me,c as Ve,r as Be,e as Ie,f as Ne,h as ne}from"./helpers.js";import"clsx";function Ae(u){const e=[],t=document.createTreeWalker(u,NodeFilter.SHOW_ELEMENT,{acceptNode:o=>o.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP});for(;t.nextNode();)e.push(t.currentNode);return e}const{name:Y}=ye("hover-card"),Ue={defaultOpen:!1,openDelay:1e3,closeDelay:100,positioning:{placement:"bottom"},arrowSize:8,closeOnOutsideClick:!0,forceVisible:!1,portal:void 0,closeOnEscape:!0,onOutsideClick:void 0},Ze=["trigger","content"];function We(u={}){const e={...Ue,...u},t=e.open??X(e.defaultOpen),o=De(t,e?.onOpenChange),i=j.writable(!1),r=j.writable(!1),n=X(!1),s=X(null),d=le(Oe(e,"ids")),{openDelay:c,closeDelay:v,positioning:g,arrowSize:m,closeOnOutsideClick:w,forceVisible:y,portal:O,closeOnEscape:k,onOutsideClick:D}=d,C=le({...He(Ze),...e.ids});let h=null,L;const P=j.derived(c,l=>()=>{h&&(window.clearTimeout(h),h=null),h=window.setTimeout(()=>{o.set(!0)},l)}),_=j.derived([v,r,i],([l,f,a])=>()=>{h&&(window.clearTimeout(h),h=null),!f&&!a&&(h=window.setTimeout(()=>{o.set(!1)},l))}),Z=q(Y("trigger"),{stores:[o,C.trigger,C.content],returned:([l,f,a])=>({role:"button","aria-haspopup":"dialog","aria-expanded":l,"data-state":l?"open":"closed","aria-controls":a,id:f}),action:l=>({destroy:ee(H(l,"pointerenter",a=>{G(a)||P.get()()}),H(l,"pointerleave",a=>{G(a)||_.get()()}),H(l,"focus",a=>{!ge(a.currentTarget)||!he(a.currentTarget)||P.get()()}),H(l,"blur",()=>_.get()()))})}),V=Le({open:o,forceVisible:y,activeTrigger:s}),W=q(Y("content"),{stores:[V,O,C.content],returned:([l,f,a])=>({hidden:l?void 0:!0,tabindex:-1,style:oe({"pointer-events":l?void 0:"none",opacity:l?1:0,userSelect:"text",WebkitUserSelect:"text"}),id:a,"data-state":l?"open":"closed","data-portal":Te(f)}),action:l=>{let f=te;const a=()=>{h&&window.clearTimeout(h)};let E=te;const B=J([V,s,g,w,O,k],([T,x,I,F,re,se])=>{E(),!(!T||!x)&&we().then(()=>{E(),E=Pe(l,{anchorElement:x,open:o,options:{floating:I,modal:{closeOnInteractOutside:F,onClose:()=>{o.set(!1),x.focus()},shouldCloseOnInteractOutside:K=>(D.get()?.(K),!(K.defaultPrevented||Q(x)&&x.contains(K.target))),open:T},portal:_e(l,re),focusTrap:null,escapeKeydown:se?void 0:null}}).destroy})});return f=ee(H(l,"pointerdown",T=>{const x=T.currentTarget,I=T.target;!Q(x)||!Q(I)||(x.contains(I)&&n.set(!0),i.set(!1),r.set(!0))}),H(l,"pointerenter",T=>{G(T)||P.get()()}),H(l,"pointerleave",T=>{G(T)||_.get()()}),H(l,"focusout",T=>{T.preventDefault()})),{destroy(){f(),E(),a(),B()}}}}),z=q(Y("arrow"),{stores:m,returned:l=>({"data-arrow":!0,style:oe({position:"absolute",width:`var(--arrow-size, ${l}px)`,height:`var(--arrow-size, ${l}px)`})})});return J([n],([l])=>{if(!ie||!l)return;const f=document.body,a=document.getElementById(C.content.get());if(!a)return;L=f.style.userSelect||f.style.webkitUserSelect;const E=a.style.userSelect||a.style.webkitUserSelect;return f.style.userSelect="none",f.style.webkitUserSelect="none",a.style.userSelect="text",a.style.webkitUserSelect="text",()=>{f.style.userSelect=L,f.style.webkitUserSelect=L,a.style.userSelect=E,a.style.webkitUserSelect=E}}),Se(()=>{const l=document.getElementById(C.trigger.get());l&&s.set(l)}),J([o],([l])=>{if(!ie||!l){i.set(!1);return}const f=()=>{n.set(!1),r.set(!1),Me(1).then(()=>{document.getSelection()?.toString()!==""&&i.set(!0)})};document.addEventListener("pointerup",f);const a=document.getElementById(C.content.get());return a?(Ae(a).forEach(B=>B.setAttribute("tabindex","-1")),()=>{document.removeEventListener("pointerup",f),i.set(!1),r.set(!1)}):void 0}),{ids:C,elements:{trigger:Z,content:W,arrow:z},states:{open:o},options:d}}function ae(){return{NAME:"link-preview",PARTS:["arrow","content","trigger"]}}function ze(u){const{NAME:e,PARTS:t}=ae(),o=Ve(e,t),i={...We({...Be(u),forceVisible:!0}),getAttrs:o};return ce(e,i),{...i,updateOption:Ie(i.options)}}function p(){const{NAME:u}=ae();return fe(u)}function Fe(u){const t={...{side:"bottom",align:"center"},...u},{options:{positioning:o}}=p();Ne(o)(t)}const Re=U((u,e,t,o)=>{let i,r,{open:n=void 0}=e,{onOpenChange:s=void 0}=e,{openDelay:d=700}=e,{closeDelay:c=300}=e,{closeOnOutsideClick:v=void 0}=e,{closeOnEscape:g=void 0}=e,{portal:m=void 0}=e,{onOutsideClick:w=void 0}=e;const{states:{open:y},updateOption:O,ids:k}=ze({defaultOpen:n,openDelay:d,closeDelay:c,closeOnOutsideClick:v,closeOnEscape:g,portal:m,onOutsideClick:w,onOpenChange:({next:C})=>(n!==C&&(s?.(C),n=C),C)}),D=xe([k.content,k.trigger],([C,h])=>({content:C,trigger:h}));return r=R(D,C=>i=C),e.open===void 0&&t.open&&n!==void 0&&t.open(n),e.onOpenChange===void 0&&t.onOpenChange&&s!==void 0&&t.onOpenChange(s),e.openDelay===void 0&&t.openDelay&&d!==void 0&&t.openDelay(d),e.closeDelay===void 0&&t.closeDelay&&c!==void 0&&t.closeDelay(c),e.closeOnOutsideClick===void 0&&t.closeOnOutsideClick&&v!==void 0&&t.closeOnOutsideClick(v),e.closeOnEscape===void 0&&t.closeOnEscape&&g!==void 0&&t.closeOnEscape(g),e.portal===void 0&&t.portal&&m!==void 0&&t.portal(m),e.onOutsideClick===void 0&&t.onOutsideClick&&w!==void 0&&t.onOutsideClick(w),n!==void 0&&y.set(n),O("openDelay",d),O("closeDelay",c),O("closeOnOutsideClick",v),O("closeOnEscape",g),O("portal",m),O("onOutsideClick",w),r(),`${o.default?o.default({ids:i}):""}`}),je=U((u,e,t,o)=>{let i,r=b(e,["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","id","side","align","sideOffset","alignOffset","collisionPadding","avoidCollisions","collisionBoundary","sameWidth","fitViewport","strategy","overlap","el"]),n,s,d,c,{transition:v=void 0}=e,{transitionConfig:g=void 0}=e,{inTransition:m=void 0}=e,{inTransitionConfig:w=void 0}=e,{outTransition:y=void 0}=e,{outTransitionConfig:O=void 0}=e,{asChild:k=!1}=e,{id:D=void 0}=e,{side:C="bottom"}=e,{align:h="center"}=e,{sideOffset:L=0}=e,{alignOffset:P=0}=e,{collisionPadding:_=8}=e,{avoidCollisions:Z=!0}=e,{collisionBoundary:V=void 0}=e,{sameWidth:W=!1}=e,{fitViewport:z=!1}=e,{strategy:l="absolute"}=e,{overlap:f=!1}=e,{el:a=void 0}=e;const{elements:{content:E},states:{open:B},ids:T,getAttrs:x}=p();c=R(E,F=>d=F),s=R(B,F=>n=F);const I=x("content");return ne(),e.transition===void 0&&t.transition&&v!==void 0&&t.transition(v),e.transitionConfig===void 0&&t.transitionConfig&&g!==void 0&&t.transitionConfig(g),e.inTransition===void 0&&t.inTransition&&m!==void 0&&t.inTransition(m),e.inTransitionConfig===void 0&&t.inTransitionConfig&&w!==void 0&&t.inTransitionConfig(w),e.outTransition===void 0&&t.outTransition&&y!==void 0&&t.outTransition(y),e.outTransitionConfig===void 0&&t.outTransitionConfig&&O!==void 0&&t.outTransitionConfig(O),e.asChild===void 0&&t.asChild&&k!==void 0&&t.asChild(k),e.id===void 0&&t.id&&D!==void 0&&t.id(D),e.side===void 0&&t.side&&C!==void 0&&t.side(C),e.align===void 0&&t.align&&h!==void 0&&t.align(h),e.sideOffset===void 0&&t.sideOffset&&L!==void 0&&t.sideOffset(L),e.alignOffset===void 0&&t.alignOffset&&P!==void 0&&t.alignOffset(P),e.collisionPadding===void 0&&t.collisionPadding&&_!==void 0&&t.collisionPadding(_),e.avoidCollisions===void 0&&t.avoidCollisions&&Z!==void 0&&t.avoidCollisions(Z),e.collisionBoundary===void 0&&t.collisionBoundary&&V!==void 0&&t.collisionBoundary(V),e.sameWidth===void 0&&t.sameWidth&&W!==void 0&&t.sameWidth(W),e.fitViewport===void 0&&t.fitViewport&&z!==void 0&&t.fitViewport(z),e.strategy===void 0&&t.strategy&&l!==void 0&&t.strategy(l),e.overlap===void 0&&t.overlap&&f!==void 0&&t.overlap(f),e.el===void 0&&t.el&&a!==void 0&&t.el(a),D&&T.content.set(D),i=d,Object.assign(i,I),n&&Fe({side:C,align:h,sideOffset:L,alignOffset:P,collisionPadding:_,avoidCollisions:Z,collisionBoundary:V,sameWidth:W,fitViewport:z,strategy:l,overlap:f}),s(),c(),`${k&&n?`${o.default?o.default({builder:i}):""}`:`${v&&n?`<div${N([S(i),S(r)],{})}${M("this",a,0)}>${o.default?o.default({builder:i}):""}</div>`:`${m&&y&&n?`<div${N([S(i),S(r)],{})}${M("this",a,0)}>${o.default?o.default({builder:i}):""}</div>`:`${m&&n?`<div${N([S(i),S(r)],{})}${M("this",a,0)}>${o.default?o.default({builder:i}):""}</div>`:`${y&&n?`<div${N([S(i),S(r)],{})}${M("this",a,0)}>${o.default?o.default({builder:i}):""}</div>`:`${n?`<div${N([S(i),S(r)],{})}${M("this",a,0)}>${o.default?o.default({builder:i}):""}</div>`:""}`}`}`}`}`}`}),Ge=U((u,e,t,o)=>{let i,r=b(e,["asChild","id","el"]),n,s,{asChild:d=!1}=e,{id:c=void 0}=e,{el:v=void 0}=e;const{elements:{trigger:g},ids:m,getAttrs:w}=p();s=R(g,O=>n=O),ne();const y=w("trigger");return e.asChild===void 0&&t.asChild&&d!==void 0&&t.asChild(d),e.id===void 0&&t.id&&c!==void 0&&t.id(c),e.el===void 0&&t.el&&v!==void 0&&t.el(v),c&&m.trigger.set(c),i=n,Object.assign(i,y),s(),`${d?`${o.default?o.default({builder:i}):""}`:` <a${N([S(i),S(r),S(y)],{})}${M("this",v,0)}>${o.default?o.default({builder:i}):""}</a>`}`}),nt={default:{a:{viewBox:"0 0 15 15",fill:"none"},path:[{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M4.93179 5.43179C4.75605 5.60753 4.75605 5.89245 4.93179 6.06819C5.10753 6.24392 5.39245 6.24392 5.56819 6.06819L7.49999 4.13638L9.43179 6.06819C9.60753 6.24392 9.89245 6.24392 10.0682 6.06819C10.2439 5.89245 10.2439 5.60753 10.0682 5.43179L7.81819 3.18179C7.73379 3.0974 7.61933 3.04999 7.49999 3.04999C7.38064 3.04999 7.26618 3.0974 7.18179 3.18179L4.93179 5.43179ZM10.0682 9.56819C10.2439 9.39245 10.2439 9.10753 10.0682 8.93179C9.89245 8.75606 9.60753 8.75606 9.43179 8.93179L7.49999 10.8636L5.56819 8.93179C5.39245 8.75606 5.10753 8.75606 4.93179 8.93179C4.75605 9.10753 4.75605 9.39245 4.93179 9.56819L7.18179 11.8182C7.35753 11.9939 7.64245 11.9939 7.81819 11.8182L10.0682 9.56819Z",fill:"currentColor"}]}},at={default:{a:{viewBox:"0 0 15 15",fill:"none"},path:[{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M11.4669 3.72684C11.7558 3.91574 11.8369 4.30308 11.648 4.59198L7.39799 11.092C7.29783 11.2452 7.13556 11.3467 6.95402 11.3699C6.77247 11.3931 6.58989 11.3355 6.45446 11.2124L3.70446 8.71241C3.44905 8.48022 3.43023 8.08494 3.66242 7.82953C3.89461 7.57412 4.28989 7.55529 4.5453 7.78749L6.75292 9.79441L10.6018 3.90792C10.7907 3.61902 11.178 3.53795 11.4669 3.72684Z",fill:"currentColor"}]}},rt={default:{a:{viewBox:"0 0 15 15",fill:"none"},path:[{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M3.13523 6.15803C3.3241 5.95657 3.64052 5.94637 3.84197 6.13523L7.5 9.56464L11.158 6.13523C11.3595 5.94637 11.6759 5.95657 11.8648 6.15803C12.0536 6.35949 12.0434 6.67591 11.842 6.86477L7.84197 10.6148C7.64964 10.7951 7.35036 10.7951 7.15803 10.6148L3.15803 6.86477C2.95657 6.67591 2.94637 6.35949 3.13523 6.15803Z",fill:"currentColor"}]}},st={default:{a:{viewBox:"0 0 15 15",fill:"none"},path:[{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M2 2.5C2 2.22386 2.22386 2 2.5 2H5.5C5.77614 2 6 2.22386 6 2.5C6 2.77614 5.77614 3 5.5 3H3V5.5C3 5.77614 2.77614 6 2.5 6C2.22386 6 2 5.77614 2 5.5V2.5ZM9 2.5C9 2.22386 9.22386 2 9.5 2H12.5C12.7761 2 13 2.22386 13 2.5V5.5C13 5.77614 12.7761 6 12.5 6C12.2239 6 12 5.77614 12 5.5V3H9.5C9.22386 3 9 2.77614 9 2.5ZM2.5 9C2.77614 9 3 9.22386 3 9.5V12H5.5C5.77614 12 6 12.2239 6 12.5C6 12.7761 5.77614 13 5.5 13H2.5C2.22386 13 2 12.7761 2 12.5V9.5C2 9.22386 2.22386 9 2.5 9ZM12.5 9C12.7761 9 13 9.22386 13 9.5V12.5C13 12.7761 12.7761 13 12.5 13H9.5C9.22386 13 9 12.7761 9 12.5C9 12.2239 9.22386 12 9.5 12H12V9.5C12 9.22386 12.2239 9 12.5 9Z",fill:"currentColor"}]}},Ke={default:{a:{viewBox:"0 0 15 15",fill:"none"},path:[{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M7.49991 0.876892C3.84222 0.876892 0.877075 3.84204 0.877075 7.49972C0.877075 11.1574 3.84222 14.1226 7.49991 14.1226C11.1576 14.1226 14.1227 11.1574 14.1227 7.49972C14.1227 3.84204 11.1576 0.876892 7.49991 0.876892ZM1.82707 7.49972C1.82707 4.36671 4.36689 1.82689 7.49991 1.82689C10.6329 1.82689 13.1727 4.36671 13.1727 7.49972C13.1727 10.6327 10.6329 13.1726 7.49991 13.1726C4.36689 13.1726 1.82707 10.6327 1.82707 7.49972ZM8.24992 4.49999C8.24992 4.9142 7.91413 5.24999 7.49992 5.24999C7.08571 5.24999 6.74992 4.9142 6.74992 4.49999C6.74992 4.08577 7.08571 3.74999 7.49992 3.74999C7.91413 3.74999 8.24992 4.08577 8.24992 4.49999ZM6.00003 5.99999H6.50003H7.50003C7.77618 5.99999 8.00003 6.22384 8.00003 6.49999V9.99999H8.50003H9.00003V11H8.50003H7.50003H6.50003H6.00003V9.99999H6.50003H7.00003V6.99999H6.50003H6.00003V5.99999Z",fill:"currentColor"}]}},dt={default:{a:{viewBox:"0 0 15 15",fill:"none"},path:[{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M10 6.5C10 8.433 8.433 10 6.5 10C4.567 10 3 8.433 3 6.5C3 4.567 4.567 3 6.5 3C8.433 3 10 4.567 10 6.5ZM9.30884 10.0159C8.53901 10.6318 7.56251 11 6.5 11C4.01472 11 2 8.98528 2 6.5C2 4.01472 4.01472 2 6.5 2C8.98528 2 11 4.01472 11 6.5C11 7.56251 10.6318 8.53901 10.0159 9.30884L12.8536 12.1464C13.0488 12.3417 13.0488 12.6583 12.8536 12.8536C12.6583 13.0488 12.3417 13.0488 12.1464 12.8536L9.30884 10.0159Z",fill:"currentColor"}]}},qe=U((u,e,t,o)=>{let i=b(e,["class","transition","transitionConfig","align","sideOffset"]),{class:r=void 0}=e,{transition:n=ve}=e,{transitionConfig:s=void 0}=e,{align:d="center"}=e,{sideOffset:c=4}=e;return e.class===void 0&&t.class&&r!==void 0&&t.class(r),e.transition===void 0&&t.transition&&n!==void 0&&t.transition(n),e.transitionConfig===void 0&&t.transitionConfig&&s!==void 0&&t.transitionConfig(s),e.align===void 0&&t.align&&d!==void 0&&t.align(d),e.sideOffset===void 0&&t.sideOffset&&c!==void 0&&t.sideOffset(c),`${A(je,"HoverCardPrimitive.Content").$$render(u,Object.assign({},{transition:n},{transitionConfig:s},{align:d},{sideOffset:c},{class:Ce("z-50 my-2 rounded-md border bg-base-100 shadow-md outline-none",r)},i),{},{default:()=>`${o.default?o.default({}):""}`})}`}),Je=Re,Qe=Ge,Xe=U((u,e,t,o)=>{let{align:i="center"}=e,{side:r="bottom"}=e,{alignOffset:n=0}=e,{sideOffset:s=4}=e,{openDelay:d=0}=e,{closeDelay:c=0}=e,{open:v=!1}=e;return e.align===void 0&&t.align&&i!==void 0&&t.align(i),e.side===void 0&&t.side&&r!==void 0&&t.side(r),e.alignOffset===void 0&&t.alignOffset&&n!==void 0&&t.alignOffset(n),e.sideOffset===void 0&&t.sideOffset&&s!==void 0&&t.sideOffset(s),e.openDelay===void 0&&t.openDelay&&d!==void 0&&t.openDelay(d),e.closeDelay===void 0&&t.closeDelay&&c!==void 0&&t.closeDelay(c),e.open===void 0&&t.open&&v!==void 0&&t.open(v),`${A(Je,"HoverCard").$$render(u,{open:v,openDelay:d,closeDelay:c},{},{default:()=>`${A(Qe,"HoverCardTrigger").$$render(u,{},{},{default:()=>`${o.trigger?o.trigger({}):""}`})} ${A(qe,"HoverCardContent").$$render(u,{align:i,side:r,alignOffset:n,sideOffset:s},{},{default:()=>`${o.content?o.content({}):""}`})}`})}`}),ct=U((u,e,t,o)=>{let i,r,n,s=de,d=()=>(s(),s=R(i,k=>n=k),i),c=Ke,{description:v=""}=e,{size:g=4}=e,{className:m=void 0}=e;const{resolveColor:w}=ke();let{color:y="base-content-muted"}=e,O=!1;return e.description===void 0&&t.description&&v!==void 0&&t.description(v),e.size===void 0&&t.size&&g!==void 0&&t.size(g),e.className===void 0&&t.className&&m!==void 0&&t.className(m),e.color===void 0&&t.color&&y!==void 0&&t.color(y),d(i=w(y)),r=Ee(n).css(),s(),`${A(Xe,"HoverCard").$$render(u,{open:O,align:"start",side:"right",alignOffset:-8,sideOffset:4},{},{content:()=>`<div slot="content" class="bg-base-100 p-2 rounded-md text-base-content text-xs max-w-52"><p class="leading-relaxed text-pretty">${$(v)}</p></div>`,trigger:()=>`<span slot="trigger" class="${"inline-block align-middle pb-0.5 pr-1 leading-4 w-fit "+$(m,!0)+" cursor-pointer"}" role="button"${M("aria-expanded",O,0)} aria-label="Toggle tooltip" tabindex="0"${ue({"--textColor":r})}>${o.handle?o.handle({}):` ${A(me,"Icon").$$render(u,{src:c,class:"w-"+g+" h-"+g+" text-(--textColor)"},{},{})} `}</span>`})}`});export{rt as C,st as E,ct as I,dt as M,at as a,nt as b};
