import{d as O,w as N,r as R,a as Se}from"./index2.js";import"deep-object-diff";import"./index5.js";import it from"lodash/merge.js";import{z as k}from"zod";import{r as ct,g as S}from"./utils.js";import{k as fe,l as ut,o as ft,n as Ue,s as te,g as V}from"./ssr.js";import{nanoid as dt}from"nanoid/non-secure";import"dequal";import{tv as Be}from"tailwind-variants";import"clsx";import"thememirror";import{closeBrackets as mt,autocompletion as pt,closeBracketsKeymap as bt,completionKeymap as gt}from"@codemirror/autocomplete";import{history as yt,defaultKeymap as ht,historyKeymap as Ct}from"@codemirror/commands";import{indentOnInput as St,syntaxHighlighting as Tt,bracketMatching as Et,defaultHighlightStyle as vt,foldKeymap as At}from"@codemirror/language";import{lintKeymap as xt}from"@codemirror/lint";import{drawSelection as _t,dropCursor as wt,keymap as kt}from"@codemirror/view";import{highlightSelectionMatches as Rt,searchKeymap as It}from"@codemirror/search";import"@uwdata/mosaic-sql";import{l as Mt}from"./Query.js";import"@evidence-dev/universal-sql/client-duckdb";import"@tidyjs/tidy";import re from"ssf";import"export-to-csv";import"./exports.js";import Z from"chroma-js";import"echarts";import{o as Ot}from"./ssr2.js";const z=[],Re=[];let le=[];const Ie=[],Ke=Promise.resolve();let he=!1;function Ft(){he||(he=!0,Ke.then(Pt))}function oe(){return Ft(),Ke}function Nt(e){le.push(e)}const de=new Set;let K=0;function Pt(){if(K!==0)return;const e=ut;do{try{for(;K<z.length;){const t=z[K];K++,fe(t),Dt(t.$$)}}catch(t){throw z.length=0,K=0,t}for(fe(null),z.length=0,K=0;Re.length;)Re.pop()();for(let t=0;t<le.length;t+=1){const r=le[t];de.has(r)||(de.add(r),r())}le.length=0}while(z.length);for(;Ie.length;)Ie.pop()();he=!1,de.clear(),fe(e)}function Dt(e){if(e.fragment!==null){e.update(),ct(e.before_update);const t=e.dirty;e.dirty=[-1],e.fragment&&e.fragment.p(e.ctx,t),e.after_update.forEach(Nt)}}function Lt(e){return Object.keys(e).reduce((t,r)=>e[r]===void 0?t:t+`${r}:${e[r]};`,"")}function an(e){return e?!0:void 0}Lt({position:"absolute",opacity:0,"pointer-events":"none",margin:0,transform:"translateX(-100%)"});function ln(e){if(e!==null)return""}function Me(e){function t(r){return r(e),()=>{}}return{subscribe:t}}function sn(e){if(!Wt)return null;const t=document.querySelector(`[data-melt-id="${e}"]`);return Te(t)?t:null}const ne=e=>new Proxy(e,{get(t,r,o){return Reflect.get(t,r,o)},ownKeys(t){return Reflect.ownKeys(t).filter(r=>r!=="action")}}),Oe=e=>typeof e=="function";$t("empty");function $t(e,t){const{stores:r,action:o,returned:n}=t??{},a=(()=>{if(r&&n)return O(r,m=>{const d=n(m);if(Oe(d)){const c=(...g)=>ne({...d(...g),[`data-melt-${e}`]:"",action:o??L});return c.action=o??L,c}return ne({...d,[`data-melt-${e}`]:"",action:o??L})});{const d=n?.();if(Oe(d)){const c=(...g)=>ne({...d(...g),[`data-melt-${e}`]:"",action:o??L});return c.action=o??L,Me(c)}return Me(ne({...d,[`data-melt-${e}`]:"",action:o??L}))}})(),i=o??(()=>{});return i.subscribe=a.subscribe,i}function cn(e){const t=a=>a?`${e}-${a}`:e,r=a=>`data-melt-${e}${a?`-${a}`:""}`,o=a=>`[data-melt-${e}${a?`-${a}`:""}]`;return{name:t,attribute:r,selector:o,getEl:a=>document.querySelector(o(a))}}const Wt=typeof document<"u",Ut=e=>typeof e=="function";function un(e){return e instanceof Element}function Te(e){return e instanceof HTMLElement}function fn(e){const t=e.getAttribute("aria-disabled"),r=e.getAttribute("disabled"),o=e.hasAttribute("data-disabled");return!!(t==="true"||r!==null||o)}function dn(e){return e.pointerType==="touch"}function mn(e){return e.matches(":focus-visible")}function Bt(e){return e!==null&&typeof e=="object"}function Kt(e){return Bt(e)&&"subscribe"in e}function Gt(...e){return(...t)=>{for(const r of e)typeof r=="function"&&r(...t)}}function L(){}function Ge(e,t,r,o){const n=Array.isArray(t)?t:[t];return n.forEach(a=>e.addEventListener(a,r,o)),()=>{n.forEach(a=>e.removeEventListener(a,r,o))}}function pn(e,t,r,o){const n=Array.isArray(t)?t:[t];if(typeof r=="function"){const a=Vt(i=>r(i));return n.forEach(i=>e.addEventListener(i,a,o)),()=>{n.forEach(i=>e.removeEventListener(i,a,o))}}return()=>void 0}function Ht(e){const t=e.currentTarget;if(!Te(t))return null;const r=new CustomEvent(`m-${e.type}`,{detail:{originalEvent:e},cancelable:!0});return t.dispatchEvent(r),r}function Vt(e){return t=>{if(!Ht(t)?.defaultPrevented)return e(t)}}const bn=e=>{try{Ot(e)}catch{return e}},jt=e=>{try{ft(e)}catch{return e}};function Yt(e,...t){const r={};for(const o of Object.keys(e))t.includes(o)||(r[o]=e[o]);return r}function He(e){return{...e,get:()=>S(e)}}He.writable=function(e){const t=N(e);let r=e;return{subscribe:t.subscribe,set(o){t.set(o),r=o},update(o){const n=o(r);t.set(n),r=n},get(){return r}}};He.derived=function(e,t){const r=new Map,o=()=>{const a=Array.isArray(e)?e.map(i=>i.get()):e.get();return t(a)};return{get:o,subscribe:a=>{const i=[];return(Array.isArray(e)?e:[e]).forEach(d=>{i.push(d.subscribe(()=>{a(o())}))}),a(o()),r.set(a,i),()=>{const d=r.get(a);if(d)for(const c of d)c();r.delete(a)}}}};const D={ARROW_DOWN:"ArrowDown",ARROW_LEFT:"ArrowLeft",ARROW_RIGHT:"ArrowRight",ARROW_UP:"ArrowUp",END:"End",ENTER:"Enter",ESCAPE:"Escape",HOME:"Home",PAGE_DOWN:"PageDown",PAGE_UP:"PageUp",SPACE:" ",TAB:"Tab"},zt=[D.ARROW_DOWN,D.PAGE_UP,D.HOME],qt=[D.ARROW_UP,D.PAGE_DOWN,D.END],gn=[...zt,...qt],yn=[D.ENTER,D.SPACE];function Xt(e,t){let r;const o=O(e,a=>{r?.(),r=t(a)}).subscribe(L),n=()=>{o(),r?.()};return jt(n),n}R(void 0,e=>{function t(o){e(o),e(void 0)}return Ge(document,"pointerup",t,{passive:!1,capture:!0})});const Jt=R(void 0,e=>{function t(o){o&&o.key===D.ESCAPE&&e(o),e(void 0)}return Ge(document,"keydown",t,{passive:!1})}),hn=(e,t={})=>{let r=L;function o(n={}){r();const a={enabled:!0,...n},i=Kt(a.enabled)?a.enabled:R(a.enabled);r=Gt(Jt.subscribe(m=>{if(!m||!S(i))return;const d=m.target;if(!(!Te(d)||d.closest("[data-escapee]")!==e)){if(m.preventDefault(),a.ignore){if(Ut(a.ignore)){if(a.ignore(m))return}else if(Array.isArray(a.ignore)&&a.ignore.length>0&&a.ignore.some(c=>c&&d===c))return}a.handler?.(m)}}),Xt(i,m=>{m?e.dataset.escapee="":delete e.dataset.escapee}))}return o(t),{update:o,destroy(){e.removeAttribute("data-escapee"),r()}}};R(!1),R(!1),R(void 0);const Zt={isDateDisabled:void 0,isDateUnavailable:void 0,value:void 0,preventDeselect:!1,numberOfMonths:1,pagedNavigation:!1,weekStartsOn:0,fixedWeeks:!1,calendarLabel:"Event Date",locale:"en",minValue:void 0,maxValue:void 0,disabled:!1,readonly:!1,weekdayFormat:"narrow"};({...Yt(Zt,"isDateDisabled","isDateUnavailable","value","locale","disabled","readonly","minValue","maxValue","weekdayFormat")});const Ve={appearance:{default:"system",switcher:!0},theme:{colors:{primary:{light:"#2563eb",dark:"#3b82f6"},accent:{light:"#c2410c",dark:"#fdba74"},"base-100":{light:"#ffffff",dark:"#09090b"},info:{light:"#0284c7",dark:"#38bdf8"},positive:{light:"#16a34a",dark:"#4ade80"},warning:{light:"#f8c900",dark:"#fbbf24"},negative:{light:"#dc2626",dark:"#f87171"},"primary-content":{light:"#f6f8fb",dark:"#030609"},"accent-content":{light:"#fbf7f6",dark:"#090603"},"base-200":{light:"#f7f7f7",dark:"#111113"},"base-300":{light:"#d6d6d6",dark:"#29292b"},"base-heading":{light:"#060606",dark:"#f8f8f9"},"base-content":{light:"#2c2c2c",dark:"#cacacb"},"base-content-muted":{light:"#717171",dark:"#7b7b7c"},"info-content":{light:"#030709",dark:"#030809"},"positive-content":{light:"#040906",dark:"#040906"},"negative-content":{light:"#fbf6f6",dark:"#090303"},"warning-content":{light:"#0a0803",dark:"#090803"}},colorPalettes:{default:{light:["#236aa4","#45a1bf","#a5cdee","#8dacbf","#85c7c6","#d2c6ac","#f4b548","#8f3d56","#71b9f4","#46a485"],dark:["#236aa4","#45a1bf","#a5cdee","#8dacbf","#85c7c6","#d2c6ac","#f4b548","#8f3d56","#71b9f4","#46a485"]}},colorScales:{default:{light:["#ADD8E6","#00008B"],dark:["#ADD8E6","#00008B"]}}}},v={light:{colors:{primary:"#2563eb",accent:"#c2410c","base-100":"#ffffff",info:"#0284c7",positive:"#16a34a",warning:"#f8c900",negative:"#dc2626","primary-content":"#f6f8fb","accent-content":"#fbf7f6","base-200":"#f7f7f7","base-300":"#d6d6d6","base-heading":"#060606","base-content":"#2c2c2c","base-content-muted":"#717171","info-content":"#030709","positive-content":"#040906","negative-content":"#fbf6f6","warning-content":"#0a0803"},colorPalettes:{default:["#236aa4","#45a1bf","#a5cdee","#8dacbf","#85c7c6","#d2c6ac","#f4b548","#8f3d56","#71b9f4","#46a485"]},colorScales:{default:["#ADD8E6","#00008B"]}},dark:{colors:{primary:"#3b82f6",accent:"#fdba74","base-100":"#09090b",info:"#38bdf8",positive:"#4ade80",warning:"#fbbf24",negative:"#f87171","primary-content":"#030609","accent-content":"#090603","base-200":"#111113","base-300":"#29292b","base-heading":"#f8f8f9","base-content":"#cacacb","base-content-muted":"#7b7b7c","info-content":"#030809","positive-content":"#040906","negative-content":"#090303","warning-content":"#090803"},colorPalettes:{default:["#236aa4","#45a1bf","#a5cdee","#8dacbf","#85c7c6","#d2c6ac","#f4b548","#8f3d56","#71b9f4","#46a485"]},colorScales:{default:["#ADD8E6","#00008B"]}}},Fe=e=>{if(!Z.valid(e))return;const t=Z(e),r=Z("white"),o=Z("black"),n=t.luminance(),a=Math.abs(n-r.luminance()),i=o.luminance()+a;return t.set("hsl.l",.75*i).saturate(.2).hex()},ee=Symbol("InputStore"),Q=e=>typeof e!="object"||e===null?!1:"subscribe"in e,Qt=e=>Q(e)?"set"in e&&"update"in e:!1,Cn=e=>{if(!Qt(e))throw console.error({InputStoreValue:e}),new Error("InputStore must be a writable store");if(Ue().has(ee)){const t=V(ee);return t.set(S(e)),t}else return te(ee,e),e},Sn=()=>Ue().has(ee)?V(ee):(console.warn("InputStoreKey not found in context. Did you forget to call ensureInputContext?"),N({})),{default:me,switcher:Ne}=Ve.appearance,er=()=>R("light",r=>{});class H{#e;get systemTheme(){return this.#e}#r;get selectedAppearance(){return Se(this.#r)}#t;get activeAppearance(){return this.#t}#o;get theme(){return this.#o}get themesConfig(){return Ve}constructor(){this.#e=er(),this.#r=Mt("evidence-theme",me,{serialize:t=>t,deserialize:t=>Ne&&["system","light","dark"].includes(t)?t:me}),this.#t=O([this.#e,this.#r],([t,r])=>r==="system"?t:r),this.#o=O(this.#t,t=>v[t])}syncThemeAttribute=t=>{const r=()=>{Array.from(t.classList.values()).forEach(i=>{i.startsWith("theme-")&&t.classList.remove(i)})};let o=!1;const n=this.#t.subscribe(i=>{requestAnimationFrame(()=>{o=!0,r(),t.classList.add(`theme-${i}`),requestAnimationFrame(()=>o=!1)})}),a=new MutationObserver(i=>{if(o)return;const d=[...i[0].target.classList.values().filter(f=>f.startsWith("theme-"))];if(d.length===0)return;const c=d[0].replace("theme-","");if(!c||!["light","dark"].includes(c))return;const g=S(this.#t);c!==g&&this.#r.set(c)});return a.observe(t,{attributeFilter:["class"]}),()=>{n(),a.disconnect()}};setAppearance=t=>{this.#r.set(t)};cycleAppearance=()=>{Ne&&this.#r.update(t=>{switch(t){case"system":return"light";case"light":return"dark";case"dark":default:return"system"}})};static#n=(t,r)=>{if(typeof t=="string"){const o=v.light.colors[t.trim()],n=v.dark.colors[t.trim()];if(r==="light")return o??t;if(r==="dark")return n??Fe(o??t)??t}if(tr(t)){const[o,n]=t,a=v.light.colors[o.trim()],i=n?v.dark.colors[n?.trim()]??n:void 0;if(r==="light")return a??o;if(r==="dark")return i??Fe(a??o)??n}};resolveColor=t=>Q(t)?t:O(this.#t,r=>H.#n(t,r));resolveColorsObject=t=>Q(t)?t:t?O(this.#t,r=>Object.fromEntries(Object.entries(t).map(([o,n])=>[o,H.#n(n,r)]))):R(void 0);resolveColorPalette=t=>Q(t)?t:typeof t=="string"?O(this.#o,r=>r.colorPalettes[t.trim()]):Array.isArray(t)?O(this.#t,r=>t.map(o=>H.#n(o,r))):R(void 0);resolveColorScale=t=>Q(t)?t:typeof t=="string"?O(this.#o,r=>{const o=r.colorScales[t.trim()];if(o)return o;const n=r.colors[t.trim()];if(n)return[r.colors["base-100"],n];if(Z.valid(t))return[r.colors["base-100"],t]}):Array.isArray(t)?O(this.#t,r=>t.map(o=>H.#n(o,r))):R(void 0)}const Pe=Symbol("__EvidenceThemeStores__"),Tn=()=>{let e=V(Pe);return e||(e=new H,te(Pe,e)),e},tr=e=>Array.isArray(e)&&(e.length===1||e.length===2)&&e.every(t=>typeof t=="string");class rr extends Error{constructor(t,r,o,n){super(t,o),this.context=Array.isArray(r)?r:[r??""],r||(this.context=[]),this.#e=n,Array.isArray(this.context)||(this.context=[])}context=[];#e;set metadata(t){this.#e=t}get metadata(){return this.#e||(this.#e={}),this.#e}}function or(e){return(...t)=>Promise.resolve(e(...t))}const pe=(e,t=200)=>{const r=[],o=or(()=>{e([...r]),r.length=0});return(...n)=>(r.push(...n),o())},nr={},ar=()=>{if(typeof process<"u")return!!process.env.EVIDENCE_STRICT_MODE;if(typeof nr<"u")return!1;throw new rr("Unable to identify if strict mode is enabled",["process is undefined","import.meta.env is undefined"])},lr=k.object({label:k.union([k.string(),k.number()]).optional().nullable(),value:k.union([k.string(),k.number(),k.null()]),idx:k.number().optional(),selected:k.boolean().optional(),__auto:k.boolean().optional(),__removeOnDeselect:k.boolean().optional()}),sr=e=>lr.safeParse(e).success,ir={multiselect:!1,initialOptions:[],defaultValues:[],noDefault:!1},En=(e={})=>{const t=it({},ir,e),r=N(q(t.initialOptions??[])),o=f=>f.filter(h=>h.selected),n=O(r,f=>o(f),o(t.initialOptions??[]));let a=!1;r.update=f=>{const h=f(S(r));r.set(q(h,a))};const i=Array.isArray(t.defaultValues)?t.defaultValues:[t.defaultValues],m=new Set(i);!t.multiselect&&m.size>1&&(m.clear(),i?.length&&m.add(i[0]),console.debug("Single-select dropdowns only accept one default value.")),(t.initialOptions?.length??0)>0&&m.clear();let d=!t.multiselect&&!t.noDefault&&i?.length===0&&!t.initialOptions?.some(f=>f.selected),c=t.multiselect&&t.selectAllByDefault&&!t.initialOptions?.length,g=!1;return{addOptions:pe((...f)=>{if(g)return;const h=q(f.flat().filter(T=>typeof T=="object"&&T!==null),a);r.update(T=>(h.forEach(p=>{if(!sr(p))return;d&&(p.selected=!0,d=!1),p.value!==null&&m.has(p.value)&&(p.selected=!0,m.delete(p.value)),"__auto"in p||(p.__auto=!1),"selected"in p||(p.selected=!1),"idx"in p||(p.idx=-1);const A=T.find(_=>ae(_,p));c&&(p.selected=!0),A&&A.__removeOnDeselect&&(A.__removeOnDeselect=!1),A||T.push(p)}),T)),setTimeout(()=>{c=!1},0)},100),removeOptions:pe((...f)=>{if(g)return;const h=f.flat();r.update(T=>T.reduce((p,A)=>{if(h.find(_=>ae(_,A)))if(A.selected)A.__removeOnDeselect=!0;else return p;return p.push(A),p},[]))},100),toggleSelected:pe((...f)=>{if(g)return;const h=f.flat();r.update(T=>{if(t.multiselect)return T.reduce((p,A)=>(h.find(_=>ae(_,A))&&(A.selected=!A.selected),p.push(A),p),[]);{T.forEach(_=>_.selected=!1);const p=h.at(-1);return T.reduce((_,W)=>(p&&ae(W,p)&&(W.selected=!0),_.push(W),_),[])}})},100),selectAll:()=>{g||r.update(f=>f.map(h=>({...h,selected:!0})))},deselectAll:()=>{g||r.update(f=>f.map(h=>({...h,selected:!1})))},options:Se(r),selectedOptions:n,pauseSorting:()=>{g||(a=!0)},resumeSorting:()=>{g||(a=!1,r.set(q(S(r))))},forceSort:()=>{g||r.set(q(S(r)))},destroy:()=>g=!0}},q=(e,t=!1)=>{e=e.filter(o=>!(o.__removeOnDeselect&&!o.selected));const r=new Set;return e=e.reduce((o,n)=>(r.has(De(n))||(r.add(De(n)),o.push(n)),o),[]),t||(e=e.sort((o,n)=>{if(o.selected&&!n.selected)return-1;if(n.selected&&!o.selected||o.__auto&&!n.__auto)return 1;if(n.__auto&&!o.__auto)return-1;if(o.idx!==n.idx)return(o.idx??0)-(n.idx??0);if(o.label===null&&n.label!==null)return 1;if(n.label===null&&o.label!==null)return-1;if(o.label===null&&n.label===null)return 0;if(typeof o.label=="number"&&typeof n.label=="number"&&o.label!==n.label)return o.label-n.label;if(typeof o.label<"u"&&o.label!==null&&typeof n.label<"u"&&n.label!==null){const a=o.label.toString().localeCompare(n.label.toString());if(a!==0)return a}if(typeof o.value<"u"&&o.value!==null&&typeof n.value<"u"&&n.value!==null){const a=o.value.toString().localeCompare(n.value.toString());if(a!==0)return a}return 0})),e},De=e=>String(e.value)+String(e.label),ae=(e,t)=>e.value===t.value&&e.label===t.label;function je(e,t=JSON.parse){try{return t(sessionStorage[e])}catch{}}const cr="sveltekit:snapshot",ur="sveltekit:scroll";je(ur);je(cr);const Le=1,fr=.9,dr=.8,mr=.17,be=.1,ge=.999,pr=.9999,br=.99,gr=/[\\/_+.#"@[({&]/,yr=/[\\/_+.#"@[({&]/g,hr=/[\s-]/,Ye=/[\s-]/g;function Ce(e,t,r,o,n,a,i){if(a===t.length)return n===e.length?Le:br;const m=`${n},${a}`;if(i[m]!==void 0)return i[m];const d=o.charAt(a);let c=r.indexOf(d,n),g=0,f,h,T,p;for(;c>=0;)f=Ce(e,t,r,o,c+1,a+1,i),f>g&&(c===n?f*=Le:gr.test(e.charAt(c-1))?(f*=dr,T=e.slice(n,c-1).match(yr),T&&n>0&&(f*=Math.pow(ge,T.length))):hr.test(e.charAt(c-1))?(f*=fr,p=e.slice(n,c-1).match(Ye),p&&n>0&&(f*=Math.pow(ge,p.length))):(f*=mr,n>0&&(f*=Math.pow(ge,c-n))),e.charAt(c)!==t.charAt(a)&&(f*=pr)),(f<be&&r.charAt(c-1)===o.charAt(a+1)||o.charAt(a+1)===o.charAt(a)&&r.charAt(c-1)!==o.charAt(a))&&(h=Ce(e,t,r,o,c+1,a+2,i),h*be>f&&(f=h*be)),f>g&&(g=f),c=r.indexOf(d,c+1);return i[m]=g,g}function $e(e){return e.toLowerCase().replace(Ye," ")}function Cr(e,t){return Ce(e,t,$e(e),$e(t),0,0,{})}const vn=typeof document<"u";function U(e){return e instanceof HTMLElement}function An(e){return e instanceof HTMLInputElement}function ze(e){return e===void 0}function P(){return dt(10)}const X={ARROW_DOWN:"ArrowDown",ARROW_UP:"ArrowUp",END:"End",ENTER:"Enter",HOME:"Home"};function Sr(e,...t){const r={};for(const o of Object.keys(e))t.includes(o)||(r[o]=e[o]);return r}function qe(e){const t={};for(const r in e){const o=e[r];o!==void 0&&(t[r]=o)}return t}function Tr(e){const t={};return Object.keys(e).forEach(r=>{const o=r,n=e[o];t[o]=N(n)}),t}const Xe="Command",Je="CommandState",Ze="CommandGroup",Er="[data-cmdk-list-sizer]",J="[data-cmdk-group]",We="[data-cmdk-group-items]",vr="[data-cmdk-group-heading]",Ar="[data-cmdk-item]",ye=`${Ar}:not([aria-disabled="true"])`,G="data-value",Qe=(e,t)=>Cr(e,t);function xn(){return V(Xe)}function _n(){return V(Je)}function wn(e){const t=P();return te(Ze,{id:t,alwaysRender:ze(e)?!1:e}),{id:t}}function kn(){const e=V(Ze);if(e)return e}function xr(e){const t={search:"",value:"",filtered:{count:0,items:new Map,groups:new Set}};return N(e?{...t,...qe(e)}:t)}const _r={label:"Command menu",shouldFilter:!0,loop:!1,onValueChange:void 0,value:void 0,filter:Qe,ids:{root:P(),list:P(),label:P(),input:P()}};function Rn(e){const t={root:P(),list:P(),label:P(),input:P(),...e.ids},r={..._r,...qe(e)},o=e.state??xr({value:r.value}),n=N(new Set),a=N(new Map),i=N(new Map),m=N(null),d=Tr(Sr(r,"value","ids")),{shouldFilter:c,loop:g,filter:f,label:h}=d,T={value:(l,u)=>{u!==S(i).get(l)&&(i.update(s=>(s.set(l,u),s)),o.update(s=>(s.filtered.items.set(l,Ae(u,s.search)),_(s,S(c)))))},item:(l,u)=>(n.update(s=>s.add(l)),u&&a.update(s=>(s.has(u)?s.get(u)?.add(l):s.set(u,new Set([l])),s)),o.update(s=>{const b=S(c),y=A(s,b),I=_(y,b);if(!I.value){const w=W();I.value=w??""}return I}),()=>{i.update(s=>(s.delete(l),s)),n.update(s=>(s.delete(l),s)),o.update(s=>{s.filtered.items.delete(l);const b=Y(),y=A(s);return b?.getAttribute("id")===l&&(y.value=W()??""),s})}),group:l=>(a.update(u=>(u.has(l)||u.set(l,new Set),u)),()=>{i.update(u=>(u.delete(l),u)),a.update(u=>(u.delete(l),u))}),filter:()=>S(c),label:S(h)||e["aria-label"]||"",commandEl:m,ids:t,updateState:p};function p(l,u,s){const b=S(c);o.update(y=>(Object.is(y[l],u)||(y[l]=u,l==="search"?(y=A(y,b),y=_(y,b),oe().then(()=>o.update(M=>(M.value=W()??"",M)))):l==="value"&&(e.onValueChange?.(y.value),s||oe().then(()=>nt()))),y))}function A(l,u){const s=u??S(c),b=S(n),y=S(i);if(!l.search||!s)return l.filtered.count=b.size,l;l.filtered.groups=new Set;let I=0;for(const w of b){const M=y.get(w),E=Ae(M,l.search);l.filtered.items.set(w,E),E>0&&I++}for(const[w,M]of S(a))for(const E of M){const x=l.filtered.items.get(E);x&&x>0&&l.filtered.groups.add(w)}return l.filtered.count=I,l}function _(l,u){const s=u??S(c);if(!l.search||!s)return l;const b=l.filtered.items,y=[],I=S(a);l.filtered.groups.forEach(E=>{const x=I.get(E);if(!x)return;let F=0;x.forEach(ue=>{const ke=b.get(ue);ze(ke)||(F=Math.max(ke,F))}),y.push([E,F])});const w=document.getElementById(t.root);if(!w)return l;const M=w.querySelector(Er);return j(w).sort((E,x)=>{const F=E.getAttribute(G)??"",ue=x.getAttribute(G)??"";return(b.get(F)??0)-(b.get(ue)??0)}).forEach(E=>{const x=E.closest(We),F=E.closest(`${We} > *`);if(U(x))if(E.parentElement===x)x.appendChild(E);else{if(!U(F))return;x.appendChild(F)}else{if(!U(M))return;if(E.parentElement===M)M.appendChild(E);else{if(!U(F))return;M.appendChild(F)}}}),y.sort((E,x)=>x[1]-E[1]).forEach(E=>{const x=w.querySelector(`${J}[${G}="${E[0]}"]`);U(x)&&x.parentElement?.appendChild(x)}),l}function W(){const l=j().find(s=>!s.ariaDisabled);if(!l)return;const u=l.getAttribute(G);if(u)return u}function Ae(l,u){const s=l?.toLowerCase().trim(),b=S(f);return b?s?b(s,u):0:s?Qe(s,u):0}function nt(){const l=Y();l&&(l.parentElement?.firstChild===l&&oe().then(()=>l.closest(J)?.querySelector(vr)?.scrollIntoView({block:"nearest"})),oe().then(()=>l.scrollIntoView({block:"nearest"})))}function j(l){const u=l??document.getElementById(t.root);return u?Array.from(u.querySelectorAll(ye)).filter(s=>U(s)):[]}function Y(l){const u=document.getElementById(t.root);if(!u)return;const s=u.querySelector(`${ye}[aria-selected="true"]`);return U(s)?s:null}function ie(l){const u=document.getElementById(t.root);!u||j(u)[l]}function ce(l){const u=Y(),s=j(),b=s.findIndex(I=>I===u);let y=s[b+l];S(g)&&(b+l<0?y=s[s.length-1]:b+l===s.length?y=s[0]:y=s[b+l]),y&&p("value",y.getAttribute(G)??"")}function xe(l){let s=Y()?.closest(J),b;for(;s&&!b;)s=l>0?wr(s,J):kr(s,J),b=s?.querySelector(ye);b?p("value",b.getAttribute(G)??""):ce(l)}function _e(){return ie(j().length-1)}function at(l){l.preventDefault(),l.metaKey?_e():l.altKey?xe(1):ce(1)}function lt(l){l.preventDefault(),l.metaKey?ie(0):l.altKey?xe(-1):ce(-1)}function st(l){switch(l.key){case X.ARROW_DOWN:at(l);break;case X.ARROW_UP:lt(l);break;case X.HOME:l.preventDefault(),ie(0);break;case X.END:l.preventDefault(),_e();break;case X.ENTER:{l.preventDefault();const u=Y();u&&u.click()}}}te(Xe,T);const we={subscribe:o.subscribe,update:o.update,set:o.set,updateState:p};return te(Je,we),{state:we,handleRootKeydown:st,commandEl:m,ids:t}}function wr(e,t){let r=e.nextElementSibling;for(;r;){if(r.matches(t))return r;r=r.nextElementSibling}}function kr(e,t){let r=e.previousElementSibling;for(;r;){if(r.matches(t))return r;r=r.previousElementSibling}}function Rr(e){return Object.keys(e).reduce((t,r)=>e[r]===void 0?t:t+`${r}:${e[r]};`,"")}Rr({position:"absolute",opacity:0,"pointer-events":"none",margin:0,transform:"translateX(-100%)"});function et(e,t,r,o){const n=Array.isArray(t)?t:[t];return n.forEach(a=>e.addEventListener(a,r,o)),()=>{n.forEach(a=>e.removeEventListener(a,r,o))}}const Ir={ESCAPE:"Escape"};R(void 0,e=>{function t(o){e(o),e(void 0)}return et(document,"pointerup",t,{passive:!1,capture:!0})});R(void 0,e=>{function t(o){o&&o.key===Ir.ESCAPE&&e(o),e(void 0)}return et(document,"keydown",t,{passive:!1,capture:!0})});function C(e,t){const r={};return t.forEach(o=>{r[o]={[`data-bits-${e}-${o}`]:""}}),o=>r[o]}function tt(e){return Object.keys(e).reduce((t,r)=>e[r]===void 0?t:t+`${r}:${e[r]};`,"")}tt({position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"});tt({position:"absolute",width:"25px",height:"25px",opacity:"0",margin:"0px",pointerEvents:"none",transform:"translateX(-100%)"});const Mr="accordion",Or=["root","content","header","item","trigger"];C(Mr,Or);const Fr="alert-dialog",Nr=["action","cancel","content","description","overlay","portal","title","trigger"];C(Fr,Nr);const Pr="avatar",Dr=["root","image","fallback"];C(Pr,Dr);const Lr="checkbox",$r=["root","input","indicator"];C(Lr,$r);const Wr="collapsible",Ur=["root","content","trigger"];C(Wr,Ur);const Br="context-menu",Kr=["arrow","checkbox-indicator","checkbox-item","content","group","item","label","radio-group","radio-item","separator","sub-content","sub-trigger","trigger"];C(Br,Kr);const Gr="dialog",Hr=["close","content","description","overlay","portal","title","trigger"];C(Gr,Hr);const Vr="dropdown-menu",jr=["arrow","checkbox-indicator","checkbox-item","content","group","item","label","radio-group","radio-item","separator","sub-content","sub-trigger","trigger"];C(Vr,jr);const Yr="link-preview",zr=["arrow","content","trigger"];C(Yr,zr);const qr="label",Xr=["root"];C(qr,Xr);const Jr="menubar",Zr=["root","arrow","checkbox-indicator","checkbox-item","content","group","item","label","radio-group","radio-item","separator","sub-content","sub-trigger","trigger"];C(Jr,Zr);const Qr="popover",eo=["arrow","close","content","trigger"];C(Qr,eo);const to="progress",ro=["root"];C(to,ro);const oo="radio-group",no=["root","item","input"];C(oo,no);const ao="select",lo=["arrow","content","group","item","input","label","trigger","value"];C(ao,lo);const so="separator",io=["root"];C(so,io);const co="slider",uo=["root","input","range","thumb","tick"];C(co,uo);const fo="switch",mo=["root","input","thumb"];C(fo,mo);const po="tabs",bo=["root","content","list","trigger"];C(po,bo);const go="toggle",yo=["root","input"];C(go,yo);const ho="toggle-group",Co=["root","item"];C(ho,Co);const So="tooltip",To=["arrow","content","trigger"];C(So,To);const In=Be({base:"inline-flex items-center justify-center rounded-md text-sm font-medium whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-base-content-muted disabled:pointer-events-none disabled:opacity-50",variants:{variant:{default:"bg-base-content shadow hover:bg-base-content/90",destructive:"bg-negative text-negative-content shadow-sm hover:bg-negative/90",outline:"border border-base-300 bg-transparent shadow-sm hover:bg-base-200",secondary:"bg-base-300 text-base-content shadow-sm hover:bg-base-300/80",ghost:"hover:bg-base-200 hover:text-base-content",link:"text-base-content underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),Mn=Be({base:"inline-flex items-center rounded-md border border-base-300 px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-base-content-muted focus:ring-offset-2 select-none",variants:{variant:{default:"border-transparent bg-base-200 text-base-content hover:bg-base-200/80",destructive:"border-transparent bg-negative text-negative-content hover:bg-negative/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}}),$="auto",Eo=3,vo=[{name:"year",description:'When lowerCase(columnName)="year" with the column having numeric values will result in no formatting',matchingFunction:(e,t,r)=>e&&t?e.toLowerCase()==="year"&&(t?.evidenceType==="number"||r?.unitType==="number"):!1,format:{formatCode:$,valueType:"number",exampleInput:2013,_autoFormat:{autoFormatCode:"@",truncateUnits:!1}}},{name:"id",description:'When lowerCase(columnName)="id" with the column having numeric values, then values will have no formatting',matchingFunction:(e,t,r)=>e&&t?e.toLowerCase()==="id"&&(t?.evidenceType==="number"||r?.unitType==="number"):!1,format:{formatCode:$,valueType:"number",exampleInput:93120121,_autoFormat:{autoFormatFunction:e=>e!=null&&!isNaN(e)?e.toLocaleString("fullwide",{useGrouping:!1}):e}}},{name:"defaultDate",description:"Formatting for Default Date",matchingFunction:(e,t,r)=>t?t?.evidenceType==="date"||r?.unitType==="date":!1,format:{formatCode:$,valueType:"date",exampleInput:"Sat Jan 01 2022 03:15:00 GMT-0500",_autoFormat:{autoFormatCode:"YYYY-MM-DD",truncateUnits:!1}}}],Ee=(e,t)=>{switch(t){case"T":return e/1e12;case"B":return e/1e9;case"M":return e/1e6;case"k":return e/1e3;default:return e}},On=(e,t)=>{let r=(t||e.formatCode)?.toLowerCase()===$,o=e._autoFormat?.autoFormatFunction||e._autoFormat?.autoFormatCode;return!!(r&&o!==void 0)},ve=(e,t=7)=>{let r,o="",n=e?.median,a;if(n!==void 0){let i;o=rt(n),o?(i=Ee(n,o),a=!0):(i=n,a=!1),e.maxDecimals===0&&!a?r="#,##0":r=Ao(i,t)}else r="#,##0",a=!1;return{formatCode:$,valueType:"number",_autoFormat:{autoFormatCode:r,truncateUnits:a,columnUnits:o}}},Fn=(e,t,r)=>{let o=vo.find(n=>n.matchingFunction(e,t,r));if(o)return o.format;if(r?.unitType==="number")return ve(r)},Nn=(e,t,r=void 0)=>{if(t._autoFormat?.autoFormatFunction)return t._autoFormat.autoFormatFunction(e,t,r);if(t._autoFormat.autoFormatCode){let o=t?._autoFormat?.autoFormatCode;if(t.valueType==="number"){let a=t?._autoFormat?.truncateUnits,i=e,m="";return a&&r?.median!==void 0&&(m=rt(r.median),i=Ee(e,m)),re.format(o,i)+m}else return re.format(o,e)}else console.warn("autoFormat called without a formatCode or function");return e},Pn=e=>typeof e=="number"?e.toLocaleString(void 0,{minimumFractionDigits:0,maximumFractionDigits:2}):e!=null?e?.toString():"-";function Ao(e,t=7,r=Eo){let o="#,##0",n=xo(e),a=0;return n-r<0&&(a=Math.min(Math.max(Math.abs(n-r+1),0),t)),a>0&&(o+=".",o+="0".repeat(a)),o}function rt(e){let t=Math.abs(e);return t>=5e12?"T":t>=5e9?"B":t>=5e6?"M":t>=5e3?"k":""}function xo(e){return e===0?0:Math.floor(Math.log10(e))}const _o=[{primaryCode:"usd",currencySymbol:"$",displayName:"USD - United States Dollar"},{primaryCode:"aud",currencySymbol:"A$",displayName:"AUD - Australian Dollar",escapeCurrencySymbol:!0},{primaryCode:"brl",currencySymbol:"R$",displayName:"BRL - Brazilian Real",escapeCurrencySymbol:!0},{primaryCode:"cad",currencySymbol:"C$",displayName:"CAD - Canadian Dollar",escapeCurrencySymbol:!0},{primaryCode:"cny",currencySymbol:"¥",displayName:"CNY - Renminbi",escapeCurrencySymbol:!0},{primaryCode:"eur",currencySymbol:"€",displayName:"EUR - Euro"},{primaryCode:"gbp",currencySymbol:"£",displayName:"GBP - Pound Sterling",escapeCurrencySymbol:!0},{primaryCode:"jpy",currencySymbol:"¥",displayName:"JPY - Japanese Yen",escapeCurrencySymbol:!0},{primaryCode:"inr",currencySymbol:"₹",displayName:"INR - Indian Rupee",escapeCurrencySymbol:!0},{primaryCode:"krw",currencySymbol:"₩",displayName:"KRW - South Korean won",escapeCurrencySymbol:!0},{primaryCode:"ngn",currencySymbol:"₦",displayName:"NGN -  Nigerian Naira",escapeCurrencySymbol:!0},{primaryCode:"rub",currencySymbol:"rub",displayName:"RUB - Russian Ruble",escapeCurrencySymbol:!0},{primaryCode:"sek",currencySymbol:"kr",displayName:"SEK - Swedish Krona",escapeCurrencySymbol:!0}],wo=[{derivedSuffix:"",valueFormatCode:"#,##0",exampleInput:412.17,auto:!0},{derivedSuffix:"0",valueFormatCode:"#,##0",exampleInput:7043.123},{derivedSuffix:"1",valueFormatCode:"#,##0.0",exampleInput:7043.123},{derivedSuffix:"2",valueFormatCode:"#,##0.00",exampleInput:7043.123},{derivedSuffix:"0k",valueFormatCode:'#,##0,"k"',exampleInput:64301.12},{derivedSuffix:"1k",valueFormatCode:'#,##0.0,"k"',exampleInput:64301.12},{derivedSuffix:"2k",valueFormatCode:'#,##0.00,"k"',exampleInput:64301.12},{derivedSuffix:"0m",valueFormatCode:'#,##0,,"M"',exampleInput:456430112e-2},{derivedSuffix:"1m",valueFormatCode:'#,##0.0,,"M"',exampleInput:456430112e-2},{derivedSuffix:"2m",valueFormatCode:'#,##0.00,,"M"',exampleInput:456430112e-2},{derivedSuffix:"0b",valueFormatCode:'#,##0,,,"B"',exampleInput:978456430112e-2},{derivedSuffix:"1b",valueFormatCode:'#,##0.0,,,"B"',exampleInput:978456430112e-2},{derivedSuffix:"2b",valueFormatCode:'#,##0.00,,,"B"',exampleInput:978456430112e-2}],ko=_o.map(e=>{let t=[];return wo.forEach(r=>{let o={formatTag:e.primaryCode+r.derivedSuffix,parentFormat:e.primaryCode,formatCategory:"currency",valueType:"number",exampleInput:r.exampleInput,titleTagReplacement:` (${e.currencySymbol})`},n=e.escapeCurrencySymbol?`"${e.currencySymbol}"`:e.currencySymbol;r.auto||$===r.formatCode?(o.formatCode=$,o._autoFormat={autoFormatFunction:(a,i,m)=>{let d=ve(m,2),c=`${n}${d._autoFormat.autoFormatCode}`,g="",f=a;return d._autoFormat.truncateUnits&&d._autoFormat.columnUnits?(g=d._autoFormat.columnUnits,f=Ee(a,d._autoFormat.columnUnits)):c.endsWith(".0")&&(c=c+"0"),re.format(c,f)+g}}):o.formatCode=`${n}${r.valueFormatCode}`,r.axisValueFormatCode&&(o.axisFormatCode=r.axisValueFormatCode),t.push(o)}),t}).flat(),Dn=[...ko,{formatTag:"ddd",formatCode:"ddd",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"dddd",formatCode:"dddd",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"mmm",formatCode:"mmm",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"mmmm",formatCode:"mmmm",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"yyyy",formatCode:"yyyy",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"shortdate",formatCode:"mmm d/yy",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"longdate",formatCode:"mmmm d, yyyy",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"fulldate",formatCode:"dddd mmmm d, yyyy",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"mdy",formatCode:"m/d/y",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"dmy",formatCode:"d/m/y",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09 12:45"},{formatTag:"hms",formatCode:"H:MM:SS AM/PM",formatCategory:"date",valueType:"date",exampleInput:"2022-01-09T11:45:03"},{formatTag:"num0",formatCode:"#,##0",formatCategory:"number",valueType:"number",exampleInput:11.23168},{formatTag:"num1",formatCode:"#,##0.0",formatCategory:"number",valueType:"number",exampleInput:11.23168},{formatTag:"num2",formatCode:"#,##0.00",formatCategory:"number",valueType:"number",exampleInput:11.23168},{formatTag:"num3",formatCode:"#,##0.000",formatCategory:"number",valueType:"number",exampleInput:11.23168},{formatTag:"num4",formatCode:"#,##0.0000",formatCategory:"number",valueType:"number",exampleInput:11.23168},{formatTag:"num0k",formatCode:'#,##0,"k"',formatCategory:"number",valueType:"number",exampleInput:64201},{formatTag:"num1k",formatCode:'#,##0.0,"k"',formatCategory:"number",valueType:"number",exampleInput:64201},{formatTag:"num2k",formatCode:'#,##0.00,"k"',formatCategory:"number",valueType:"number",exampleInput:64201},{formatTag:"num0m",formatCode:'#,##0,,"M"',formatCategory:"number",valueType:"number",exampleInput:42539483},{formatTag:"num1m",formatCode:'#,##0.0,,"M"',formatCategory:"number",valueType:"number",exampleInput:42539483},{formatTag:"num2m",formatCode:'#,##0.00,,"M"',formatCategory:"number",valueType:"number",exampleInput:42539483},{formatTag:"num0b",formatCode:'#,##0,,,"B"',formatCategory:"number",valueType:"number",exampleInput:1384937584},{formatTag:"num1b",formatCode:'#,##0.0,,,"B"',formatCategory:"number",valueType:"number",exampleInput:1384937584},{formatTag:"num2b",formatCode:'#,##0.00,,,"B"',formatCategory:"number",valueType:"number",exampleInput:1384937584},{formatTag:"id",formatCode:"0",formatCategory:"number",valueType:"number",exampleInput:"921594675",titleTagReplacement:" id"},{formatTag:"fract",formatCode:"# ?/?",formatCategory:"number",valueType:"number",exampleInput:"0.25"},{formatTag:"mult",formatCode:'#,##0.0"x"',formatCategory:"number",valueType:"number",exampleInput:"5.32"},{formatTag:"mult0",formatCode:'#,##0"x"',formatCategory:"number",valueType:"number",exampleInput:"5.32"},{formatTag:"mult1",formatCode:'#,##0.0"x"',formatCategory:"number",valueType:"number",exampleInput:"5.32"},{formatTag:"mult2",formatCode:'#,##0.00"x"',formatCategory:"number",valueType:"number",exampleInput:"5.32"},{formatTag:"sci",formatCode:"0.00E+0",formatCategory:"number",valueType:"number",exampleInput:"16546.1561"},{formatTag:"pct",formatCode:$,formatCategory:"percent",valueType:"number",exampleInput:.731,titleTagReplacement:"",_autoFormat:{autoFormatFunction:(e,t,r)=>{if(r?.unitType==="number"){let o={min:r.min*100,max:r.max*100,median:r.median*100,maxDecimals:Math.max(r.maxDecimals-2,0),unitType:r.unitType},n=ve(o);return re.format(n._autoFormat.autoFormatCode,e*100)+"%"}else return re.format("#,##0%",e)}}},{formatTag:"pct0",formatCode:"#,##0%",formatCategory:"percent",valueType:"number",exampleInput:.731,titleTagReplacement:""},{formatTag:"pct1",formatCode:"#,##0.0%",formatCategory:"percent",valueType:"number",exampleInput:.731,titleTagReplacement:""},{formatTag:"pct2",formatCode:"#,##0.00%",formatCategory:"percent",valueType:"number",exampleInput:.731,titleTagReplacement:""},{formatTag:"pct3",formatCode:"#,##0.000%",formatCategory:"percent",valueType:"number",exampleInput:.731,titleTagReplacement:""}];var B;(function(e){e.BOOLEAN="boolean",e.NUMBER="number",e.STRING="string",e.DATE="date"})(B||(B={}));var se;(function(e){e.INFERRED="inferred",e.PRECISE="precise"})(se||(se={}));const Ro=function(e){return typeof e=="number"?B.NUMBER:typeof e=="boolean"?B.BOOLEAN:e instanceof Date?B.DATE:B.STRING};function Ln(e){return e?._evidenceColumnTypes?e._evidenceColumnTypes:e&&e.length>0?Object.keys(e[0])?.map(o=>{let n=e.find(a=>a[o]!=null);if(n){let a=Ro(n[o]);return{name:o,evidenceType:a,typeFidelity:se.INFERRED}}else return{name:o,evidenceType:B.STRING,typeFidelity:se.INFERRED}}):[]}const $n=Symbol(),Wn=Symbol(),Un=ar();yt(),_t(),wt(),St(),Tt(vt,{fallback:!0}),Et(),mt(),pt(),Rt(),kt.of([...bt,...ht,...It,...Ct,...At,...gt,...xt]);const Io=N(new Map);Se(Io);const ot=e=>{const t=v[e].colors["base-content-muted"],r=v[e].colors["base-content-muted"],o=v[e].colors["base-content-muted"],n=v[e].colors["base-300"],a=v[e].colors["base-100"],i=v[e].colors["base-content-muted"],m=v[e].colors["base-content-muted"],d=v[e].colors["base-content-muted"],c=v[e].colors["base-300"],g=v[e].colors["base-100"],f=v[e].colors["base-content"],h=v[e].colors["base-heading"],T=v[e].colors["base-content-muted"];return{darkMode:e==="dark",backgroundColor:v[e].colors["base-100"],textStyle:{fontFamily:["Inter","sans-serif"]},grid:{left:"1%",right:"4%",bottom:"0%",top:"15%",containLabel:!0},color:v[e].colorPalettes.default,title:{padding:0,itemGap:7,textStyle:{fontSize:14,color:h},subtextStyle:{fontSize:13,color:T,overflow:"break"},top:"1px"},line:{itemStyle:{borderWidth:0},lineStyle:{width:2,join:"round"},symbolSize:0,symbol:"circle",smooth:!1},radar:{itemStyle:{borderWidth:0},lineStyle:{width:2},symbolSize:0,symbol:"circle",smooth:!1},pie:{itemStyle:{borderWidth:0,borderColor:"#cccccc"}},scatter:{itemStyle:{borderWidth:0,borderColor:"#cccccc"}},boxplot:{itemStyle:{borderWidth:1.5}},parallel:{itemStyle:{borderWidth:0,borderColor:"#cccccc"}},sankey:{itemStyle:{borderWidth:0,borderColor:"#cccccc"}},funnel:{itemStyle:{borderWidth:0,borderColor:"#cccccc"}},gauge:{itemStyle:{borderWidth:0,borderColor:"#cccccc"}},candlestick:{itemStyle:{color:"#eb5454",color0:"#47b262",borderColor:"#eb5454",borderColor0:"#47b262",borderWidth:1}},graph:{itemStyle:{borderWidth:0,borderColor:"#cccccc"},lineStyle:{width:1,color:"#aaaaaa"},symbolSize:0,symbol:"circle",smooth:!1,color:["#923d59","#488f96","#518eca","#b3a9a0","#ffc857","#495867","#bfdbf7","#bc4749","#eeebd0"],label:{color:"#f2f2f2"}},map:{itemStyle:{areaColor:"#eee",borderColor:"#444",borderWidth:.5},label:{color:"#000"},emphasis:{itemStyle:{areaColor:"rgba(255,215,0,0.8)",borderColor:"#444",borderWidth:1},label:{color:"rgb(100,0,0)"}}},geo:{itemStyle:{areaColor:"#eee",borderColor:"#444",borderWidth:.5},label:{color:"#000"},emphasis:{itemStyle:{areaColor:"rgba(255,215,0,0.8)",borderColor:"#444",borderWidth:1},label:{color:"rgb(100,0,0)"}}},categoryAxis:{axisLine:{show:!0,lineStyle:{color:t}},axisTick:{show:!1,lineStyle:{color:r},length:3,alignWithLabel:!0},axisLabel:{show:!0,color:o},splitLine:{show:!1,lineStyle:{color:[n]}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},valueAxis:{axisLine:{show:!1,lineStyle:{color:t}},axisTick:{show:!1,lineStyle:{color:r},length:2},axisLabel:{show:!0,color:o},splitLine:{show:!0,lineStyle:{color:[n],width:1}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}},nameTextStyle:{backgroundColor:a}},logAxis:{axisLine:{show:!1,lineStyle:{color:t}},axisTick:{show:!1,lineStyle:{color:r},length:2},axisLabel:{show:!0,color:o},splitLine:{show:!0,lineStyle:{color:[n]}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}},nameTextStyle:{backgroundColor:a}},timeAxis:{axisLine:{show:!0,lineStyle:{color:t}},axisTick:{show:!0,lineStyle:{color:r},length:3},axisLabel:{show:!0,color:o},splitLine:{show:!1,lineStyle:{color:[n]}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},toolbox:{iconStyle:{borderColor:"#999999"},emphasis:{iconStyle:{borderColor:"#459cde"}}},legend:{textStyle:{padding:[0,0,0,-7],color:i},icon:"circle",pageIcons:{horizontal:["M 17 3 h 2 c 0.386 0 0.738 0.223 0.904 0.572 s 0.115 0.762 -0.13 1.062 L 11.292 15 l 8.482 10.367 c 0.245 0.299 0.295 0.712 0.13 1.062 S 19.386 27 19 27 h -2 c -0.3 0 -0.584 -0.135 -0.774 -0.367 l -9 -11 c -0.301 -0.369 -0.301 -0.898 0 -1.267 l 9 -11 C 16.416 3.135 16.7 3 17 3 Z","M 12 27 h -2 c -0.386 0 -0.738 -0.223 -0.904 -0.572 s -0.115 -0.762 0.13 -1.062 L 17.708 15 L 9.226 4.633 c -0.245 -0.299 -0.295 -0.712 -0.13 -1.062 S 9.614 3 10 3 h 2 c 0.3 0 0.584 0.135 0.774 0.367 l 9 11 c 0.301 0.369 0.301 0.898 0 1.267 l -9 11 C 12.584 26.865 12.3 27 12 27 Z"]},pageIconColor:m,pageIconSize:12,pageTextStyle:{color:d},pageButtonItemGap:-2,animationDurationUpdate:300},tooltip:{axisPointer:{lineStyle:{color:"#cccccc",width:1},crossStyle:{color:"#cccccc",width:1}},borderRadius:4,borderWidth:1,borderColor:c,backgroundColor:g,textStyle:{color:f,fontSize:12,fontWeight:400},padding:6},timeline:{lineStyle:{color:"#e3e3e3",width:2},itemStyle:{color:"#d6d6d6",borderWidth:1},controlStyle:{color:"#bfbfbf",borderColor:"#bfbfbf",borderWidth:1},checkpointStyle:{color:"#8f8f8f",borderColor:"#ffffff"},label:{color:"#c9c9c9"},emphasis:{itemStyle:{color:"#9c9c9c"},controlStyle:{color:"#bfbfbf",borderColor:"#bfbfbf",borderWidth:1},label:{color:"#c9c9c9"}}},visualMap:{color:["#c41621","#e39588","#f5ed98"]},dataZoom:{type:"slider",bottom:10,height:30,showDetail:!1,handleSize:"80%",borderColor:n,handleStyle:{borderColor:n,color:n},moveHandleStyle:{borderColor:n,color:n},textStyle:{},emphasis:{handleStyle:{borderColor:n,color:n},moveHandleStyle:{borderColor:n,color:n}}},markPoint:{label:{color:"#f2f2f2"},emphasis:{label:{color:"#f2f2f2"}}}}};ot("light");ot("dark");export{Pn as $,P as A,vn as B,An as C,kn as D,ze as E,gn as F,U as G,Mn as H,Ar as I,Sn as J,En as K,or as L,Wn as M,$n as N,Un as O,Cn as P,Ut as Q,In as R,yn as S,mn as T,dn as U,G as V,Dn as W,Fn as X,Ro as Y,On as Z,Nn as _,Te as a,Ln as a0,se as a1,B as a2,Gt as b,pn as c,cn as d,Xt as e,bn as f,fn as g,an as h,Wt as i,Ge as j,D as k,Tn as l,$t as m,L as n,Yt as o,ln as p,sn as q,un as r,Lt as s,oe as t,hn as u,Rn as v,He as w,_n as x,wn as y,xn as z};
