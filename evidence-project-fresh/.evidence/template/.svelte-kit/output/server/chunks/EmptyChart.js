import{a as p}from"./utils.js";import{c as m,b as v,o as h,v as c,h as u}from"./ssr.js";import{c as y}from"./index7.js";import{Q as f}from"./Query.js";import{O as b}from"./VennDiagram.svelte_svelte_type_style_lang.js";const _=typeof window<"u"?window:typeof globalThis<"u"?globalThis:global,w=m((s,e,t,o)=>{let{class:r=void 0}=e;return e.class===void 0&&t.class&&r!==void 0&&t.class(r),`<div${v("class",y("animate-pulse h-full w-full mt-2 mb-4",r),0)}><span class="sr-only" data-svelte-h="svelte-1wtojot">Loading...</span> <div class="h-full w-full rounded-md bg-base-300"></div></div>`});function g(s){return!s||!s[0]||!s.length}const D=m((s,e,t,o)=>{let r=p(o),{data:a}=e,{height:d=200}=e,{skeletonClass:l=void 0}=e,n=()=>{},i;return h(n),e.data===void 0&&t.data&&a!==void 0&&t.data(a),e.height===void 0&&t.height&&d!==void 0&&t.height(d),e.skeletonClass===void 0&&t.skeletonClass&&l!==void 0&&t.skeletonClass(l),f.isQuery(a)&&(a.fetch(),n(),n=a.subscribe(x=>{i=x})),`${a?`${f.isQuery(a)?`${!i||!i.dataLoaded&&!i.error?`${o.skeleton?o.skeleton({loaded:i}):` <div class="w-full" style="${"height: "+u(d,!0)+"px"}">${c(w,"Skeleton").$$render(s,{class:l},{},{})}</div> `}`:`${i.error&&r.error?`${o.error?o.error({loaded:i}):""}`:`${!i.length&&!i.error&&r.empty?`${o.empty?o.empty({loaded:i}):""}`:`${o.default?o.default({loaded:i}):""}`}`}`}`:` ${(Array.isArray(a)||!a)&&g(a)&&r.empty?` ${o.empty?o.empty({loaded:a}):""}`:` ${o.default?o.default({loaded:a}):""}`}`}`:` ${o.default?o.default({loaded:a}):""}`}`}),k=m((s,e,t,o)=>{let{error:r}=e,{title:a}=e,{height:d=200}=e;return e.error===void 0&&t.error&&r!==void 0&&t.error(r),e.title===void 0&&t.title&&a!==void 0&&t.title(a),e.height===void 0&&t.height&&d!==void 0&&t.height(d),`<div width="100%"${v("class",`grid grid-rows-auto grid-cols-1 justify-center relative
			bg-negative/10 text-negative
			font-ui font-normal
			rounded-sm border border-negative/50
			py-5 px-8 mt-2 mb-4
			print:break-inside-avoid`,0)} style="${"min-height: "+u(d,!0)+"px"}"><div class="m-auto w-full"><div class="font-bold text-center text-lg">${u(a)}</div> <div class="w-full [word-wrap:break-work] text-xs"><pre class="text-left font-sans mx-auto w-fit select-text text-wrap">${u(r)}</pre></div></div></div>`}),E=m((s,e,t,o)=>{let{error:r=void 0}=e;return e.error===void 0&&t.error&&r!==void 0&&t.error(r),`<span class="group inline-flex items-center relative cursor-help cursor-helpfont-sans px-1 border border-negative/50 py-[1px] bg-negative/10 rounded-sm"><span class="inline font-sans font-medium text-xs text-negative" data-svelte-h="svelte-1e4f3hi">error</span> <span class="hidden font-sans group-hover:inline absolute -top-1 left-[105%] text-sm z-10 px-2 py-1 bg-base-100 border border-base-300 leading-relaxed min-w-[150px] max-w-[400px] rounded-md">${u(r)}</span></span>`}),V=m((s,e,t,o)=>{let{error:r}=e;return e.error===void 0&&t.error&&r!==void 0&&t.error(r),`<div width="100%" class="inline-block group w-[100px] relative cursor-help cursor-helpfont-sans box-content grid-cols-1 justify-center bg-negative/10 font-ui font-normal rounded-sm border border-negative/50 h-[38px] mt-0.5 py-3 px-3 print:break-inside-avoid"><div class="font-bold text-center text-sm text-negative" data-svelte-h="svelte-irqt29">Big Value</div> <div class="m-auto w-[100px]"><div class="text-center [word-wrap:break-work] w-full font-medium text-xs text-negative">error
			<span class="hidden font-sans group-hover:inline-block absolute top-[50%] left-[50%] text-sm px-2 py-1 bg-base-100 border border-base-300 leading-relaxed min-w-[150px] max-w-[400px] rounded-md z-50 overflow-visible">${u(r)}</span></div></div></div>`}),I=m((s,e,t,o)=>{let{isInitial:r=!0}=e,{emptySet:a="error"}=e,{emptyMessage:d="No Records"}=e,{chartType:l="Component"}=e,n="Dataset is empty - query ran successfully, but no data was returned from the database";if(l==="Big Value"&&(n="Dataset is empty"),a==="error"&&r){if(console.error("\x1B[31m%s\x1B[0m",`Error in ${l}: ${n}`),b)throw Error(n)}else a==="warn"&&r&&console.warn(`Warning in ${l}: Dataset is empty - query ran successfully, but no data was returned from the database`);return e.isInitial===void 0&&t.isInitial&&r!==void 0&&t.isInitial(r),e.emptySet===void 0&&t.emptySet&&a!==void 0&&t.emptySet(a),e.emptyMessage===void 0&&t.emptyMessage&&d!==void 0&&t.emptyMessage(d),e.chartType===void 0&&t.chartType&&l!==void 0&&t.chartType(l),`${["warn","pass"].includes(a)||!r?`${l==="Value"?`<span class="text-xs text-base-content-muted p-2 my-2 w-full border border-base-300 border-dashed rounded-sm">${u(d)}</span>`:`${l==="Big Value"?`<p class="text-xs text-base-content-muted p-2 pt-[32px] my-0 text-center w-full align-middle h-[80px] border border-base-300 border-dashed rounded-sm min-w-[120px]">${u(d)}</p>`:`<p class="text-xs text-base-content-muted p-2 my-2 w-full border border-base-300 border-dashed rounded-sm">${u(d)}</p>`}`}`:`${l==="Value"?`${c(E,"ValueError").$$render(s,{error:n},{},{})}`:`${l==="Big Value"?`${c(V,"BigValueError").$$render(s,{error:n},{},{})}`:`${c(k,"ErrorChart").$$render(s,{title:l,error:n},{},{})}`}`}`}`});export{k as E,D as Q,w as S,E as V,I as a,_ as g};
