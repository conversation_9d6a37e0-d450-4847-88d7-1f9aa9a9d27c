import{s as h,n as z,d as F}from"./utils.js";import{c as x,b as u,i as S,h as e,v as g}from"./ssr.js";import{g as V,e as R,D as _}from"./CodeBlock.js";import{throttle as j}from"echarts";import{l as W}from"./VennDiagram.svelte_svelte_type_style_lang.js";import H from"prismjs";import{s as X,l as k}from"./Query.js";import{p as Z}from"./stores.js";const J={code:"svg.svelte-lqleyo.svelte-lqleyo{display:inline-block;vertical-align:middle;transition:transform 0.15s ease-in}span.svelte-lqleyo.svelte-lqleyo{margin:auto 0 auto 0}[aria-expanded='true'].svelte-lqleyo svg.svelte-lqleyo{transform:rotate(0.25turn)}",map:`{"version":3,"file":"ChevronToggle.svelte","sources":["ChevronToggle.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport { getThemeStores } from '../../themes/themes.js';\\n\\n\\tconst { resolveColor } = getThemeStores();\\n\\n\\texport let toggled = false;\\n\\n\\texport let color = 'base-content';\\n\\t$: colorStore = resolveColor(color);\\n\\n\\texport let size = 10;\\n<\/script>\\n\\n<span aria-expanded={toggled}>\\n\\t<svg viewBox=\\"0 0 16 16\\" width={size} height={size}\\n\\t\\t><path\\n\\t\\t\\tfill={$colorStore}\\n\\t\\t\\tfill-rule=\\"evenodd\\"\\n\\t\\t\\td=\\"M6.22 3.22a.75.75 0 011.06 0l4.25 4.25a.75.75 0 010 1.06l-4.25 4.25a.75.75 0 01-1.06-1.06L9.94 8 6.22 4.28a.75.75 0 010-1.06z\\"\\n\\t\\t/></svg\\n\\t>\\n</span>\\n\\n<style>\\n\\tsvg {\\n\\t\\tdisplay: inline-block;\\n\\t\\tvertical-align: middle;\\n\\t\\ttransition: transform 0.15s ease-in;\\n\\t}\\n\\n\\tspan {\\n\\t\\tmargin: auto 0 auto 0;\\n\\t}\\n\\n\\t[aria-expanded='true'] svg {\\n\\t\\ttransform: rotate(0.25turn);\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AA4BC,+BAAI,CACH,OAAO,CAAE,YAAY,CACrB,cAAc,CAAE,MAAM,CACtB,UAAU,CAAE,SAAS,CAAC,KAAK,CAAC,OAC7B,CAEA,gCAAK,CACJ,MAAM,CAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CACrB,CAEA,CAAC,aAAa,CAAC,MAAM,eAAC,CAAC,iBAAI,CAC1B,SAAS,CAAE,OAAO,QAAQ,CAC3B"}`},D=x((r,n,o,T)=>{let t,l,d=z,c=()=>(d(),d=h(t,v=>l=v),t);const{resolveColor:E}=W();let{toggled:a=!1}=n,{color:i="base-content"}=n,{size:b=10}=n;return n.toggled===void 0&&o.toggled&&a!==void 0&&o.toggled(a),n.color===void 0&&o.color&&i!==void 0&&o.color(i),n.size===void 0&&o.size&&b!==void 0&&o.size(b),r.css.add(J),c(t=E(i)),d(),`<span${u("aria-expanded",a,0)} class="svelte-lqleyo"><svg viewBox="0 0 16 16"${u("width",b,0)}${u("height",b,0)} class="svelte-lqleyo"><path${u("fill",l,0)} fill-rule="evenodd" d="M6.22 3.22a.75.75 0 011.06 0l4.25 4.25a.75.75 0 010 1.06l-4.25 4.25a.75.75 0 01-1.06-1.06L9.94 8 6.22 4.28a.75.75 0 010-1.06z"></path></svg> </span>`}),tt={code:"div.pagination.svelte-ghf30y.svelte-ghf30y{padding:0px 5px;align-content:center;border-bottom:1px solid var(--base-200);height:1.25em;background-color:var(--base-100);display:flex;flex-direction:row;justify-content:space-between;align-items:center}.slider.svelte-ghf30y.svelte-ghf30y{-webkit-appearance:none;width:75%;height:10px;margin:0 0;outline:none;border-radius:10px;display:inline-block;cursor:pointer}.slider.svelte-ghf30y.svelte-ghf30y::-webkit-slider-thumb{background-color:var(--color-info);-webkit-appearance:none;appearance:none;width:10px;height:10px;cursor:pointer;border-radius:10px}.slider.svelte-ghf30y.svelte-ghf30y::-moz-range-thumb{background-color:var(--color-info);width:10px;height:10px;cursor:pointer}.slider.svelte-ghf30y.svelte-ghf30y::-moz-range-thumb{background-color:var(--color-info);width:10px;height:10px;cursor:pointer}span.svelte-ghf30y.svelte-ghf30y{font-family:var(--ui-font-family-compact);-webkit-font-smoothing:antialiased;float:right}.scrollbox.svelte-ghf30y.svelte-ghf30y{width:100%;overflow-x:auto;border-bottom:1px solid var(--base-300);background-color:var(--base-100)}.results-pane.svelte-ghf30y .download-button{margin-top:10px}table.svelte-ghf30y.svelte-ghf30y{width:100%;border-collapse:collapse;font-family:var(--ui-font-family);font-variant-numeric:tabular-nums}td.svelte-ghf30y.svelte-ghf30y{padding:2px 8px;overflow:hidden;text-overflow:ellipsis}td.svelte-ghf30y div.svelte-ghf30y{width:100px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.other.svelte-ghf30y.svelte-ghf30y{text-align:left}.string.svelte-ghf30y.svelte-ghf30y{text-align:left}.date.svelte-ghf30y.svelte-ghf30y{text-align:left}.number.svelte-ghf30y.svelte-ghf30y{text-align:right}.boolean.svelte-ghf30y.svelte-ghf30y{text-align:left}.index.svelte-ghf30y.svelte-ghf30y{text-align:left;max-width:min-content}tr.type-indicator.svelte-ghf30y.svelte-ghf30y{border-bottom:1px solid var(--base-300)}.footer.svelte-ghf30y.svelte-ghf30y{display:flex;justify-content:flex-end;font-size:12px}",map:`{"version":3,"file":"QueryDataTable.svelte","sources":["QueryDataTable.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport { blur, slide } from 'svelte/transition';\\n\\timport DownloadData from '../DownloadData.svelte';\\n\\timport getColumnSummary from '@evidence-dev/component-utilities/getColumnSummary';\\n\\timport { formatValue } from '@evidence-dev/component-utilities/formatting';\\n\\timport { throttle } from 'echarts';\\n\\n\\texport let queryID;\\n\\texport let data;\\n\\n\\t$: columnSummary = getColumnSummary(data, 'array');\\n\\t$: columnWidths = 90 / (columnSummary.length + 1);\\n\\n\\t// Slicer\\n\\tlet index = 0;\\n\\tlet size = 5;\\n\\t$: max = Math.max(data.length - size, 0);\\n\\t$: dataPage = data.slice(index, index + size);\\n\\tlet updatedSlice;\\n\\n\\tfunction slice() {\\n\\t\\tupdatedSlice = data.slice(index, index + size);\\n\\t\\tdataPage = updatedSlice;\\n\\t}\\n\\n\\tconst updateIndex = throttle((event) => {\\n\\t\\tindex = Math.min(Math.max(0, index + Math.floor(event.deltaY / Math.abs(event.deltaY))), max);\\n\\t\\tslice();\\n\\t}, 60);\\n\\n\\tfunction handleWheel(event) {\\n\\t\\t// abort if scroll is in x-direction\\n\\t\\tif (Math.abs(event.deltaX) >= Math.abs(event.deltaY)) {\\n\\t\\t\\treturn;\\n\\t\\t}\\n\\n\\t\\tconst hasScrolledToTop = event.deltaY < 0 && index === 0;\\n\\t\\tconst hasScrolledToBottom = event.deltaY > 0 && index === max;\\n\\n\\t\\tif (hasScrolledToTop || hasScrolledToBottom) {\\n\\t\\t\\treturn;\\n\\t\\t}\\n\\n\\t\\tevent.preventDefault();\\n\\t\\tupdateIndex(event);\\n\\t}\\n<\/script>\\n\\n<div class=\\"results-pane py-1\\" transition:slide|local>\\n\\t<div class=\\"scrollbox pretty-scrollbar\\">\\n\\t\\t<table class=\\"text-xs\\" in:blur>\\n\\t\\t\\t<thead>\\n\\t\\t\\t\\t<tr>\\n\\t\\t\\t\\t\\t<th class=\\"py-0 px-2 font-medium index text-base-content-muted\\" style=\\"width:10%\\" />\\n\\t\\t\\t\\t\\t{#each columnSummary as column}\\n\\t\\t\\t\\t\\t\\t<th\\n\\t\\t\\t\\t\\t\\t\\tclass=\\"py-0 px-2 font-medium {column.type}\\"\\n\\t\\t\\t\\t\\t\\t\\tstyle=\\"width:{columnWidths}%\\"\\n\\t\\t\\t\\t\\t\\t\\tevidenceType={column.evidenceColumnType?.evidenceType || 'unavailable'}\\n\\t\\t\\t\\t\\t\\t\\tevidenceTypeFidelity={column.evidenceColumnType?.typeFidelity || 'unavailable'}\\n\\t\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t\\t{column.id}\\n\\t\\t\\t\\t\\t\\t</th>\\n\\t\\t\\t\\t\\t{/each}\\n\\t\\t\\t\\t</tr><tr />\\n\\t\\t\\t\\t<tr class=\\"type-indicator\\">\\n\\t\\t\\t\\t\\t<th\\n\\t\\t\\t\\t\\t\\tclass=\\"py-0 px-2 index type-indicator text-base-content-muted font-normal\\"\\n\\t\\t\\t\\t\\t\\tstyle=\\"width:10%\\"\\n\\t\\t\\t\\t\\t/>\\n\\t\\t\\t\\t\\t{#each columnSummary as column}\\n\\t\\t\\t\\t\\t\\t<th\\n\\t\\t\\t\\t\\t\\t\\tclass=\\"{column.type} type-indicator text-base-content-muted font-normal py-0 px-2\\"\\n\\t\\t\\t\\t\\t\\t\\tstyle=\\"width:{columnWidths}%\\"\\n\\t\\t\\t\\t\\t\\t\\tevidenceType={column.evidenceColumnType?.evidenceType || 'unavailable'}\\n\\t\\t\\t\\t\\t\\t\\tevidenceTypeFidelity={column.evidenceColumnType?.typeFidelity || 'unavailable'}\\n\\t\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t\\t{column.type}\\n\\t\\t\\t\\t\\t\\t</th>\\n\\t\\t\\t\\t\\t{/each}\\n\\t\\t\\t\\t</tr><tr />\\n\\t\\t\\t</thead>\\n\\t\\t\\t<tbody on:wheel={handleWheel}>\\n\\t\\t\\t\\t{#each dataPage as row, i}\\n\\t\\t\\t\\t\\t<tr>\\n\\t\\t\\t\\t\\t\\t<td class=\\"index text-base-content-muted\\" style=\\"width:10%\\">\\n\\t\\t\\t\\t\\t\\t\\t{#if i === 0}\\n\\t\\t\\t\\t\\t\\t\\t\\t<!-- <input type=\\"number\\" bind:value={index} max={max} min=0 on:input={slice} class=\\"index-key\\" autofocus reversed> -->\\n\\t\\t\\t\\t\\t\\t\\t\\t{(index + i + 1).toLocaleString()}\\n\\t\\t\\t\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t\\t\\t\\t{(index + i + 1).toLocaleString()}\\n\\t\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t{#each columnSummary as column, j}\\n\\t\\t\\t\\t\\t\\t\\t{#if row[column.id] == null}\\n\\t\\t\\t\\t\\t\\t\\t\\t<td\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tclass=\\"text-base-content-muted {columnSummary[j].type}\\"\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tstyle=\\"width:{columnWidths}%\\"\\n\\t\\t\\t\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{'Ø'}\\n\\t\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\t{:else if columnSummary[j].type === 'number'}\\n\\t\\t\\t\\t\\t\\t\\t\\t<td class=\\"number\\" style=\\"width:{columnWidths}%;\\">\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{formatValue(\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\trow[column.id],\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tcolumnSummary[j].format,\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tcolumnSummary[j].columnUnitSummary\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t)}\\n\\t\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\t{:else if columnSummary[j].type === 'date'}\\n\\t\\t\\t\\t\\t\\t\\t\\t<td\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tclass=\\"string\\"\\n\\t\\t\\t\\t\\t\\t\\t\\t\\tstyle=\\"width:{columnWidths}%\\"\\n\\t\\t\\t\\t\\t\\t\\t\\t\\ttitle={formatValue(\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\trow[column.id],\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tcolumnSummary[j].format,\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tcolumnSummary[j].columnUnitSummary\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t)}\\n\\t\\t\\t\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<div>\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{formatValue(\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\trow[column.id],\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tcolumnSummary[j].format,\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\tcolumnSummary[j].columnUnitSummary\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t)}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\t{:else if columnSummary[j].type === 'string'}\\n\\t\\t\\t\\t\\t\\t\\t\\t<td class=\\"string\\" style=\\"width:{columnWidths}%\\" title={row[column.id]}>\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<div>\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{row[column.id] || 'Ø'}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\t{:else if columnSummary[j].type === 'boolean'}\\n\\t\\t\\t\\t\\t\\t\\t\\t<td class=\\"boolean\\" style=\\"width:{columnWidths}%\\" title={row[column.id]}>\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t<div>\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t{row[column.id] ?? 'Ø'}\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t\\t\\t\\t<td class=\\"other\\" style=\\"width:{columnWidths}%\\">\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t{row[column.id] || 'Ø'}\\n\\t\\t\\t\\t\\t\\t\\t\\t</td>\\n\\t\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t\\t{/each}\\n\\t\\t\\t\\t\\t</tr>\\n\\t\\t\\t\\t{/each}\\n\\t\\t\\t</tbody>\\n\\t\\t</table>\\n\\t</div>\\n\\n\\t{#if max > 0}\\n\\t\\t<div class=\\"pagination\\">\\n\\t\\t\\t<input\\n\\t\\t\\t\\ttype=\\"range\\"\\n\\t\\t\\t\\t{max}\\n\\t\\t\\t\\tstep=\\"1\\"\\n\\t\\t\\t\\tbind:value={index}\\n\\t\\t\\t\\ton:input={slice}\\n\\t\\t\\t\\tclass=\\"slider bg-info/30 hover:bg-info/40 transition-colors\\"\\n\\t\\t\\t/>\\n\\t\\t\\t<span class=\\"text-xs\\">\\n\\t\\t\\t\\t{(index + size).toLocaleString()} of {(max + size).toLocaleString()}\\n\\t\\t\\t</span>\\n\\t\\t</div>\\n\\t{/if}\\n\\n\\t<div class=\\"footer\\">\\n\\t\\t<DownloadData class=\\"download-button\\" {data} {queryID} display />\\n\\t</div>\\n</div>\\n\\n<style>\\n\\tdiv.pagination {\\n\\t\\tpadding: 0px 5px;\\n\\t\\talign-content: center;\\n\\t\\tborder-bottom: 1px solid var(--base-200);\\n\\t\\theight: 1.25em;\\n\\t\\tbackground-color: var(--base-100);\\n\\t\\tdisplay: flex;\\n\\t\\tflex-direction: row;\\n\\t\\tjustify-content: space-between;\\n\\t\\talign-items: center;\\n\\t}\\n\\n\\t.slider {\\n\\t\\t-webkit-appearance: none;\\n\\t\\twidth: 75%;\\n\\t\\theight: 10px;\\n\\t\\tmargin: 0 0;\\n\\t\\toutline: none;\\n\\t\\tborder-radius: 10px;\\n\\t\\tdisplay: inline-block;\\n\\t\\tcursor: pointer;\\n\\t}\\n\\n\\t.slider::-webkit-slider-thumb {\\n\\t\\tbackground-color: var(--color-info);\\n\\t\\t-webkit-appearance: none;\\n\\t\\tappearance: none;\\n\\t\\twidth: 10px;\\n\\t\\theight: 10px;\\n\\t\\tcursor: pointer;\\n\\t\\tborder-radius: 10px;\\n\\t}\\n\\n\\t.slider::-moz-range-thumb {\\n\\t\\tbackground-color: var(--color-info);\\n\\t\\twidth: 10px;\\n\\t\\theight: 10px;\\n\\t\\tcursor: pointer;\\n\\t}\\n\\n\\t.slider::-moz-range-thumb {\\n\\t\\tbackground-color: var(--color-info);\\n\\t\\twidth: 10px;\\n\\t\\theight: 10px;\\n\\t\\tcursor: pointer;\\n\\t}\\n\\n\\tspan {\\n\\t\\tfont-family: var(--ui-font-family-compact);\\n\\t\\t-webkit-font-smoothing: antialiased;\\n\\t\\tfloat: right;\\n\\t}\\n\\n\\t.scrollbox {\\n\\t\\twidth: 100%;\\n\\t\\toverflow-x: auto;\\n\\t\\tborder-bottom: 1px solid var(--base-300);\\n\\t\\tbackground-color: var(--base-100);\\n\\t}\\n\\n\\t.results-pane :global(.download-button) {\\n\\t\\tmargin-top: 10px;\\n\\t}\\n\\n\\ttable {\\n\\t\\twidth: 100%;\\n\\t\\tborder-collapse: collapse;\\n\\t\\tfont-family: var(--ui-font-family);\\n\\t\\tfont-variant-numeric: tabular-nums;\\n\\t}\\n\\n\\ttd {\\n\\t\\tpadding: 2px 8px;\\n\\t\\toverflow: hidden;\\n\\t\\ttext-overflow: ellipsis;\\n\\t}\\n\\n\\ttd div {\\n\\t\\twidth: 100px;\\n\\t\\twhite-space: nowrap;\\n\\t\\toverflow: hidden;\\n\\t\\ttext-overflow: ellipsis;\\n\\t}\\n\\n\\t.other {\\n\\t\\ttext-align: left;\\n\\t}\\n\\n\\t.string {\\n\\t\\ttext-align: left;\\n\\t}\\n\\n\\t.date {\\n\\t\\ttext-align: left;\\n\\t}\\n\\n\\t.number {\\n\\t\\ttext-align: right;\\n\\t}\\n\\n\\t.boolean {\\n\\t\\ttext-align: left;\\n\\t}\\n\\n\\t.index {\\n\\t\\ttext-align: left;\\n\\t\\tmax-width: min-content;\\n\\t}\\n\\n\\ttr.type-indicator {\\n\\t\\tborder-bottom: 1px solid var(--base-300);\\n\\t}\\n\\n\\t.footer {\\n\\t\\tdisplay: flex;\\n\\t\\tjustify-content: flex-end;\\n\\t\\tfont-size: 12px;\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AAiLC,GAAG,uCAAY,CACd,OAAO,CAAE,GAAG,CAAC,GAAG,CAChB,aAAa,CAAE,MAAM,CACrB,aAAa,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACxC,MAAM,CAAE,MAAM,CACd,gBAAgB,CAAE,IAAI,UAAU,CAAC,CACjC,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,GAAG,CACnB,eAAe,CAAE,aAAa,CAC9B,WAAW,CAAE,MACd,CAEA,mCAAQ,CACP,kBAAkB,CAAE,IAAI,CACxB,KAAK,CAAE,GAAG,CACV,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,CAAC,CAAC,CAAC,CACX,OAAO,CAAE,IAAI,CACb,aAAa,CAAE,IAAI,CACnB,OAAO,CAAE,YAAY,CACrB,MAAM,CAAE,OACT,CAEA,mCAAO,sBAAuB,CAC7B,gBAAgB,CAAE,IAAI,YAAY,CAAC,CACnC,kBAAkB,CAAE,IAAI,CACxB,UAAU,CAAE,IAAI,CAChB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,OAAO,CACf,aAAa,CAAE,IAChB,CAEA,mCAAO,kBAAmB,CACzB,gBAAgB,CAAE,IAAI,YAAY,CAAC,CACnC,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,OACT,CAEA,mCAAO,kBAAmB,CACzB,gBAAgB,CAAE,IAAI,YAAY,CAAC,CACnC,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,MAAM,CAAE,OACT,CAEA,gCAAK,CACJ,WAAW,CAAE,IAAI,wBAAwB,CAAC,CAC1C,sBAAsB,CAAE,WAAW,CACnC,KAAK,CAAE,KACR,CAEA,sCAAW,CACV,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,IAAI,CAChB,aAAa,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACxC,gBAAgB,CAAE,IAAI,UAAU,CACjC,CAEA,2BAAa,CAAS,gBAAkB,CACvC,UAAU,CAAE,IACb,CAEA,iCAAM,CACL,KAAK,CAAE,IAAI,CACX,eAAe,CAAE,QAAQ,CACzB,WAAW,CAAE,IAAI,gBAAgB,CAAC,CAClC,oBAAoB,CAAE,YACvB,CAEA,8BAAG,CACF,OAAO,CAAE,GAAG,CAAC,GAAG,CAChB,QAAQ,CAAE,MAAM,CAChB,aAAa,CAAE,QAChB,CAEA,gBAAE,CAAC,iBAAI,CACN,KAAK,CAAE,KAAK,CACZ,WAAW,CAAE,MAAM,CACnB,QAAQ,CAAE,MAAM,CAChB,aAAa,CAAE,QAChB,CAEA,kCAAO,CACN,UAAU,CAAE,IACb,CAEA,mCAAQ,CACP,UAAU,CAAE,IACb,CAEA,iCAAM,CACL,UAAU,CAAE,IACb,CAEA,mCAAQ,CACP,UAAU,CAAE,KACb,CAEA,oCAAS,CACR,UAAU,CAAE,IACb,CAEA,kCAAO,CACN,UAAU,CAAE,IAAI,CAChB,SAAS,CAAE,WACZ,CAEA,EAAE,2CAAgB,CACjB,aAAa,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CACxC,CAEA,mCAAQ,CACP,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,QAAQ,CACzB,SAAS,CAAE,IACZ"}`};let B=5;const et=x((r,n,o,T)=>{let t,l,d,c,{queryID:E}=n,{data:a}=n,i=0,b;function v(){b=a.slice(i,i+B),c=b}return j(A=>{i=Math.min(Math.max(0,i+Math.floor(A.deltaY/Math.abs(A.deltaY))),d),v()},60),n.queryID===void 0&&o.queryID&&E!==void 0&&o.queryID(E),n.data===void 0&&o.data&&a!==void 0&&o.data(a),r.css.add(tt),t=V(a,"array"),l=90/(t.length+1),d=Math.max(a.length-B,0),c=a.slice(i,i+B),`<div class="results-pane py-1 svelte-ghf30y"><div class="scrollbox pretty-scrollbar svelte-ghf30y"><table class="text-xs svelte-ghf30y"><thead><tr><th class="py-0 px-2 font-medium index text-base-content-muted svelte-ghf30y" style="width:10%"></th> ${S(t,A=>`<th class="${"py-0 px-2 font-medium "+e(A.type,!0)+" svelte-ghf30y"}" style="${"width:"+e(l,!0)+"%"}"${u("evidencetype",A.evidenceColumnType?.evidenceType||"unavailable",0)}${u("evidencetypefidelity",A.evidenceColumnType?.typeFidelity||"unavailable",0)}>${e(A.id)} </th>`)} </tr><tr></tr> <tr class="type-indicator svelte-ghf30y"><th class="py-0 px-2 index type-indicator text-base-content-muted font-normal svelte-ghf30y" style="width:10%"></th> ${S(t,A=>`<th class="${e(A.type,!0)+" type-indicator text-base-content-muted font-normal py-0 px-2 svelte-ghf30y"}" style="${"width:"+e(l,!0)+"%"}"${u("evidencetype",A.evidenceColumnType?.evidenceType||"unavailable",0)}${u("evidencetypefidelity",A.evidenceColumnType?.typeFidelity||"unavailable",0)}>${e(A.type)} </th>`)} </tr><tr></tr></thead> <tbody>${S(c,(A,I)=>`<tr><td class="index text-base-content-muted svelte-ghf30y" style="width:10%">${I===0?` ${e((i+I+1).toLocaleString())}`:`${e((i+I+1).toLocaleString())}`}</td> ${S(t,(p,C)=>`${A[p.id]==null?`<td class="${"text-base-content-muted "+e(t[C].type,!0)+" svelte-ghf30y"}" style="${"width:"+e(l,!0)+"%"}">${e("Ø")} </td>`:`${t[C].type==="number"?`<td class="number svelte-ghf30y" style="${"width:"+e(l,!0)+"%;"}">${e(R(A[p.id],t[C].format,t[C].columnUnitSummary))} </td>`:`${t[C].type==="date"?`<td class="string svelte-ghf30y" style="${"width:"+e(l,!0)+"%"}"${u("title",R(A[p.id],t[C].format,t[C].columnUnitSummary),0)}><div class="svelte-ghf30y">${e(R(A[p.id],t[C].format,t[C].columnUnitSummary))}</div> </td>`:`${t[C].type==="string"?`<td class="string svelte-ghf30y" style="${"width:"+e(l,!0)+"%"}"${u("title",A[p.id],0)}><div class="svelte-ghf30y">${e(A[p.id]||"Ø")}</div> </td>`:`${t[C].type==="boolean"?`<td class="boolean svelte-ghf30y" style="${"width:"+e(l,!0)+"%"}"${u("title",A[p.id],0)}><div class="svelte-ghf30y">${e(A[p.id]??"Ø")}</div> </td>`:`<td class="other svelte-ghf30y" style="${"width:"+e(l,!0)+"%"}">${e(A[p.id]||"Ø")} </td>`}`}`}`}`}`)} </tr>`)}</tbody></table></div> ${d>0?`<div class="pagination svelte-ghf30y"><input type="range"${u("max",d,0)} step="1" class="slider bg-info/30 hover:bg-info/40 transition-colors svelte-ghf30y"${u("value",i,0)}> <span class="text-xs svelte-ghf30y">${e((i+B).toLocaleString())} of ${e((d+B).toLocaleString())}</span></div>`:""} <div class="footer svelte-ghf30y">${g(_,"DownloadData").$$render(r,{class:"download-button",data:a,queryID:E,display:!0},{},{})}</div> </div>`}),At={comment:{pattern:/(^|[^\\])(?:\/\*[\s\S]*?\*\/|(?:--|\/\/|#).*)/,lookbehind:!0},variable:[{pattern:/@(["'`])(?:\\[\s\S]|(?!\1)[^\\])+\1/,greedy:!0},/@[\w.$]+/],string:{pattern:/(^|[^@\\])("|')(?:\\[\s\S]|(?!\2)[^\\]|\2\2)*\2/,greedy:!0,lookbehind:!0},identifier:{pattern:/(^|[^@\\])`(?:\\[\s\S]|[^`\\]|``)*`/,greedy:!0,lookbehind:!0,inside:{punctuation:/^`|`$/}},function:/\b(?:AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\s*\()/i,keyword:/\b(?:ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:COL|_INSERT)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|KEYS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:ING|S)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\b/i,boolean:/\b(?:FALSE|NULL|TRUE)\b/i,number:/\b0x[\da-f]+\b|\b\d+(?:\.\d*)?|\B\.\d+\b/i,operator:/[-+*/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\b(?:AND|BETWEEN|DIV|ILIKE|IN|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\b/i,punctuation:/[;[\]()`,.]/},nt={code:"code.svelte-re3fhx{display:block}",map:`{"version":3,"file":"Prismjs.svelte","sources":["Prismjs.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport './prismtheme.css';\\n\\timport Prism from 'prismjs';\\n\\timport { prism_sql } from './prism-sql';\\n\\n\\texport let code = '';\\n<\/script>\\n\\n<pre class=\\"text-xs max-h-56 overflow-auto pretty-scrollbar\\">\\n  <code class=\\"language-sql\\">{@html Prism.highlight(code, prism_sql)}</code>\\n</pre>\\n\\n<style>\\n\\tcode {\\n\\t\\tdisplay: block; /* inline-block has odd behavior when it overflows on webkit mobile */\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AAiBC,kBAAK,CACJ,OAAO,CAAE,KACV"}`},K=x((r,n,o,T)=>{let{code:t=""}=n;return n.code===void 0&&o.code&&t!==void 0&&o.code(t),r.css.add(nt),`<pre class="text-xs max-h-56 overflow-auto pretty-scrollbar">  <code class="language-sql svelte-re3fhx"><!-- HTML_TAG_START -->${H.highlight(t,At)}<!-- HTML_TAG_END --></code>
</pre>`}),ot={code:"div.toggle.svelte-ska6l4{background-color:var(--base-200);border-left:1px solid var(--base-300);border-right:1px solid var(--base-300);padding:6px 0 10px 12px;font-family:var(--ui-font-family);font-size:10px;user-select:none;-webkit-user-select:none;-moz-user-select:none}button.svelte-ska6l4{padding:2px 4px 2px 4px;border-radius:3px;cursor:pointer;background-color:transparent;font-size:1em;font-weight:600}button.off.svelte-ska6l4{border:1px solid var(--base-300);transition:all 400ms}button.off.svelte-ska6l4:hover{background-color:var(--base-300);transition:all 400ms}",map:`{"version":3,"file":"CompilerToggle.svelte","sources":["CompilerToggle.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport { slide } from 'svelte/transition';\\n\\n\\texport let showCompiled;\\n\\n\\tconst toggleCompiled = function () {\\n\\t\\tshowCompiled = !showCompiled;\\n\\t};\\n<\/script>\\n\\n<div class=\\"toggle\\" transition:slide|local>\\n\\t{#if showCompiled}\\n\\t\\t<button class=\\"text-info bg-info/10 border border-info\\">Compiled</button>\\n\\t\\t<button on:click={toggleCompiled} class=\\"off\\">Written</button>\\n\\t{:else}\\n\\t\\t<button on:click={toggleCompiled} class=\\"off\\">Compiled</button>\\n\\t\\t<button class=\\"text-info bg-info/10 border border-info\\">Written</button>\\n\\t{/if}\\n</div>\\n\\n<style>\\n\\tdiv.toggle {\\n\\t\\tbackground-color: var(--base-200);\\n\\t\\tborder-left: 1px solid var(--base-300);\\n\\t\\tborder-right: 1px solid var(--base-300);\\n\\t\\tpadding: 6px 0 10px 12px;\\n\\t\\tfont-family: var(--ui-font-family);\\n\\t\\tfont-size: 10px;\\n\\t\\tuser-select: none;\\n\\t\\t-webkit-user-select: none;\\n\\t\\t-moz-user-select: none;\\n\\t}\\n\\n\\tbutton {\\n\\t\\tpadding: 2px 4px 2px 4px;\\n\\t\\tborder-radius: 3px;\\n\\t\\tcursor: pointer;\\n\\t\\tbackground-color: transparent;\\n\\t\\tfont-size: 1em;\\n\\t\\tfont-weight: 600;\\n\\t}\\n\\n\\tbutton.off {\\n\\t\\tborder: 1px solid var(--base-300);\\n\\t\\ttransition: all 400ms;\\n\\t}\\n\\n\\tbutton.off:hover {\\n\\t\\tbackground-color: var(--base-300);\\n\\t\\ttransition: all 400ms;\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AAyBC,GAAG,qBAAQ,CACV,gBAAgB,CAAE,IAAI,UAAU,CAAC,CACjC,WAAW,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACtC,YAAY,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACvC,OAAO,CAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CACxB,WAAW,CAAE,IAAI,gBAAgB,CAAC,CAClC,SAAS,CAAE,IAAI,CACf,WAAW,CAAE,IAAI,CACjB,mBAAmB,CAAE,IAAI,CACzB,gBAAgB,CAAE,IACnB,CAEA,oBAAO,CACN,OAAO,CAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CACxB,aAAa,CAAE,GAAG,CAClB,MAAM,CAAE,OAAO,CACf,gBAAgB,CAAE,WAAW,CAC7B,SAAS,CAAE,GAAG,CACd,WAAW,CAAE,GACd,CAEA,MAAM,kBAAK,CACV,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACjC,UAAU,CAAE,GAAG,CAAC,KACjB,CAEA,MAAM,kBAAI,MAAO,CAChB,gBAAgB,CAAE,IAAI,UAAU,CAAC,CACjC,UAAU,CAAE,GAAG,CAAC,KACjB"}`},rt=x((r,n,o,T)=>{let{showCompiled:t}=n;return n.showCompiled===void 0&&o.showCompiled&&t!==void 0&&o.showCompiled(t),r.css.add(ot),`<div class="toggle svelte-ska6l4">${t?'<button class="text-info bg-info/10 border border-info svelte-ska6l4" data-svelte-h="svelte-wrfleh">Compiled</button> <button class="off svelte-ska6l4" data-svelte-h="svelte-v36xno">Written</button>':'<button class="off svelte-ska6l4" data-svelte-h="svelte-1vzm9jy">Compiled</button> <button class="text-info bg-info/10 border border-info svelte-ska6l4" data-svelte-h="svelte-qu81ez">Written</button>'} </div>`}),lt={code:":root{--scrollbar-track-color:transparent;--scrollbar-color:rgba(0, 0, 0, 0.2);--scrollbar-active-color:rgba(0, 0, 0, 0.4);--scrollbar-size:0.75rem;--scrollbar-minlength:1.5rem}.code-container.svelte-1ursthx{background-color:var(--base-200);border-left:1px solid var(--base-300);border-right:1px solid var(--base-300);overflow-x:auto;overflow-y:hidden;padding-top:0;padding-right:12px;padding-bottom:6px;padding-left:15px;scrollbar-width:thin;scrollbar-color:var(--scrollbar-color) var(--scrollbar-track-color)}.code-container.svelte-1ursthx::-webkit-scrollbar{height:var(--scrollbar-size);width:var(--scrollbar-size)}.code-container.svelte-1ursthx::-webkit-scrollbar-track{background-color:var(--scrollbar-track-color)}.over-container.svelte-1ursthx{overflow-y:hidden;overflow-x:auto}.code-container.svelte-1ursthx::-webkit-scrollbar-thumb{background-color:var(--scrollbar-color);border-radius:7px;background-clip:padding-box}.code-container.svelte-1ursthx::-webkit-scrollbar-thumb:hover{background-color:var(--scrollbar-active-color)}.code-container.svelte-1ursthx::-webkit-scrollbar-thumb:vertical{min-height:var(--scrollbar-minlength);border:3px solid transparent}.code-container.svelte-1ursthx::-webkit-scrollbar-thumb:horizontal{min-width:var(--scrollbar-minlength);border:3px solid transparent}.status-bar.svelte-1ursthx{margin-top:0px;margin-bottom:0px;background-color:var(--base-200);border-left:1px solid var(--base-300);border-right:1px solid var(--base-300);border-bottom:1px solid var(--base-300);overflow-x:auto;overflow-y:hidden;scrollbar-width:thin;scrollbar-color:var(--scrollbar-color) var(--scrollbar-track-color)}.status-bar.svelte-1ursthx::-webkit-scrollbar{height:var(--scrollbar-size);width:var(--scrollbar-size)}.status-bar.svelte-1ursthx::-webkit-scrollbar-track{background-color:var(--scrollbar-track-color)}.status-bar.svelte-1ursthx::-webkit-scrollbar-thumb{background-color:var(--scrollbar-color);border-radius:7px;background-clip:padding-box}.status-bar.svelte-1ursthx::-webkit-scrollbar-thumb:hover{background-color:var(--scrollbar-active-color)}.status-bar.svelte-1ursthx::-webkit-scrollbar-thumb:vertical{min-height:var(--scrollbar-minlength);border:3px solid transparent}.status-bar.svelte-1ursthx::-webkit-scrollbar-thumb:horizontal{min-width:var(--scrollbar-minlength);border:3px solid transparent}.closed.svelte-1ursthx{border-bottom-left-radius:6px;border-bottom-right-radius:6px}.open.svelte-1ursthx{border-bottom-left-radius:0px;border-bottom-right-radius:0px}.status-bar.success.svelte-1ursthx{color:var(--info);cursor:pointer}.status-bar.error.svelte-1ursthx{color:var(--negative);-webkit-user-select:all;-moz-user-select:all;user-select:all;cursor:auto}button.svelte-1ursthx{font-family:var(--ui-font-family-compact);-webkit-font-smoothing:antialiased;font-size:12px;-webkit-user-select:none;user-select:none;white-space:nowrap;text-align:left;width:100%;background-color:var(--base-200);border:none;border-left:1px solid var(--base-300);border-right:1px solid var(--base-300);margin-bottom:0px;cursor:pointer;padding:5px}button.title.svelte-1ursthx{border-top:1px solid var(--base-300);border-top-left-radius:6px;border-top-right-radius:6px}.scrollbox.svelte-1ursthx{display:flex;flex-direction:column}.container-a.svelte-1ursthx{background-color:var(--base-200);border-top-left-radius:6px;border-top-right-radius:6px;box-sizing:border-box;display:flex;flex-direction:column}@media print{.scrollbox.svelte-1ursthx{break-inside:avoid}}",map:`{"version":3,"file":"QueryViewer.svelte","sources":["QueryViewer.svelte"],"sourcesContent":["<script context=\\"module\\">\\n\\texport const evidenceInclude = true;\\n<\/script>\\n\\n<script>\\n\\timport { slide, blur } from 'svelte/transition';\\n\\timport DataTable from './QueryViewerSupport/QueryDataTable.svelte';\\n\\timport ChevronToggle from './ChevronToggle.svelte';\\n\\timport Prism from './QueryViewerSupport/Prismjs.svelte';\\n\\timport { showQueries, localStorageStore } from '@evidence-dev/component-utilities/stores';\\n\\timport CompilerToggle from './QueryViewerSupport/CompilerToggle.svelte';\\n\\timport { page } from '$app/stores';\\n\\timport { getThemeStores } from '../../themes/themes.js';\\n\\n\\texport let queryID;\\n\\t/** @type {import(\\"@evidence-dev/sdk/usql\\").QueryValue} */\\n\\texport let queryResult;\\n\\n\\t$: pageQueries = $page.data.evidencemeta.queries;\\n\\n\\t// Title & Query Toggle\\n\\tlet showSQL = localStorageStore('showSQL_'.concat(queryID), false);\\n\\t// Query text & Compiler Toggle\\n\\tlet showResults = localStorageStore(\`showResults_\${queryID}\`);\\n\\n\\tconst toggleSQL = function () {\\n\\t\\t$showSQL = !$showSQL;\\n\\t};\\n\\n\\tconst toggleResults = function () {\\n\\t\\tif (!error && $queryResult.length > 0) {\\n\\t\\t\\t$showResults = !$showResults;\\n\\t\\t}\\n\\t};\\n\\n\\tlet inputQuery;\\n\\tlet showCompilerToggle;\\n\\tlet showCompiled = true;\\n\\t/** @type {undefined | Error } */\\n\\tlet error = undefined;\\n\\n\\t// Enter an error state if the queryResult isn't defined\\n\\t$: {\\n\\t\\tif (!$queryResult) error = new Error('queryResult is undefined');\\n\\t\\telse error = $queryResult.error;\\n\\t}\\n\\n\\t$: rowCount = $queryResult?.length ?? 0;\\n\\t$: colCount = $queryResult.columns.length ?? $queryResult?._evidenceColumnTypes.length ?? 0;\\n\\n\\t$: {\\n\\t\\tlet query = pageQueries?.find((d) => d.id === queryID);\\n\\n\\t\\tif (query) {\\n\\t\\t\\tinputQuery = query.inputQueryString;\\n\\t\\t\\tshowCompilerToggle = query.compiled && query.compileError === undefined;\\n\\t\\t}\\n\\t}\\n\\n\\tconst { theme } = getThemeStores();\\n<\/script>\\n\\n<div class=\\"over-container\\" in:blur|local>\\n\\t{#if $showQueries}\\n\\t\\t<!-- Title -->\\n\\t\\t<div class=\\"scrollbox my-3\\" transition:slide|local>\\n\\t\\t\\t<div class=\\"container-a\\">\\n\\t\\t\\t\\t<button type=\\"button\\" aria-label=\\"show-sql\\" on:click={toggleSQL} class=\\"title\\">\\n\\t\\t\\t\\t\\t<ChevronToggle toggled={$showSQL} />\\n\\t\\t\\t\\t\\t{queryID}\\n\\t\\t\\t\\t</button>\\n\\t\\t\\t\\t<!-- Compile Toggle  -->\\n\\t\\t\\t\\t{#if $showSQL && showCompilerToggle}\\n\\t\\t\\t\\t\\t<CompilerToggle bind:showCompiled />\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t<!-- Query Display -->\\n\\t\\t\\t\\t{#if $showSQL}\\n\\t\\t\\t\\t\\t<div class=\\"code-container\\" transition:slide|local>\\n\\t\\t\\t\\t\\t\\t{#if showCompiled}\\n\\t\\t\\t\\t\\t\\t\\t<Prism code={queryResult.originalText} />\\n\\t\\t\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\t\\t\\t<Prism code={inputQuery} />\\n\\t\\t\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t</div>\\n\\t\\t\\t<!-- Status -->\\n\\t\\t\\t<button\\n\\t\\t\\t\\ttype=\\"button\\"\\n\\t\\t\\t\\taria-label=\\"view-query\\"\\n\\t\\t\\t\\tclass={'status-bar'}\\n\\t\\t\\t\\tclass:error\\n\\t\\t\\t\\tclass:success={!error}\\n\\t\\t\\t\\tclass:open={$showResults}\\n\\t\\t\\t\\tclass:closed={!$showResults}\\n\\t\\t\\t\\ton:click={toggleResults}\\n\\t\\t\\t>\\n\\t\\t\\t\\t{#if error}\\n\\t\\t\\t\\t\\t{error.message}\\n\\t\\t\\t\\t{:else if rowCount}\\n\\t\\t\\t\\t\\t<ChevronToggle toggled={$showResults} color={$theme.colors['info']} />\\n\\t\\t\\t\\t\\t{rowCount.toLocaleString()}\\n\\t\\t\\t\\t\\t{rowCount > 1 ? 'records' : 'record'} with {colCount.toLocaleString()}\\n\\t\\t\\t\\t\\t{colCount > 1 ? 'properties' : 'property'}\\n\\t\\t\\t\\t{:else if $queryResult.loading}\\n\\t\\t\\t\\t\\tloading...\\n\\t\\t\\t\\t{:else}\\n\\t\\t\\t\\t\\tran successfully but no data was returned\\n\\t\\t\\t\\t{/if}\\n\\t\\t\\t\\t<!-- Results -->\\n\\t\\t\\t</button>\\n\\t\\t\\t{#if rowCount > 0 && !error && $showResults}\\n\\t\\t\\t\\t<DataTable data={queryResult} {queryID} />\\n\\t\\t\\t{/if}\\n\\t\\t</div>\\n\\t{/if}\\n</div>\\n\\n<style>\\n\\t:root {\\n\\t\\t--scrollbar-track-color: transparent;\\n\\t\\t--scrollbar-color: rgba(0, 0, 0, 0.2);\\n\\t\\t--scrollbar-active-color: rgba(0, 0, 0, 0.4);\\n\\t\\t--scrollbar-size: 0.75rem;\\n\\t\\t--scrollbar-minlength: 1.5rem; /* Minimum length of scrollbar thumb (width of horizontal, height of vertical) */\\n\\t}\\n\\n\\t.code-container {\\n\\t\\tbackground-color: var(--base-200);\\n\\t\\tborder-left: 1px solid var(--base-300);\\n\\t\\tborder-right: 1px solid var(--base-300);\\n\\t\\toverflow-x: auto;\\n\\t\\toverflow-y: hidden;\\n\\t\\tpadding-top: 0;\\n\\t\\tpadding-right: 12px;\\n\\t\\tpadding-bottom: 6px;\\n\\t\\tpadding-left: 15px;\\n\\t\\tscrollbar-width: thin;\\n\\t\\tscrollbar-color: var(--scrollbar-color) var(--scrollbar-track-color);\\n\\t}\\n\\t.code-container::-webkit-scrollbar {\\n\\t\\theight: var(--scrollbar-size);\\n\\t\\twidth: var(--scrollbar-size);\\n\\t}\\n\\t.code-container::-webkit-scrollbar-track {\\n\\t\\tbackground-color: var(--scrollbar-track-color);\\n\\t}\\n\\n\\t.over-container {\\n\\t\\toverflow-y: hidden;\\n\\t\\toverflow-x: auto;\\n\\t}\\n\\n\\t.code-container::-webkit-scrollbar-thumb {\\n\\t\\tbackground-color: var(--scrollbar-color);\\n\\t\\tborder-radius: 7px;\\n\\t\\tbackground-clip: padding-box;\\n\\t}\\n\\t.code-container::-webkit-scrollbar-thumb:hover {\\n\\t\\tbackground-color: var(--scrollbar-active-color);\\n\\t}\\n\\t.code-container::-webkit-scrollbar-thumb:vertical {\\n\\t\\tmin-height: var(--scrollbar-minlength);\\n\\t\\tborder: 3px solid transparent;\\n\\t}\\n\\t.code-container::-webkit-scrollbar-thumb:horizontal {\\n\\t\\tmin-width: var(--scrollbar-minlength);\\n\\t\\tborder: 3px solid transparent;\\n\\t}\\n\\n\\t.status-bar {\\n\\t\\tmargin-top: 0px;\\n\\t\\tmargin-bottom: 0px;\\n\\t\\tbackground-color: var(--base-200);\\n\\t\\tborder-left: 1px solid var(--base-300);\\n\\t\\tborder-right: 1px solid var(--base-300);\\n\\t\\tborder-bottom: 1px solid var(--base-300);\\n\\t\\toverflow-x: auto;\\n\\t\\toverflow-y: hidden;\\n\\t\\tscrollbar-width: thin;\\n\\t\\tscrollbar-color: var(--scrollbar-color) var(--scrollbar-track-color);\\n\\t}\\n\\n\\t.status-bar::-webkit-scrollbar {\\n\\t\\theight: var(--scrollbar-size);\\n\\t\\twidth: var(--scrollbar-size);\\n\\t}\\n\\t.status-bar::-webkit-scrollbar-track {\\n\\t\\tbackground-color: var(--scrollbar-track-color);\\n\\t}\\n\\t.status-bar::-webkit-scrollbar-thumb {\\n\\t\\tbackground-color: var(--scrollbar-color);\\n\\t\\tborder-radius: 7px;\\n\\t\\tbackground-clip: padding-box;\\n\\t}\\n\\t.status-bar::-webkit-scrollbar-thumb:hover {\\n\\t\\tbackground-color: var(--scrollbar-active-color);\\n\\t}\\n\\t.status-bar::-webkit-scrollbar-thumb:vertical {\\n\\t\\tmin-height: var(--scrollbar-minlength);\\n\\t\\tborder: 3px solid transparent;\\n\\t}\\n\\t.status-bar::-webkit-scrollbar-thumb:horizontal {\\n\\t\\tmin-width: var(--scrollbar-minlength);\\n\\t\\tborder: 3px solid transparent;\\n\\t}\\n\\n\\t.closed {\\n\\t\\tborder-bottom-left-radius: 6px;\\n\\t\\tborder-bottom-right-radius: 6px;\\n\\t}\\n\\n\\t.open {\\n\\t\\tborder-bottom-left-radius: 0px;\\n\\t\\tborder-bottom-right-radius: 0px;\\n\\t}\\n\\n\\t.status-bar.success {\\n\\t\\tcolor: var(--info);\\n\\t\\tcursor: pointer;\\n\\t}\\n\\n\\t.status-bar.error {\\n\\t\\tcolor: var(--negative);\\n\\t\\t-webkit-user-select: all;\\n\\t\\t-moz-user-select: all;\\n\\t\\tuser-select: all;\\n\\t\\tcursor: auto;\\n\\t}\\n\\n\\tbutton {\\n\\t\\tfont-family: var(--ui-font-family-compact);\\n\\t\\t-webkit-font-smoothing: antialiased;\\n\\t\\tfont-size: 12px;\\n\\t\\t-webkit-user-select: none;\\n\\t\\tuser-select: none;\\n\\t\\twhite-space: nowrap;\\n\\t\\ttext-align: left;\\n\\t\\twidth: 100%;\\n\\t\\tbackground-color: var(--base-200);\\n\\t\\tborder: none;\\n\\t\\tborder-left: 1px solid var(--base-300);\\n\\t\\tborder-right: 1px solid var(--base-300);\\n\\t\\tmargin-bottom: 0px;\\n\\t\\tcursor: pointer;\\n\\t\\tpadding: 5px;\\n\\t}\\n\\n\\tbutton.title {\\n\\t\\tborder-top: 1px solid var(--base-300);\\n\\t\\tborder-top-left-radius: 6px;\\n\\t\\tborder-top-right-radius: 6px;\\n\\t}\\n\\n\\t.scrollbox {\\n\\t\\tdisplay: flex;\\n\\t\\tflex-direction: column;\\n\\t}\\n\\n\\t.container-a {\\n\\t\\tbackground-color: var(--base-200);\\n\\t\\tborder-top-left-radius: 6px;\\n\\t\\tborder-top-right-radius: 6px;\\n\\t\\tbox-sizing: border-box;\\n\\t\\tdisplay: flex;\\n\\t\\tflex-direction: column;\\n\\t}\\n\\t/* container-a avoids whitespace appearing in the slide transition */\\n\\n\\t@media print {\\n\\t\\t.scrollbox {\\n\\t\\t\\tbreak-inside: avoid;\\n\\t\\t}\\n\\t}\\n</style>\\n"],"names":[],"mappings":"AAuHC,KAAM,CACL,uBAAuB,CAAE,WAAW,CACpC,iBAAiB,CAAE,kBAAkB,CACrC,wBAAwB,CAAE,kBAAkB,CAC5C,gBAAgB,CAAE,OAAO,CACzB,qBAAqB,CAAE,MACxB,CAEA,8BAAgB,CACf,gBAAgB,CAAE,IAAI,UAAU,CAAC,CACjC,WAAW,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACtC,YAAY,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACvC,UAAU,CAAE,IAAI,CAChB,UAAU,CAAE,MAAM,CAClB,WAAW,CAAE,CAAC,CACd,aAAa,CAAE,IAAI,CACnB,cAAc,CAAE,GAAG,CACnB,YAAY,CAAE,IAAI,CAClB,eAAe,CAAE,IAAI,CACrB,eAAe,CAAE,IAAI,iBAAiB,CAAC,CAAC,IAAI,uBAAuB,CACpE,CACA,8BAAe,mBAAoB,CAClC,MAAM,CAAE,IAAI,gBAAgB,CAAC,CAC7B,KAAK,CAAE,IAAI,gBAAgB,CAC5B,CACA,8BAAe,yBAA0B,CACxC,gBAAgB,CAAE,IAAI,uBAAuB,CAC9C,CAEA,8BAAgB,CACf,UAAU,CAAE,MAAM,CAClB,UAAU,CAAE,IACb,CAEA,8BAAe,yBAA0B,CACxC,gBAAgB,CAAE,IAAI,iBAAiB,CAAC,CACxC,aAAa,CAAE,GAAG,CAClB,eAAe,CAAE,WAClB,CACA,8BAAe,yBAAyB,MAAO,CAC9C,gBAAgB,CAAE,IAAI,wBAAwB,CAC/C,CACA,8BAAe,yBAAyB,SAAU,CACjD,UAAU,CAAE,IAAI,qBAAqB,CAAC,CACtC,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,WACnB,CACA,8BAAe,yBAAyB,WAAY,CACnD,SAAS,CAAE,IAAI,qBAAqB,CAAC,CACrC,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,WACnB,CAEA,0BAAY,CACX,UAAU,CAAE,GAAG,CACf,aAAa,CAAE,GAAG,CAClB,gBAAgB,CAAE,IAAI,UAAU,CAAC,CACjC,WAAW,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACtC,YAAY,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACvC,aAAa,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACxC,UAAU,CAAE,IAAI,CAChB,UAAU,CAAE,MAAM,CAClB,eAAe,CAAE,IAAI,CACrB,eAAe,CAAE,IAAI,iBAAiB,CAAC,CAAC,IAAI,uBAAuB,CACpE,CAEA,0BAAW,mBAAoB,CAC9B,MAAM,CAAE,IAAI,gBAAgB,CAAC,CAC7B,KAAK,CAAE,IAAI,gBAAgB,CAC5B,CACA,0BAAW,yBAA0B,CACpC,gBAAgB,CAAE,IAAI,uBAAuB,CAC9C,CACA,0BAAW,yBAA0B,CACpC,gBAAgB,CAAE,IAAI,iBAAiB,CAAC,CACxC,aAAa,CAAE,GAAG,CAClB,eAAe,CAAE,WAClB,CACA,0BAAW,yBAAyB,MAAO,CAC1C,gBAAgB,CAAE,IAAI,wBAAwB,CAC/C,CACA,0BAAW,yBAAyB,SAAU,CAC7C,UAAU,CAAE,IAAI,qBAAqB,CAAC,CACtC,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,WACnB,CACA,0BAAW,yBAAyB,WAAY,CAC/C,SAAS,CAAE,IAAI,qBAAqB,CAAC,CACrC,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,WACnB,CAEA,sBAAQ,CACP,yBAAyB,CAAE,GAAG,CAC9B,0BAA0B,CAAE,GAC7B,CAEA,oBAAM,CACL,yBAAyB,CAAE,GAAG,CAC9B,0BAA0B,CAAE,GAC7B,CAEA,WAAW,uBAAS,CACnB,KAAK,CAAE,IAAI,MAAM,CAAC,CAClB,MAAM,CAAE,OACT,CAEA,WAAW,qBAAO,CACjB,KAAK,CAAE,IAAI,UAAU,CAAC,CACtB,mBAAmB,CAAE,GAAG,CACxB,gBAAgB,CAAE,GAAG,CACrB,WAAW,CAAE,GAAG,CAChB,MAAM,CAAE,IACT,CAEA,qBAAO,CACN,WAAW,CAAE,IAAI,wBAAwB,CAAC,CAC1C,sBAAsB,CAAE,WAAW,CACnC,SAAS,CAAE,IAAI,CACf,mBAAmB,CAAE,IAAI,CACzB,WAAW,CAAE,IAAI,CACjB,WAAW,CAAE,MAAM,CACnB,UAAU,CAAE,IAAI,CAChB,KAAK,CAAE,IAAI,CACX,gBAAgB,CAAE,IAAI,UAAU,CAAC,CACjC,MAAM,CAAE,IAAI,CACZ,WAAW,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACtC,YAAY,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACvC,aAAa,CAAE,GAAG,CAClB,MAAM,CAAE,OAAO,CACf,OAAO,CAAE,GACV,CAEA,MAAM,qBAAO,CACZ,UAAU,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,CACrC,sBAAsB,CAAE,GAAG,CAC3B,uBAAuB,CAAE,GAC1B,CAEA,yBAAW,CACV,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MACjB,CAEA,2BAAa,CACZ,gBAAgB,CAAE,IAAI,UAAU,CAAC,CACjC,sBAAsB,CAAE,GAAG,CAC3B,uBAAuB,CAAE,GAAG,CAC5B,UAAU,CAAE,UAAU,CACtB,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MACjB,CAGA,OAAO,KAAM,CACZ,yBAAW,CACV,YAAY,CAAE,KACf,CACD"}`},pt=x((r,n,o,T)=>{let t,l,d,c,E,a,i,b,v,A,I,p,C,U,L;I=h(Z,s=>A=s),C=h(X,s=>p=s);let{queryID:f}=n,{queryResult:y}=n;E=h(y,s=>c=s);let $=k("showSQL_".concat(f),!1);v=h($,s=>b=s);let q=k();i=h(q,s=>a=s);let N,M,w=!0,m;const{theme:P}=W();L=h(P,s=>U=s),n.queryID===void 0&&o.queryID&&f!==void 0&&o.queryID(f),n.queryResult===void 0&&o.queryResult&&y!==void 0&&o.queryResult(y),r.css.add(lt);let O,G,Q=r.head;do{O=!0,r.head=Q,t=A.data.evidencemeta.queries,c?m=c.error:m=new Error("queryResult is undefined"),l=c?.length??0,d=c.columns.length??c?._evidenceColumnTypes.length??0;{let s=t?.find(Y=>Y.id===f);s&&(N=s.inputQueryString,M=s.compiled&&s.compileError===void 0)}G=`<div class="over-container svelte-1ursthx">${p?` <div class="scrollbox my-3 svelte-1ursthx"><div class="container-a svelte-1ursthx"><button type="button" aria-label="show-sql" class="title svelte-1ursthx">${g(D,"ChevronToggle").$$render(r,{toggled:b},{},{})} ${e(f)}</button>  ${b&&M?`${g(rt,"CompilerToggle").$$render(r,{showCompiled:w},{showCompiled:s=>{w=s,O=!1}},{})}`:""}  ${b?`<div class="code-container svelte-1ursthx">${w?`${g(K,"Prism").$$render(r,{code:y.originalText},{},{})}`:`${g(K,"Prism").$$render(r,{code:N},{},{})}`}</div>`:""}</div>  <button type="button" aria-label="view-query" class="${[e(F("status-bar"),!0)+" svelte-1ursthx",(m?"error":"")+" "+(m?"":"success")+" "+(a?"open":"")+" "+(a?"":"closed")].join(" ").trim()}">${m?`${e(m.message)}`:`${l?`${g(D,"ChevronToggle").$$render(r,{toggled:a,color:U.colors.info},{},{})} ${e(l.toLocaleString())} ${e(l>1?"records":"record")} with ${e(d.toLocaleString())} ${e(d>1?"properties":"property")}`:`${c.loading?"loading...":"ran successfully but no data was returned"}`}`} </button> ${l>0&&!m&&a?`${g(et,"DataTable").$$render(r,{data:y,queryID:f},{},{})}`:""}</div>`:""} </div>`}while(!O);return E(),i(),v(),I(),C(),L(),G});export{pt as Q};
