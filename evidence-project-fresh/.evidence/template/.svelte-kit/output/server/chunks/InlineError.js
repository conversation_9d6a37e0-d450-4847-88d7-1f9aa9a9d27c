import{c as l,v as s,h as i}from"./ssr.js";import{I as v,o as h}from"./index7.js";const p=l((a,t,r,c)=>{let{inputType:o=void 0}=t,{error:e=void 0}=t,{height:n=""}=t,{width:d=""}=t;return Array.isArray(e)||(e=[e]),e.length>0&&(e=e.join(`
`)),t.inputType===void 0&&r.inputType&&o!==void 0&&r.inputType(o),t.error===void 0&&r.error&&e!==void 0&&r.error(e),t.height===void 0&&r.height&&n!==void 0&&r.height(n),t.width===void 0&&r.width&&d!==void 0&&r.width(d),`<div style="${"height: "+i(n,!0)+"px; width: "+i(d,!0)+"px"}" class="group relative cursor-help cursor-helpfont-sans bg-negative/10 font-ui font-normal rounded border border-negative/50 print:break-inside-avoid text-center text-negative flex items-center justify-center mb-4 mt-4"><div>${s(v,"Icon").$$render(a,{src:h,class:"mb-[2px] h-4 w-4 stroke-[1.8px] text-negative inline"},{},{})} <span class="font-medium text-center text-sm">${i(o)}</span></div> <div class="hidden font-sans group-hover:inline-block absolute top-[50%] left-[50%] text-xs px-2 py-1 bg-base-100 border border-base-300 leading-relaxed rounded-md text-left z-50 w-fit"><pre>${i(e)}</pre></div></div>`});export{p as I};
