import * as universal from '../entries/pages/explore/_layout.js';

export const index = 2;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/fallbacks/layout.svelte.js')).default;
export { universal };
export const universal_id = "src/pages/explore/+layout.js";
export const imports = ["_app/immutable/nodes/2.DdeTNzV-.js","_app/immutable/chunks/index.rV6zwFgL.js","_app/immutable/nodes/3.CFJtRtoH.js","_app/immutable/chunks/scheduler.C5eBzNnH.js","_app/immutable/chunks/index.BSd9q3aW.js"];
export const stylesheets = [];
export const fonts = [];
