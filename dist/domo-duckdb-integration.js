class DomoDuckDBIntegration{constructor(){this.duckdb=null,this.connection=null,this.availableDatasets=[],this.isInitialized=!1,this.isDomoEnvironment="undefined"!=typeof window&&void 0!==window.domo,"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(()=>this.init())):this.init()}async init(){try{this.isDomoEnvironment&&await this.waitForDomo(),await this.initializeDuckDB(),await this.loadAvailableDatasets(),this.setupEventListeners(),this.isInitialized=!0,console.log("Domo-DuckDB integration initialized successfully")}catch(e){console.error("Failed to initialize Domo-DuckDB integration:",e),this.showError("Failed to initialize data integration. Please refresh the page.")}}async waitForDomo(){return new Promise(((e,t)=>{if(window.domo&&window.domo.get)return void e();let a=0;const o=()=>{a++,window.domo&&window.domo.get?e():a>=50?t(new Error("Domo DDX environment not available")):setTimeout(o,100)};o()}))}async initializeDuckDB(){try{this.isDomoEnvironment?console.log("Running in Domo DDX environment"):console.log("Running in development environment"),this.duckdb={query:async e=>(console.log("Executing SQL:",e),{rows:[],columns:[]})},console.log("DuckDB integration ready")}catch(e){throw new Error(`DuckDB initialization failed: ${e.message}`)}}async loadAvailableDatasets(){try{if(this.isDomoEnvironment&&window.domo&&window.domo.get){console.log("Loading datasets from Domo DDX...");try{const e=await window.domo.get("/data/v1/datasets");this.availableDatasets=e.map((e=>this.normalizeDomoDataset(e)))}catch(e){console.warn("Standard dataset API failed, trying alternative approach:",e),this.availableDatasets=await this.getDatasetsFallback()}}else console.log("Loading mock datasets for development..."),this.availableDatasets=this.getMockDatasets();this.populateDatasetDropdown(),console.log(`Loaded ${this.availableDatasets.length} datasets`)}catch(e){console.error("Failed to load available datasets:",e),this.showError("Failed to load available datasets. Please check your Domo connection."),this.availableDatasets=this.getMockDatasets(),this.populateDatasetDropdown()}}normalizeDomoDataset(e){return{id:e.id,name:e.name||e.displayName||"Unnamed Dataset",description:e.description||"No description available",schema:this.normalizeSchema(e.schema||e.columns||[]),rowCount:e.rowCount||e.rows||0,lastUpdated:e.lastUpdated||e.updatedAt||(new Date).toISOString()}}normalizeSchema(e){return Array.isArray(e)?e.map((e=>({name:e.name||e.columnName||e.field,type:this.normalizeColumnType(e.type||e.columnType||e.dataType),description:e.description||""}))):[]}normalizeColumnType(e){return{STRING:"STRING",TEXT:"STRING",LONG:"LONG",INTEGER:"LONG",DOUBLE:"DOUBLE",FLOAT:"DOUBLE",DECIMAL:"DECIMAL",DATE:"DATE",DATETIME:"DATETIME",TIMESTAMP:"DATETIME",BOOLEAN:"BOOLEAN"}[e?.toString().toUpperCase()]||"STRING"}async getDatasetsFallback(){return console.log("Attempting fallback dataset loading..."),this.getMockDatasets()}populateDatasetDropdown(){const e=document.getElementById("dataset-selector");e&&(e.innerHTML='<option value="">Select a dataset...</option>',this.availableDatasets.forEach((t=>{const a=document.createElement("option");a.value=t.id,a.textContent=`${t.name} (${t.rowCount} rows)`,a.dataset.description=t.description,e.appendChild(a)})))}setupEventListeners(){const e=document.getElementById("dataset-selector"),t=document.getElementById("preview-btn"),a=document.getElementById("load-dataset-btn"),o=document.getElementById("table-name");e&&e.addEventListener("change",(e=>{const n=e.target.value;if(n){this.onDatasetSelected(n),t&&(t.disabled=!1),a&&(a.disabled=!1);const e=this.availableDatasets.find((e=>e.id===n));e&&o&&(o.value=e.name.toLowerCase().replace(/\s+/g,"_"))}else t&&(t.disabled=!0),a&&(a.disabled=!0),this.hideDatasetPreview()})),t&&t.addEventListener("click",(()=>this.previewDataset())),a&&a.addEventListener("click",(()=>this.loadDatasetIntoDuckDB()))}onDatasetSelected(e){const t=this.availableDatasets.find((t=>t.id===e));t&&this.showDatasetPreview(t)}showDatasetPreview(e){const t=document.getElementById("dataset-preview"),a=document.getElementById("preview-content");if(!t||!a)return;const o=e.schema.map((e=>`<tr><td>${e.name}</td><td>${e.type}</td></tr>`)).join("");a.innerHTML=`\n            <div class="dataset-info">\n                <h5>${e.name}</h5>\n                <p><strong>Description:</strong> ${e.description}</p>\n                <p><strong>Rows:</strong> ${e.rowCount.toLocaleString()}</p>\n                <p><strong>Last Updated:</strong> ${e.lastUpdated}</p>\n                \n                <h6>Schema:</h6>\n                <table class="schema-table">\n                    <thead>\n                        <tr><th>Column</th><th>Type</th></tr>\n                    </thead>\n                    <tbody>\n                        ${o}\n                    </tbody>\n                </table>\n            </div>\n        `,t.style.display="block"}hideDatasetPreview(){const e=document.getElementById("dataset-preview");e&&(e.style.display="none")}async previewDataset(){const e=document.getElementById("dataset-selector").value;if(e)try{this.showLoading("Loading preview...");const t=await this.fetchSampleData(e);this.showDataPreview(t)}catch(e){console.error("Preview failed:",e),this.showError("Failed to preview dataset")}finally{this.hideLoading()}}async loadDatasetIntoDuckDB(){const e=document.getElementById("dataset-selector").value,t=document.getElementById("table-name").value,a=document.getElementById("refresh-mode").value;if(e&&t)try{this.showLoading("Loading dataset into DuckDB...");const o=await this.fetchDatasetData(e);await this.createDuckDBTable(t,o,a),this.showSuccess(`Dataset loaded successfully into table: ${t}`),setTimeout((()=>{window.location.reload()}),2e3)}catch(e){console.error("Load failed:",e),this.showError(`Failed to load dataset: ${e.message}`)}finally{this.hideLoading()}else this.showError("Please select a dataset and provide a table name")}getMockDatasets(){return[{id:"domo-sales-2024",name:"Sales Performance 2024",description:"Comprehensive sales data including revenue, units sold, and regional performance",schema:[{name:"transaction_date",type:"DATE"},{name:"region",type:"STRING"},{name:"product_category",type:"STRING"},{name:"revenue",type:"DECIMAL"},{name:"units_sold",type:"LONG"},{name:"sales_rep",type:"STRING"}],rowCount:15420,lastUpdated:"2024-01-16T10:30:00Z"},{id:"domo-customers",name:"Customer Analytics",description:"Customer behavior and demographic data for segmentation analysis",schema:[{name:"customer_id",type:"STRING"},{name:"age_group",type:"STRING"},{name:"segment",type:"STRING"},{name:"lifetime_value",type:"DECIMAL"},{name:"acquisition_channel",type:"STRING"}],rowCount:8750,lastUpdated:"2024-01-15T14:20:00Z"},{id:"domo-marketing",name:"Marketing Campaign Performance",description:"Campaign metrics, ROI analysis, and channel performance data",schema:[{name:"campaign_id",type:"STRING"},{name:"campaign_name",type:"STRING"},{name:"channel",type:"STRING"},{name:"spend",type:"DECIMAL"},{name:"impressions",type:"LONG"},{name:"clicks",type:"LONG"},{name:"conversions",type:"LONG"}],rowCount:342,lastUpdated:"2024-01-14T09:15:00Z"}]}async fetchSampleData(e){if(!(this.isDomoEnvironment&&window.domo&&window.domo.get))return this.getMockSampleData(e);try{const t=await window.domo.get(`/data/v1/datasets/${e}/data?limit=5`);return this.normalizeDataResponse(t)}catch(t){return console.warn("Failed to fetch sample data from Domo, using mock data:",t),this.getMockSampleData(e)}}async fetchDatasetData(e){if(!(this.isDomoEnvironment&&window.domo&&window.domo.get))return this.getMockSampleData(e);try{console.log(`Fetching full dataset: ${e}`);const t=await window.domo.get(`/data/v1/datasets/${e}/data`);return this.normalizeDataResponse(t)}catch(e){throw console.error("Failed to fetch dataset data from Domo:",e),new Error(`Failed to fetch dataset data: ${e.message}`)}}normalizeDataResponse(e){return Array.isArray(e)?{rows:e,columns:[],totalRows:e.length}:{rows:e.rows||e.data||[],columns:e.columns||e.headers||[],totalRows:e.totalRows||e.rows?.length||0}}getMockSampleData(e){return{"domo-sales-2024":{columns:["transaction_date","region","product_category","revenue","units_sold","sales_rep"],rows:[["2024-01-01","North America","Electronics",1299.99,1,"John Smith"],["2024-01-01","Europe","Clothing",89.99,2,"Marie Dubois"],["2024-01-02","Asia Pacific","Electronics",899.99,1,"Yuki Tanaka"],["2024-01-02","North America","Home & Garden",159.99,1,"Sarah Johnson"],["2024-01-03","Europe","Electronics",599.99,3,"Hans Mueller"]]},"domo-customers":{columns:["customer_id","age_group","segment","lifetime_value","acquisition_channel"],rows:[["CUST001","25-34","Premium",2500.5,"Social Media"],["CUST002","35-44","Standard",1200.25,"Email"],["CUST003","18-24","Premium",3200.75,"Search"],["CUST004","45-54","Standard",800,"Referral"],["CUST005","25-34","Premium",1800.3,"Social Media"]]},"domo-marketing":{columns:["campaign_id","campaign_name","channel","spend","impressions","clicks","conversions"],rows:[["CAMP001","Holiday Sale 2024","Social Media",5e3,1e5,2500,125],["CAMP002","New Product Launch","Search",8e3,15e4,3200,180],["CAMP003","Email Newsletter","Email",1200,25e3,800,45],["CAMP004","Retargeting Campaign","Display",3500,75e3,1800,95],["CAMP005","Influencer Partnership","Social Media",6e3,2e5,4500,220]]}}[e]||{columns:["id","name","value"],rows:[["1","Sample Data",100],["2","Test Record",200],["3","Demo Entry",300]]}}async createDuckDBTable(e,t,a){return console.log(`Creating table ${e} with mode ${a}`),console.log("Dataset:",t),Promise.resolve()}showLoading(e){const t=document.getElementById("loading-status");t&&(t.querySelector("p").textContent=e,t.style.display="block")}hideLoading(){const e=document.getElementById("loading-status");e&&(e.style.display="none")}showError(e){alert(`Error: ${e}`)}showSuccess(e){alert(`Success: ${e}`)}showDataPreview(e){console.log("Data preview:",e)}}window.domoDuckDBIntegration=new DomoDuckDBIntegration;