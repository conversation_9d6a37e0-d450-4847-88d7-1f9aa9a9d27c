function P(e,...t){return e.call(this,...t)}const d=Symbol("Unset"),b=Symbol("IsSetTracked"),h=Symbol("GetModKeys"),m=Symbol("GetOwnKey"),a=Symbol("GetOwnPath"),y=Symbol("GetParent"),S=(e={},t={},r=void 0,n=void 0)=>{if(r&&!r[b])throw new Error("SetTracked parent must be SetTracked");const o=Object.assign((()=>{}),t??{}),s=Object.keys(o),i=new Proxy(o,{get(o,c){switch(c){case d:return!(null!=r&&r[h].includes(n));case h:return s;case m:return n;case y:return r;case a:{const e=[n];let t=r;for(;void 0!==t;)e.unshift(t[m]),t=t[y];return e.join(".")}case b:return!0;case"toJSON":return()=>({...o});case"toString":case"toPrimitive":case Symbol.toPrimitive:return i[d]?n&&n in e?()=>e[n]:()=>"":t.toString.bind(t);default:return c in o||(o[c]=S(e,void 0,i,c)),o[c]}},set:(t,r,n)=>(s.push(r),"object"==typeof n&&(n=S(e,n,i,r)),t[r]=n,!0)});return i},G=(e,...t)=>0!==t.filter((e=>null==e?void 0:e[d])).length;export{G as h,P as p,S as s};