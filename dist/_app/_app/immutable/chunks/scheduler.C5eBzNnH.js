var F=Object.defineProperty,G=(t,n,e)=>n in t?F(t,n,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[n]=e,_=(t,n,e)=>G(t,"symbol"!=typeof n?n+"":n,e);function L(){}const bt=t=>t;function U(t,n){for(const e in n)t[e]=n[e];return t}function gt(t){return!!t&&("object"==typeof t||"function"==typeof t)&&"function"==typeof t.then}function J(t){return t()}function xt(){return Object.create(null)}function K(t){t.forEach(J)}function Q(t){return"function"==typeof t}function wt(t,n){return t!=t?n==n:t!==n||t&&"object"==typeof t||"function"==typeof t}let p;function Et(t,n){return t===n||(p||(p=document.createElement("a")),p.href=n,t===p.href)}function Tt(t){return 0===Object.keys(t).length}function M(t,...n){if(null==t){for(const t of n)t(void 0);return L}const e=t.subscribe(...n);return e.unsubscribe?()=>e.unsubscribe():e}function vt(t){let n;return M(t,(t=>n=t))(),n}function Nt(t,n,e){t.$$.on_destroy.push(M(n,e))}function kt(t,n,e,o){if(t){const i=P(t,n,e,o);return t[0](i)}}function P(t,n,e,o){return t[1]&&o?U(e.ctx.slice(),t[1](o(n))):e.ctx}function At(t,n,e,o){if(t[2]&&o){const i=t[2](o(e));if(void 0===n.dirty)return i;if("object"==typeof i){const t=[],e=Math.max(n.dirty.length,i.length);for(let o=0;o<e;o+=1)t[o]=n.dirty[o]|i[o];return t}return n.dirty|i}return n.dirty}function Ct(t,n,e,o,i,r){if(i){const s=P(n,e,o,r);t.p(s,i)}}function jt(t){if(t.ctx.length>32){const n=[],e=t.ctx.length/32;for(let t=0;t<e;t++)n[t]=-1;return n}return-1}function Dt(t){const n={};for(const e in t)"$"!==e[0]&&(n[e]=t[e]);return n}function St(t,n){const e={};n=new Set(n);for(const o in t)!n.has(o)&&"$"!==o[0]&&(e[o]=t[o]);return e}function Ht(t){const n={};for(const e in t)n[e]=!0;return n}function Lt(t){return t??""}function Mt(t,n,e){return t.set(e),n}function Pt(t){return t&&Q(t.destroy)?t.destroy:L}function Ot(t){const n="string"==typeof t&&t.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return n?[parseFloat(n[1]),n[2]||"px"]:[t,"px"]}const V=["",!0,1,"true","contenteditable"];let x=!1;function qt(){x=!0}function zt(){x=!1}function X(t,n,e,o){for(;t<n;){const i=t+(n-t>>1);e(i)<=o?t=i+1:n=i}return t}function Y(t){if(t.hydrate_init)return;t.hydrate_init=!0;let n=t.childNodes;if("HEAD"===t.nodeName){const t=[];for(let e=0;e<n.length;e++){const o=n[e];void 0!==o.claim_order&&t.push(o)}n=t}const e=new Int32Array(n.length+1),o=new Int32Array(n.length);e[0]=-1;let i=0;for(let t=0;t<n.length;t++){const r=n[t].claim_order,s=(i>0&&n[e[i]].claim_order<=r?i+1:X(1,i,(t=>n[e[t]].claim_order),r))-1;o[t]=e[s]+1;const c=s+1;e[c]=t,i=Math.max(c,i)}const r=[],s=[];let c=n.length-1;for(let t=e[i]+1;0!=t;t=o[t-1]){for(r.push(n[t-1]);c>=t;c--)s.push(n[c]);c--}for(;c>=0;c--)s.push(n[c]);r.reverse(),s.sort(((t,n)=>t.claim_order-n.claim_order));for(let n=0,e=0;n<s.length;n++){for(;e<r.length&&s[n].claim_order>=r[e].claim_order;)e++;const o=e<r.length?r[e]:null;t.insertBefore(s[n],o)}}function O(t,n){t.appendChild(n)}function Z(t){if(!t)return document;const n=t.getRootNode?t.getRootNode():t.ownerDocument;return n&&n.host?n:t.ownerDocument}function Bt(t){const n=w("style");return n.textContent="/* empty */",$(Z(t),n),n.sheet}function $(t,n){return O(t.head||t,n),n.sheet}function tt(t,n){if(x){for(Y(t),(void 0===t.actual_end_child||null!==t.actual_end_child&&t.actual_end_child.parentNode!==t)&&(t.actual_end_child=t.firstChild);null!==t.actual_end_child&&void 0===t.actual_end_child.claim_order;)t.actual_end_child=t.actual_end_child.nextSibling;n!==t.actual_end_child?(void 0!==n.claim_order||n.parentNode!==t)&&t.insertBefore(n,t.actual_end_child):t.actual_end_child=n.nextSibling}else(n.parentNode!==t||null!==n.nextSibling)&&t.appendChild(n)}function et(t,n,e){t.insertBefore(n,e||null)}function nt(t,n,e){x&&!e?tt(t,n):(n.parentNode!==t||n.nextSibling!=e)&&t.insertBefore(n,e||null)}function b(t){t.parentNode&&t.parentNode.removeChild(t)}function Rt(t,n){for(let e=0;e<t.length;e+=1)t[e]&&t[e].d(n)}function w(t){return document.createElement(t)}function q(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function A(t){return document.createTextNode(t)}function Wt(){return A(" ")}function It(){return A("")}function D(t,n,e,o){return t.addEventListener(n,e,o),()=>t.removeEventListener(n,e,o)}function Ft(t){return function(n){return n.preventDefault(),t.call(this,n)}}function Gt(t){return function(n){return n.stopPropagation(),t.call(this,n)}}function C(t,n,e){null==e?t.removeAttribute(n):t.getAttribute(n)!==e&&t.setAttribute(n,e)}const it=["width","height"];function st(t,n){const e=Object.getOwnPropertyDescriptors(t.__proto__);for(const o in n)null==n[o]?t.removeAttribute(o):"style"===o?t.style.cssText=n[o]:"__value"===o?t.value=t[o]=n[o]:e[o]&&e[o].set&&-1===it.indexOf(o)?t[o]=n[o]:C(t,o,n[o])}function Ut(t,n){for(const e in n)C(t,e,n[e])}function rt(t,n){Object.keys(n).forEach((e=>{ot(t,e,n[e])}))}function ot(t,n,e){const o=n.toLowerCase();o in t?t[o]="boolean"==typeof t[o]&&""===e||e:n in t?t[n]="boolean"==typeof t[n]&&""===e||e:C(t,n,e)}function Jt(t){return/-/.test(t)?rt:st}function Kt(t){return t.dataset.svelteH}function Qt(t){return""===t?null:+t}function Vt(t){return Array.from(t.childNodes)}function z(t){void 0===t.claim_info&&(t.claim_info={last_index:0,total_claimed:0})}function B(t,n,e,o,i=!1){z(t);const r=(()=>{for(let o=t.claim_info.last_index;o<t.length;o++){const r=t[o];if(n(r)){const n=e(r);return void 0===n?t.splice(o,1):t[o]=n,i||(t.claim_info.last_index=o),r}}for(let o=t.claim_info.last_index-1;o>=0;o--){const r=t[o];if(n(r)){const n=e(r);return void 0===n?t.splice(o,1):t[o]=n,i?void 0===n&&t.claim_info.last_index--:t.claim_info.last_index=o,r}}return o()})();return r.claim_order=t.claim_info.total_claimed,t.claim_info.total_claimed+=1,r}function R(t,n,e,o){return B(t,(t=>t.nodeName===n),(t=>{const n=[];for(let o=0;o<t.attributes.length;o++){const i=t.attributes[o];e[i.name]||n.push(i.name)}n.forEach((n=>t.removeAttribute(n)))}),(()=>o(n)))}function Xt(t,n,e){return R(t,n,e,w)}function Yt(t,n,e){return R(t,n,e,q)}function ct(t,n){return B(t,(t=>3===t.nodeType),(t=>{const e=""+n;if(t.data.startsWith(e)){if(t.data.length!==e.length)return t.splitText(e.length)}else t.data=e}),(()=>A(n)),!0)}function Zt(t){return ct(t," ")}function S(t,n,e){for(let o=e;o<t.length;o+=1){const e=t[o];if(8===e.nodeType&&e.textContent.trim()===n)return o}return-1}function $t(t,n){const e=S(t,"HTML_TAG_START",0),o=S(t,"HTML_TAG_END",e+1);if(-1===e||-1===o)return new E(n);z(t);const i=t.splice(e,o-e+1);b(i[0]),b(i[i.length-1]);const r=i.slice(1,i.length-1);if(0===r.length)return new E(n);for(const n of r)n.claim_order=t.claim_info.total_claimed,t.claim_info.total_claimed+=1;return new E(n,r)}function lt(t,n){n=""+n,t.data!==n&&(t.data=n)}function at(t,n){n=""+n,t.wholeText!==n&&(t.data=n)}function te(t,n,e){~V.indexOf(e)?at(t,n):lt(t,n)}function ee(t,n){t.value=n??""}function ne(t,n,e,o){null==e?t.style.removeProperty(n):t.style.setProperty(n,e,"")}function ie(t,n,e){for(let e=0;e<t.options.length;e+=1){const o=t.options[e];if(o.__value===n)return void(o.selected=!0)}(!e||void 0!==n)&&(t.selectedIndex=-1)}function se(t){const n=t.querySelector(":checked");return n&&n.__value}let y,g;function ut(){if(void 0===y){y=!1;try{typeof window<"u"&&window.parent&&window.parent.document}catch{y=!0}}return y}function re(t,n){"static"===getComputedStyle(t).position&&(t.style.position="relative");const e=w("iframe");e.setAttribute("style","display: block; position: absolute; top: 0; left: 0; width: 100%; height: 100%; overflow: hidden; border: 0; opacity: 0; pointer-events: none; z-index: -1;"),e.setAttribute("aria-hidden","true"),e.tabIndex=-1;const o=ut();let i;return o?(e.src="data:text/html,<script>onresize=function(){parent.postMessage(0,'*')}<\/script>",i=D(window,"message",(t=>{t.source===e.contentWindow&&n()}))):(e.src="about:blank",e.onload=()=>{i=D(e.contentWindow,"resize",n),n()}),O(t,e),()=>{(o||i&&e.contentWindow)&&i(),b(e)}}function oe(t,n,e){t.classList.toggle(n,!!e)}function ft(t,n,{bubbles:e=!1,cancelable:o=!1}={}){return new CustomEvent(t,{detail:n,bubbles:e,cancelable:o})}function ce(t,n){const e=[];let o=0;for(const i of n.childNodes)if(8===i.nodeType){const n=i.textContent.trim();n===`HEAD_${t}_END`?(o-=1,e.push(i)):n===`HEAD_${t}_START`&&(o+=1,e.push(i))}else o>0&&e.push(i);return e}class _t{constructor(t=!1){_(this,"is_svg",!1),_(this,"e"),_(this,"n"),_(this,"t"),_(this,"a"),this.is_svg=t,this.e=this.n=null}c(t){this.h(t)}m(t,n,e=null){this.e||(this.is_svg?this.e=q(n.nodeName):this.e=w(11===n.nodeType?"TEMPLATE":n.nodeName),this.t="TEMPLATE"!==n.tagName?n:n.content,this.c(t)),this.i(e)}h(t){this.e.innerHTML=t,this.n=Array.from("TEMPLATE"===this.e.nodeName?this.e.content.childNodes:this.e.childNodes)}i(t){for(let n=0;n<this.n.length;n+=1)et(this.t,this.n[n],t)}p(t){this.d(),this.h(t),this.i(this.a)}d(){this.n.forEach(b)}}class E extends _t{constructor(t=!1,n){super(t),_(this,"l"),this.e=this.n=null,this.l=n}c(t){this.l?this.n=this.l:super.c(t)}i(t){for(let n=0;n<this.n.length;n+=1)nt(this.t,this.n[n],t)}}function le(t,n){return new t(n)}function T(t){g=t}function u(){if(!g)throw new Error("Function called outside component initialization");return g}function ae(t){u().$$.before_update.push(t)}function ue(t){u().$$.on_mount.push(t)}function fe(t){u().$$.after_update.push(t)}function _e(t){u().$$.on_destroy.push(t)}function de(){const t=u();return(n,e,{cancelable:o=!1}={})=>{const i=t.$$.callbacks[n];if(i){const r=ft(n,e,{cancelable:o});return i.slice().forEach((n=>{n.call(t,r)})),!r.defaultPrevented}return!0}}function he(t,n){return u().$$.context.set(t,n),n}function me(t){return u().$$.context.get(t)}function pe(){return u().$$.context}function ye(t,n){const e=t.$$.callbacks[n.type];e&&e.slice().forEach((t=>t.call(this,n)))}const m=[],H=[];let h=[];const N=[],W=Promise.resolve();let k=!1;function dt(){k||(k=!0,W.then(mt))}function be(){return dt(),W}function ht(t){h.push(t)}function ge(t){N.push(t)}const v=new Set;let d=0;function mt(){if(0!==d)return;const t=g;do{try{for(;d<m.length;){const t=m[d];d++,T(t),pt(t.$$)}}catch(t){throw m.length=0,d=0,t}for(T(null),m.length=0,d=0;H.length;)H.pop()();for(let t=0;t<h.length;t+=1){const n=h[t];v.has(n)||(v.add(n),n())}h.length=0}while(m.length);for(;N.length;)N.pop()();k=!1,v.clear(),T(t)}function pt(t){if(null!==t.fragment){t.update(),K(t.before_update);const n=t.dirty;t.dirty=[-1],t.fragment&&t.fragment.p(t.ctx,n),t.after_update.forEach(ht)}}function xe(t){const n=[],e=[];h.forEach((o=>-1===t.indexOf(o)?n.push(o):e.push(o))),e.forEach((t=>t())),h=n}export{Qt as $,Pt as A,ue as B,M as C,H as D,Q as E,ee as F,ne as G,ce as H,Mt as I,_e as J,he as K,Rt as L,gt as M,u as N,T as O,mt as P,oe as Q,fe as R,be as S,le as T,St as U,U as V,Dt as W,Jt as X,vt as Y,Yt as Z,q as _,At as a,$t as a0,E as a1,Lt as a2,ge as a3,Z as a4,Bt as a5,bt as a6,ft as a7,Tt as a8,g as a9,xt as aa,xe as ab,J as ac,qt as ad,zt as ae,m as af,dt as ag,me as ah,st as ai,Ht as aj,te as ak,re as al,de as am,ae as an,se as ao,ie as ap,Ft as aq,Ut as ar,Et as as,pe as at,Ot as au,C as b,kt as c,b as d,tt as e,Gt as f,jt as g,Xt as h,nt as i,Vt as j,Zt as k,D as l,w as m,Wt as n,ye as o,ht as p,Kt as q,It as r,wt as s,Nt as t,Ct as u,L as v,lt as w,ct as x,A as y,K as z};