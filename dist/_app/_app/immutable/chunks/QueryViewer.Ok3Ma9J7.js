import{s as ge,v as $,d as c,b as d,i as N,e as m,h as I,j as b,Z as Le,m as v,_ as Oe,C as Qe,L as be,p as _e,l as le,G as V,k as P,n as B,w as Q,x as U,y as k,z as ze,F as ye,$ as et,r as Ce,a0 as tt,a1 as lt,q as Re,t as pe,Q as ee,a2 as it,I as Ae,D as nt,a3 as rt}from"./scheduler.C5eBzNnH.js";import{S as Ie,i as ve,d as se,t as H,f as ie,h as $e,a as w,m as oe,b as ae,e as ue,g as de,c as he,j as st}from"./index.BSd9q3aW.js";import{g as Ze,m as te,M as ot,e as ne,N as je,O as at,R as ut,S as Ee,P as De,U as ft,V as Ue}from"./VennDiagram.svelte_svelte_type_style_lang.CJe6lW-S.js";import{p as ct}from"./stores.C41LEeiH.js";const Wt=typeof window<"u"?window:typeof globalThis<"u"?globalThis:global;function dt(e){let t,l,n;return{c(){t=v("span"),l=Oe("svg"),n=Oe("path"),this.h()},l(e){t=I(e,"SPAN",{"aria-expanded":!0,class:!0});var s=b(t);l=Le(s,"svg",{viewBox:!0,width:!0,height:!0,class:!0});var a=b(l);n=Le(a,"path",{fill:!0,"fill-rule":!0,d:!0}),b(n).forEach(c),a.forEach(c),s.forEach(c),this.h()},h(){d(n,"fill",e[3]),d(n,"fill-rule","evenodd"),d(n,"d","M6.22 3.22a.75.75 0 011.06 0l4.25 4.25a.75.75 0 010 1.06l-4.25 4.25a.75.75 0 01-1.06-1.06L9.94 8 6.22 4.28a.75.75 0 010-1.06z"),d(l,"viewBox","0 0 16 16"),d(l,"width",e[1]),d(l,"height",e[1]),d(l,"class","svelte-lqleyo"),d(t,"aria-expanded",e[0]),d(t,"class","svelte-lqleyo")},m(e,s){N(e,t,s),m(t,l),m(l,n)},p(e,[s]){8&s&&d(n,"fill",e[3]),2&s&&d(l,"width",e[1]),2&s&&d(l,"height",e[1]),1&s&&d(t,"aria-expanded",e[0])},i:$,o:$,d(e){e&&c(t)}}}function ht(e,t,l){let n,s,a=$;e.$$.on_destroy.push((()=>a()));const{resolveColor:c}=Ze();let{toggled:r=!1}=t,{color:o="base-content"}=t,{size:i=10}=t;return e.$$set=e=>{"toggled"in e&&l(0,r=e.toggled),"color"in e&&l(4,o=e.color),"size"in e&&l(1,i=e.size)},e.$$.update=()=>{16&e.$$.dirty&&(l(2,n=c(o)),a(),a=Qe(n,(e=>l(3,s=e))))},[r,i,n,s,o]}class Je extends Ie{constructor(e){super(),ve(this,e,ht,dt,ge,{toggled:0,color:4,size:1})}}function ke(e,t,l){const n=e.slice();return n[12]=t[l],n[14]=l,n}function we(e,t,l){const n=e.slice();return n[15]=t[l],n[17]=l,n}function Me(e,t,l){const n=e.slice();return n[15]=t[l],n}function Pe(e,t,l){const n=e.slice();return n[15]=t[l],n}function Be(e){let t,l,n,s,a,r=e[15].id+"";return{c(){t=v("th"),l=k(r),this.h()},l(e){t=I(e,"TH",{class:!0,style:!0,evidencetype:!0,evidencetypefidelity:!0});var n=b(t);l=U(n,r),n.forEach(c),this.h()},h(){var l,c;d(t,"class",n="py-0 px-2 font-medium "+e[15].type+" svelte-ghf30y"),V(t,"width",e[6]+"%"),d(t,"evidencetype",s=(null==(l=e[15].evidenceColumnType)?void 0:l.evidenceType)||"unavailable"),d(t,"evidencetypefidelity",a=(null==(c=e[15].evidenceColumnType)?void 0:c.typeFidelity)||"unavailable")},m(e,n){N(e,t,n),m(t,l)},p(e,c){var o,i;8&c&&r!==(r=e[15].id+"")&&Q(l,r),8&c&&n!==(n="py-0 px-2 font-medium "+e[15].type+" svelte-ghf30y")&&d(t,"class",n),64&c&&V(t,"width",e[6]+"%"),8&c&&s!==(s=(null==(o=e[15].evidenceColumnType)?void 0:o.evidenceType)||"unavailable")&&d(t,"evidencetype",s),8&c&&a!==(a=(null==(i=e[15].evidenceColumnType)?void 0:i.typeFidelity)||"unavailable")&&d(t,"evidencetypefidelity",a)},d(e){e&&c(t)}}}function Fe(e){let t,l,n,s,a,r=e[15].type+"";return{c(){t=v("th"),l=k(r),this.h()},l(e){t=I(e,"TH",{class:!0,style:!0,evidencetype:!0,evidencetypefidelity:!0});var n=b(t);l=U(n,r),n.forEach(c),this.h()},h(){var l,c;d(t,"class",n=e[15].type+" type-indicator text-base-content-muted font-normal py-0 px-2 svelte-ghf30y"),V(t,"width",e[6]+"%"),d(t,"evidencetype",s=(null==(l=e[15].evidenceColumnType)?void 0:l.evidenceType)||"unavailable"),d(t,"evidencetypefidelity",a=(null==(c=e[15].evidenceColumnType)?void 0:c.typeFidelity)||"unavailable")},m(e,n){N(e,t,n),m(t,l)},p(e,c){var o,i;8&c&&r!==(r=e[15].type+"")&&Q(l,r),8&c&&n!==(n=e[15].type+" type-indicator text-base-content-muted font-normal py-0 px-2 svelte-ghf30y")&&d(t,"class",n),64&c&&V(t,"width",e[6]+"%"),8&c&&s!==(s=(null==(o=e[15].evidenceColumnType)?void 0:o.evidenceType)||"unavailable")&&d(t,"evidencetype",s),8&c&&a!==(a=(null==(i=e[15].evidenceColumnType)?void 0:i.typeFidelity)||"unavailable")&&d(t,"evidencetypefidelity",a)},d(e){e&&c(t)}}}function Et(e){let t,l=(e[2]+e[14]+1).toLocaleString()+"";return{c(){t=k(l)},l(e){t=U(e,l)},m(e,l){N(e,t,l)},p(e,n){4&n&&l!==(l=(e[2]+e[14]+1).toLocaleString()+"")&&Q(t,l)},d(e){e&&c(t)}}}function _t(e){let t,l=(e[2]+e[14]+1).toLocaleString()+"";return{c(){t=k(l)},l(e){t=U(e,l)},m(e,l){N(e,t,l)},p(e,n){4&n&&l!==(l=(e[2]+e[14]+1).toLocaleString()+"")&&Q(t,l)},d(e){e&&c(t)}}}function Tt(e){let t,l,n=(e[12][e[15].id]||"Ø")+"";return{c(){t=v("td"),l=k(n),this.h()},l(e){t=I(e,"TD",{class:!0,style:!0});var s=b(t);l=U(s,n),s.forEach(c),this.h()},h(){d(t,"class","other svelte-ghf30y"),V(t,"width",e[6]+"%")},m(e,n){N(e,t,n),m(t,l)},p(e,s){40&s&&n!==(n=(e[12][e[15].id]||"Ø")+"")&&Q(l,n),64&s&&V(t,"width",e[6]+"%")},d(e){e&&c(t)}}}function mt(e){let t,l,n,s,a=(e[12][e[15].id]??"Ø")+"";return{c(){t=v("td"),l=v("div"),n=k(a),this.h()},l(e){t=I(e,"TD",{class:!0,style:!0,title:!0});var s=b(t);l=I(s,"DIV",{class:!0});var r=b(l);n=U(r,a),r.forEach(c),s.forEach(c),this.h()},h(){d(l,"class","svelte-ghf30y"),d(t,"class","boolean svelte-ghf30y"),V(t,"width",e[6]+"%"),d(t,"title",s=e[12][e[15].id])},m(e,s){N(e,t,s),m(t,l),m(l,n)},p(e,l){40&l&&a!==(a=(e[12][e[15].id]??"Ø")+"")&&Q(n,a),64&l&&V(t,"width",e[6]+"%"),40&l&&s!==(s=e[12][e[15].id])&&d(t,"title",s)},d(e){e&&c(t)}}}function pt(e){let t,l,n,s,a=(e[12][e[15].id]||"Ø")+"";return{c(){t=v("td"),l=v("div"),n=k(a),this.h()},l(e){t=I(e,"TD",{class:!0,style:!0,title:!0});var s=b(t);l=I(s,"DIV",{class:!0});var r=b(l);n=U(r,a),r.forEach(c),s.forEach(c),this.h()},h(){d(l,"class","svelte-ghf30y"),d(t,"class","string svelte-ghf30y"),V(t,"width",e[6]+"%"),d(t,"title",s=e[12][e[15].id])},m(e,s){N(e,t,s),m(t,l),m(l,n)},p(e,l){40&l&&a!==(a=(e[12][e[15].id]||"Ø")+"")&&Q(n,a),64&l&&V(t,"width",e[6]+"%"),40&l&&s!==(s=e[12][e[15].id])&&d(t,"title",s)},d(e){e&&c(t)}}}function gt(e){let t,l,n,s,a=Ee(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary)+"";return{c(){t=v("td"),l=v("div"),n=k(a),this.h()},l(e){t=I(e,"TD",{class:!0,style:!0,title:!0});var s=b(t);l=I(s,"DIV",{class:!0});var r=b(l);n=U(r,a),r.forEach(c),s.forEach(c),this.h()},h(){d(l,"class","svelte-ghf30y"),d(t,"class","string svelte-ghf30y"),V(t,"width",e[6]+"%"),d(t,"title",s=Ee(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary))},m(e,s){N(e,t,s),m(t,l),m(l,n)},p(e,l){40&l&&a!==(a=Ee(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary)+"")&&Q(n,a),64&l&&V(t,"width",e[6]+"%"),40&l&&s!==(s=Ee(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary))&&d(t,"title",s)},d(e){e&&c(t)}}}function It(e){let t,l,n=Ee(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary)+"";return{c(){t=v("td"),l=k(n),this.h()},l(e){t=I(e,"TD",{class:!0,style:!0});var s=b(t);l=U(s,n),s.forEach(c),this.h()},h(){d(t,"class","number svelte-ghf30y"),V(t,"width",e[6]+"%")},m(e,n){N(e,t,n),m(t,l)},p(e,s){40&s&&n!==(n=Ee(e[12][e[15].id],e[3][e[17]].format,e[3][e[17]].columnUnitSummary)+"")&&Q(l,n),64&s&&V(t,"width",e[6]+"%")},d(e){e&&c(t)}}}function vt(e){let t,l,n;return{c(){t=v("td"),l=k("Ø"),this.h()},l(e){t=I(e,"TD",{class:!0,style:!0});var n=b(t);l=U(n,"Ø"),n.forEach(c),this.h()},h(){d(t,"class",n="text-base-content-muted "+e[3][e[17]].type+" svelte-ghf30y"),V(t,"width",e[6]+"%")},m(e,n){N(e,t,n),m(t,l)},p(e,l){8&l&&n!==(n="text-base-content-muted "+e[3][e[17]].type+" svelte-ghf30y")&&d(t,"class",n),64&l&&V(t,"width",e[6]+"%")},d(e){e&&c(t)}}}function Ge(e){let t;function l(e,t){return null==e[12][e[15].id]?vt:"number"===e[3][e[17]].type?It:"date"===e[3][e[17]].type?gt:"string"===e[3][e[17]].type?pt:"boolean"===e[3][e[17]].type?mt:Tt}let n=l(e),s=n(e);return{c(){s.c(),t=Ce()},l(e){s.l(e),t=Ce()},m(e,l){s.m(e,l),N(e,t,l)},p(e,a){n===(n=l(e))&&s?s.p(e,a):(s.d(1),s=n(e),s&&(s.c(),s.m(t.parentNode,t)))},d(e){e&&c(t),s.d(e)}}}function He(e){let t,l,n,s,a=(0===e[14]?_t:Et)(e),r=te(e[3]),o=[];for(let t=0;t<r.length;t+=1)o[t]=Ge(we(e,r,t));return{c(){t=v("tr"),l=v("td"),a.c(),n=B();for(let e=0;e<o.length;e+=1)o[e].c();s=B(),this.h()},l(e){t=I(e,"TR",{});var r=b(t);l=I(r,"TD",{class:!0,style:!0});var i=b(l);a.l(i),i.forEach(c),n=P(r);for(let e=0;e<o.length;e+=1)o[e].l(r);s=P(r),r.forEach(c),this.h()},h(){d(l,"class","index text-base-content-muted svelte-ghf30y"),V(l,"width","10%")},m(e,c){N(e,t,c),m(t,l),a.m(l,null),m(t,n);for(let e=0;e<o.length;e+=1)o[e]&&o[e].m(t,null);m(t,s)},p(e,l){if(a.p(e,l),104&l){let n;for(r=te(e[3]),n=0;n<r.length;n+=1){const a=we(e,r,n);o[n]?o[n].p(a,l):(o[n]=Ge(a),o[n].c(),o[n].m(t,s))}for(;n<o.length;n+=1)o[n].d(1);o.length=r.length}},d(e){e&&c(t),a.d(),be(o,e)}}}function Ve(e){let t,l,n,s,a,r,o,i,E,h=(e[2]+re).toLocaleString()+"",u=(e[4]+re).toLocaleString()+"";return{c(){t=v("div"),l=v("input"),n=B(),s=v("span"),a=k(h),r=k(" of "),o=k(u),this.h()},l(e){t=I(e,"DIV",{class:!0});var i=b(t);l=I(i,"INPUT",{type:!0,max:!0,step:!0,class:!0}),n=P(i),s=I(i,"SPAN",{class:!0});var d=b(s);a=U(d,h),r=U(d," of "),o=U(d,u),d.forEach(c),i.forEach(c),this.h()},h(){d(l,"type","range"),d(l,"max",e[4]),d(l,"step","1"),d(l,"class","slider bg-info/30 hover:bg-info/40 transition-colors svelte-ghf30y"),d(s,"class","text-xs svelte-ghf30y"),d(t,"class","pagination svelte-ghf30y")},m(c,d){N(c,t,d),m(t,l),ye(l,e[2]),m(t,n),m(t,s),m(s,a),m(s,r),m(s,o),i||(E=[le(l,"change",e[9]),le(l,"input",e[9]),le(l,"input",e[7])],i=!0)},p(e,t){16&t&&d(l,"max",e[4]),4&t&&ye(l,e[2]),4&t&&h!==(h=(e[2]+re).toLocaleString()+"")&&Q(a,h),16&t&&u!==(u=(e[4]+re).toLocaleString()+"")&&Q(o,u)},d(e){e&&c(t),i=!1,ze(E)}}}function bt(e){let t,l,n,s,a,r,o,i,E,h,u,f,T,p,R,g,y,L,O,S,A,C,D,$,U,M,x=te(e[3]),F=[];for(let t=0;t<x.length;t+=1)F[t]=Be(Pe(e,x,t));let G=te(e[3]),k=[];for(let t=0;t<G.length;t+=1)k[t]=Fe(Me(e,G,t));let Y=te(e[5]),Q=[];for(let t=0;t<Y.length;t+=1)Q[t]=He(ke(e,Y,t));let q=e[4]>0&&Ve(e);return C=new ot({props:{class:"download-button",data:e[1],queryID:e[0],display:!0}}),{c(){t=v("div"),l=v("div"),n=v("table"),s=v("thead"),a=v("tr"),r=v("th"),o=B();for(let e=0;e<F.length;e+=1)F[e].c();i=B(),E=v("tr"),h=B(),u=v("tr"),f=v("th"),T=B();for(let e=0;e<k.length;e+=1)k[e].c();p=B(),R=v("tr"),g=B(),y=v("tbody");for(let e=0;e<Q.length;e+=1)Q[e].c();O=B(),q&&q.c(),S=B(),A=v("div"),ue(C.$$.fragment),this.h()},l(e){t=I(e,"DIV",{class:!0});var d=b(t);l=I(d,"DIV",{class:!0});var m=b(l);n=I(m,"TABLE",{class:!0});var v=b(n);s=I(v,"THEAD",{});var N=b(s);a=I(N,"TR",{});var L=b(a);r=I(L,"TH",{class:!0,style:!0}),b(r).forEach(c),o=P(L);for(let e=0;e<F.length;e+=1)F[e].l(L);i=P(L),L.forEach(c),E=I(N,"TR",{}),b(E).forEach(c),h=P(N),u=I(N,"TR",{class:!0});var D=b(u);f=I(D,"TH",{class:!0,style:!0}),b(f).forEach(c),T=P(D);for(let e=0;e<k.length;e+=1)k[e].l(D);p=P(D),D.forEach(c),R=I(N,"TR",{}),b(R).forEach(c),N.forEach(c),g=P(v),y=I(v,"TBODY",{});var $=b(y);for(let e=0;e<Q.length;e+=1)Q[e].l($);$.forEach(c),v.forEach(c),m.forEach(c),O=P(d),q&&q.l(d),S=P(d),A=I(d,"DIV",{class:!0});var U=b(A);ae(C.$$.fragment,U),U.forEach(c),d.forEach(c),this.h()},h(){d(r,"class","py-0 px-2 font-medium index text-base-content-muted svelte-ghf30y"),V(r,"width","10%"),d(f,"class","py-0 px-2 index type-indicator text-base-content-muted font-normal svelte-ghf30y"),V(f,"width","10%"),d(u,"class","type-indicator svelte-ghf30y"),d(n,"class","text-xs svelte-ghf30y"),d(l,"class","scrollbox pretty-scrollbar svelte-ghf30y"),d(A,"class","footer svelte-ghf30y"),d(t,"class","results-pane py-1 svelte-ghf30y")},m(c,d){N(c,t,d),m(t,l),m(l,n),m(n,s),m(s,a),m(a,r),m(a,o);for(let e=0;e<F.length;e+=1)F[e]&&F[e].m(a,null);m(a,i),m(s,E),m(s,h),m(s,u),m(u,f),m(u,T);for(let e=0;e<k.length;e+=1)k[e]&&k[e].m(u,null);m(u,p),m(s,R),m(n,g),m(n,y);for(let e=0;e<Q.length;e+=1)Q[e]&&Q[e].m(y,null);m(t,O),q&&q.m(t,null),m(t,S),m(t,A),oe(C,A,null),$=!0,U||(M=le(y,"wheel",e[8]),U=!0)},p(e,[l]){if(72&l){let t;for(x=te(e[3]),t=0;t<x.length;t+=1){const n=Pe(e,x,t);F[t]?F[t].p(n,l):(F[t]=Be(n),F[t].c(),F[t].m(a,i))}for(;t<F.length;t+=1)F[t].d(1);F.length=x.length}if(72&l){let t;for(G=te(e[3]),t=0;t<G.length;t+=1){const n=Me(e,G,t);k[t]?k[t].p(n,l):(k[t]=Fe(n),k[t].c(),k[t].m(u,p))}for(;t<k.length;t+=1)k[t].d(1);k.length=G.length}if(108&l){let t;for(Y=te(e[5]),t=0;t<Y.length;t+=1){const n=ke(e,Y,t);Q[t]?Q[t].p(n,l):(Q[t]=He(n),Q[t].c(),Q[t].m(y,null))}for(;t<Q.length;t+=1)Q[t].d(1);Q.length=Y.length}e[4]>0?q?q.p(e,l):(q=Ve(e),q.c(),q.m(t,S)):q&&(q.d(1),q=null);const n={};2&l&&(n.data=e[1]),1&l&&(n.queryID=e[0]),C.$set(n)},i(e){$||(e&&(L||_e((()=>{L=$e(n,je,{}),L.start()}))),w(C.$$.fragment,e),e&&_e((()=>{$&&(D||(D=ie(t,ne,{},!0)),D.run(1))})),$=!0)},o(e){H(C.$$.fragment,e),e&&(D||(D=ie(t,ne,{},!1)),D.run(0)),$=!1},d(e){e&&c(t),be(F,e),be(k,e),be(Q,e),q&&q.d(),se(C),e&&D&&D.end(),U=!1,M()}}}let re=5;function Rt(e,t,l){let n,s,a,c,r,{queryID:o}=t,{data:i}=t,d=0;function E(){r=i.slice(d,d+re),l(5,c=r)}const h=ut((e=>{l(2,d=Math.min(Math.max(0,d+Math.floor(e.deltaY/Math.abs(e.deltaY))),a)),E()}),60);return e.$$set=e=>{"queryID"in e&&l(0,o=e.queryID),"data"in e&&l(1,i=e.data)},e.$$.update=()=>{2&e.$$.dirty&&l(3,n=at(i,"array")),8&e.$$.dirty&&l(6,s=90/(n.length+1)),2&e.$$.dirty&&l(4,a=Math.max(i.length-re,0)),6&e.$$.dirty&&l(5,c=i.slice(d,d+re))},[o,i,d,n,a,c,s,E,function(e){if(Math.abs(e.deltaX)>=Math.abs(e.deltaY))return;const t=e.deltaY<0&&0===d,l=e.deltaY>0&&d===a;t||l||(e.preventDefault(),h(e))},function(){d=et(this.value),l(2,d)}]}class Nt extends Ie{constructor(e){super(),ve(this,e,Rt,bt,ge,{queryID:0,data:1})}}const Ye={comment:{pattern:/(^|[^\\])(?:\/\*[\s\S]*?\*\/|(?:--|\/\/|#).*)/,lookbehind:!0},variable:[{pattern:/@(["'`])(?:\\[\s\S]|(?!\1)[^\\])+\1/,greedy:!0},/@[\w.$]+/],string:{pattern:/(^|[^@\\])("|')(?:\\[\s\S]|(?!\2)[^\\]|\2\2)*\2/,greedy:!0,lookbehind:!0},identifier:{pattern:/(^|[^@\\])`(?:\\[\s\S]|[^`\\]|``)*`/,greedy:!0,lookbehind:!0,inside:{punctuation:/^`|`$/}},function:/\b(?:AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\s*\()/i,keyword:/\b(?:ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:COL|_INSERT)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|KEYS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:ING|S)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\b/i,boolean:/\b(?:FALSE|NULL|TRUE)\b/i,number:/\b0x[\da-f]+\b|\b\d+(?:\.\d*)?|\B\.\d+\b/i,operator:/[-+*/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\b(?:AND|BETWEEN|DIV|ILIKE|IN|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\b/i,punctuation:/[;[\]()`,.]/};function St(e){let t,l,n,s,a,r=De.highlight(e[0],Ye)+"";return{c(){t=v("pre"),l=k("  "),n=v("code"),s=new lt(!1),a=k("\n"),this.h()},l(e){t=I(e,"PRE",{class:!0});var r=b(t);l=U(r,"  "),n=I(r,"CODE",{class:!0});var o=b(n);s=tt(o,!1),o.forEach(c),a=U(r,"\n"),r.forEach(c),this.h()},h(){s.a=null,d(n,"class","language-sql svelte-re3fhx"),d(t,"class","text-xs max-h-56 overflow-auto pretty-scrollbar")},m(e,c){N(e,t,c),m(t,l),m(t,n),s.m(r,n),m(t,a)},p(e,[t]){1&t&&r!==(r=De.highlight(e[0],Ye)+"")&&s.p(r)},i:$,o:$,d(e){e&&c(t)}}}function Lt(e,t,l){let{code:n=""}=t;return e.$$set=e=>{"code"in e&&l(0,n=e.code)},[n]}class xe extends Ie{constructor(e){super(),ve(this,e,Lt,St,ge,{code:0})}}function Ot(e){let t,l,n,s,a,r="Compiled",o="Written";return{c(){t=v("button"),t.textContent=r,l=B(),n=v("button"),n.textContent=o,this.h()},l(e){t=I(e,"BUTTON",{class:!0,"data-svelte-h":!0}),"svelte-1vzm9jy"!==Re(t)&&(t.textContent=r),l=P(e),n=I(e,"BUTTON",{class:!0,"data-svelte-h":!0}),"svelte-qu81ez"!==Re(n)&&(n.textContent=o),this.h()},h(){d(t,"class","off svelte-ska6l4"),d(n,"class","text-info bg-info/10 border border-info svelte-ska6l4")},m(c,r){N(c,t,r),N(c,l,r),N(c,n,r),s||(a=le(t,"click",e[1]),s=!0)},p:$,d(e){e&&(c(t),c(l),c(n)),s=!1,a()}}}function yt(e){let t,l,n,s,a,r="Compiled",o="Written";return{c(){t=v("button"),t.textContent=r,l=B(),n=v("button"),n.textContent=o,this.h()},l(e){t=I(e,"BUTTON",{class:!0,"data-svelte-h":!0}),"svelte-wrfleh"!==Re(t)&&(t.textContent=r),l=P(e),n=I(e,"BUTTON",{class:!0,"data-svelte-h":!0}),"svelte-v36xno"!==Re(n)&&(n.textContent=o),this.h()},h(){d(t,"class","text-info bg-info/10 border border-info svelte-ska6l4"),d(n,"class","off svelte-ska6l4")},m(c,r){N(c,t,r),N(c,l,r),N(c,n,r),s||(a=le(n,"click",e[1]),s=!0)},p:$,d(e){e&&(c(t),c(l),c(n)),s=!1,a()}}}function Ct(e){let t,l,n;function s(e,t){return e[0]?yt:Ot}let a=s(e),r=a(e);return{c(){t=v("div"),r.c(),this.h()},l(e){t=I(e,"DIV",{class:!0});var l=b(t);r.l(l),l.forEach(c),this.h()},h(){d(t,"class","toggle svelte-ska6l4")},m(e,l){N(e,t,l),r.m(t,null),n=!0},p(e,[l]){a===(a=s(e))&&r?r.p(e,l):(r.d(1),r=a(e),r&&(r.c(),r.m(t,null)))},i(e){n||(e&&_e((()=>{n&&(l||(l=ie(t,ne,{},!0)),l.run(1))})),n=!0)},o(e){e&&(l||(l=ie(t,ne,{},!1)),l.run(0)),n=!1},d(e){e&&c(t),r.d(),e&&l&&l.end()}}}function At(e,t,l){let{showCompiled:n}=t;return e.$$set=e=>{"showCompiled"in e&&l(0,n=e.showCompiled)},[n,function(){l(0,n=!n)}]}class Dt extends Ie{constructor(e){super(),ve(this,e,At,Ct,ge,{showCompiled:0})}}function qe(e){let t,l,n,s,a,r,o,i,E,h,u,f,T,p,R,g,y;s=new Je({props:{toggled:e[10]}});let L=e[10]&&e[4]&&We(e),O=e[10]&&Ke(e);const S=[Bt,Pt,Mt,wt],A=[];function C(e,t){return e[6]?0:e[8]?1:e[2].loading?2:3}u=C(e),f=A[u]=S[u](e);let D=e[8]>0&&!e[6]&&e[9]&&Xe(e);return{c(){t=v("div"),l=v("div"),n=v("button"),ue(s.$$.fragment),a=B(),r=k(e[0]),o=B(),L&&L.c(),i=B(),O&&O.c(),E=B(),h=v("button"),f.c(),T=B(),D&&D.c(),this.h()},l(d){t=I(d,"DIV",{class:!0});var u=b(t);l=I(u,"DIV",{class:!0});var m=b(l);n=I(m,"BUTTON",{type:!0,"aria-label":!0,class:!0});var v=b(n);ae(s.$$.fragment,v),a=P(v),r=U(v,e[0]),v.forEach(c),o=P(m),L&&L.l(m),i=P(m),O&&O.l(m),m.forEach(c),E=P(u),h=I(u,"BUTTON",{type:!0,"aria-label":!0,class:!0});var N=b(h);f.l(N),N.forEach(c),T=P(u),D&&D.l(u),u.forEach(c),this.h()},h(){d(n,"type","button"),d(n,"aria-label","show-sql"),d(n,"class","title svelte-1ursthx"),d(l,"class","container-a svelte-1ursthx"),d(h,"type","button"),d(h,"aria-label","view-query"),d(h,"class",it("status-bar")+" svelte-1ursthx"),ee(h,"error",e[6]),ee(h,"success",!e[6]),ee(h,"open",e[9]),ee(h,"closed",!e[9]),d(t,"class","scrollbox my-3 svelte-1ursthx")},m(c,d){N(c,t,d),m(t,l),m(l,n),oe(s,n,null),m(n,a),m(n,r),m(l,o),L&&L.m(l,null),m(l,i),O&&O.m(l,null),m(t,E),m(t,h),A[u].m(h,null),m(t,T),D&&D.m(t,null),R=!0,g||(y=[le(n,"click",e[15]),le(h,"click",e[16])],g=!0)},p(e,n){const a={};1024&n&&(a.toggled=e[10]),s.$set(a),(!R||1&n)&&Q(r,e[0]),e[10]&&e[4]?L?(L.p(e,n),1040&n&&w(L,1)):(L=We(e),L.c(),w(L,1),L.m(l,i)):L&&(de(),H(L,1,1,(()=>{L=null})),he()),e[10]?O?(O.p(e,n),1024&n&&w(O,1)):(O=Ke(e),O.c(),w(O,1),O.m(l,null)):O&&(de(),H(O,1,1,(()=>{O=null})),he());let c=u;u=C(e),u===c?A[u].p(e,n):(de(),H(A[c],1,1,(()=>{A[c]=null})),he(),f=A[u],f?f.p(e,n):(f=A[u]=S[u](e),f.c()),w(f,1),f.m(h,null)),(!R||64&n)&&ee(h,"error",e[6]),(!R||64&n)&&ee(h,"success",!e[6]),(!R||512&n)&&ee(h,"open",e[9]),(!R||512&n)&&ee(h,"closed",!e[9]),e[8]>0&&!e[6]&&e[9]?D?(D.p(e,n),832&n&&w(D,1)):(D=Xe(e),D.c(),w(D,1),D.m(t,null)):D&&(de(),H(D,1,1,(()=>{D=null})),he())},i(e){R||(w(s.$$.fragment,e),w(L),w(O),w(f),w(D),e&&_e((()=>{R&&(p||(p=ie(t,ne,{},!0)),p.run(1))})),R=!0)},o(e){H(s.$$.fragment,e),H(L),H(O),H(f),H(D),e&&(p||(p=ie(t,ne,{},!1)),p.run(0)),R=!1},d(e){e&&c(t),se(s),L&&L.d(),O&&O.d(),A[u].d(),D&&D.d(),e&&p&&p.end(),g=!1,ze(y)}}}function We(e){let t,l,n;function s(t){e[20](t)}let a={};return void 0!==e[5]&&(a.showCompiled=e[5]),t=new Dt({props:a}),nt.push((()=>st(t,"showCompiled",s))),{c(){ue(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,l){oe(t,e,l),n=!0},p(e,n){const s={};!l&&32&n&&(l=!0,s.showCompiled=e[5],rt((()=>l=!1))),t.$set(s)},i(e){n||(w(t.$$.fragment,e),n=!0)},o(e){H(t.$$.fragment,e),n=!1},d(e){se(t,e)}}}function Ke(e){let t,l,n,s,a;const r=[kt,Ut],o=[];function i(e,t){return e[5]?0:1}return l=i(e),n=o[l]=r[l](e),{c(){t=v("div"),n.c(),this.h()},l(e){t=I(e,"DIV",{class:!0});var l=b(t);n.l(l),l.forEach(c),this.h()},h(){d(t,"class","code-container svelte-1ursthx")},m(e,n){N(e,t,n),o[l].m(t,null),a=!0},p(e,s){let a=l;l=i(e),l===a?o[l].p(e,s):(de(),H(o[a],1,1,(()=>{o[a]=null})),he(),n=o[l],n?n.p(e,s):(n=o[l]=r[l](e),n.c()),w(n,1),n.m(t,null))},i(e){a||(w(n),e&&_e((()=>{a&&(s||(s=ie(t,ne,{},!0)),s.run(1))})),a=!0)},o(e){H(n),e&&(s||(s=ie(t,ne,{},!1)),s.run(0)),a=!1},d(e){e&&c(t),o[l].d(),e&&s&&s.end()}}}function Ut(e){let t,l;return t=new xe({props:{code:e[3]}}),{c(){ue(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,n){oe(t,e,n),l=!0},p(e,l){const n={};8&l&&(n.code=e[3]),t.$set(n)},i(e){l||(w(t.$$.fragment,e),l=!0)},o(e){H(t.$$.fragment,e),l=!1},d(e){se(t,e)}}}function kt(e){let t,l;return t=new xe({props:{code:e[1].originalText}}),{c(){ue(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,n){oe(t,e,n),l=!0},p(e,l){const n={};2&l&&(n.code=e[1].originalText),t.$set(n)},i(e){l||(w(t.$$.fragment,e),l=!0)},o(e){H(t.$$.fragment,e),l=!1},d(e){se(t,e)}}}function wt(e){let t;return{c(){t=k("ran successfully but no data was returned")},l(e){t=U(e,"ran successfully but no data was returned")},m(e,l){N(e,t,l)},p:$,i:$,o:$,d(e){e&&c(t)}}}function Mt(e){let t;return{c(){t=k("loading...")},l(e){t=U(e,"loading...")},m(e,l){N(e,t,l)},p:$,i:$,o:$,d(e){e&&c(t)}}}function Pt(e){let t,l,n,s,a,r,o,i,d,E,h=e[8].toLocaleString()+"",u=e[8]>1?"records":"record",I=e[7].toLocaleString()+"",f=e[7]>1?"properties":"property";return t=new Je({props:{toggled:e[9],color:e[12].colors.info}}),{c(){ue(t.$$.fragment),l=B(),n=k(h),s=B(),a=k(u),r=k(" with "),o=k(I),i=B(),d=k(f)},l(e){ae(t.$$.fragment,e),l=P(e),n=U(e,h),s=P(e),a=U(e,u),r=U(e," with "),o=U(e,I),i=P(e),d=U(e,f)},m(e,c){oe(t,e,c),N(e,l,c),N(e,n,c),N(e,s,c),N(e,a,c),N(e,r,c),N(e,o,c),N(e,i,c),N(e,d,c),E=!0},p(e,l){const s={};512&l&&(s.toggled=e[9]),4096&l&&(s.color=e[12].colors.info),t.$set(s),(!E||256&l)&&h!==(h=e[8].toLocaleString()+"")&&Q(n,h),(!E||256&l)&&u!==(u=e[8]>1?"records":"record")&&Q(a,u),(!E||128&l)&&I!==(I=e[7].toLocaleString()+"")&&Q(o,I),(!E||128&l)&&f!==(f=e[7]>1?"properties":"property")&&Q(d,f)},i(e){E||(w(t.$$.fragment,e),E=!0)},o(e){H(t.$$.fragment,e),E=!1},d(e){e&&(c(l),c(n),c(s),c(a),c(r),c(o),c(i),c(d)),se(t,e)}}}function Bt(e){let t,l=e[6].message+"";return{c(){t=k(l)},l(e){t=U(e,l)},m(e,l){N(e,t,l)},p(e,n){64&n&&l!==(l=e[6].message+"")&&Q(t,l)},i:$,o:$,d(e){e&&c(t)}}}function Xe(e){let t,l;return t=new Nt({props:{data:e[1],queryID:e[0]}}),{c(){ue(t.$$.fragment)},l(e){ae(t.$$.fragment,e)},m(e,n){oe(t,e,n),l=!0},p(e,l){const n={};2&l&&(n.data=e[1]),1&l&&(n.queryID=e[0]),t.$set(n)},i(e){l||(w(t.$$.fragment,e),l=!0)},o(e){H(t.$$.fragment,e),l=!1},d(e){se(t,e)}}}function Ft(e){let t,l,n,s=e[11]&&qe(e);return{c(){t=v("div"),s&&s.c(),this.h()},l(e){t=I(e,"DIV",{class:!0});var l=b(t);s&&s.l(l),l.forEach(c),this.h()},h(){d(t,"class","over-container svelte-1ursthx")},m(e,l){N(e,t,l),s&&s.m(t,null),n=!0},p(e,[l]){e[11]?s?(s.p(e,l),2048&l&&w(s,1)):(s=qe(e),s.c(),w(s,1),s.m(t,null)):s&&(de(),H(s,1,1,(()=>{s=null})),he())},i(e){n||(w(s),e&&(l||_e((()=>{l=$e(t,je,{}),l.start()}))),n=!0)},o(e){H(s),n=!1},d(e){e&&c(t),s&&s.d()}}}function Gt(e,t,l){let n,s,a,c,r,o,i,d,E,h=$,u=()=>(h(),h=Qe(f,(e=>l(2,c=e))),f);pe(e,ct,(e=>l(19,i=e))),pe(e,ft,(e=>l(11,d=e))),e.$$.on_destroy.push((()=>h()));let{queryID:I}=t,{queryResult:f}=t;u();let T=Ue("showSQL_".concat(I),!1);pe(e,T,(e=>l(10,o=e)));let m=Ue(`showResults_${I}`);pe(e,m,(e=>l(9,r=e)));let v,N,p,R=!0;const{theme:g}=Ze();return pe(e,g,(e=>l(12,E=e))),e.$$set=e=>{"queryID"in e&&l(0,I=e.queryID),"queryResult"in e&&u(l(1,f=e.queryResult))},e.$$.update=()=>{if(524288&e.$$.dirty&&l(18,n=i.data.evidencemeta.queries),4&e.$$.dirty&&l(6,p=c?c.error:new Error("queryResult is undefined")),4&e.$$.dirty&&l(8,s=(null==c?void 0:c.length)??0),4&e.$$.dirty&&l(7,a=c.columns.length??(null==c?void 0:c._evidenceColumnTypes.length)??0),262145&e.$$.dirty){let e=null==n?void 0:n.find((e=>e.id===I));e&&(l(3,v=e.inputQueryString),l(4,N=e.compiled&&void 0===e.compileError))}},[I,f,c,v,N,R,p,a,s,r,o,d,E,T,m,function(){Ae(T,o=!o,o)},function(){!p&&c.length>0&&Ae(m,r=!r,r)},g,n,i,function(e){R=e,l(5,R)}]}class Kt extends Ie{constructor(e){super(),ve(this,e,Gt,Ft,ge,{queryID:0,queryResult:1})}}export{Kt as Q,Wt as g};