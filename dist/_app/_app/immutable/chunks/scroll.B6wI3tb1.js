import{n as v}from"./VennDiagram.svelte_svelte_type_style_lang.CJe6lW-S.js";const g=()=>typeof window<"u";function L(){const t=navigator.userAgentData;return(null==t?void 0:t.platform)??navigator.platform}const y=t=>g()&&t.test(L().toLowerCase()),w=()=>g()&&!!navigator.maxTouchPoints,A=()=>y(/^mac/)&&!w(),T=()=>y(/mac|iphone|ipad|ipod/i),$=()=>T()&&!A(),d="data-melt-scroll-lock";function p(t,e){if(!t)return;const o=t.style.cssText;return Object.assign(t.style,e),()=>{t.style.cssText=o}}function C(t,e,o){if(!t)return;const n=t.style.getPropertyValue(e);return t.style.setProperty(e,o),()=>{n?t.style.setProperty(e,n):t.style.removeProperty(e)}}function M(t){const e=t.getBoundingClientRect().left;return Math.round(e)+t.scrollLeft?"paddingLeft":"paddingRight"}function D(t){const e=document,o=e.defaultView??window,{documentElement:n,body:r}=e;if(r.hasAttribute(d))return v;r.setAttribute(d,"");const l=o.innerWidth-n.clientWidth,i=M(n),s=o.getComputedStyle(r)[i],a=[C(n,"--scrollbar-width",`${l}px`),$()?(()=>{const{scrollX:t,scrollY:e,visualViewport:n}=o,a=(null==n?void 0:n.offsetLeft)??0,c=(null==n?void 0:n.offsetTop)??0,u=p(r,{position:"fixed",overflow:"hidden",top:-(e-Math.floor(c))+"px",left:-(t-Math.floor(a))+"px",right:"0",[i]:`calc(${s} + ${l}px)`});return()=>{null==u||u(),o.scrollTo(t,e)}})():p(r,{overflow:"hidden",[i]:`calc(${s} + ${l}px)`})];return()=>{a.forEach((t=>null==t?void 0:t())),r.removeAttribute(d)}}export{D as r};