import{s as kl,d as s,i as d,e as _,b as n,F as It,G as ht,k as v,H as wl,h as u,r as Tt,q as w,j as q,n as p,m,t as dl,I as Cl,J as bl,K as Tl,B as Ll,v as xe,x as Le,y as ge,L as gl,w as Be}from"../chunks/scheduler.C5eBzNnH.js";import{S as xl,i as Ml,d as kt,t as Ue,a as Oe,c as Dl,m as wt,b as Ct,e as bt,g as Hl}from"../chunks/index.BSd9q3aW.js";import{g as El,Q as Il}from"../chunks/QueryViewer.Ok3Ma9J7.js";import{h as qt,j as ql,k as Sl,Q as Ol,p as Rl,l as cl,m as ul,r as ml,o as Nl}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.CJe6lW-S.js";import{w as Pl}from"../chunks/entry.CjmEikbu.js";import{h as fl,p as Ul}from"../chunks/setTrackProxy.DjIbdjlZ.js";import{p as Vl}from"../chunks/stores.C41LEeiH.js";const{document:ae}=El;function vl(e,t,s){const a=e.slice();return a[22]=t[s],a}function Al(e){let t,a,l=b.title+"";return{c(){t=m("h1"),a=ge(l),this.h()},l(e){t=u(e,"H1",{class:!0});var n=q(t);a=Le(n,l),n.forEach(s),this.h()},h(){n(t,"class","title")},m(e,s){d(e,t,s),_(t,a)},p:xe,d(e){e&&s(t)}}}function Bl(e){return{c(){this.h()},l(e){this.h()},h(){ae.title="Evidence"},m:xe,p:xe,d:xe}}function Fl(e){let t,a,l,o,r;return ae.title=t=b.title,{c(){a=p(),l=m("meta"),o=p(),r=m("meta"),this.h()},l(e){a=v(e),l=u(e,"META",{property:!0,content:!0}),o=v(e),r=u(e,"META",{name:!0,content:!0}),this.h()},h(){var e,t;n(l,"property","og:title"),n(l,"content",(null==(e=b.og)?void 0:e.title)??b.title),n(r,"name","twitter:title"),n(r,"content",(null==(t=b.og)?void 0:t.title)??b.title)},m(e,t){d(e,a,t),d(e,l,t),d(e,o,t),d(e,r,t)},p(e,s){0&s&&t!==(t=b.title)&&(ae.title=t)},d(e){e&&(s(a),s(l),s(o),s(r))}}}function $l(e){var t,a;let n,l,o=(b.description||(null==(t=b.og)?void 0:t.description))&&jl(),r=(null==(a=b.og)?void 0:a.image)&&Gl();return{c(){o&&o.c(),n=p(),r&&r.c(),l=Tt()},l(e){o&&o.l(e),n=v(e),r&&r.l(e),l=Tt()},m(e,t){o&&o.m(e,t),d(e,n,t),r&&r.m(e,t),d(e,l,t)},p(e,t){var s,a;(b.description||null!=(s=b.og)&&s.description)&&o.p(e,t),null!=(a=b.og)&&a.image&&r.p(e,t)},d(e){e&&(s(n),s(l)),o&&o.d(e),r&&r.d(e)}}}function jl(e){let t,a,l,o,r;return{c(){t=m("meta"),a=p(),l=m("meta"),o=p(),r=m("meta"),this.h()},l(e){t=u(e,"META",{name:!0,content:!0}),a=v(e),l=u(e,"META",{property:!0,content:!0}),o=v(e),r=u(e,"META",{name:!0,content:!0}),this.h()},h(){var e,s,a;n(t,"name","description"),n(t,"content",b.description??(null==(e=b.og)?void 0:e.description)),n(l,"property","og:description"),n(l,"content",(null==(s=b.og)?void 0:s.description)??b.description),n(r,"name","twitter:description"),n(r,"content",(null==(a=b.og)?void 0:a.description)??b.description)},m(e,s){d(e,t,s),d(e,a,s),d(e,l,s),d(e,o,s),d(e,r,s)},p:xe,d(e){e&&(s(t),s(a),s(l),s(o),s(r))}}}function Gl(e){let t,a,l;return{c(){t=m("meta"),a=p(),l=m("meta"),this.h()},l(e){t=u(e,"META",{property:!0,content:!0}),a=v(e),l=u(e,"META",{name:!0,content:!0}),this.h()},h(){var e,s;n(t,"property","og:image"),n(t,"content",cl(null==(e=b.og)?void 0:e.image)),n(l,"name","twitter:image"),n(l,"content",cl(null==(s=b.og)?void 0:s.image))},m(e,s){d(e,t,s),d(e,a,s),d(e,l,s)},p:xe,d(e){e&&(s(t),s(a),s(l))}}}function Ql(e){let t,a='<h4 class="svelte-10vyee7">🛠️ Development Mode</h4> <p>Running in development mode with mock Domo data.</p>';return{c(){t=m("div"),t.innerHTML=a,this.h()},l(e){t=u(e,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-eca4iq"!==w(t)&&(t.innerHTML=a),this.h()},h(){n(t,"class","dev-info svelte-10vyee7")},m(e,s){d(e,t,s)},p:xe,d(e){e&&s(t)}}}function zl(e){let t,a,l,o,r,i="🔗 Connected to Domo",c="This Evidence app is running in your Domo environment with access to your datasets.",h=e[3]&&pl(e);return{c(){t=m("div"),a=m("h4"),a.textContent=i,l=p(),h&&h.c(),o=p(),r=m("p"),r.textContent=c,this.h()},l(e){t=u(e,"DIV",{class:!0});var n=q(t);a=u(n,"H4",{class:!0,"data-svelte-h":!0}),"svelte-xeif1v"!==w(a)&&(a.textContent=i),l=v(n),h&&h.l(n),o=v(n),r=u(n,"P",{"data-svelte-h":!0}),"svelte-fqc5um"!==w(r)&&(r.textContent=c),n.forEach(s),this.h()},h(){n(a,"class","svelte-10vyee7"),n(t,"class","domo-info svelte-10vyee7")},m(e,s){d(e,t,s),_(t,a),_(t,l),h&&h.m(t,null),_(t,o),_(t,r)},p(e,s){e[3]?h?h.p(e,s):(h=pl(e),h.c(),h.m(t,o)):h&&(h.d(1),h=null)},d(e){e&&s(t),h&&h.d()}}}function pl(e){let t,a,n,l,o=(e[3].displayName||e[3].name)+"";return{c(){t=m("p"),a=ge("Welcome, "),n=ge(o),l=ge("!")},l(e){t=u(e,"P",{});var r=q(t);a=Le(r,"Welcome, "),n=Le(r,o),l=Le(r,"!"),r.forEach(s)},m(e,s){d(e,t,s),_(t,a),_(t,n),_(t,l)},p(e,t){8&t&&o!==(o=(e[3].displayName||e[3].name)+"")&&Be(n,o)},d(e){e&&s(t)}}}function Wl(e){let t,a='<p class="svelte-10vyee7">No datasets loaded yet. Use the workflow picker above to load your first dataset.</p>';return{c(){t=m("div"),t.innerHTML=a,this.h()},l(e){t=u(e,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-1io82co"!==w(t)&&(t.innerHTML=a),this.h()},h(){n(t,"class","no-datasets svelte-10vyee7")},m(e,s){d(e,t,s)},p:xe,d(e){e&&s(t)}}}function Yl(e){let t,a,l,o="Successfully Loaded Datasets",r=ul(e[1]),i=[];for(let t=0;t<r.length;t+=1)i[t]=_l(vl(e,r,t));return{c(){t=m("div"),a=m("h3"),a.textContent=o,l=p();for(let e=0;e<i.length;e+=1)i[e].c();this.h()},l(e){t=u(e,"DIV",{class:!0});var n=q(t);a=u(n,"H3",{"data-svelte-h":!0}),"svelte-1043zgz"!==w(a)&&(a.textContent=o),l=v(n);for(let e=0;e<i.length;e+=1)i[e].l(n);n.forEach(s),this.h()},h(){n(t,"class","loaded-datasets svelte-10vyee7")},m(e,s){d(e,t,s),_(t,a),_(t,l);for(let e=0;e<i.length;e+=1)i[e]&&i[e].m(t,null)},p(e,s){if(2&s){let a;for(r=ul(e[1]),a=0;a<r.length;a+=1){const n=vl(e,r,a);i[a]?i[a].p(n,s):(i[a]=_l(n),i[a].c(),i[a].m(t,null))}for(;a<i.length;a+=1)i[a].d(1);i.length=r.length}},d(e){e&&s(t),gl(i,e)}}}function _l(e){let t,a,l,o,r,i,c,h,g,y,f,k,b,T,L,C,M,D,H,E=e[22].datasetName+"",x="Table Name:",$=e[22].tableName+"",I="Rows:",O=e[22].rowCount.toLocaleString()+"",S="Dataset ID:",R=e[22].datasetId+"";return{c(){t=m("div"),a=m("h4"),l=ge(E),o=p(),r=m("p"),i=m("strong"),i.textContent=x,c=p(),h=ge($),g=p(),y=m("p"),f=m("strong"),f.textContent=I,k=p(),b=ge(O),T=p(),L=m("p"),C=m("strong"),C.textContent=S,M=p(),D=ge(R),H=p(),this.h()},l(e){t=u(e,"DIV",{class:!0});var n=q(t);a=u(n,"H4",{class:!0});var d=q(a);l=Le(d,E),d.forEach(s),o=v(n),r=u(n,"P",{class:!0});var m=q(r);i=u(m,"STRONG",{"data-svelte-h":!0}),"svelte-1t5jaf3"!==w(i)&&(i.textContent=x),c=v(m),h=Le(m,$),m.forEach(s),g=v(n),y=u(n,"P",{class:!0});var p=q(y);f=u(p,"STRONG",{"data-svelte-h":!0}),"svelte-wddeo5"!==w(f)&&(f.textContent=I),k=v(p),b=Le(p,O),p.forEach(s),T=v(n),L=u(n,"P",{class:!0});var _=q(L);C=u(_,"STRONG",{"data-svelte-h":!0}),"svelte-oldt31"!==w(C)&&(C.textContent=S),M=v(_),D=Le(_,R),_.forEach(s),H=v(n),n.forEach(s),this.h()},h(){n(a,"class","svelte-10vyee7"),n(r,"class","svelte-10vyee7"),n(y,"class","svelte-10vyee7"),n(L,"class","svelte-10vyee7"),n(t,"class","dataset-card svelte-10vyee7")},m(e,s){d(e,t,s),_(t,a),_(a,l),_(t,o),_(t,r),_(r,i),_(r,c),_(r,h),_(t,g),_(t,y),_(y,f),_(y,k),_(y,b),_(t,T),_(t,L),_(L,C),_(L,M),_(L,D),_(t,H)},p(e,t){2&t&&E!==(E=e[22].datasetName+"")&&Be(l,E),2&t&&$!==($=e[22].tableName+"")&&Be(h,$),2&t&&O!==(O=e[22].rowCount.toLocaleString()+"")&&Be(b,O),2&t&&R!==(R=e[22].datasetId+"")&&Be(D,R)},d(e){e&&s(t)}}}function yl(e){let t,s;return t=new Il({props:{queryID:"my_analysis",queryResult:e[0]}}),{c(){bt(t.$$.fragment)},l(e){Ct(t.$$.fragment,e)},m(e,a){wt(t,e,a),s=!0},p(e,s){const a={};1&s&&(a.queryResult=e[0]),t.$set(a)},i(e){s||(Oe(t.$$.fragment,e),s=!0)},o(e){Ue(t.$$.fragment,e),s=!1},d(e){kt(t,e)}}}function Xl(e){let t,a,l,o,r,i,c,h,g,y,f,k,T,L,C,M,D,H,E,x,$,I,O,S,R,N,B,P,U,j,V,A,F,G,z,W,Q,Y,X,J,K,Z,ee,te,se,ne,le,oe,re,de,ie,ce,me,ve,ue,pe,he,we,ge,ye,fe,_e,ke,be,Te,Le,Ce,Me,De,He,Ee,xe,qe,$e,Ie,Se,Re,Ne,Be,Pe,je,Ve,Ae,Fe,Ge,ze,We,Qe,Ye,Xe,Je='<a href="#domo-dataset-to-duckdb-workflow">Domo Dataset to DuckDB Workflow</a>',Ke="This page allows you to select and load Domo datasets into DuckDB for analysis in Evidence.",Ze='<a href="#how-it-works">How it Works</a>',et='<li class="markdown"><strong class="markdown">Select a Dataset</strong>: Choose from available Domo datasets in your instance</li> <li class="markdown"><strong class="markdown">Preview Data</strong>: Review the dataset schema and sample data</li> <li class="markdown"><strong class="markdown">Configure Loading</strong>: Set table name and refresh mode</li> <li class="markdown"><strong class="markdown">Load into DuckDB</strong>: Import the data for use in Evidence queries</li>',tt='<h3 class="svelte-10vyee7">Domo Dataset Workflow</h3> <p class="svelte-10vyee7">Select and load Domo datasets into DuckDB for analysis</p>',st="Select Dataset:",at="Choose a dataset...",nt='<h4>Dataset Information</h4> <div id="dataset-info" class="dataset-info svelte-10vyee7"></div> <h5>Schema</h5> <div id="schema-table" class="schema-table"></div> <div class="preview-actions svelte-10vyee7"><button id="preview-data-btn" class="btn btn-secondary svelte-10vyee7">Preview Data</button></div> <div id="data-preview" class="data-preview svelte-10vyee7" style="display: none;"></div>',lt="Loading Configuration",ot='<label for="table-name" class="svelte-10vyee7">Table Name in DuckDB:</label> <input id="table-name" type="text" placeholder="Enter table name" class="svelte-10vyee7"/>',rt="Refresh Mode:",dt="Replace existing data",it="Append to existing data",ct='<button id="load-dataset-btn" class="btn btn-primary svelte-10vyee7">Load Dataset into DuckDB</button>',mt='<div class="loading-spinner svelte-10vyee7"></div> <p id="loading-message" class="svelte-10vyee7">Loading...</p>',vt='<a href="#loaded-datasets">Loaded Datasets</a>',ut='<a href="#using-your-data">Using Your Data</a>',pt="Once you've loaded datasets, you can use them in Evidence pages with SQL queries:",gt='<a href="#example-queries">Example Queries</a>',yt="Here are some example queries you can run on your loaded datasets:",ft='<strong class="markdown">Basic Data Exploration:</strong>',_t='<strong class="markdown">Time Series Analysis:</strong>',Lt='<strong class="markdown">Category Analysis:</strong>',Mt='<a href="#next-steps">Next Steps</a>',Dt='<li class="markdown"><strong class="markdown">Create Visualizations</strong>: Use Evidence components like <code class="markdown">&lt;BarChart&gt;</code>, <code class="markdown">&lt;LineChart&gt;</code>, etc.</li> <li class="markdown"><strong class="markdown">Add Interactivity</strong>: Use <code class="markdown">&lt;Dropdown&gt;</code> and other input components</li> <li class="markdown"><strong class="markdown">Build Dashboards</strong>: Create multiple pages with different analyses</li> <li class="markdown"><strong class="markdown">Deploy to Domo</strong>: Package your Evidence app for Domo DDX</li>',Ht='<a href="#troubleshooting">Troubleshooting</a>',Et='<a href="#common-issues">Common Issues</a>',xt='<strong class="markdown">Dataset Not Loading:</strong>',$t='<li class="markdown">Check your Domo permissions</li> <li class="markdown">Verify the dataset exists and is accessible</li> <li class="markdown">Try refreshing the page</li>',Ot='<strong class="markdown">Table Name Conflicts:</strong>',St='<li class="markdown">Use unique table names</li> <li class="markdown">Choose &quot;Replace existing data&quot; to overwrite</li>',Rt='<strong class="markdown">Performance Issues:</strong>',Nt='<li class="markdown">Large datasets may take time to load</li> <li class="markdown">Consider filtering data in Domo before loading</li> <li class="markdown">Use appropriate data types for better performance</li>',Bt='<a href="#getting-help">Getting Help</a>',Pt='<li class="markdown">Check the browser console for error messages</li> <li class="markdown">Verify your Domo DDX environment is properly configured</li> <li class="markdown">Contact your Domo administrator for dataset access issues</li>',Ut=typeof b<"u"&&b.title&&!0!==b.hide_title&&Al(),jt=(typeof b<"u"&&b.title?Fl:Bl)(e),Vt="object"==typeof b&&$l();function At(e,t){return e[2]?zl:Ql}let Ft=At(e),Gt=Ft(e);function zt(e,t){return e[1].length>0?Yl:Wl}let Wt=zt(e),Qt=Wt(e),Yt=e[0]&&yl(e);return he=new qt({props:{source:"-- Get row count and basic stats\nSELECT \n  COUNT(*) as total_rows,\n  COUNT(DISTINCT column_name) as unique_values\nFROM your_table_name;",copyToClipboard:"true",language:"sql"}}),fe=new qt({props:{source:"-- Aggregate by date (if you have date columns)\nSELECT \n  DATE_TRUNC('month', date_column) as month,\n  SUM(numeric_column) as total\nFROM your_table_name\nGROUP BY month\nORDER BY month;",copyToClipboard:"true",language:"sql"}}),Te=new qt({props:{source:"-- Group by categorical columns\nSELECT \n  category_column,\n  COUNT(*) as count,\n  AVG(numeric_column) as average\nFROM your_table_name\nGROUP BY category_column\nORDER BY count DESC;",copyToClipboard:"true",language:"sql"}}),{c(){Ut&&Ut.c(),t=p(),jt.c(),a=m("meta"),l=m("meta"),Vt&&Vt.c(),o=Tt(),r=p(),i=m("h1"),i.innerHTML=Je,c=p(),Gt.c(),h=p(),g=m("p"),g.textContent=Ke,y=p(),f=m("h2"),f.innerHTML=Ze,k=p(),T=m("ol"),T.innerHTML=et,L=p(),C=m("div"),M=m("div"),M.innerHTML=tt,D=p(),H=m("div"),E=m("label"),E.textContent=st,x=p(),$=m("select"),I=m("option"),I.textContent=at,O=p(),S=m("div"),S.innerHTML=nt,R=p(),N=m("div"),B=m("h4"),B.textContent=lt,P=p(),U=m("div"),j=m("div"),j.innerHTML=ot,V=p(),A=m("div"),F=m("label"),F.textContent=rt,G=p(),z=m("select"),W=m("option"),W.textContent=dt,Q=m("option"),Q.textContent=it,Y=p(),X=m("div"),X.innerHTML=ct,J=p(),K=m("div"),K.innerHTML=mt,Z=p(),ee=m("h2"),ee.innerHTML=vt,te=p(),Qt.c(),se=p(),ne=m("h2"),ne.innerHTML=ut,le=p(),oe=m("p"),oe.textContent=pt,re=p(),Yt&&Yt.c(),de=p(),ie=m("h3"),ie.innerHTML=gt,ce=p(),me=m("p"),me.textContent=yt,ve=p(),ue=m("p"),ue.innerHTML=ft,pe=p(),bt(he.$$.fragment),we=p(),ge=m("p"),ge.innerHTML=_t,ye=p(),bt(fe.$$.fragment),_e=p(),ke=m("p"),ke.innerHTML=Lt,be=p(),bt(Te.$$.fragment),Le=p(),Ce=m("h2"),Ce.innerHTML=Mt,Me=p(),De=m("ol"),De.innerHTML=Dt,He=p(),Ee=m("h2"),Ee.innerHTML=Ht,xe=p(),qe=m("h3"),qe.innerHTML=Et,$e=p(),Ie=m("p"),Ie.innerHTML=xt,Se=p(),Re=m("ul"),Re.innerHTML=$t,Ne=p(),Be=m("p"),Be.innerHTML=Ot,Pe=p(),je=m("ul"),je.innerHTML=St,Ve=p(),Ae=m("p"),Ae.innerHTML=Rt,Fe=p(),Ge=m("ul"),Ge.innerHTML=Nt,ze=p(),We=m("h3"),We.innerHTML=Bt,Qe=p(),Ye=m("ul"),Ye.innerHTML=Pt,this.h()},l(e){Ut&&Ut.l(e),t=v(e);const n=wl("svelte-2igo1p",ae.head);jt.l(n),a=u(n,"META",{name:!0,content:!0}),l=u(n,"META",{name:!0,content:!0}),Vt&&Vt.l(n),o=Tt(),n.forEach(s),r=v(e),i=u(e,"H1",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-l4qjn8"!==w(i)&&(i.innerHTML=Je),c=v(e),Gt.l(e),h=v(e),g=u(e,"P",{class:!0,"data-svelte-h":!0}),"svelte-1jvntq6"!==w(g)&&(g.textContent=Ke),y=v(e),f=u(e,"H2",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-m9j39v"!==w(f)&&(f.innerHTML=Ze),k=v(e),T=u(e,"OL",{class:!0,"data-svelte-h":!0}),"svelte-i541gc"!==w(T)&&(T.innerHTML=et),L=v(e),C=u(e,"DIV",{id:!0,class:!0});var d=q(C);M=u(d,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-1qqhjj8"!==w(M)&&(M.innerHTML=tt),D=v(d),H=u(d,"DIV",{class:!0});var m=q(H);E=u(m,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),"svelte-gxhz7z"!==w(E)&&(E.textContent=st),x=v(m),$=u(m,"SELECT",{id:!0,class:!0});var p=q($);I=u(p,"OPTION",{"data-svelte-h":!0}),"svelte-59d9xk"!==w(I)&&(I.textContent=at),p.forEach(s),m.forEach(s),O=v(d),S=u(d,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),"svelte-1dp1fod"!==w(S)&&(S.innerHTML=nt),R=v(d),N=u(d,"DIV",{id:!0,class:!0,style:!0});var _=q(N);B=u(_,"H4",{"data-svelte-h":!0}),"svelte-1foy07w"!==w(B)&&(B.textContent=lt),P=v(_),U=u(_,"DIV",{class:!0});var b=q(U);j=u(b,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-1m428t"!==w(j)&&(j.innerHTML=ot),V=v(b),A=u(b,"DIV",{class:!0});var Oe=q(A);F=u(Oe,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),"svelte-p1qydn"!==w(F)&&(F.textContent=rt),G=v(Oe),z=u(Oe,"SELECT",{id:!0,class:!0});var Ue=q(z);W=u(Ue,"OPTION",{"data-svelte-h":!0}),"svelte-qvzdub"!==w(W)&&(W.textContent=dt),Q=u(Ue,"OPTION",{"data-svelte-h":!0}),"svelte-idsvi6"!==w(Q)&&(Q.textContent=it),Ue.forEach(s),Oe.forEach(s),b.forEach(s),_.forEach(s),Y=v(d),X=u(d,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),"svelte-efrb90"!==w(X)&&(X.innerHTML=ct),J=v(d),K=u(d,"DIV",{id:!0,class:!0,style:!0,"data-svelte-h":!0}),"svelte-mbg3er"!==w(K)&&(K.innerHTML=mt),d.forEach(s),Z=v(e),ee=u(e,"H2",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-1f0ly50"!==w(ee)&&(ee.innerHTML=vt),te=v(e),Qt.l(e),se=v(e),ne=u(e,"H2",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-d20xgf"!==w(ne)&&(ne.innerHTML=ut),le=v(e),oe=u(e,"P",{class:!0,"data-svelte-h":!0}),"svelte-8qpe1e"!==w(oe)&&(oe.textContent=pt),re=v(e),Yt&&Yt.l(e),de=v(e),ie=u(e,"H3",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-1y9nphi"!==w(ie)&&(ie.innerHTML=gt),ce=v(e),me=u(e,"P",{class:!0,"data-svelte-h":!0}),"svelte-1jqarqq"!==w(me)&&(me.textContent=yt),ve=v(e),ue=u(e,"P",{class:!0,"data-svelte-h":!0}),"svelte-fi6le8"!==w(ue)&&(ue.innerHTML=ft),pe=v(e),Ct(he.$$.fragment,e),we=v(e),ge=u(e,"P",{class:!0,"data-svelte-h":!0}),"svelte-8raaax"!==w(ge)&&(ge.innerHTML=_t),ye=v(e),Ct(fe.$$.fragment,e),_e=v(e),ke=u(e,"P",{class:!0,"data-svelte-h":!0}),"svelte-1m0hm5v"!==w(ke)&&(ke.innerHTML=Lt),be=v(e),Ct(Te.$$.fragment,e),Le=v(e),Ce=u(e,"H2",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-b2v2p6"!==w(Ce)&&(Ce.innerHTML=Mt),Me=v(e),De=u(e,"OL",{class:!0,"data-svelte-h":!0}),"svelte-1h5tzx0"!==w(De)&&(De.innerHTML=Dt),He=v(e),Ee=u(e,"H2",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-1uk16ny"!==w(Ee)&&(Ee.innerHTML=Ht),xe=v(e),qe=u(e,"H3",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-1iqe0ip"!==w(qe)&&(qe.innerHTML=Et),$e=v(e),Ie=u(e,"P",{class:!0,"data-svelte-h":!0}),"svelte-1syr9k0"!==w(Ie)&&(Ie.innerHTML=xt),Se=v(e),Re=u(e,"UL",{class:!0,"data-svelte-h":!0}),"svelte-1nuau76"!==w(Re)&&(Re.innerHTML=$t),Ne=v(e),Be=u(e,"P",{class:!0,"data-svelte-h":!0}),"svelte-1eb0x8p"!==w(Be)&&(Be.innerHTML=Ot),Pe=v(e),je=u(e,"UL",{class:!0,"data-svelte-h":!0}),"svelte-1fulwkk"!==w(je)&&(je.innerHTML=St),Ve=v(e),Ae=u(e,"P",{class:!0,"data-svelte-h":!0}),"svelte-ta14c3"!==w(Ae)&&(Ae.innerHTML=Rt),Fe=v(e),Ge=u(e,"UL",{class:!0,"data-svelte-h":!0}),"svelte-1sdbkgn"!==w(Ge)&&(Ge.innerHTML=Nt),ze=v(e),We=u(e,"H3",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-1f92uqx"!==w(We)&&(We.innerHTML=Bt),Qe=v(e),Ye=u(e,"UL",{class:!0,"data-svelte-h":!0}),"svelte-1t69tq3"!==w(Ye)&&(Ye.innerHTML=Pt),this.h()},h(){n(a,"name","twitter:card"),n(a,"content","summary_large_image"),n(l,"name","twitter:site"),n(l,"content","@evidence_dev"),n(i,"class","markdown"),n(i,"id","domo-dataset-to-duckdb-workflow"),n(g,"class","markdown"),n(f,"class","markdown"),n(f,"id","how-it-works"),n(T,"class","markdown"),n(M,"class","picker-header svelte-10vyee7"),n(E,"for","dataset-select"),n(E,"class","svelte-10vyee7"),I.__value="",It(I,I.__value),n($,"id","dataset-select"),n($,"class","dataset-dropdown svelte-10vyee7"),n(H,"class","workflow-step svelte-10vyee7"),n(S,"id","dataset-preview"),n(S,"class","dataset-preview svelte-10vyee7"),ht(S,"display","none"),n(j,"class","config-item"),n(F,"for","refresh-mode"),n(F,"class","svelte-10vyee7"),W.__value="replace",It(W,W.__value),Q.__value="append",It(Q,Q.__value),n(z,"id","refresh-mode"),n(z,"class","svelte-10vyee7"),n(A,"class","config-item"),n(U,"class","config-grid svelte-10vyee7"),n(N,"id","loading-config"),n(N,"class","workflow-step svelte-10vyee7"),ht(N,"display","none"),n(X,"id","workflow-actions"),n(X,"class","workflow-actions svelte-10vyee7"),ht(X,"display","none"),n(K,"id","loading-overlay"),n(K,"class","loading-overlay svelte-10vyee7"),ht(K,"display","none"),n(C,"id","domo-workflow-picker"),n(C,"class","workflow-picker svelte-10vyee7"),n(ee,"class","markdown"),n(ee,"id","loaded-datasets"),n(ne,"class","markdown"),n(ne,"id","using-your-data"),n(oe,"class","markdown"),n(ie,"class","markdown"),n(ie,"id","example-queries"),n(me,"class","markdown"),n(ue,"class","markdown"),n(ge,"class","markdown"),n(ke,"class","markdown"),n(Ce,"class","markdown"),n(Ce,"id","next-steps"),n(De,"class","markdown"),n(Ee,"class","markdown"),n(Ee,"id","troubleshooting"),n(qe,"class","markdown"),n(qe,"id","common-issues"),n(Ie,"class","markdown"),n(Re,"class","markdown"),n(Be,"class","markdown"),n(je,"class","markdown"),n(Ae,"class","markdown"),n(Ge,"class","markdown"),n(We,"class","markdown"),n(We,"id","getting-help"),n(Ye,"class","markdown")},m(e,s){Ut&&Ut.m(e,s),d(e,t,s),jt.m(ae.head,null),_(ae.head,a),_(ae.head,l),Vt&&Vt.m(ae.head,null),_(ae.head,o),d(e,r,s),d(e,i,s),d(e,c,s),Gt.m(e,s),d(e,h,s),d(e,g,s),d(e,y,s),d(e,f,s),d(e,k,s),d(e,T,s),d(e,L,s),d(e,C,s),_(C,M),_(C,D),_(C,H),_(H,E),_(H,x),_(H,$),_($,I),_(C,O),_(C,S),_(C,R),_(C,N),_(N,B),_(N,P),_(N,U),_(U,j),_(U,V),_(U,A),_(A,F),_(A,G),_(A,z),_(z,W),_(z,Q),_(C,Y),_(C,X),_(C,J),_(C,K),d(e,Z,s),d(e,ee,s),d(e,te,s),Qt.m(e,s),d(e,se,s),d(e,ne,s),d(e,le,s),d(e,oe,s),d(e,re,s),Yt&&Yt.m(e,s),d(e,de,s),d(e,ie,s),d(e,ce,s),d(e,me,s),d(e,ve,s),d(e,ue,s),d(e,pe,s),wt(he,e,s),d(e,we,s),d(e,ge,s),d(e,ye,s),wt(fe,e,s),d(e,_e,s),d(e,ke,s),d(e,be,s),wt(Te,e,s),d(e,Le,s),d(e,Ce,s),d(e,Me,s),d(e,De,s),d(e,He,s),d(e,Ee,s),d(e,xe,s),d(e,qe,s),d(e,$e,s),d(e,Ie,s),d(e,Se,s),d(e,Re,s),d(e,Ne,s),d(e,Be,s),d(e,Pe,s),d(e,je,s),d(e,Ve,s),d(e,Ae,s),d(e,Fe,s),d(e,Ge,s),d(e,ze,s),d(e,We,s),d(e,Qe,s),d(e,Ye,s),Xe=!0},p(e,[t]){typeof b<"u"&&b.title&&!0!==b.hide_title&&Ut.p(e,t),jt.p(e,t),"object"==typeof b&&Vt.p(e,t),Ft===(Ft=At(e))&&Gt?Gt.p(e,t):(Gt.d(1),Gt=Ft(e),Gt&&(Gt.c(),Gt.m(h.parentNode,h))),Wt===(Wt=zt(e))&&Qt?Qt.p(e,t):(Qt.d(1),Qt=Wt(e),Qt&&(Qt.c(),Qt.m(se.parentNode,se))),e[0]?Yt?(Yt.p(e,t),1&t&&Oe(Yt,1)):(Yt=yl(e),Yt.c(),Oe(Yt,1),Yt.m(de.parentNode,de)):Yt&&(Hl(),Ue(Yt,1,1,(()=>{Yt=null})),Dl())},i(e){Xe||(Oe(Yt),Oe(he.$$.fragment,e),Oe(fe.$$.fragment,e),Oe(Te.$$.fragment,e),Xe=!0)},o(e){Ue(Yt),Ue(he.$$.fragment,e),Ue(fe.$$.fragment,e),Ue(Te.$$.fragment,e),Xe=!1},d(e){e&&(s(t),s(r),s(i),s(c),s(h),s(g),s(y),s(f),s(k),s(T),s(L),s(C),s(Z),s(ee),s(te),s(se),s(ne),s(le),s(oe),s(re),s(de),s(ie),s(ce),s(me),s(ve),s(ue),s(pe),s(we),s(ge),s(ye),s(_e),s(ke),s(be),s(Le),s(Ce),s(Me),s(De),s(He),s(Ee),s(xe),s(qe),s($e),s(Ie),s(Se),s(Re),s(Ne),s(Be),s(Pe),s(je),s(Ve),s(Ae),s(Fe),s(Ge),s(ze),s(We),s(Qe),s(Ye)),Ut&&Ut.d(e),jt.d(e),s(a),s(l),Vt&&Vt.d(e),s(o),Gt.d(e),Qt.d(e),Yt&&Yt.d(e),kt(he,e),kt(fe,e),kt(Te,e)}}}const b={title:"Domo Dataset Workflow"};function Kl(e,t,s){let a,n;dl(e,Vl,(e=>s(10,a=e))),dl(e,ml,(e=>s(16,n=e)));let{data:l}=t,{data:o={},customFormattingSettings:r,__db:d,inputs:i}=l;Cl(ml,n="fa3a9ba979503cc33be1e8689543296c",n);let c=ql(Pl(i));bl(c.subscribe((e=>i=e))),Tl(Nl,{getCustomFormats:()=>r.customFormats||[]});const m=(e,t)=>Ul(d.query,e,{query_name:t});Sl(m),a.params,Ll((()=>!0));let v={initialData:void 0,initialError:void 0},u=fl`SELECT * FROM your_table_name LIMIT 10`,p="SELECT * FROM your_table_name LIMIT 10";o.my_analysis_data&&(o.my_analysis_data instanceof Error?v.initialError=o.my_analysis_data:v.initialData=o.my_analysis_data,o.my_analysis_columns&&(v.knownColumns=o.my_analysis_columns));let h,w=!1;const g=Ol.createReactive({callback:e=>{s(0,h=e)},execFn:m},{id:"my_analysis",...v});g(p,{noResolve:u,...v}),globalThis[Symbol.for("my_analysis")]={get value(){return h}};let y=!1,f=null;return typeof window<"u"&&(y=typeof window.domo<"u",y&&window.domo&&window.domo.get&&window.domo.get("/domo/users/v1/me").then((e=>{s(3,f=e),console.log("Domo user:",f)})).catch((e=>{console.warn("Could not get Domo user info:",e)}))),typeof window<"u"&&window.addEventListener("DOMContentLoaded",(function(){const e=document.createElement("script");e.src="/static/domo-duckdb-integration.js",e.onload=function(){console.log("Domo integration script loaded")},document.head.appendChild(e)})),e.$$set=e=>{"data"in e&&s(4,l=e.data)},e.$$.update=()=>{16&e.$$.dirty&&s(5,({data:o={},customFormattingSettings:r,__db:d}=l),o),32&e.$$.dirty&&Rl.set(Object.keys(o).length>0),1024&e.$$.dirty&&a.params,960&e.$$.dirty&&(u||!w?u||(g(p,{noResolve:u,...v}),s(9,w=!0)):g(p,{noResolve:u}))},s(7,u=fl`SELECT * FROM your_table_name LIMIT 10`),s(8,p="SELECT * FROM your_table_name LIMIT 10"),[h,[],y,f,l,o,v,u,p,w,a]}class is extends xl{constructor(e){super(),Ml(this,e,Kl,Xl,kl,{data:4})}}export{is as component};