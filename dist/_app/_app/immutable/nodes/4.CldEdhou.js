import{S as Cl,s as me,d as g,i as S,r as te,U as ue,t as Be,V as U,W as Re,c as he,u as _e,g as ge,a as be,v as we,ai as Le,A as Ze,h as G,j as le,m as R,D as ot,K as Al,ah as Sl,p as nl,z as sl,l as Xe,Q as tn,b as V,B as rl,ak as ks,e as ke,x as Ue,k as Q,y as Ye,n as K,I as zi,F as ln,o as Hl,a3 as xl,w as bt,X as nn,G as W,al as Ys,J as Ni,aj as Qs,C as gt,L as Cs,a2 as sn,E as jt,q as Ct,am as Ks,P as Js,Z as ql,_ as Gl,an as Zs,H as xs}from"../chunks/scheduler.C5eBzNnH.js";import{S as Ae,i as Se,t as C,a as b,g as fe,c as ae,f as Ql,k as ws,h as Hi,j as $l,d as J,m as Z,b as x,e as $}from"../chunks/index.BSd9q3aW.js";import{z as rn,W as $s,Y as er,K as tr,X as lr,ad as ir,ae as nr,A as yl,af as Ei,ag as sr,a2 as pi,J as on,Z as rr,$ as fn,a0 as Rl,ah as or,n as Wl,a3 as Xl,ai as fr,aj as an,ak as ar,al as ur,y as cr,a6 as As,a5 as Ss,a7 as Os,B as Ve,am as dr,a8 as mr,aa as De,an as hr,ao as Dl,ap as _r,aq as ei,ar as Ts,as as Kl,at as gr,au as br,av as yr,aw as kr,ax as Cr,ay as wr,F as ze,H as Tt,I as ti,az as Ar,aA as Ls,aB as Sr,aC as un,m as pt,u as vs,q as Ds,aD as Or,aE as Tr,aF as Lr,aG as vr,ab as Dr,aH as _t,aI as Er,ac as Es,aJ as pr,e as cn,aK as Ol,aL as Mr,aM as Ir,aN as qi,aO as Gi,aP as ps,aQ as Pr,g as El,f as Fr,h as Nr,M as Ms,aR as Nt,aS as Br,aT as jr,aU as Mi,aV as Bi,aW as ji,aX as dn,O as Ii,aY as Vr,aZ as Ot,a_ as Ul,S as Et,a$ as zr,b0 as Is,b1 as Ps,b2 as Fs,b3 as Hr,Q as Tl,b4 as qr,b5 as Gr,b6 as Rr,j as Wr,k as Xr,p as Ur,l as mn,r as hn,o as Yr}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.CJe6lW-S.js";import{w as Jl,d as Bt}from"../chunks/entry.CjmEikbu.js";import{h as Yl,p as Qr}from"../chunks/setTrackProxy.DjIbdjlZ.js";import{g as Kr,Q as Ns}from"../chunks/QueryViewer.Ok3Ma9J7.js";import{p as Bs}from"../chunks/stores.C41LEeiH.js";import{r as Jr}from"../chunks/scroll.B6wI3tb1.js";import{h as _n,c as Zr,B as xr}from"../chunks/button.BTWiJWck.js";import{c as $r}from"../chunks/checkRequiredProps.o_C_V3S5.js";const Vi=(e,t={serializeStrings:!0})=>null==e?"null":"string"==typeof e?!1!==t.serializeStrings?`'${e.replaceAll("'","''")}'`:e:"number"==typeof e||"bigint"==typeof e||"boolean"==typeof e?String(e):e instanceof Date?`'${e.toISOString()}'::TIMESTAMP_MS`:Array.isArray(e)?`[${e.map((e=>Vi(e,t))).join(", ")}]`:JSON.stringify(e),eo={positioning:{placement:"bottom"},arrowSize:8,defaultOpen:!1,disableFocusTrap:!1,closeOnEscape:!0,preventScroll:!1,onOpenChange:void 0,closeOnOutsideClick:!0,portal:void 0,forceVisible:!1,openFocus:void 0,closeFocus:void 0,onOutsideClick:void 0},{name:kl}=rr("popover"),to=["trigger","content"];function lo(e){const t={...eo,...e},n=rn($s(t,"open","ids")),{positioning:s,arrowSize:l,disableFocusTrap:i,preventScroll:o,closeOnEscape:a,closeOnOutsideClick:r,portal:c,forceVisible:d,openFocus:u,closeFocus:p,onOutsideClick:f}=n,h=t.open??Jl(t.defaultOpen),$=er(h,null==t?void 0:t.onOpenChange),g=tr.writable(null),m=rn({...lr(to),...t.ids});function y(){$.set(!1);const e=document.getElementById(m.trigger.get());_n({prop:p.get(),defaultEl:e})}ir((()=>{g.set(document.getElementById(m.trigger.get()))}));const b=nr({open:$,activeTrigger:g,forceVisible:d}),x=yl(kl("content"),{stores:[b,c,m.content],returned:([e,t,n])=>({hidden:!e||!on||void 0,tabindex:-1,style:pi({display:e?void 0:"none"}),id:n,"data-state":e?"open":"closed","data-portal":sr(t)}),action:e=>{let t=Wl;const n=Ei([b,g,s,i,a,r,c],(([n,s,l,i,o,a,r])=>{t(),n&&s&&Cl().then((()=>{t(),t=fr(e,{anchorElement:s,open:$,options:{floating:l,focusTrap:i?null:{returnFocusOnDeactivate:!1,clickOutsideDeactivates:a,allowOutsideClick:!0,escapeDeactivates:o},modal:{shouldCloseOnInteractOutside:w,onClose:y,open:n,closeOnInteractOutside:a},escapeKeydown:o?{handler:()=>{y()}}:null,portal:an(e,r)}}).destroy}))}));return{destroy(){n(),t()}}}});function v(e){$.update((e=>!e)),e&&e!==g.get()&&g.set(e)}function w(e){var t;if(null==(t=f.get())||t(e),e.defaultPrevented)return!1;const n=e.target,s=document.getElementById(m.trigger.get());return!(s&&ar(n)&&(n===s||s.contains(n)))}const C=yl(kl("trigger"),{stores:[b,m.content,m.trigger],returned:([e,t,n])=>({role:"button","aria-haspopup":"dialog","aria-expanded":e?"true":"false","data-state":gn(e),"aria-controls":t,id:n}),action:e=>({destroy:fn(Rl(e,"click",(()=>{v(e)})),Rl(e,"keydown",(t=>{t.key!==Xl.ENTER&&t.key!==Xl.SPACE||(t.preventDefault(),v(e))})))})}),S=yl(kl("overlay"),{stores:[b],returned:([e])=>({hidden:!e||void 0,tabindex:-1,style:pi({display:e?void 0:"none"}),"aria-hidden":"true","data-state":gn(e)}),action:e=>{let t=Wl,n=Wl,s=Wl;if(a.get()){const n=or(e,{handler:()=>{y()}});n&&n.destroy&&(t=n.destroy)}return n=Ei([c],(([t])=>{if(s(),null===t)return;const n=an(e,t);null!==n&&(s=ur(e,n).destroy)})),{destroy(){t(),n(),s()}}}}),k=yl(kl("arrow"),{stores:l,returned:e=>({"data-arrow":!0,style:pi({position:"absolute",width:`var(--arrow-size, ${e}px)`,height:`var(--arrow-size, ${e}px)`})})}),T=yl(kl("close"),{returned:()=>({type:"button"}),action:e=>({destroy:fn(Rl(e,"click",(e=>{e.defaultPrevented||y()})),Rl(e,"keydown",(e=>{e.defaultPrevented||e.key!==Xl.ENTER&&e.key!==Xl.SPACE||(e.preventDefault(),v())})))})});return Ei([$,g,o],(([e,t,n])=>{if(!on)return;const s=[];if(e){t||Cl().then((()=>{const e=document.getElementById(m.trigger.get());cr(e)&&g.set(e)})),n&&s.push(Jr());const e=t??document.getElementById(m.trigger.get());_n({prop:u.get(),defaultEl:e})}return()=>{s.forEach((e=>e()))}})),{ids:m,elements:{trigger:C,content:x,arrow:k,close:T,overlay:S},states:{open:$},options:n}}function gn(e){return e?"open":"closed"}function io(){return{NAME:"separator",PARTS:["root"]}}function no(e){const{NAME:t,PARTS:n}=io(),s=As(t,n),l={...Zr(Ss(e)),getAttrs:s};return{...l,updateOption:Os(l.options)}}const so=e=>({builder:4&e}),bn=e=>({builder:e[2]});function ro(e){let t,n,s,l=[e[2],e[4]],i={};for(let e=0;e<l.length;e+=1)i=U(i,l[e]);return{c(){t=R("div"),this.h()},l(e){t=G(e,"DIV",{}),le(t).forEach(g),this.h()},h(){Le(t,i)},m(l,i){S(l,t,i),e[10](t),n||(s=Ze(e[2].action(t)),n=!0)},p(e,n){Le(t,i=Ve(l,[4&n&&e[2],16&n&&e[4]]))},i:we,o:we,d(l){l&&g(t),e[10](null),n=!1,s()}}}function oo(e){let t;const n=e[9].default,s=he(n,e,e[8],bn);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||260&l)&&_e(s,n,e,e[8],t?be(n,e[8],l,so):ge(e[8]),bn)},i(e){t||(b(s,e),t=!0)},o(e){C(s,e),t=!1},d(e){s&&s.d(e)}}}function fo(e){let t,n,s,l;const i=[oo,ro],o=[];function a(e,t){return e[1]?0:1}return t=a(e),n=o[t]=i[t](e),{c(){n.c(),s=te()},l(e){n.l(e),s=te()},m(e,n){o[t].m(e,n),S(e,s,n),l=!0},p(e,[l]){let r=t;t=a(e),t===r?o[t].p(e,l):(fe(),C(o[r],1,1,(()=>{o[r]=null})),ae(),n=o[t],n?n.p(e,l):(n=o[t]=i[t](e),n.c()),b(n,1),n.m(s.parentNode,s))},i(e){l||(b(n),l=!0)},o(e){C(n),l=!1},d(e){e&&g(s),o[t].d(e)}}}function ao(e,t,n){let s;const l=["orientation","decorative","asChild","el"];let i,o=ue(t,l),{$$slots:a={},$$scope:r}=t,{orientation:c="horizontal"}=t,{decorative:d=!0}=t,{asChild:u=!1}=t,{el:p}=t;const{elements:{root:f},updateOption:h,getAttrs:$}=no({orientation:c,decorative:d});Be(e,f,(e=>n(7,i=e)));const g=$("root");return e.$$set=e=>{t=U(U({},t),Re(e)),n(4,o=ue(t,l)),"orientation"in e&&n(5,c=e.orientation),"decorative"in e&&n(6,d=e.decorative),"asChild"in e&&n(1,u=e.asChild),"el"in e&&n(0,p=e.el),"$$scope"in e&&n(8,r=e.$$scope)},e.$$.update=()=>{32&e.$$.dirty&&h("orientation",c),64&e.$$.dirty&&h("decorative",d),128&e.$$.dirty&&n(2,s=i),4&e.$$.dirty&&Object.assign(s,g)},[p,u,s,f,o,c,d,i,r,a,function(e){ot[e?"unshift":"push"]((()=>{p=e,n(0,p)}))}]}let uo=class extends Ae{constructor(e){super(),Se(this,e,ao,fo,me,{orientation:5,decorative:6,asChild:1,el:0})}};function js(){return{NAME:"popover",PARTS:["arrow","close","content","trigger"]}}function co(e){const{NAME:t,PARTS:n}=js(),s=As(t,n),l={...lo({positioning:{placement:"bottom",gutter:0},...Ss(e),forceVisible:!0}),getAttrs:s};return Al(t,l),{...l,updateOption:Os(l.options)}}function Ri(){const{NAME:e}=js();return Sl(e)}function mo(e){const t={side:"bottom",align:"center",...e},{options:{positioning:n}}=Ri();dr(n)(t)}const ho=e=>({ids:1&e}),yn=e=>({ids:e[0]});function _o(e){let t;const n=e[13].default,s=he(n,e,e[12],yn);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,[l]){s&&s.p&&(!t||4097&l)&&_e(s,n,e,e[12],t?be(n,e[12],l,ho):ge(e[12]),yn)},i(e){t||(b(s,e),t=!0)},o(e){C(s,e),t=!1},d(e){s&&s.d(e)}}}function go(e,t,n){let s,{$$slots:l={},$$scope:i}=t,{disableFocusTrap:o}=t,{closeOnEscape:a}=t,{closeOnOutsideClick:r}=t,{preventScroll:c}=t,{portal:d}=t,{open:u}=t,{onOpenChange:p}=t,{openFocus:f}=t,{closeFocus:h}=t,{onOutsideClick:$}=t;const{updateOption:g,states:{open:m},ids:y}=co({disableFocusTrap:o,closeOnEscape:a,closeOnOutsideClick:r,preventScroll:c,portal:d,defaultOpen:u,openFocus:f,closeFocus:h,onOutsideClick:$,onOpenChange:({next:e})=>(u!==e&&(null==p||p(e),n(2,u=e)),e),positioning:{gutter:0,offset:{mainAxis:1}}}),b=Bt([y.content,y.trigger],(([e,t])=>({content:e,trigger:t})));return Be(e,b,(e=>n(0,s=e))),e.$$set=e=>{"disableFocusTrap"in e&&n(3,o=e.disableFocusTrap),"closeOnEscape"in e&&n(4,a=e.closeOnEscape),"closeOnOutsideClick"in e&&n(5,r=e.closeOnOutsideClick),"preventScroll"in e&&n(6,c=e.preventScroll),"portal"in e&&n(7,d=e.portal),"open"in e&&n(2,u=e.open),"onOpenChange"in e&&n(8,p=e.onOpenChange),"openFocus"in e&&n(9,f=e.openFocus),"closeFocus"in e&&n(10,h=e.closeFocus),"onOutsideClick"in e&&n(11,$=e.onOutsideClick),"$$scope"in e&&n(12,i=e.$$scope)},e.$$.update=()=>{4&e.$$.dirty&&void 0!==u&&m.set(u),8&e.$$.dirty&&g("disableFocusTrap",o),16&e.$$.dirty&&g("closeOnEscape",a),32&e.$$.dirty&&g("closeOnOutsideClick",r),64&e.$$.dirty&&g("preventScroll",c),128&e.$$.dirty&&g("portal",d),512&e.$$.dirty&&g("openFocus",f),1024&e.$$.dirty&&g("closeFocus",h),2048&e.$$.dirty&&g("onOutsideClick",$)},[s,b,u,o,a,r,c,d,p,f,h,$,i,l]}class bo extends Ae{constructor(e){super(),Se(this,e,go,_o,me,{disableFocusTrap:3,closeOnEscape:4,closeOnOutsideClick:5,preventScroll:6,portal:7,open:2,onOpenChange:8,openFocus:9,closeFocus:10,onOutsideClick:11})}}const yo=e=>({builder:256&e[0]}),kn=e=>({builder:e[8]}),ko=e=>({builder:256&e[0]}),Cn=e=>({builder:e[8]}),Co=e=>({builder:256&e[0]}),wn=e=>({builder:e[8]}),wo=e=>({builder:256&e[0]}),An=e=>({builder:e[8]}),Ao=e=>({builder:256&e[0]}),Sn=e=>({builder:e[8]}),So=e=>({builder:256&e[0]}),On=e=>({builder:e[8]});function Oo(e){let t,n,s,l;const i=e[27].default,o=he(i,e,e[26],kn);let a=[e[8],e[12]],r={};for(let e=0;e<a.length;e+=1)r=U(r,a[e]);return{c(){t=R("div"),o&&o.c(),this.h()},l(e){t=G(e,"DIV",{});var n=le(t);o&&o.l(n),n.forEach(g),this.h()},h(){Le(t,r)},m(i,a){S(i,t,a),o&&o.m(t,null),e[32](t),n=!0,s||(l=Ze(e[8].action(t)),s=!0)},p(e,s){o&&o.p&&(!n||67109120&s[0])&&_e(o,i,e,e[26],n?be(i,e[26],s,yo):ge(e[26]),kn),Le(t,r=Ve(a,[256&s[0]&&e[8],4096&s[0]&&e[12]]))},i(e){n||(b(o,e),n=!0)},o(e){C(o,e),n=!1},d(n){n&&g(t),o&&o.d(n),e[32](null),s=!1,l()}}}function To(e){let t,n,s,l,i;const o=e[27].default,a=he(o,e,e[26],Cn);let r=[e[8],e[12]],c={};for(let e=0;e<r.length;e+=1)c=U(c,r[e]);return{c(){t=R("div"),a&&a.c(),this.h()},l(e){t=G(e,"DIV",{});var n=le(t);a&&a.l(n),n.forEach(g),this.h()},h(){Le(t,c)},m(n,o){S(n,t,o),a&&a.m(t,null),e[31](t),s=!0,l||(i=Ze(e[8].action(t)),l=!0)},p(n,l){e=n,a&&a.p&&(!s||67109120&l[0])&&_e(a,o,e,e[26],s?be(o,e[26],l,ko):ge(e[26]),Cn),Le(t,c=Ve(r,[256&l[0]&&e[8],4096&l[0]&&e[12]]))},i(e){s||(b(a,e),n&&n.end(1),s=!0)},o(l){C(a,l),l&&(n=ws(t,e[5],e[6])),s=!1},d(s){s&&g(t),a&&a.d(s),e[31](null),s&&n&&n.end(),l=!1,i()}}}function Lo(e){let t,n,s,l,i;const o=e[27].default,a=he(o,e,e[26],wn);let r=[e[8],e[12]],c={};for(let e=0;e<r.length;e+=1)c=U(c,r[e]);return{c(){t=R("div"),a&&a.c(),this.h()},l(e){t=G(e,"DIV",{});var n=le(t);a&&a.l(n),n.forEach(g),this.h()},h(){Le(t,c)},m(n,o){S(n,t,o),a&&a.m(t,null),e[30](t),s=!0,l||(i=Ze(e[8].action(t)),l=!0)},p(n,l){e=n,a&&a.p&&(!s||67109120&l[0])&&_e(a,o,e,e[26],s?be(o,e[26],l,Co):ge(e[26]),wn),Le(t,c=Ve(r,[256&l[0]&&e[8],4096&l[0]&&e[12]]))},i(l){s||(b(a,l),l&&(n||nl((()=>{n=Hi(t,e[3],e[4]),n.start()}))),s=!0)},o(e){C(a,e),s=!1},d(n){n&&g(t),a&&a.d(n),e[30](null),l=!1,i()}}}function vo(e){let t,n,s,l,i,o;const a=e[27].default,r=he(a,e,e[26],An);let c=[e[8],e[12]],d={};for(let e=0;e<c.length;e+=1)d=U(d,c[e]);return{c(){t=R("div"),r&&r.c(),this.h()},l(e){t=G(e,"DIV",{});var n=le(t);r&&r.l(n),n.forEach(g),this.h()},h(){Le(t,d)},m(n,s){S(n,t,s),r&&r.m(t,null),e[29](t),l=!0,i||(o=Ze(e[8].action(t)),i=!0)},p(n,s){e=n,r&&r.p&&(!l||67109120&s[0])&&_e(r,a,e,e[26],l?be(a,e[26],s,wo):ge(e[26]),An),Le(t,d=Ve(c,[256&s[0]&&e[8],4096&s[0]&&e[12]]))},i(i){l||(b(r,i),i&&nl((()=>{l&&(s&&s.end(1),n=Hi(t,e[3],e[4]),n.start())})),l=!0)},o(i){C(r,i),n&&n.invalidate(),i&&(s=ws(t,e[5],e[6])),l=!1},d(n){n&&g(t),r&&r.d(n),e[29](null),n&&s&&s.end(),i=!1,o()}}}function Do(e){let t,n,s,l,i;const o=e[27].default,a=he(o,e,e[26],Sn);let r=[e[8],e[12]],c={};for(let e=0;e<r.length;e+=1)c=U(c,r[e]);return{c(){t=R("div"),a&&a.c(),this.h()},l(e){t=G(e,"DIV",{});var n=le(t);a&&a.l(n),n.forEach(g),this.h()},h(){Le(t,c)},m(n,o){S(n,t,o),a&&a.m(t,null),e[28](t),s=!0,l||(i=Ze(e[8].action(t)),l=!0)},p(n,l){e=n,a&&a.p&&(!s||67109120&l[0])&&_e(a,o,e,e[26],s?be(o,e[26],l,Ao):ge(e[26]),Sn),Le(t,c=Ve(r,[256&l[0]&&e[8],4096&l[0]&&e[12]]))},i(l){s||(b(a,l),l&&nl((()=>{s&&(n||(n=Ql(t,e[1],e[2],!0)),n.run(1))})),s=!0)},o(l){C(a,l),l&&(n||(n=Ql(t,e[1],e[2],!1)),n.run(0)),s=!1},d(s){s&&g(t),a&&a.d(s),e[28](null),s&&n&&n.end(),l=!1,i()}}}function Eo(e){let t;const n=e[27].default,s=he(n,e,e[26],On);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||67109120&l[0])&&_e(s,n,e,e[26],t?be(n,e[26],l,So):ge(e[26]),On)},i(e){t||(b(s,e),t=!0)},o(e){C(s,e),t=!1},d(e){s&&s.d(e)}}}function po(e){let t,n,s,l;const i=[Eo,Do,vo,Lo,To,Oo],o=[];function a(e,t){return e[7]&&e[9]?0:e[1]&&e[9]?1:e[3]&&e[5]&&e[9]?2:e[3]&&e[9]?3:e[5]&&e[9]?4:e[9]?5:-1}return~(t=a(e))&&(n=o[t]=i[t](e)),{c(){n&&n.c(),s=te()},l(e){n&&n.l(e),s=te()},m(e,n){~t&&o[t].m(e,n),S(e,s,n),l=!0},p(e,l){let r=t;t=a(e),t===r?~t&&o[t].p(e,l):(n&&(fe(),C(o[r],1,1,(()=>{o[r]=null})),ae()),~t?(n=o[t],n?n.p(e,l):(n=o[t]=i[t](e),n.c()),b(n,1),n.m(s.parentNode,s)):n=null)},i(e){l||(b(n),l=!0)},o(e){C(n),l=!1},d(e){e&&g(s),~t&&o[t].d(e)}}}function Mo(e,t,n){let s;const l=["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","id","side","align","sideOffset","alignOffset","collisionPadding","avoidCollisions","collisionBoundary","sameWidth","fitViewport","strategy","overlap","el"];let i,o,a=ue(t,l),{$$slots:r={},$$scope:c}=t,{transition:d}=t,{transitionConfig:u}=t,{inTransition:p}=t,{inTransitionConfig:f}=t,{outTransition:h}=t,{outTransitionConfig:$}=t,{asChild:g=!1}=t,{id:m}=t,{side:y="bottom"}=t,{align:b="center"}=t,{sideOffset:x=0}=t,{alignOffset:v=0}=t,{collisionPadding:w=8}=t,{avoidCollisions:C=!0}=t,{collisionBoundary:S}=t,{sameWidth:k=!1}=t,{fitViewport:T=!1}=t,{strategy:A="absolute"}=t,{overlap:O=!1}=t,{el:L}=t;const{elements:{content:D},states:{open:E},ids:V,getAttrs:_}=Ri();Be(e,D,(e=>n(25,o=e))),Be(e,E,(e=>n(9,i=e)));const M=_("content");return e.$$set=e=>{t=U(U({},t),Re(e)),n(12,a=ue(t,l)),"transition"in e&&n(1,d=e.transition),"transitionConfig"in e&&n(2,u=e.transitionConfig),"inTransition"in e&&n(3,p=e.inTransition),"inTransitionConfig"in e&&n(4,f=e.inTransitionConfig),"outTransition"in e&&n(5,h=e.outTransition),"outTransitionConfig"in e&&n(6,$=e.outTransitionConfig),"asChild"in e&&n(7,g=e.asChild),"id"in e&&n(13,m=e.id),"side"in e&&n(14,y=e.side),"align"in e&&n(15,b=e.align),"sideOffset"in e&&n(16,x=e.sideOffset),"alignOffset"in e&&n(17,v=e.alignOffset),"collisionPadding"in e&&n(18,w=e.collisionPadding),"avoidCollisions"in e&&n(19,C=e.avoidCollisions),"collisionBoundary"in e&&n(20,S=e.collisionBoundary),"sameWidth"in e&&n(21,k=e.sameWidth),"fitViewport"in e&&n(22,T=e.fitViewport),"strategy"in e&&n(23,A=e.strategy),"overlap"in e&&n(24,O=e.overlap),"el"in e&&n(0,L=e.el),"$$scope"in e&&n(26,c=e.$$scope)},e.$$.update=()=>{8192&e.$$.dirty[0]&&m&&V.content.set(m),33554432&e.$$.dirty[0]&&n(8,s=o),256&e.$$.dirty[0]&&Object.assign(s,M),33538560&e.$$.dirty[0]&&i&&mo({side:y,align:b,sideOffset:x,alignOffset:v,collisionPadding:w,avoidCollisions:C,collisionBoundary:S,sameWidth:k,fitViewport:T,strategy:A,overlap:O})},[L,d,u,p,f,h,$,g,s,i,D,E,a,m,y,b,x,v,w,C,S,k,T,A,O,o,c,r,function(e){ot[e?"unshift":"push"]((()=>{L=e,n(0,L)}))},function(e){ot[e?"unshift":"push"]((()=>{L=e,n(0,L)}))},function(e){ot[e?"unshift":"push"]((()=>{L=e,n(0,L)}))},function(e){ot[e?"unshift":"push"]((()=>{L=e,n(0,L)}))},function(e){ot[e?"unshift":"push"]((()=>{L=e,n(0,L)}))}]}let Io=class extends Ae{constructor(e){super(),Se(this,e,Mo,po,me,{transition:1,transitionConfig:2,inTransition:3,inTransitionConfig:4,outTransition:5,outTransitionConfig:6,asChild:7,id:13,side:14,align:15,sideOffset:16,alignOffset:17,collisionPadding:18,avoidCollisions:19,collisionBoundary:20,sameWidth:21,fitViewport:22,strategy:23,overlap:24,el:0},null,[-1,-1])}};const Po=e=>({builder:4&e}),Tn=e=>({builder:e[2]}),Fo=e=>({builder:4&e}),Ln=e=>({builder:e[2]});function No(e){let t,n,s,l;const i=e[12].default,o=he(i,e,e[11],Tn);let a=[e[2],{type:"button"},e[6]],r={};for(let e=0;e<a.length;e+=1)r=U(r,a[e]);return{c(){t=R("button"),o&&o.c(),this.h()},l(e){t=G(e,"BUTTON",{type:!0});var n=le(t);o&&o.l(n),n.forEach(g),this.h()},h(){Le(t,r)},m(i,a){S(i,t,a),o&&o.m(t,null),t.autofocus&&t.focus(),e[13](t),n=!0,s||(l=[Ze(e[2].action(t)),Xe(t,"m-click",e[5]),Xe(t,"m-keydown",e[5])],s=!0)},p(e,s){o&&o.p&&(!n||2052&s)&&_e(o,i,e,e[11],n?be(i,e[11],s,Po):ge(e[11]),Tn),Le(t,r=Ve(a,[4&s&&e[2],{type:"button"},64&s&&e[6]]))},i(e){n||(b(o,e),n=!0)},o(e){C(o,e),n=!1},d(n){n&&g(t),o&&o.d(n),e[13](null),s=!1,sl(l)}}}function Bo(e){let t;const n=e[12].default,s=he(n,e,e[11],Ln);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||2052&l)&&_e(s,n,e,e[11],t?be(n,e[11],l,Fo):ge(e[11]),Ln)},i(e){t||(b(s,e),t=!0)},o(e){C(s,e),t=!1},d(e){s&&s.d(e)}}}function jo(e){let t,n,s,l;const i=[Bo,No],o=[];function a(e,t){return e[1]?0:1}return t=a(e),n=o[t]=i[t](e),{c(){n.c(),s=te()},l(e){n.l(e),s=te()},m(e,n){o[t].m(e,n),S(e,s,n),l=!0},p(e,[l]){let r=t;t=a(e),t===r?o[t].p(e,l):(fe(),C(o[r],1,1,(()=>{o[r]=null})),ae(),n=o[t],n?n.p(e,l):(n=o[t]=i[t](e),n.c()),b(n,1),n.m(s.parentNode,s))},i(e){l||(b(n),l=!0)},o(e){C(n),l=!1},d(e){e&&g(s),o[t].d(e)}}}function Vo(e,t,n){let s,l;const i=["asChild","id","el"];let o,a,r=ue(t,i),{$$slots:c={},$$scope:d}=t,{asChild:u=!1}=t,{id:p}=t,{el:f}=t;const{elements:{trigger:h},states:{open:$},ids:g,getAttrs:m}=Ri();Be(e,h,(e=>n(9,o=e))),Be(e,$,(e=>n(10,a=e)));const y=mr(),b=m("trigger");return e.$$set=e=>{t=U(U({},t),Re(e)),n(6,r=ue(t,i)),"asChild"in e&&n(1,u=e.asChild),"id"in e&&n(7,p=e.id),"el"in e&&n(0,f=e.el),"$$scope"in e&&n(11,d=e.$$scope)},e.$$.update=()=>{128&e.$$.dirty&&p&&g.trigger.set(p),1024&e.$$.dirty&&n(8,s={...b,"aria-controls":a?g.content:void 0}),512&e.$$.dirty&&n(2,l=o),260&e.$$.dirty&&Object.assign(l,s)},[f,u,l,h,$,y,r,p,s,o,a,d,c,function(e){ot[e?"unshift":"push"]((()=>{f=e,n(0,f)}))}]}class zo extends Ae{constructor(e){super(),Se(this,e,Vo,jo,me,{asChild:1,id:7,el:0})}}function Ho(e){let t,n;const s=e[2].default,l=he(s,e,e[1],null);return{c(){t=R("div"),l&&l.c(),this.h()},l(e){t=G(e,"DIV",{class:!0});var n=le(t);l&&l.l(n),n.forEach(g),this.h()},h(){V(t,"class","contents"),tn(t,"print:hidden",e[0])},m(e,s){S(e,t,s),l&&l.m(t,null),n=!0},p(e,[i]){l&&l.p&&(!n||2&i)&&_e(l,s,e,e[1],n?be(s,e[1],i,null):ge(e[1]),null),(!n||1&i)&&tn(t,"print:hidden",e[0])},i(e){n||(b(l,e),n=!0)},o(e){C(l,e),n=!1},d(e){e&&g(t),l&&l.d(e)}}}function qo(e,t,n){let{$$slots:s={},$$scope:l}=t,{enabled:i=!0}=t;return e.$$set=e=>{"enabled"in e&&n(0,i=e.enabled),"$$scope"in e&&n(1,l=e.$$scope)},e.$$.update=()=>{1&e.$$.dirty&&n(0,i=De(i))},[i,l,s]}class Go extends Ae{constructor(e){super(),Se(this,e,qo,Ho,me,{enabled:0})}}const Vs=Symbol("EVIDENCE_DROPDOWN_CTX");let Ro=0;function Wo(e,t,n){let{value:s}=t,{valueLabel:l=s}=t,{idx:i=-1}=t,{__auto:o=!1}=t;o||(i=Ro++);const a=Sl(Vs);return rl((()=>a.registerOption({value:s,label:l,idx:i,__auto:o}))),e.$$set=e=>{"value"in e&&n(1,s=e.value),"valueLabel"in e&&n(2,l=e.valueLabel),"idx"in e&&n(0,i=e.idx),"__auto"in e&&n(3,o=e.__auto)},[i,s,l,o]}class ll extends Ae{constructor(e){super(),Se(this,e,Wo,null,me,{value:1,valueLabel:2,idx:0,__auto:3})}}function Xo(e){return Object.keys(e).reduce(((t,n)=>void 0===e[n]?t:t+`${n}:${e[n]};`),"")}const Uo={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"};function Zl(e,t,n,s){const l=Array.isArray(t)?t:[t];return l.forEach((t=>e.addEventListener(t,n,s))),()=>{l.forEach((t=>e.removeEventListener(t,n,s)))}}function zs(...e){return(...t)=>{for(const n of e)"function"==typeof n&&n(...t)}}const Yo=e=>4&e,Qo=e=>({}),vn=e=>({...e[2]}),Ko=e=>4&e,Jo=e=>({}),Dn=e=>({...e[2]});function Zo(e){let t,n,s,l,i,o,a,r=(e[0]??"")+"",c=[e[6]],d={};for(let e=0;e<c.length;e+=1)d=U(d,c[e]);const u=e[18].default,p=he(u,e,e[17],vn);let f=[e[5],e[7]],h={};for(let e=0;e<f.length;e+=1)h=U(h,f[e]);return{c(){t=R("div"),n=R("label"),s=Ye(r),l=K(),p&&p.c(),this.h()},l(e){t=G(e,"DIV",{});var i=le(t);n=G(i,"LABEL",{});var o=le(n);s=Ue(o,r),o.forEach(g),l=Q(i),p&&p.l(i),i.forEach(g),this.h()},h(){Le(n,d),Le(t,h)},m(r,c){S(r,t,c),ke(t,n),ke(n,s),ke(t,l),p&&p.m(t,null),i=!0,o||(a=Ze(e[4].call(null,t)),o=!0)},p(e,n){(!i||1&n)&&r!==(r=(e[0]??"")+"")&&ks(s,r,d.contenteditable),p&&p.p&&(!i||131076&n)&&_e(p,u,e,e[17],Yo(n)||!i?ge(e[17]):be(u,e[17],n,Qo),vn),Le(t,h=Ve(f,[e[5],128&n&&e[7]]))},i(e){i||(b(p,e),i=!0)},o(e){C(p,e),i=!1},d(e){e&&g(t),p&&p.d(e),o=!1,a()}}}function xo(e){let t;const n=e[18].default,s=he(n,e,e[17],Dn);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||131076&l)&&_e(s,n,e,e[17],Ko(l)||!t?ge(e[17]):be(n,e[17],l,Jo),Dn)},i(e){t||(b(s,e),t=!0)},o(e){C(s,e),t=!1},d(e){s&&s.d(e)}}}function $o(e){let t,n,s,l;const i=[xo,Zo],o=[];function a(e,t){return e[1]?0:1}return t=a(e),n=o[t]=i[t](e),{c(){n.c(),s=te()},l(e){n.l(e),s=te()},m(e,n){o[t].m(e,n),S(e,s,n),l=!0},p(e,[l]){let r=t;t=a(e),t===r?o[t].p(e,l):(fe(),C(o[r],1,1,(()=>{o[r]=null})),ae(),n=o[t],n?n.p(e,l):(n=o[t]=i[t](e),n.c()),b(n,1),n.m(s.parentNode,s))},i(e){l||(b(n),l=!0)},o(e){C(n),l=!1},d(e){e&&g(s),o[t].d(e)}}}function ef(e,t,n){let s;const l=["label","shouldFilter","filter","value","onValueChange","loop","onKeydown","state","ids","asChild"];let i,o=ue(t,l),{$$slots:a={},$$scope:r}=t,{label:c}=t,{shouldFilter:d=!0}=t,{filter:u}=t,{value:p}=t,{onValueChange:f}=t,{loop:h}=t,{onKeydown:$}=t,{state:g}=t,{ids:m}=t,{asChild:y=!1}=t;const{commandEl:b,handleRootKeydown:x,ids:v,state:w}=hr({label:c,shouldFilter:d,filter:u,value:p,onValueChange:e=>{e!==p&&(n(8,p=e),null==f||f(e))},loop:h,state:g,ids:m});function C(e){return b.set(e),{destroy:zs(Zl(e,"keydown",T))}}Be(e,w,(e=>n(16,i=e)));const S={role:"application",id:v.root,"data-cmdk-root":""},k={"data-cmdk-label":"",for:v.input,id:v.label,style:Xo(Uo)};function T(e){null==$||$(e),!e.defaultPrevented&&x(e)}const A={action:C,attrs:S};return e.$$set=e=>{t=U(U({},t),Re(e)),n(7,o=ue(t,l)),"label"in e&&n(0,c=e.label),"shouldFilter"in e&&n(9,d=e.shouldFilter),"filter"in e&&n(10,u=e.filter),"value"in e&&n(8,p=e.value),"onValueChange"in e&&n(11,f=e.onValueChange),"loop"in e&&n(12,h=e.loop),"onKeydown"in e&&n(13,$=e.onKeydown),"state"in e&&n(14,g=e.state),"ids"in e&&n(15,m=e.ids),"asChild"in e&&n(1,y=e.asChild),"$$scope"in e&&n(17,r=e.$$scope)},e.$$.update=()=>{var t;256&e.$$.dirty&&(t=p)&&t!==i.value&&zi(w,i.value=t,i),65536&e.$$.dirty&&n(2,s={root:A,label:{attrs:k},stateStore:w,state:i})},[c,y,s,w,C,S,k,o,p,d,u,f,h,$,g,m,i,r,a]}let tf=class extends Ae{constructor(e){super(),Se(this,e,ef,$o,me,{label:0,shouldFilter:9,filter:10,value:8,onValueChange:11,loop:12,onKeydown:13,state:14,ids:15,asChild:1})}};const lf=e=>({}),En=e=>({attrs:e[4]});function pn(e){let t,n,s,l;const i=[sf,nf],o=[];function a(e,t){return e[0]?0:1}return t=a(e),n=o[t]=i[t](e),{c(){n.c(),s=te()},l(e){n.l(e),s=te()},m(e,n){o[t].m(e,n),S(e,s,n),l=!0},p(e,l){let r=t;t=a(e),t===r?o[t].p(e,l):(fe(),C(o[r],1,1,(()=>{o[r]=null})),ae(),n=o[t],n?n.p(e,l):(n=o[t]=i[t](e),n.c()),b(n,1),n.m(s.parentNode,s))},i(e){l||(b(n),l=!0)},o(e){C(n),l=!1},d(e){e&&g(s),o[t].d(e)}}}function nf(e){let t,n;const s=e[8].default,l=he(s,e,e[7],null);let i=[e[4],e[5]],o={};for(let e=0;e<i.length;e+=1)o=U(o,i[e]);return{c(){t=R("div"),l&&l.c(),this.h()},l(e){t=G(e,"DIV",{});var n=le(t);l&&l.l(n),n.forEach(g),this.h()},h(){Le(t,o)},m(e,s){S(e,t,s),l&&l.m(t,null),n=!0},p(e,a){l&&l.p&&(!n||128&a)&&_e(l,s,e,e[7],n?be(s,e[7],a,null):ge(e[7]),null),Le(t,o=Ve(i,[e[4],32&a&&e[5]]))},i(e){n||(b(l,e),n=!0)},o(e){C(l,e),n=!1},d(e){e&&g(t),l&&l.d(e)}}}function sf(e){let t;const n=e[8].default,s=he(n,e,e[7],En);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||128&l)&&_e(s,n,e,e[7],t?be(n,e[7],l,lf):ge(e[7]),En)},i(e){t||(b(s,e),t=!0)},o(e){C(s,e),t=!1},d(e){s&&s.d(e)}}}function rf(e){let t,n,s=!e[1]&&e[2]&&pn(e);return{c(){s&&s.c(),t=te()},l(e){s&&s.l(e),t=te()},m(e,l){s&&s.m(e,l),S(e,t,l),n=!0},p(e,[n]){!e[1]&&e[2]?s?(s.p(e,n),6&n&&b(s,1)):(s=pn(e),s.c(),b(s,1),s.m(t.parentNode,t)):s&&(fe(),C(s,1,1,(()=>{s=null})),ae())},i(e){n||(b(s),n=!0)},o(e){C(s),n=!1},d(e){e&&g(t),s&&s.d(e)}}}function of(e,t,n){let s;const l=["asChild"];let i,o=ue(t,l),{$$slots:a={},$$scope:r}=t,{asChild:c=!1}=t,d=!0;rl((()=>{n(1,d=!1)}));const u=Dl();return Be(e,u,(e=>n(6,i=e))),e.$$set=e=>{t=U(U({},t),Re(e)),n(5,o=ue(t,l)),"asChild"in e&&n(0,c=e.asChild),"$$scope"in e&&n(7,r=e.$$scope)},e.$$.update=()=>{64&e.$$.dirty&&n(2,s=0===i.filtered.count)},[c,d,s,u,{"data-cmdk-empty":"",role:"presentation"},o,i,r,a]}class ff extends Ae{constructor(e){super(),Se(this,e,of,rf,me,{asChild:0})}}const af=e=>({container:32&e,group:16&e}),Mn=e=>({container:e[5],group:e[4],heading:{attrs:e[8]}}),uf=e=>({container:32&e,group:16&e}),In=e=>({container:e[5],group:e[4],heading:{attrs:e[8]}});function cf(e){let t,n,s,l,i,o,a=e[0]&&Pn(e);const r=e[14].default,c=he(r,e,e[13],Mn);let d=[e[2]],u={};for(let e=0;e<d.length;e+=1)u=U(u,d[e]);let p=[e[3],e[9]],f={};for(let e=0;e<p.length;e+=1)f=U(f,p[e]);return{c(){t=R("div"),a&&a.c(),n=K(),s=R("div"),c&&c.c(),this.h()},l(e){t=G(e,"DIV",{});var l=le(t);a&&a.l(l),n=Q(l),s=G(l,"DIV",{});var i=le(s);c&&c.l(i),i.forEach(g),l.forEach(g),this.h()},h(){Le(s,u),Le(t,f)},m(r,d){S(r,t,d),a&&a.m(t,null),ke(t,n),ke(t,s),c&&c.m(s,null),l=!0,i||(o=Ze(e[7].call(null,t)),i=!0)},p(e,i){e[0]?a?a.p(e,i):(a=Pn(e),a.c(),a.m(t,n)):a&&(a.d(1),a=null),c&&c.p&&(!l||8240&i)&&_e(c,r,e,e[13],l?be(r,e[13],i,af):ge(e[13]),Mn),Le(s,u=Ve(d,[4&i&&e[2]])),Le(t,f=Ve(p,[8&i&&e[3],512&i&&e[9]]))},i(e){l||(b(c,e),l=!0)},o(e){C(c,e),l=!1},d(e){e&&g(t),a&&a.d(),c&&c.d(e),i=!1,o()}}}function df(e){let t;const n=e[14].default,s=he(n,e,e[13],In);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||8240&l)&&_e(s,n,e,e[13],t?be(n,e[13],l,uf):ge(e[13]),In)},i(e){t||(b(s,e),t=!0)},o(e){C(s,e),t=!1},d(e){s&&s.d(e)}}}function Pn(e){let t,n,s=[e[8]],l={};for(let e=0;e<s.length;e+=1)l=U(l,s[e]);return{c(){t=R("div"),n=Ye(e[0]),this.h()},l(s){t=G(s,"DIV",{});var l=le(t);n=Ue(l,e[0]),l.forEach(g),this.h()},h(){Le(t,l)},m(e,s){S(e,t,s),ke(t,n)},p(e,t){1&t&&ks(n,e[0],l.contenteditable)},d(e){e&&g(t)}}}function mf(e){let t,n,s,l;const i=[df,cf],o=[];function a(e,t){return e[1]?0:1}return t=a(e),n=o[t]=i[t](e),{c(){n.c(),s=te()},l(e){n.l(e),s=te()},m(e,n){o[t].m(e,n),S(e,s,n),l=!0},p(e,[l]){let r=t;t=a(e),t===r?o[t].p(e,l):(fe(),C(o[r],1,1,(()=>{o[r]=null})),ae(),n=o[t],n?n.p(e,l):(n=o[t]=i[t](e),n.c()),b(n,1),n.m(s.parentNode,s))},i(e){l||(b(n),l=!0)},o(e){C(n),l=!1},d(e){e&&g(s),o[t].d(e)}}}function hf(e,t,n){let s,l,i,o;const a=["heading","value","alwaysRender","asChild"];let r,c=ue(t,a),{$$slots:d={},$$scope:u}=t,{heading:p}=t,{value:f=""}=t,{alwaysRender:h=!1}=t,{asChild:$=!1}=t;const{id:g}=_r(h),m=ei(),y=Dl(),b=Ts(),x=Bt(y,(e=>!(!h&&!1!==m.filter()&&e.search)||e.filtered.groups.has(g)));function v(e){if(f)return m.value(g,f),void e.setAttribute(Kl,f);p?n(10,f=p.trim().toLowerCase()):e.textContent&&n(10,f=e.textContent.trim().toLowerCase()),m.value(g,f),e.setAttribute(Kl,f)}Be(e,x,(e=>n(12,r=e))),rl((()=>m.group(g)));const w={"data-cmdk-group-heading":"","aria-hidden":!0,id:b};return e.$$set=e=>{t=U(U({},t),Re(e)),n(9,c=ue(t,a)),"heading"in e&&n(0,p=e.heading),"value"in e&&n(10,f=e.value),"alwaysRender"in e&&n(11,h=e.alwaysRender),"asChild"in e&&n(1,$=e.asChild),"$$scope"in e&&n(13,u=e.$$scope)},e.$$.update=()=>{5120&e.$$.dirty&&n(3,s={"data-cmdk-group":"",role:"presentation",hidden:!r||void 0,"data-value":f}),1&e.$$.dirty&&n(2,l={"data-cmdk-group-items":"",role:"group","aria-labelledby":p?b:void 0}),8&e.$$.dirty&&n(5,i={action:v,attrs:s}),4&e.$$.dirty&&n(4,o={attrs:l})},[p,$,l,s,o,i,x,v,w,c,f,h,r,u,d]}class _f extends Ae{constructor(e){super(),Se(this,e,hf,mf,me,{heading:0,value:10,alwaysRender:11,asChild:1})}}function gf(e){return new Promise((t=>setTimeout(t,e)))}const bf=e=>({attrs:8&e}),Fn=e=>({action:e[6],attrs:e[3]});function yf(e){let t,n,s,l=[e[3],e[7]],i={};for(let e=0;e<l.length;e+=1)i=U(i,l[e]);return{c(){t=R("input"),this.h()},l(e){t=G(e,"INPUT",{}),this.h()},h(){Le(t,i)},m(l,i){S(l,t,i),t.autofocus&&t.focus(),e[16](t),ln(t,e[0]),n||(s=[Xe(t,"input",e[17]),Ze(e[6].call(null,t)),Xe(t,"input",e[12]),Xe(t,"focus",e[13]),Xe(t,"blur",e[14]),Xe(t,"change",e[15])],n=!0)},p(e,n){Le(t,i=Ve(l,[8&n&&e[3],128&n&&e[7]])),1&n&&t.value!==e[0]&&ln(t,e[0])},i:we,o:we,d(l){l&&g(t),e[16](null),n=!1,sl(s)}}}function kf(e){let t;const n=e[11].default,s=he(n,e,e[10],Fn);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||1032&l)&&_e(s,n,e,e[10],t?be(n,e[10],l,bf):ge(e[10]),Fn)},i(e){t||(b(s,e),t=!0)},o(e){C(s,e),t=!1},d(e){s&&s.d(e)}}}function Cf(e){let t,n,s,l;const i=[kf,yf],o=[];function a(e,t){return e[2]?0:1}return t=a(e),n=o[t]=i[t](e),{c(){n.c(),s=te()},l(e){n.l(e),s=te()},m(e,n){o[t].m(e,n),S(e,s,n),l=!0},p(e,[l]){let r=t;t=a(e),t===r?o[t].p(e,l):(fe(),C(o[r],1,1,(()=>{o[r]=null})),ae(),n=o[t],n?n.p(e,l):(n=o[t]=i[t](e),n.c()),b(n,1),n.m(s.parentNode,s))},i(e){l||(b(n),l=!0)},o(e){C(n),l=!1},d(e){e&&g(s),o[t].d(e)}}}function wf(e,t,n){const s=["autofocus","value","asChild","el"];let l,i,o=ue(t,s),{$$slots:a={},$$scope:r}=t;const{ids:c,commandEl:d}=ei(),u=Dl(),p=Bt(u,(e=>e.search));Be(e,p,(e=>n(18,i=e)));const f=Bt(u,(e=>e.value));let{autofocus:h}=t,{value:$=i}=t,{asChild:g=!1}=t,{el:m}=t;const y=Bt([f,d],(([e,t])=>{if(!br)return;const n=null==t?void 0:t.querySelector(`${yr}[${Kl}="${e}"]`);return null==n?void 0:n.getAttribute("id")}));let b;return Be(e,y,(e=>n(9,l=e))),e.$$set=e=>{t=U(U({},t),Re(e)),n(7,o=ue(t,s)),"autofocus"in e&&n(8,h=e.autofocus),"value"in e&&n(0,$=e.value),"asChild"in e&&n(2,g=e.asChild),"el"in e&&n(1,m=e.el),"$$scope"in e&&n(10,r=e.$$scope)},e.$$.update=()=>{var t;1&e.$$.dirty&&(t=$,u.updateState("search",t)),512&e.$$.dirty&&n(3,b={type:"text","data-cmdk-input":"",autocomplete:"off",autocorrect:"off",spellcheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":c.list,"aria-labelledby":c.label,"aria-activedescendant":l??void 0,id:c.input})},[$,m,g,b,p,y,function(e){return h&&gf(10).then((()=>e.focus())),{destroy:Zl(e,"change",(e=>{gr(e.target)&&u.updateState("search",e.target.value)}))}},o,h,l,r,a,function(t){Hl.call(this,e,t)},function(t){Hl.call(this,e,t)},function(t){Hl.call(this,e,t)},function(t){Hl.call(this,e,t)},function(e){ot[e?"unshift":"push"]((()=>{m=e,n(1,m)}))},function(){$=this.value,n(0,$)}]}class Af extends Ae{constructor(e){super(),Se(this,e,wf,Cf,me,{autofocus:8,value:0,asChild:2,el:1})}}const Sf=e=>({attrs:4&e}),Nn=e=>({action:e[6],attrs:e[2]}),Of=e=>({attrs:4&e}),Bn=e=>({action:e[6],attrs:e[2]});function jn(e){let t,n,s,l;const i=[Lf,Tf],o=[];function a(e,t){return e[0]?0:1}return t=a(e),n=o[t]=i[t](e),{c(){n.c(),s=te()},l(e){n.l(e),s=te()},m(e,n){o[t].m(e,n),S(e,s,n),l=!0},p(e,l){let r=t;t=a(e),t===r?o[t].p(e,l):(fe(),C(o[r],1,1,(()=>{o[r]=null})),ae(),n=o[t],n?n.p(e,l):(n=o[t]=i[t](e),n.c()),b(n,1),n.m(s.parentNode,s))},i(e){l||(b(n),l=!0)},o(e){C(n),l=!1},d(e){e&&g(s),o[t].d(e)}}}function Tf(e){let t,n,s,l;const i=e[15].default,o=he(i,e,e[14],Nn);let a=[e[2],e[7]],r={};for(let e=0;e<a.length;e+=1)r=U(r,a[e]);return{c(){t=R("div"),o&&o.c(),this.h()},l(e){t=G(e,"DIV",{});var n=le(t);o&&o.l(n),n.forEach(g),this.h()},h(){Le(t,r)},m(i,a){S(i,t,a),o&&o.m(t,null),n=!0,s||(l=Ze(e[6].call(null,t)),s=!0)},p(e,s){o&&o.p&&(!n||16388&s)&&_e(o,i,e,e[14],n?be(i,e[14],s,Sf):ge(e[14]),Nn),Le(t,r=Ve(a,[4&s&&e[2],128&s&&e[7]]))},i(e){n||(b(o,e),n=!0)},o(e){C(o,e),n=!1},d(e){e&&g(t),o&&o.d(e),s=!1,l()}}}function Lf(e){let t;const n=e[15].default,s=he(n,e,e[14],Bn);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||16388&l)&&_e(s,n,e,e[14],t?be(n,e[14],l,Of):ge(e[14]),Bn)},i(e){t||(b(s,e),t=!0)},o(e){C(s,e),t=!1},d(e){s&&s.d(e)}}}function vf(e){let t,n,s=(e[3]||e[1])&&jn(e);return{c(){s&&s.c(),t=te()},l(e){s&&s.l(e),t=te()},m(e,l){s&&s.m(e,l),S(e,t,l),n=!0},p(e,[n]){e[3]||e[1]?s?(s.p(e,n),10&n&&b(s,1)):(s=jn(e),s.c(),b(s,1),s.m(t.parentNode,t)):s&&(fe(),C(s,1,1,(()=>{s=null})),ae())},i(e){n||(b(s),n=!0)},o(e){C(s),n=!1},d(e){e&&g(t),s&&s.d(e)}}}function Df(e,t,n){let s;const l=["disabled","value","onSelect","alwaysRender","asChild","id"];let i,o,a=ue(t,l),{$$slots:r={},$$scope:c}=t,{disabled:d=!1}=t,{value:u=""}=t,{onSelect:p}=t,{alwaysRender:f=!1}=t,{asChild:h=!1}=t,{id:$=Ts()}=t;const g=kr(),m=ei(),y=Dl(),b=f??(null==g?void 0:g.alwaysRender),x=Bt(y,(e=>{if(b||!1===m.filter()||!e.search)return!0;const t=e.filtered.items.get($);return!Cr(t)&&t>0}));Be(e,x,(e=>n(3,o=e)));let v=!0;rl((()=>(n(1,v=!1),m.item($,null==g?void 0:g.id))));const w=Bt(y,(e=>e.value===u));function C(){y.updateState("value",u,!0)}return Be(e,w,(e=>n(13,i=e))),e.$$set=e=>{t=U(U({},t),Re(e)),n(7,a=ue(t,l)),"disabled"in e&&n(9,d=e.disabled),"value"in e&&n(8,u=e.value),"onSelect"in e&&n(10,p=e.onSelect),"alwaysRender"in e&&n(11,f=e.alwaysRender),"asChild"in e&&n(0,h=e.asChild),"id"in e&&n(12,$=e.id),"$$scope"in e&&n(14,c=e.$$scope)},e.$$.update=()=>{13056&e.$$.dirty&&n(2,s={"aria-disabled":!!d||void 0,"aria-selected":!!i||void 0,"data-disabled":!!d||void 0,"data-selected":!!i||void 0,"data-cmdk-item":"","data-value":u,role:"option",id:$})},[h,v,s,o,x,w,function(e){!u&&e.textContent&&n(8,u=e.textContent.trim().toLowerCase()),m.value($,u),e.setAttribute(Kl,u);const t=zs(Zl(e,"pointermove",(()=>{d||C()})),Zl(e,"click",(()=>{d||(C(),null==p||p(u))})));return{destroy(){t()}}},a,u,d,p,f,$,i,c,r]}class Ef extends Ae{constructor(e){super(),Se(this,e,Df,vf,me,{disabled:9,value:8,onSelect:10,alwaysRender:11,asChild:0,id:12})}}const pf=e=>({}),Vn=e=>({list:e[7],sizer:e[8]});function Mf(e){let t,n,s,l,i,o=""===e[2].search,a=zn(e),r=[e[6]],c={};for(let e=0;e<r.length;e+=1)c=U(c,r[e]);let d=[e[5],e[9]],u={};for(let e=0;e<d.length;e+=1)u=U(u,d[e]);return{c(){t=R("div"),n=R("div"),a.c(),this.h()},l(e){t=G(e,"DIV",{});var s=le(t);n=G(s,"DIV",{});var l=le(n);a.l(l),l.forEach(g),s.forEach(g),this.h()},h(){Le(n,c),Le(t,u)},m(o,r){S(o,t,r),ke(t,n),a.m(n,null),e[12](t),s=!0,l||(i=Ze(e[4].call(null,n)),l=!0)},p(e,s){4&s&&me(o,o=""===e[2].search)?(fe(),C(a,1,1,we),ae(),a=zn(e),a.c(),b(a,1),a.m(n,null)):a.p(e,s),Le(t,u=Ve(d,[e[5],512&s&&e[9]]))},i(e){s||(b(a),s=!0)},o(e){C(a),s=!1},d(n){n&&g(t),a.d(n),e[12](null),l=!1,i()}}}function If(e){let t,n,s=""===e[2].search,l=Hn(e);return{c(){l.c(),t=te()},l(e){l.l(e),t=te()},m(e,s){l.m(e,s),S(e,t,s),n=!0},p(e,n){4&n&&me(s,s=""===e[2].search)?(fe(),C(l,1,1,we),ae(),l=Hn(e),l.c(),b(l,1),l.m(t.parentNode,t)):l.p(e,n)},i(e){n||(b(l),n=!0)},o(e){C(l),n=!1},d(e){e&&g(t),l.d(e)}}}function zn(e){let t;const n=e[11].default,s=he(n,e,e[10],null);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||1024&l)&&_e(s,n,e,e[10],t?be(n,e[10],l,null):ge(e[10]),null)},i(e){t||(b(s,e),t=!0)},o(e){C(s,e),t=!1},d(e){s&&s.d(e)}}}function Hn(e){let t;const n=e[11].default,s=he(n,e,e[10],Vn);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||1024&l)&&_e(s,n,e,e[10],t?be(n,e[10],l,pf):ge(e[10]),Vn)},i(e){t||(b(s,e),t=!0)},o(e){C(s,e),t=!1},d(e){s&&s.d(e)}}}function Pf(e){let t,n,s,l;const i=[If,Mf],o=[];function a(e,t){return e[1]?0:1}return t=a(e),n=o[t]=i[t](e),{c(){n.c(),s=te()},l(e){n.l(e),s=te()},m(e,n){o[t].m(e,n),S(e,s,n),l=!0},p(e,[l]){let r=t;t=a(e),t===r?o[t].p(e,l):(fe(),C(o[r],1,1,(()=>{o[r]=null})),ae(),n=o[t],n?n.p(e,l):(n=o[t]=i[t](e),n.c()),b(n,1),n.m(s.parentNode,s))},i(e){l||(b(n),l=!0)},o(e){C(n),l=!1},d(e){e&&g(s),o[t].d(e)}}}function Ff(e,t,n){const s=["el","asChild"];let l,i=ue(t,s),{$$slots:o={},$$scope:a}=t;const{ids:r}=ei(),c=Dl();Be(e,c,(e=>n(2,l=e)));let{el:d}=t,{asChild:u=!1}=t;function p(e){let t;const n=e.closest("[data-cmdk-list]");if(!wr(n))return;const s=new ResizeObserver((()=>{t=requestAnimationFrame((()=>{const t=e.offsetHeight;n.style.setProperty("--cmdk-list-height",t.toFixed(1)+"px")}))}));return s.observe(e),{destroy(){cancelAnimationFrame(t),s.unobserve(e)}}}const f={"data-cmdk-list":"",role:"listbox","aria-label":"Suggestions",id:r.list,"aria-labelledby":r.input},h={"data-cmdk-list-sizer":""},$={attrs:f},g={attrs:h,action:p};return e.$$set=e=>{t=U(U({},t),Re(e)),n(9,i=ue(t,s)),"el"in e&&n(0,d=e.el),"asChild"in e&&n(1,u=e.asChild),"$$scope"in e&&n(10,a=e.$$scope)},[d,u,l,c,p,f,h,$,g,i,a,o,function(e){ot[e?"unshift":"push"]((()=>{d=e,n(0,d)}))}]}class Nf extends Ae{constructor(e){super(),Se(this,e,Ff,Pf,me,{el:0,asChild:1})}}function Bf(e){let t;const n=e[3].default,s=he(n,e,e[5],null);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||32&l)&&_e(s,n,e,e[5],t?be(n,e[5],l,null):ge(e[5]),null)},i(e){t||(b(s,e),t=!0)},o(e){C(s,e),t=!1},d(e){s&&s.d(e)}}}function jf(e){let t,n,s;const l=[{class:ze("flex h-full w-full flex-col overflow-hidden rounded-md bg-base-100",e[1])},e[2]];function i(t){e[4](t)}let o={$$slots:{default:[Bf]},$$scope:{ctx:e}};for(let e=0;e<l.length;e+=1)o=U(o,l[e]);return void 0!==e[0]&&(o.value=e[0]),t=new tf({props:o}),ot.push((()=>$l(t,"value",i))),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,n){Z(t,e,n),s=!0},p(e,[s]){const i=6&s?Ve(l,[2&s&&{class:ze("flex h-full w-full flex-col overflow-hidden rounded-md bg-base-100",e[1])},4&s&&Tt(e[2])]):{};32&s&&(i.$$scope={dirty:s,ctx:e}),!n&&1&s&&(n=!0,i.value=e[0],xl((()=>n=!1))),t.$set(i)},i(e){s||(b(t.$$.fragment,e),s=!0)},o(e){C(t.$$.fragment,e),s=!1},d(e){J(t,e)}}}function Vf(e,t,n){const s=["value","class"];let l=ue(t,s),{$$slots:i={},$$scope:o}=t,{value:a}=t,{class:r}=t;return e.$$set=e=>{t=U(U({},t),Re(e)),n(2,l=ue(t,s)),"value"in e&&n(0,a=e.value),"class"in e&&n(1,r=e.class),"$$scope"in e&&n(5,o=e.$$scope)},[a,r,l,i,function(e){a=e,n(0,a)},o]}class zf extends Ae{constructor(e){super(),Se(this,e,Vf,jf,me,{value:0,class:1})}}function Hf(e){let t;const n=e[2].default,s=he(n,e,e[3],null);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||8&l)&&_e(s,n,e,e[3],t?be(n,e[3],l,null):ge(e[3]),null)},i(e){t||(b(s,e),t=!0)},o(e){C(s,e),t=!1},d(e){s&&s.d(e)}}}function qf(e){let t,n;const s=[{class:ze("py-6 text-center text-sm",e[0])},e[1]];let l={$$slots:{default:[Hf]},$$scope:{ctx:e}};for(let e=0;e<s.length;e+=1)l=U(l,s[e]);return t=new ff({props:l}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,[n]){const l=3&n?Ve(s,[1&n&&{class:ze("py-6 text-center text-sm",e[0])},2&n&&Tt(e[1])]):{};8&n&&(l.$$scope={dirty:n,ctx:e}),t.$set(l)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Gf(e,t,n){const s=["class"];let l=ue(t,s),{$$slots:i={},$$scope:o}=t,{class:a}=t;return e.$$set=e=>{t=U(U({},t),Re(e)),n(1,l=ue(t,s)),"class"in e&&n(0,a=e.class),"$$scope"in e&&n(3,o=e.$$scope)},[a,l,i,o]}class Rf extends Ae{constructor(e){super(),Se(this,e,Gf,qf,me,{class:0})}}function Wf(e){let t;const n=e[2].default,s=he(n,e,e[3],null);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||8&l)&&_e(s,n,e,e[3],t?be(n,e[3],l,null):ge(e[3]),null)},i(e){t||(b(s,e),t=!0)},o(e){C(s,e),t=!1},d(e){s&&s.d(e)}}}function Xf(e){let t,n;const s=[{class:ze("text-foreground overflow-hidden p-1 [&_[data-cmdk-group-heading]]:px-2 [&_[data-cmdk-group-heading]]:py-1.5 [&_[data-cmdk-group-heading]]:text-xs [&_[data-cmdk-group-heading]]:font-medium [&_[data-cmdk-group-heading]]:text-base-content-muted",e[0])},e[1]];let l={$$slots:{default:[Wf]},$$scope:{ctx:e}};for(let e=0;e<s.length;e+=1)l=U(l,s[e]);return t=new _f({props:l}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,[n]){const l=3&n?Ve(s,[1&n&&{class:ze("text-foreground overflow-hidden p-1 [&_[data-cmdk-group-heading]]:px-2 [&_[data-cmdk-group-heading]]:py-1.5 [&_[data-cmdk-group-heading]]:text-xs [&_[data-cmdk-group-heading]]:font-medium [&_[data-cmdk-group-heading]]:text-base-content-muted",e[0])},2&n&&Tt(e[1])]):{};8&n&&(l.$$scope={dirty:n,ctx:e}),t.$set(l)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Uf(e,t,n){const s=["class"];let l=ue(t,s),{$$slots:i={},$$scope:o}=t,{class:a}=t;return e.$$set=e=>{t=U(U({},t),Re(e)),n(1,l=ue(t,s)),"class"in e&&n(0,a=e.class),"$$scope"in e&&n(3,o=e.$$scope)},[a,l,i,o]}class Yf extends Ae{constructor(e){super(),Se(this,e,Uf,Xf,me,{class:0})}}function Qf(e){let t;const n=e[2].default,s=he(n,e,e[3],null);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||8&l)&&_e(s,n,e,e[3],t?be(n,e[3],l,null):ge(e[3]),null)},i(e){t||(b(s,e),t=!0)},o(e){C(s,e),t=!1},d(e){s&&s.d(e)}}}function Kf(e){let t,n;const s=[{class:ze("relative flex cursor-default select-none items-center rounded-xs px-2 py-1.5 text-sm outline-none aria-selected:bg-base-200 data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e[0])},e[1]];let l={$$slots:{default:[Qf]},$$scope:{ctx:e}};for(let e=0;e<s.length;e+=1)l=U(l,s[e]);return t=new Ef({props:l}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,[n]){const l=3&n?Ve(s,[1&n&&{class:ze("relative flex cursor-default select-none items-center rounded-xs px-2 py-1.5 text-sm outline-none aria-selected:bg-base-200 data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e[0])},2&n&&Tt(e[1])]):{};8&n&&(l.$$scope={dirty:n,ctx:e}),t.$set(l)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Jf(e,t,n){const s=["class"];let l=ue(t,s),{$$slots:i={},$$scope:o}=t,{class:a}=t;return e.$$set=e=>{t=U(U({},t),Re(e)),n(1,l=ue(t,s)),"class"in e&&n(0,a=e.class),"$$scope"in e&&n(3,o=e.$$scope)},[a,l,i,o]}class Wi extends Ae{constructor(e){super(),Se(this,e,Jf,Kf,me,{class:0})}}function Zf(e){let t,n,s,l,i,o;n=new ti({props:{src:Ar,class:"mr-2 h-4 w-4 shrink-0 text-base-content-muted"}});const a=[{class:ze("flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-base-content-muted disabled:cursor-not-allowed disabled:opacity-50",e[1])},e[2]];function r(t){e[3](t)}let c={};for(let e=0;e<a.length;e+=1)c=U(c,a[e]);return void 0!==e[0]&&(c.value=e[0]),l=new Af({props:c}),ot.push((()=>$l(l,"value",r))),{c(){t=R("div"),$(n.$$.fragment),s=K(),$(l.$$.fragment),this.h()},l(e){t=G(e,"DIV",{class:!0,"data-cmdk-input-wrapper":!0});var i=le(t);x(n.$$.fragment,i),s=Q(i),x(l.$$.fragment,i),i.forEach(g),this.h()},h(){V(t,"class","flex items-center border-b border-base-300 px-3"),V(t,"data-cmdk-input-wrapper","")},m(e,i){S(e,t,i),Z(n,t,null),ke(t,s),Z(l,t,null),o=!0},p(e,[t]){const n=6&t?Ve(a,[2&t&&{class:ze("flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-base-content-muted disabled:cursor-not-allowed disabled:opacity-50",e[1])},4&t&&Tt(e[2])]):{};!i&&1&t&&(i=!0,n.value=e[0],xl((()=>i=!1))),l.$set(n)},i(e){o||(b(n.$$.fragment,e),b(l.$$.fragment,e),o=!0)},o(e){C(n.$$.fragment,e),C(l.$$.fragment,e),o=!1},d(e){e&&g(t),J(n),J(l)}}}function xf(e,t,n){const s=["class","value"];let l=ue(t,s),{class:i}=t,{value:o=""}=t;return e.$$set=e=>{t=U(U({},t),Re(e)),n(2,l=ue(t,s)),"class"in e&&n(1,i=e.class),"value"in e&&n(0,o=e.value)},[o,i,l,function(e){o=e,n(0,o)}]}class $f extends Ae{constructor(e){super(),Se(this,e,xf,Zf,me,{class:1,value:0})}}function ea(e){let t;const n=e[2].default,s=he(n,e,e[3],null);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||8&l)&&_e(s,n,e,e[3],t?be(n,e[3],l,null):ge(e[3]),null)},i(e){t||(b(s,e),t=!0)},o(e){C(s,e),t=!1},d(e){s&&s.d(e)}}}function ta(e){let t,n;const s=[{class:ze("max-h-[300px] overflow-y-auto overflow-x-hidden",e[0])},e[1]];let l={$$slots:{default:[ea]},$$scope:{ctx:e}};for(let e=0;e<s.length;e+=1)l=U(l,s[e]);return t=new Nf({props:l}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,[n]){const l=3&n?Ve(s,[1&n&&{class:ze("max-h-[300px] overflow-y-auto overflow-x-hidden",e[0])},2&n&&Tt(e[1])]):{};8&n&&(l.$$scope={dirty:n,ctx:e}),t.$set(l)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function la(e,t,n){const s=["class"];let l=ue(t,s),{$$slots:i={},$$scope:o}=t,{class:a}=t;return e.$$set=e=>{t=U(U({},t),Re(e)),n(1,l=ue(t,s)),"class"in e&&n(0,a=e.class),"$$scope"in e&&n(3,o=e.$$scope)},[a,l,i,o]}class ia extends Ae{constructor(e){super(),Se(this,e,la,ta,me,{class:0})}}function na(e){let t,n,s;return n=new ti({props:{src:Ls,class:ze("h-4 w-4",e[2]?"":"text-transparent")}}),{c(){t=R("div"),$(n.$$.fragment),this.h()},l(e){t=G(e,"DIV",{class:!0});var s=le(t);x(n.$$.fragment,s),s.forEach(g),this.h()},h(){V(t,"class","mr-2 flex h-4 w-4 items-center justify-center")},m(e,l){S(e,t,l),Z(n,t,null),s=!0},p(e,t){const s={};4&t&&(s.class=ze("h-4 w-4",e[2]?"":"text-transparent")),n.$set(s)},i(e){s||(b(n.$$.fragment,e),s=!0)},o(e){C(n.$$.fragment,e),s=!1},d(e){e&&g(t),J(n)}}}function sa(e){let t,n,s,l;return n=new ti({props:{src:Ls,class:ze("h-4 w-4")}}),{c(){t=R("div"),$(n.$$.fragment),this.h()},l(e){t=G(e,"DIV",{class:!0});var s=le(t);x(n.$$.fragment,s),s.forEach(g),this.h()},h(){V(t,"class",s=ze("mr-2 flex h-4 w-4 items-center justify-center rounded-xs border border-base-content",e[2]?"bg-base-content text-base-100":"opacity-50 [&_svg]:invisible"))},m(e,s){S(e,t,s),Z(n,t,null),l=!0},p(e,n){(!l||4&n&&s!==(s=ze("mr-2 flex h-4 w-4 items-center justify-center rounded-xs border border-base-content",e[2]?"bg-base-content text-base-100":"opacity-50 [&_svg]:invisible")))&&V(t,"class",s)},i(e){l||(b(n.$$.fragment,e),l=!0)},o(e){C(n.$$.fragment,e),l=!1},d(e){e&&g(t),J(n)}}}function ra(e){let t,n,s,l,i,o;const a=[sa,na],r=[];function c(e,t){return e[4]?0:1}return t=c(e),n=r[t]=a[t](e),{c(){n.c(),s=K(),l=R("span"),i=Ye(e[1]),this.h()},l(t){n.l(t),s=Q(t),l=G(t,"SPAN",{class:!0});var o=le(l);i=Ue(o,e[1]),o.forEach(g),this.h()},h(){V(l,"class","line-clamp-4")},m(e,n){r[t].m(e,n),S(e,s,n),S(e,l,n),ke(l,i),o=!0},p(e,l){let d=t;t=c(e),t===d?r[t].p(e,l):(fe(),C(r[d],1,1,(()=>{r[d]=null})),ae(),n=r[t],n?n.p(e,l):(n=r[t]=a[t](e),n.c()),b(n,1),n.m(s.parentNode,s)),(!o||2&l)&&bt(i,e[1])},i(e){o||(b(n),o=!0)},o(e){C(n),o=!1},d(e){e&&(g(s),g(l)),r[t].d(e)}}}function oa(e){let t,n;return t=new Wi({props:{value:String(e[1]),onSelect:e[5],$$slots:{default:[ra]},$$scope:{ctx:e}}}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,[n]){const s={};2&n&&(s.value=String(e[1])),11&n&&(s.onSelect=e[5]),86&n&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function fa(e,t,n){let{value:s}=t,{valueLabel:l=s}=t,{active:i=!1}=t,{handleSelect:o}=t,{multiple:a}=t;return e.$$set=e=>{"value"in e&&n(0,s=e.value),"valueLabel"in e&&n(1,l=e.valueLabel),"active"in e&&n(2,i=e.active),"handleSelect"in e&&n(3,o=e.handleSelect),"multiple"in e&&n(4,a=e.multiple)},[s,l,i,o,a,()=>o({value:s,label:l})]}class Hs extends Ae{constructor(e){super(),Se(this,e,fa,oa,me,{value:0,valueLabel:1,active:2,handleSelect:3,multiple:4})}}function aa(e){let t;const n=e[6].default,s=he(n,e,e[7],null);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||128&l)&&_e(s,n,e,e[7],t?be(n,e[7],l,null):ge(e[7]),null)},i(e){t||(b(s,e),t=!0)},o(e){C(s,e),t=!1},d(e){s&&s.d(e)}}}function ua(e){let t,n;const s=[{transition:e[1]},{transitionConfig:e[2]},{align:e[3]},{sideOffset:e[4]},e[5],{class:ze("z-50 w-72 rounded-md border border-base-300 p-4 shadow-md outline-none bg-base-100",e[0])}];let l={$$slots:{default:[aa]},$$scope:{ctx:e}};for(let e=0;e<s.length;e+=1)l=U(l,s[e]);return t=new Io({props:l}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,[n]){const l=63&n?Ve(s,[2&n&&{transition:e[1]},4&n&&{transitionConfig:e[2]},8&n&&{align:e[3]},16&n&&{sideOffset:e[4]},32&n&&Tt(e[5]),1&n&&{class:ze("z-50 w-72 rounded-md border border-base-300 p-4 shadow-md outline-none bg-base-100",e[0])}]):{};128&n&&(l.$$scope={dirty:n,ctx:e}),t.$set(l)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function ca(e,t,n){const s=["class","transition","transitionConfig","align","sideOffset"];let l=ue(t,s),{$$slots:i={},$$scope:o}=t,{class:a}=t,{transition:r=Sr}=t,{transitionConfig:c}=t,{align:d="center"}=t,{sideOffset:u=4}=t;return e.$$set=e=>{t=U(U({},t),Re(e)),n(5,l=ue(t,s)),"class"in e&&n(0,a=e.class),"transition"in e&&n(1,r=e.transition),"transitionConfig"in e&&n(2,c=e.transitionConfig),"align"in e&&n(3,d=e.align),"sideOffset"in e&&n(4,u=e.sideOffset),"$$scope"in e&&n(7,o=e.$$scope)},[a,r,c,d,u,l,i,o]}class da extends Ae{constructor(e){super(),Se(this,e,ca,ua,me,{class:0,transition:1,transitionConfig:2,align:3,sideOffset:4})}}const ma=bo,ha=zo;function _a(e){let t,n;const s=[{class:ze("shrink-0 bg-base-300","horizontal"===e[1]?"h-[1px] w-full":"h-full w-[1px]",e[0])},{orientation:e[1]},{decorative:e[2]},e[3]];let l={};for(let e=0;e<s.length;e+=1)l=U(l,s[e]);return t=new uo({props:l}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,[n]){const l=15&n?Ve(s,[3&n&&{class:ze("shrink-0 bg-base-300","horizontal"===e[1]?"h-[1px] w-full":"h-full w-[1px]",e[0])},2&n&&{orientation:e[1]},4&n&&{decorative:e[2]},8&n&&Tt(e[3])]):{};t.$set(l)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function ga(e,t,n){const s=["class","orientation","decorative"];let l=ue(t,s),{class:i}=t,{orientation:o="horizontal"}=t,{decorative:a}=t;return e.$$set=e=>{t=U(U({},t),Re(e)),n(3,l=ue(t,s)),"class"in e&&n(0,i=e.class),"orientation"in e&&n(1,o=e.orientation),"decorative"in e&&n(2,a=e.decorative)},[i,o,a,l]}class qs extends Ae{constructor(e){super(),Se(this,e,ga,_a,me,{class:0,orientation:1,decorative:2})}}function Pi(e){let t,n,s;const l=e[5].default,i=he(l,e,e[4],null);let o=[{href:e[1]},{class:n=ze(un({variant:e[2],className:e[0]}))},e[3]],a={};for(let e=0;e<o.length;e+=1)a=U(a,o[e]);return{c(){t=R(e[1]?"a":"span"),i&&i.c(),this.h()},l(n){t=G(n,((e[1]?"a":"span")||"null").toUpperCase(),{href:!0,class:!0});var s=le(t);i&&i.l(s),s.forEach(g),this.h()},h(){nn(e[1]?"a":"span")(t,a)},m(e,n){S(e,t,n),i&&i.m(t,null),s=!0},p(e,r){i&&i.p&&(!s||16&r)&&_e(i,l,e,e[4],s?be(l,e[4],r,null):ge(e[4]),null),nn(e[1]?"a":"span")(t,a=Ve(o,[(!s||2&r)&&{href:e[1]},(!s||5&r&&n!==(n=ze(un({variant:e[2],className:e[0]}))))&&{class:n},8&r&&e[3]]))},i(e){s||(b(i,e),s=!0)},o(e){C(i,e),s=!1},d(e){e&&g(t),i&&i.d(e)}}}function ba(e){let t,n,s=e[1]?"a":"span",l=(e[1]?"a":"span")&&Pi(e);return{c(){l&&l.c(),t=te()},l(e){l&&l.l(e),t=te()},m(e,s){l&&l.m(e,s),S(e,t,s),n=!0},p(e,[n]){e[1],s?me(s,e[1]?"a":"span")?(l.d(1),l=Pi(e),s=e[1]?"a":"span",l.c(),l.m(t.parentNode,t)):l.p(e,n):(l=Pi(e),s=e[1]?"a":"span",l.c(),l.m(t.parentNode,t))},i(e){n||(b(l,e),n=!0)},o(e){C(l,e),n=!1},d(e){e&&g(t),l&&l.d(e)}}}function ya(e,t,n){const s=["class","href","variant"];let l=ue(t,s),{$$slots:i={},$$scope:o}=t,{class:a}=t,{href:r}=t,{variant:c="default"}=t;return e.$$set=e=>{t=U(U({},t),Re(e)),n(3,l=ue(t,s)),"class"in e&&n(0,a=e.class),"href"in e&&n(1,r=e.href),"variant"in e&&n(2,c=e.variant),"$$scope"in e&&n(4,o=e.$$scope)},[a,r,c,l,o,i]}class Xi extends Ae{constructor(e){super(),Se(this,e,ya,ba,me,{class:0,href:1,variant:2})}}function qn(e){return t=>t.map((t=>{var n;const s={},l=Object.keys(t);for(const i of l)s[null!=(n=e[i])?n:i]=t[i];return s}))}function ka(e,t){if(0===e.length||0===t.length)return{};const n=Object.keys(e[0]),s=Object.keys(t[0]),l={};for(const e of n)s.includes(e)&&(l[e]=e);return l}function Ca(e,t,n){for(const s in n)if(e[n[s]]!==t[s])return!1;return!0}function wa(e,t){return t=>{if(!e.length)return t;const n=ka(t,e),s=Object.keys(e[0]);return t.flatMap((t=>{const l=e.filter((e=>Ca(t,e,n)));if(l.length)return l.map((e=>({...t,...e})));const i=Object.fromEntries(s.filter((e=>null==t[e])).map((e=>[e,void 0])));return{...t,...i}}))}}function Gn(e){return t=>{const n=t.map((e=>({...e})));for(const s in e){const l=e[s],i="function"==typeof l?l(n):l,o=null!=i&&i[Symbol.iterator]&&"string"!=typeof i?i:t.map((()=>i));let a=-1;for(const e of n)e[s]=o[++a]}return n}}function Aa(e){return t=>{const n=Oa(e),s=[];for(const e in n){const l=n[e];let i;i="function"==typeof l?l(t):Array.isArray(l)?l:Array.from(new Set(t.map((t=>t[e])))),s.push(i.map((t=>({[e]:t}))))}return Sa(s)}}function Sa(e){const t=[];return function e(t,n,s){if(!s.length&&null!=n)return void t.push(n);const l=s[0],i=s.slice(1);for(const s of l)e(t,{...n,...s},i)}(t,null,e),t}function Oa(e){if(Array.isArray(e)){const t={};for(const n of e)t[n]=n;return t}return"object"==typeof e?e:{[e]:e}}function Ta(e){return t=>{const n=[];for(const s of t){const t={...s};for(const n in e)null==t[n]&&(t[n]=e[n]);n.push(t)}return n}}function Rn(e,t){return n=>{const s=Aa(e)(n),l=wa(n)(s);return t?Ta(t)(l):l}}function Wn(e,t,n){return null==e||null==t?void 0:0===t&&0===e?0:n||0!==t?e/t:void 0}function Xn(e,t,n){const s="function"==typeof e?e:t=>t[e],l=e=>e[t],{predicate:i,allowDivideByZero:o}={};return null==i?(e,t,n)=>{const i=l(e);return Wn(s(e,t,n),i,o)}:(e,t,n)=>{if(!i(e,t,n))return;const a=l(e);return Wn(s(e,t,n),a,o)}}function Un(e,t,n){const s=e.slice();return s[22]=t[n],s}const La=e=>({item:16&e}),Yn=e=>({item:e[22].data});function va(e){let t;return{c(){t=Ye("Missing template")},l(e){t=Ue(e,"Missing template")},m(e,n){S(e,t,n)},d(e){e&&g(t)}}}function Qn(e,t){let n,s,l;const i=t[14].default,o=he(i,t,t[13],Yn),a=o||va();return{key:e,first:null,c(){n=R("div"),a&&a.c(),s=K(),this.h()},l(e){n=G(e,"DIV",{class:!0});var t=le(n);a&&a.l(t),s=Q(t),t.forEach(g),this.h()},h(){V(n,"class","row svelte-1youqmj"),this.first=n},m(e,t){S(e,n,t),a&&a.m(n,null),ke(n,s),l=!0},p(e,n){t=e,o&&o.p&&(!l||8208&n)&&_e(o,i,t,t[13],l?be(i,t[13],n,La):ge(t[13]),Yn)},i(e){l||(b(a,e),l=!0)},o(e){C(a,e),l=!1},d(e){e&&g(n),a&&a.d(e)}}}function Da(e){let t,n,s,l,i,o,a=[],r=new Map,c=pt(e[4]);const d=e=>e[22].index;for(let t=0;t<c.length;t+=1){let n=Un(e,c,t),s=d(n);r.set(s,a[t]=Qn(s,n))}return{c(){t=R("div"),n=R("div");for(let e=0;e<a.length;e+=1)a[e].c();this.h()},l(e){t=G(e,"DIV",{style:!0,class:!0});var s=le(t);n=G(s,"DIV",{class:!0,style:!0});var l=le(n);for(let e=0;e<a.length;e+=1)a[e].l(l);l.forEach(g),s.forEach(g),this.h()},h(){V(n,"class","contents svelte-1youqmj"),W(n,"padding-top",e[5]+"px"),W(n,"padding-bottom",e[6]+"px"),W(t,"height",e[0]),V(t,"class","viewport svelte-1youqmj"),nl((()=>e[17].call(t)))},m(r,c){S(r,t,c),ke(t,n);for(let e=0;e<a.length;e+=1)a[e]&&a[e].m(n,null);e[15](n),e[16](t),s=Ys(t,e[17].bind(t)),l=!0,i||(o=Xe(t,"scroll",e[7]),i=!0)},p(e,[s]){8208&s&&(c=pt(e[4]),fe(),a=vs(a,s,d,1,e,c,r,n,Ds,Qn,null,Un),ae()),(!l||32&s)&&W(n,"padding-top",e[5]+"px"),(!l||64&s)&&W(n,"padding-bottom",e[6]+"px"),(!l||1&s)&&W(t,"height",e[0])},i(e){if(!l){for(let e=0;e<c.length;e+=1)b(a[e]);l=!0}},o(e){for(let e=0;e<a.length;e+=1)C(a[e]);l=!1},d(n){n&&g(t);for(let e=0;e<a.length;e+=1)a[e].d();e[15](null),e[16](null),s(),i=!1,o()}}}function Ea(e,t,n){let s,l,i,o,a,r,{$$slots:c={},$$scope:d}=t,{items:u}=t,{height:p="100%"}=t,{itemHeight:f}=t,{start:h=0}=t,{end:$=0}=t,g=[],m=0,y=0,b=0;return rl((()=>(s=i.getElementsByClassName("row"),n(12,a=!0),()=>n(12,a=!1)))),e.$$set=e=>{"items"in e&&n(10,u=e.items),"height"in e&&n(0,p=e.height),"itemHeight"in e&&n(11,f=e.itemHeight),"start"in e&&n(8,h=e.start),"end"in e&&n(9,$=e.end),"$$scope"in e&&n(13,d=e.$$scope)},e.$$.update=()=>{1792&e.$$.dirty&&n(4,o=u.slice(h,$).map(((e,t)=>({index:t+h,data:e})))),7170&e.$$.dirty&&a&&async function(e,t,i){const{scrollTop:o}=l;if(await Cl(),!a)return;let c=y-o,d=h;for(;c<t&&d<e.length;){let e=s[d-h];if(!e){if(n(9,$=d+1),await Cl(),!a)return;e=s[d-h]}c+=g[d]=i||(null==e?void 0:e.offsetHeight)||Number.MAX_SAFE_INTEGER,d+=1}n(9,$=d);const u=e.length-$;r=(y+c)/$,n(6,b=u*r),g.length=e.length}(u,m,f)},[p,m,l,i,o,y,b,async function(){var e,t;const{scrollTop:i}=l,o=h;for(let t=0;t<s.length;t+=1)g[h+t]=f||(null==(e=s[t])?void 0:e.offsetHeight)||Number.MAX_SAFE_INTEGER;let a=0,c=0;for(;a<u.length;){const e=g[a]||r;if(c+e>i){n(8,h=a),n(5,y=c);break}c+=e,a+=1}for(;a<u.length&&(c+=g[a]||r,a+=1,!(c>i+m)););n(9,$=a);const d=u.length-$;for(r=c/$;a<u.length;)g[a++]=r;if(n(6,b=d*r),h<o){await Cl();let e=0,n=0;for(let l=h;l<o;l+=1)s[l-h]&&(e+=g[l],n+=f||(null==(t=s[l-h])?void 0:t.offsetHeight)||Number.MAX_SAFE_INTEGER);const a=n-e;l.scrollTo(0,i+a)}},h,$,u,f,a,d,c,function(e){ot[e?"unshift":"push"]((()=>{i=e,n(3,i)}))},function(e){ot[e?"unshift":"push"]((()=>{l=e,n(2,l)}))},function(){m=this.offsetHeight,n(1,m)}]}class pa extends Ae{constructor(e){super(),Se(this,e,Ea,Da,me,{items:10,height:0,itemHeight:11,start:8,end:9})}}const{Boolean:Gs}=Kr;function Kn(e,t,n){const s=e.slice();return s[58]=t[n],s[60]=n,s}function Jn(e,t,n){const s=e.slice();return s[58]=t[n],s}function Zn(e,t,n){const s=e.slice();return s[58]=t[n],s}function xn(e,t){let n,s,l;return s=new ll({props:{value:t[58][t[6]]??t[58].value,valueLabel:t[58][t[7]]??t[58].label,idx:os(t[58]),__auto:!0}}),{key:e,first:null,c(){n=te(),$(s.$$.fragment),this.h()},l(e){n=te(),x(s.$$.fragment,e),this.h()},h(){this.first=n},m(e,t){S(e,n,t),Z(s,e,t),l=!0},p(e,n){t=e;const l={};4160&n[0]&&(l.value=t[58][t[6]]??t[58].value),4224&n[0]&&(l.valueLabel=t[58][t[7]]??t[58].label),4096&n[0]&&(l.idx=os(t[58])),s.$set(l)},i(e){l||(b(s.$$.fragment,e),l=!0)},o(e){C(s.$$.fragment,e),l=!1},d(e){e&&g(n),J(s,e)}}}function Ma(e){let t,n,s;function l(t){e[40](t)}let i={$$slots:{default:[$a]},$$scope:{ctx:e}};return void 0!==e[8]&&(i.open=e[8]),t=new ma({props:i}),ot.push((()=>$l(t,"open",l))),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,n){Z(t,e,n),s=!0},p(e,s){const l={};49981&s[0]|1024&s[1]&&(l.$$scope={dirty:s,ctx:e}),!n&&256&s[0]&&(n=!0,l.open=e[8],xl((()=>n=!1))),t.$set(l)},i(e){s||(b(t.$$.fragment,e),s=!0)},o(e){C(t.$$.fragment,e),s=!1},d(e){J(t,e)}}}function Ia(e){let t,n;return t=new Dr({props:{inputType:"Dropdown",error:e[10],height:"32",width:"140"}}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,n){const s={};1024&n[0]&&(s.error=e[10]),t.$set(s)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Pa(e){let t,n,s,l,i=(e[3]??_t(e[4]))+"",o=e[5]&&$n(e);return{c(){t=Ye(i),n=K(),o&&o.c(),s=te()},l(e){t=Ue(e,i),n=Q(e),o&&o.l(e),s=te()},m(e,i){S(e,t,i),S(e,n,i),o&&o.m(e,i),S(e,s,i),l=!0},p(e,n){(!l||24&n[0])&&i!==(i=(e[3]??_t(e[4]))+"")&&bt(t,i),e[5]?o?(o.p(e,n),32&n[0]&&b(o,1)):(o=$n(e),o.c(),b(o,1),o.m(s.parentNode,s)):o&&(fe(),C(o,1,1,(()=>{o=null})),ae())},i(e){l||(b(o),l=!0)},o(e){C(o),l=!1},d(e){e&&(g(t),g(n),g(s)),o&&o.d(e)}}}function Fa(e){let t,n=e[14][0].label+"";return{c(){t=Ye(n)},l(e){t=Ue(e,n)},m(e,n){S(e,t,n)},p(e,s){16384&s[0]&&n!==(n=e[14][0].label+"")&&bt(t,n)},i:we,o:we,d(e){e&&g(t)}}}function Na(e){let t,n,s,l,i,o=e[5]&&es(e),a=e[14].length>0&&ts(e);return{c(){t=Ye(e[3]),n=K(),o&&o.c(),s=K(),a&&a.c(),l=te()},l(i){t=Ue(i,e[3]),n=Q(i),o&&o.l(i),s=Q(i),a&&a.l(i),l=te()},m(e,r){S(e,t,r),S(e,n,r),o&&o.m(e,r),S(e,s,r),a&&a.m(e,r),S(e,l,r),i=!0},p(e,n){(!i||8&n[0])&&bt(t,e[3]),e[5]?o?(o.p(e,n),32&n[0]&&b(o,1)):(o=es(e),o.c(),b(o,1),o.m(s.parentNode,s)):o&&(fe(),C(o,1,1,(()=>{o=null})),ae()),e[14].length>0?a?(a.p(e,n),16384&n[0]&&b(a,1)):(a=ts(e),a.c(),b(a,1),a.m(l.parentNode,l)):a&&(fe(),C(a,1,1,(()=>{a=null})),ae())},i(e){i||(b(o),b(a),i=!0)},o(e){C(o),C(a),i=!1},d(e){e&&(g(t),g(n),g(s),g(l)),o&&o.d(e),a&&a.d(e)}}}function $n(e){let t,n;return t=new Es({props:{description:e[5],className:"pl-1"}}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,n){const s={};32&n[0]&&(s.description=e[5]),t.$set(s)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function es(e){let t,n;return t=new Es({props:{description:e[5],className:"pl-1"}}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,n){const s={};32&n[0]&&(s.description=e[5]),t.$set(s)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function ts(e){let t,n,s,l,i=e[14][0].label+"";return t=new qs({props:{orientation:"vertical",class:"mx-2 h-4"}}),{c(){$(t.$$.fragment),n=K(),s=Ye(i)},l(e){x(t.$$.fragment,e),n=Q(e),s=Ue(e,i)},m(e,i){Z(t,e,i),S(e,n,i),S(e,s,i),l=!0},p(e,t){(!l||16384&t[0])&&i!==(i=e[14][0].label+"")&&bt(s,i)},i(e){l||(b(t.$$.fragment,e),l=!0)},o(e){C(t.$$.fragment,e),l=!1},d(e){e&&(g(n),g(s)),J(t,e)}}}function ls(e){let t,n,s,l,i,o,a,r;t=new qs({props:{orientation:"vertical",class:"mx-2 h-4"}}),s=new Xi({props:{variant:"default",class:"rounded-xs px-1 font-normal sm:hidden",$$slots:{default:[Ba]},$$scope:{ctx:e}}});const c=[Va,ja],d=[];function u(e,t){return e[14].length>3?0:1}return o=u(e),a=d[o]=c[o](e),{c(){$(t.$$.fragment),n=K(),$(s.$$.fragment),l=K(),i=R("div"),a.c(),this.h()},l(e){x(t.$$.fragment,e),n=Q(e),x(s.$$.fragment,e),l=Q(e),i=G(e,"DIV",{class:!0});var o=le(i);a.l(o),o.forEach(g),this.h()},h(){V(i,"class","hidden space-x-1 sm:flex")},m(e,a){Z(t,e,a),S(e,n,a),Z(s,e,a),S(e,l,a),S(e,i,a),d[o].m(i,null),r=!0},p(e,t){const n={};16384&t[0]|1024&t[1]&&(n.$$scope={dirty:t,ctx:e}),s.$set(n);let l=o;o=u(e),o===l?d[o].p(e,t):(fe(),C(d[l],1,1,(()=>{d[l]=null})),ae(),a=d[o],a?a.p(e,t):(a=d[o]=c[o](e),a.c()),b(a,1),a.m(i,null))},i(e){r||(b(t.$$.fragment,e),b(s.$$.fragment,e),b(a),r=!0)},o(e){C(t.$$.fragment,e),C(s.$$.fragment,e),C(a),r=!1},d(e){e&&(g(n),g(l),g(i)),J(t,e),J(s,e),d[o].d()}}}function Ba(e){let t,n=e[14].length+"";return{c(){t=Ye(n)},l(e){t=Ue(e,n)},m(e,n){S(e,t,n)},p(e,s){16384&s[0]&&n!==(n=e[14].length+"")&&bt(t,n)},d(e){e&&g(t)}}}function ja(e){let t,n,s=pt(e[14]),l=[];for(let t=0;t<s.length;t+=1)l[t]=is(Jn(e,s,t));const i=e=>C(l[e],1,1,(()=>{l[e]=null}));return{c(){for(let e=0;e<l.length;e+=1)l[e].c();t=te()},l(e){for(let t=0;t<l.length;t+=1)l[t].l(e);t=te()},m(e,s){for(let t=0;t<l.length;t+=1)l[t]&&l[t].m(e,s);S(e,t,s),n=!0},p(e,n){if(16384&n[0]){let o;for(s=pt(e[14]),o=0;o<s.length;o+=1){const i=Jn(e,s,o);l[o]?(l[o].p(i,n),b(l[o],1)):(l[o]=is(i),l[o].c(),b(l[o],1),l[o].m(t.parentNode,t))}for(fe(),o=s.length;o<l.length;o+=1)i(o);ae()}},i(e){if(!n){for(let e=0;e<s.length;e+=1)b(l[e]);n=!0}},o(e){l=l.filter(Gs);for(let e=0;e<l.length;e+=1)C(l[e]);n=!1},d(e){e&&g(t),Cs(l,e)}}}function Va(e){let t,n;return t=new Xi({props:{variant:"default",class:"rounded-xs px-1 font-normal",$$slots:{default:[Ha]},$$scope:{ctx:e}}}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,n){const s={};16384&n[0]|1024&n[1]&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function za(e){let t,n,s=e[58].label+"";return{c(){t=Ye(s),n=K()},l(e){t=Ue(e,s),n=Q(e)},m(e,s){S(e,t,s),S(e,n,s)},p(e,n){16384&n[0]&&s!==(s=e[58].label+"")&&bt(t,s)},d(e){e&&(g(t),g(n))}}}function is(e){let t,n;return t=new Xi({props:{variant:"default",class:"rounded-xs px-1 font-normal",$$slots:{default:[za]},$$scope:{ctx:e}}}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,n){const s={};16384&n[0]|1024&n[1]&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Ha(e){let t,n,s=e[14].length+"";return{c(){t=Ye(s),n=Ye(" Selected")},l(e){t=Ue(e,s),n=Ue(e," Selected")},m(e,s){S(e,t,s),S(e,n,s)},p(e,n){16384&n[0]&&s!==(s=e[14].length+"")&&bt(t,s)},d(e){e&&(g(t),g(n))}}}function qa(e){let t,n,s,l,i,o,a;const r=[Na,Fa,Pa],c=[];function d(e,t){return e[3]&&!e[0]?0:e[14].length>0&&!e[0]?1:2}t=d(e),n=c[t]=r[t](e),l=new ti({props:{src:Er,class:"ml-2 h-4 w-4"}});let u=e[14].length>0&&e[0]&&ls(e);return{c(){n.c(),s=K(),$(l.$$.fragment),i=K(),u&&u.c(),o=te()},l(e){n.l(e),s=Q(e),x(l.$$.fragment,e),i=Q(e),u&&u.l(e),o=te()},m(e,n){c[t].m(e,n),S(e,s,n),Z(l,e,n),S(e,i,n),u&&u.m(e,n),S(e,o,n),a=!0},p(e,l){let i=t;t=d(e),t===i?c[t].p(e,l):(fe(),C(c[i],1,1,(()=>{c[i]=null})),ae(),n=c[t],n?n.p(e,l):(n=c[t]=r[t](e),n.c()),b(n,1),n.m(s.parentNode,s)),e[14].length>0&&e[0]?u?(u.p(e,l),16385&l[0]&&b(u,1)):(u=ls(e),u.c(),b(u,1),u.m(o.parentNode,o)):u&&(fe(),C(u,1,1,(()=>{u=null})),ae())},i(e){a||(b(n),b(l.$$.fragment,e),b(u),a=!0)},o(e){C(n),C(l.$$.fragment,e),C(u),a=!1},d(e){e&&(g(s),g(i),g(o)),c[t].d(e),J(l,e),u&&u.d(e)}}}function Ga(e){let t,n;return t=new xr({props:{builders:[e[61]],variant:"outline",role:"combobox",size:"sm",class:"min-w-5 h-8 border border-base-300","aria-label":e[3]??_t(e[4]),$$slots:{default:[qa]},$$scope:{ctx:e}}}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,n){const s={};1073741824&n[1]&&(s.builders=[e[61]]),24&n[0]&&(s["aria-label"]=e[3]??_t(e[4])),16441&n[0]|1024&n[1]&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Ra(e){let t;return{c(){t=Ye("No results found.")},l(e){t=Ue(e,"No results found.")},m(e,n){S(e,t,n)},d(e){e&&g(t)}}}function Wa(e){let t,n;return t=new pa({props:{height:32*Rs+"px",items:e[15],$$slots:{default:[Ua,({item:e})=>({58:e}),({item:e})=>[0,e?134217728:0]]},$$scope:{ctx:e}}}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,n){const s={};32768&n[0]&&(s.items=e[15]),16641&n[0]|134218752&n[1]&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Xa(e){let t,n,s=pt(e[15]),l=[];for(let t=0;t<s.length;t+=1)l[t]=ns(Kn(e,s,t));const i=e=>C(l[e],1,1,(()=>{l[e]=null}));return{c(){for(let e=0;e<l.length;e+=1)l[e].c();t=te()},l(e){for(let t=0;t<l.length;t+=1)l[t].l(e);t=te()},m(e,s){for(let t=0;t<l.length;t+=1)l[t]&&l[t].m(e,s);S(e,t,s),n=!0},p(e,n){if(4243713&n[0]){let o;for(s=pt(e[15]),o=0;o<s.length;o+=1){const i=Kn(e,s,o);l[o]?(l[o].p(i,n),b(l[o],1)):(l[o]=ns(i),l[o].c(),b(l[o],1),l[o].m(t.parentNode,t))}for(fe(),o=s.length;o<l.length;o+=1)i(o);ae()}},i(e){if(!n){for(let e=0;e<s.length;e+=1)b(l[e]);n=!0}},o(e){l=l.filter(Gs);for(let e=0;e<l.length;e+=1)C(l[e]);n=!1},d(e){e&&g(t),Cs(l,e)}}}function Ua(e){var t,n;let s,l;function i(...t){return e[39](e[58],...t)}return s=new Hs({props:{value:null==(t=e[58])?void 0:t.value,valueLabel:null==(n=e[58])?void 0:n.label,handleSelect:e[38],multiple:e[0],active:e[14].some(i)}}),{c(){$(s.$$.fragment)},l(e){x(s.$$.fragment,e)},m(e,t){Z(s,e,t),l=!0},p(t,n){var l,o;e=t;const a={};134217728&n[1]&&(a.value=null==(l=e[58])?void 0:l.value),134217728&n[1]&&(a.valueLabel=null==(o=e[58])?void 0:o.label),257&n[0]&&(a.handleSelect=e[38]),1&n[0]&&(a.multiple=e[0]),16384&n[0]|134217728&n[1]&&(a.active=e[14].some(i)),s.$set(a)},i(e){l||(b(s.$$.fragment,e),l=!0)},o(e){C(s.$$.fragment,e),l=!1},d(e){J(s,e)}}}function ns(e){let t,n;function s(...t){return e[37](e[58],...t)}return t=new Hs({props:{id:e[60],value:e[58].value,valueLabel:e[58].label,handleSelect:e[36],multiple:e[0],active:e[14].some(s)}}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(n,l){e=n;const i={};32768&l[0]&&(i.value=e[58].value),32768&l[0]&&(i.valueLabel=e[58].label),257&l[0]&&(i.handleSelect=e[36]),1&l[0]&&(i.multiple=e[0]),49152&l[0]&&(i.active=e[14].some(s)),t.$set(i)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Ya(e){let t,n,s,l;const i=[Xa,Wa],o=[];function a(e,t){return e[15].length<=Rs?0:1}return t=a(e),n=o[t]=i[t](e),{c(){n.c(),s=te()},l(e){n.l(e),s=te()},m(e,n){o[t].m(e,n),S(e,s,n),l=!0},p(e,l){let r=t;t=a(e),t===r?o[t].p(e,l):(fe(),C(o[r],1,1,(()=>{o[r]=null})),ae(),n=o[t],n?n.p(e,l):(n=o[t]=i[t](e),n.c()),b(n,1),n.m(s.parentNode,s))},i(e){l||(b(n),l=!0)},o(e){C(n),l=!1},d(e){e&&g(s),o[t].d(e)}}}function ss(e){let t,n,s,l,i,o=!e[2]&&rs(e);return l=new Wi({props:{disabled:0===e[14].length,class:"justify-center text-center",onSelect:e[21],$$slots:{default:[Ka]},$$scope:{ctx:e}}}),{c(){o&&o.c(),t=K(),n=R("div"),s=K(),$(l.$$.fragment),this.h()},l(e){o&&o.l(e),t=Q(e),n=G(e,"DIV",{class:!0}),le(n).forEach(g),s=Q(e),x(l.$$.fragment,e),this.h()},h(){V(n,"class","-mx-1 h-px bg-base-300")},m(e,a){o&&o.m(e,a),S(e,t,a),S(e,n,a),S(e,s,a),Z(l,e,a),i=!0},p(e,n){e[2]?o&&(fe(),C(o,1,1,(()=>{o=null})),ae()):o?(o.p(e,n),4&n[0]&&b(o,1)):(o=rs(e),o.c(),b(o,1),o.m(t.parentNode,t));const s={};16384&n[0]&&(s.disabled=0===e[14].length),1024&n[1]&&(s.$$scope={dirty:n,ctx:e}),l.$set(s)},i(e){i||(b(o),b(l.$$.fragment,e),i=!0)},o(e){C(o),C(l.$$.fragment,e),i=!1},d(e){e&&(g(t),g(n),g(s)),o&&o.d(e),J(l,e)}}}function rs(e){let t,n,s,l;return s=new Wi({props:{class:"justify-center text-center",onSelect:e[20],$$slots:{default:[Qa]},$$scope:{ctx:e}}}),{c(){t=R("div"),n=K(),$(s.$$.fragment),this.h()},l(e){t=G(e,"DIV",{class:!0}),le(t).forEach(g),n=Q(e),x(s.$$.fragment,e),this.h()},h(){V(t,"class","-mx-1 h-px bg-base-300")},m(e,i){S(e,t,i),S(e,n,i),Z(s,e,i),l=!0},p(e,t){const n={};1024&t[1]&&(n.$$scope={dirty:t,ctx:e}),s.$set(n)},i(e){l||(b(s.$$.fragment,e),l=!0)},o(e){C(s.$$.fragment,e),l=!1},d(e){e&&(g(t),g(n)),J(s,e)}}}function Qa(e){let t;return{c(){t=Ye("Select all")},l(e){t=Ue(e,"Select all")},m(e,n){S(e,t,n)},d(e){e&&g(t)}}}function Ka(e){let t;return{c(){t=Ye("Clear selection")},l(e){t=Ue(e,"Clear selection")},m(e,n){S(e,t,n)},d(e){e&&g(t)}}}function Ja(e){let t,n,s,l,i,o;t=new Rf({props:{$$slots:{default:[Ra]},$$scope:{ctx:e}}}),s=new Yf({props:{$$slots:{default:[Ya]},$$scope:{ctx:e}}});let a=e[0]&&ss(e);return{c(){$(t.$$.fragment),n=K(),$(s.$$.fragment),l=K(),a&&a.c(),i=te()},l(e){x(t.$$.fragment,e),n=Q(e),x(s.$$.fragment,e),l=Q(e),a&&a.l(e),i=te()},m(e,r){Z(t,e,r),S(e,n,r),Z(s,e,r),S(e,l,r),a&&a.m(e,r),S(e,i,r),o=!0},p(e,n){const l={};1024&n[1]&&(l.$$scope={dirty:n,ctx:e}),t.$set(l);const o={};49409&n[0]|1024&n[1]&&(o.$$scope={dirty:n,ctx:e}),s.$set(o),e[0]?a?(a.p(e,n),1&n[0]&&b(a,1)):(a=ss(e),a.c(),b(a,1),a.m(i.parentNode,i)):a&&(fe(),C(a,1,1,(()=>{a=null})),ae())},i(e){o||(b(t.$$.fragment,e),b(s.$$.fragment,e),b(a),o=!0)},o(e){C(t.$$.fragment,e),C(s.$$.fragment,e),C(a),o=!1},d(e){e&&(g(n),g(l),g(i)),J(t,e),J(s,e),a&&a.d(e)}}}function Za(e){let t,n,s,l,i;function o(t){e[35](t)}let a={placeholder:e[3]};return void 0!==e[9]&&(a.value=e[9]),t=new $f({props:a}),ot.push((()=>$l(t,"value",o))),l=new ia({props:{$$slots:{default:[Ja]},$$scope:{ctx:e}}}),{c(){$(t.$$.fragment),s=K(),$(l.$$.fragment)},l(e){x(t.$$.fragment,e),s=Q(e),x(l.$$.fragment,e)},m(e,n){Z(t,e,n),S(e,s,n),Z(l,e,n),i=!0},p(e,s){const i={};8&s[0]&&(i.placeholder=e[3]),!n&&512&s[0]&&(n=!0,i.value=e[9],xl((()=>n=!1))),t.$set(i);const o={};49413&s[0]|1024&s[1]&&(o.$$scope={dirty:s,ctx:e}),l.$set(o)},i(e){i||(b(t.$$.fragment,e),b(l.$$.fragment,e),i=!0)},o(e){C(t.$$.fragment,e),C(l.$$.fragment,e),i=!1},d(e){e&&g(s),J(t,e),J(l,e)}}}function xa(e){let t,n;return t=new zf({props:{shouldFilter:!1,$$slots:{default:[Za]},$$scope:{ctx:e}}}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,n){const s={};49933&n[0]|1024&n[1]&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function $a(e){let t,n,s,l;return t=new ha({props:{asChild:!0,$$slots:{default:[Ga,({builder:e})=>({61:e}),({builder:e})=>[0,e?1073741824:0]]},$$scope:{ctx:e}}}),s=new da({props:{class:"w-[200px] p-0",align:"start",side:"bottom",$$slots:{default:[xa]},$$scope:{ctx:e}}}),{c(){$(t.$$.fragment),n=K(),$(s.$$.fragment)},l(e){x(t.$$.fragment,e),n=Q(e),x(s.$$.fragment,e)},m(e,i){Z(t,e,i),S(e,n,i),Z(s,e,i),l=!0},p(e,n){const l={};16441&n[0]|1073742848&n[1]&&(l.$$scope={dirty:n,ctx:e}),t.$set(l);const i={};49933&n[0]|1024&n[1]&&(i.$$scope={dirty:n,ctx:e}),s.$set(i)},i(e){l||(b(t.$$.fragment,e),b(s.$$.fragment,e),l=!0)},o(e){C(t.$$.fragment,e),C(s.$$.fragment,e),l=!1},d(e){e&&g(n),J(t,e),J(s,e)}}}function eu(e){let t,n,s,l;const i=[Ia,Ma],o=[];function a(e,t){return e[10].length>0?0:1}return n=a(e),s=o[n]=i[n](e),{c(){t=R("div"),s.c(),this.h()},l(e){t=G(e,"DIV",{class:!0});var n=le(t);s.l(n),n.forEach(g),this.h()},h(){V(t,"class","mt-2 mb-4 ml-0 mr-2 inline-block")},m(e,s){S(e,t,s),o[n].m(t,null),l=!0},p(e,l){let r=n;n=a(e),n===r?o[n].p(e,l):(fe(),C(o[r],1,1,(()=>{o[r]=null})),ae(),s=o[n],s?s.p(e,l):(s=o[n]=i[n](e),s.c()),b(s,1),s.m(t,null))},i(e){l||(b(s),l=!0)},o(e){C(s),l=!1},d(e){e&&g(t),o[n].d()}}}function tu(e){let t,n,s,l,i=[],o=new Map;const a=e[34].default,r=he(a,e,e[41],null);let c=pt(e[12]);const d=e=>{var t,n;return`${null==(t=e[58].label)?void 0:t.toString()} ${null==(n=e[58].value)?void 0:n.toString()}`};for(let t=0;t<c.length;t+=1){let n=Zn(e,c,t),s=d(n);o.set(s,i[t]=xn(s,n))}return s=new Go({props:{enabled:e[1],$$slots:{default:[eu]},$$scope:{ctx:e}}}),{c(){r&&r.c(),t=K();for(let e=0;e<i.length;e+=1)i[e].c();n=K(),$(s.$$.fragment)},l(e){r&&r.l(e),t=Q(e);for(let t=0;t<i.length;t+=1)i[t].l(e);n=Q(e),x(s.$$.fragment,e)},m(e,o){r&&r.m(e,o),S(e,t,o);for(let t=0;t<i.length;t+=1)i[t]&&i[t].m(e,o);S(e,n,o),Z(s,e,o),l=!0},p(e,t){r&&r.p&&(!l||1024&t[1])&&_e(r,a,e,e[41],l?be(a,e[41],t,null):ge(e[41]),null),4288&t[0]&&(c=pt(e[12]),fe(),i=vs(i,t,d,1,e,c,o,n.parentNode,Ds,xn,n,Zn),ae());const u={};2&t[0]&&(u.enabled=e[1]),51005&t[0]|1024&t[1]&&(u.$$scope={dirty:t,ctx:e}),s.$set(u)},i(e){if(!l){b(r,e);for(let e=0;e<c.length;e+=1)b(i[e]);b(s.$$.fragment,e),l=!0}},o(e){C(r,e);for(let e=0;e<i.length;e+=1)C(i[e]);C(s.$$.fragment,e),l=!1},d(e){e&&(g(t),g(n)),r&&r.d(e);for(let t=0;t<i.length;t+=1)i[t].d(e);J(s,e)}}}const Rs=5;function os(e){return"similarity"in e?-1*e.similarity:e.ordinal??0}function lu(e,t,n){var s;let l,i,o,a,r,c,d,u,p=we,f=we,h=()=>(f(),f=gt(Y,(e=>n(32,o=e))),Y);Be(e,Bs,(e=>n(45,d=e))),e.$$.on_destroy.push((()=>p())),e.$$.on_destroy.push((()=>f()));let{$$slots:$={},$$scope:g}=t;const m=Qs($),y=Or();Be(e,y,(e=>n(44,r=e)));let{title:b}=t,{name:x}=t,{multiple:v=!1}=t,{hideDuringPrint:w=!0}=t,{disableSelectAll:C=!1}=t,{defaultValue:S=[]}=t,{noDefault:k=!1}=t,{selectAllByDefault:T=!1}=t,{description:A}=t,{value:O="value",data:L,label:D=O,order:E,where:V}=t;const{results:_,update:M}=Tr({value:O,data:L,label:D,order:E,where:V},`Dropdown-${x}`,null==(s=null==d?void 0:d.data)?void 0:s.data[`Dropdown-${x}_data`]);Be(e,_,(e=>n(33,c=e)));let R=!!L;const G=x in r&&"rawValues"in r[x]&&Array.isArray(r[x].rawValues)?r[x].rawValues:[],F=Lr({multiselect:v,defaultValues:Array.isArray(S)?S:[S],initialOptions:G,noDefault:k,selectAllByDefault:De(T)}),{addOptions:W,removeOptions:I,options:B,selectedOptions:U,selectAll:N,deselectAll:j,toggleSelected:z,pauseSorting:Z,resumeSorting:P,forceSort:Q,destroy:J}=F;Be(e,B,(e=>n(15,u=e))),Be(e,U,(e=>n(14,a=e))),Ni(J);const K=e=>{JSON.stringify(e)!==JSON.stringify(r[x])&&zi(y,r[x]=e,r)};let H=[],X=a.length>0;Ni(U.subscribe((e=>{if(X||(X=e.length>0),e&&X){const t=e;v?K({label:t.map((e=>e.label)).join(", "),value:t.length?`(${t.map((e=>Vi(e.value)))})`:"(select null where 0)",rawValues:t}):t.length?t.length&&K({label:t[0].label,value:Vi(t[0].value,{serializeStrings:!1}),rawValues:t}):K({label:"",value:null,rawValues:[]})}}))),Al(Vs,{registerOption:e=>(W(e),()=>{I(e)})});let q,Y,ee="",te=0;const ne=vr((()=>{if(te++,ee&&R){const e=te,t=l.search(ee,"label");t.hash!==(null==Y?void 0:Y.hash)&&pr((()=>{e===te&&(h(n(13,Y=t)),Q())}),t.fetch())}else h(n(13,Y=l??L))}));let se=[];O||(L?se.push('Missing required prop: "value".'):m.default||se.push('Dropdown requires either "value" and "data" props or <DropdownOption />.')),L&&"object"!=typeof L&&("string"==typeof L?se.push(`'${L}' is not a recognized query result. Data should be provided in the format: data = {'${L.replace("data.","")}'}`):se.push(`'${L}' is not a recognized query result. Data should be an object. e.g data = {QueryName}`));try{$r({name:x})}catch(e){se.push(e.message)}let le=!1;return e.$$set=e=>{"title"in e&&n(3,b=e.title),"name"in e&&n(4,x=e.name),"multiple"in e&&n(0,v=e.multiple),"hideDuringPrint"in e&&n(1,w=e.hideDuringPrint),"disableSelectAll"in e&&n(2,C=e.disableSelectAll),"defaultValue"in e&&n(25,S=e.defaultValue),"noDefault"in e&&n(23,k=e.noDefault),"selectAllByDefault"in e&&n(24,T=e.selectAllByDefault),"description"in e&&n(5,A=e.description),"value"in e&&n(6,O=e.value),"data"in e&&n(26,L=e.data),"label"in e&&n(7,D=e.label),"order"in e&&n(27,E=e.order),"where"in e&&n(28,V=e.where),"$$scope"in e&&n(41,g=e.$$scope)},e.$$.update=()=>{1&e.$$.dirty[0]&&n(0,v=De(v)),2&e.$$.dirty[0]&&n(1,w=De(w)),4&e.$$.dirty[0]&&n(2,C=De(C)),8388608&e.$$.dirty[0]&&n(23,k=De(k)),16777216&e.$$.dirty[0]&&n(24,T=De(T)),469762240&e.$$.dirty[0]&&M({value:O,data:L,label:D,order:E,where:V}),4&e.$$.dirty[1]&&n(29,({hasQuery:R,query:l}=c),R,(n(11,l),p(),p=gt(l,(e=>n(31,i=e))),l)),2048&e.$$.dirty[0]&&l&&l.fetch(),67111424&e.$$.dirty[0]&&ne(),256&e.$$.dirty[0]&&(q?Z():P()),2&e.$$.dirty[1]&&null!=o&&o.dataLoaded&&n(12,H=o),1610613760&e.$$.dirty[0]|1&e.$$.dirty[1]&&null!=i&&i.error&&R&&!le&&(n(10,se=[...se,i.error]),n(30,le=!0))},[v,w,C,b,x,A,O,D,q,ee,se,l,H,Y,a,u,y,_,B,U,N,j,z,k,T,S,L,E,V,R,le,i,o,c,$,function(e){ee=e,n(9,ee)},({value:e,label:t})=>{z({value:e,label:t}),v||n(8,q=!1)},(e,t)=>t.value===e.value&&t.label===e.label,({value:e,label:t})=>{z({value:e,label:t}),v||n(8,q=!1)},(e,t)=>t.value===e.value&&t.label===e.label,function(e){q=e,n(8,q)},g]}class fs extends Ae{constructor(e){super(),Se(this,e,lu,tu,me,{title:3,name:4,multiple:0,hideDuringPrint:1,disableSelectAll:2,defaultValue:25,noDefault:23,selectAllByDefault:24,description:5,value:6,data:26,label:7,order:27,where:28},null,[-1,-1,-1])}}function iu(e){let t,n,s,l,i,o;const a=e[5].default,r=he(a,e,e[4],null);return{c(){t=R("div"),n=R("span"),s=Ye(e[2]),l=K(),i=R("div"),r&&r.c(),this.h()},l(o){t=G(o,"DIV",{class:!0});var a=le(t);n=G(a,"SPAN",{class:!0});var c=le(n);s=Ue(c,e[2]),c.forEach(g),l=Q(a),i=G(a,"DIV",{class:!0});var d=le(i);r&&r.l(d),d.forEach(g),a.forEach(g),this.h()},h(){V(n,"class","text-sm font-semibold inline-flex"),V(i,"class","pt-1 mb-6 text-sm"),V(t,"class","mb-4 mt-2 text-base-content-muted")},m(e,a){S(e,t,a),ke(t,n),ke(n,s),ke(t,l),ke(t,i),r&&r.m(i,null),o=!0},p(e,t){(!o||4&t)&&bt(s,e[2]),r&&r.p&&(!o||16&t)&&_e(r,a,e,e[4],o?be(a,e[4],t,null):ge(e[4]),null)},i(e){o||(b(r,e),o=!0)},o(e){C(r,e),o=!1},d(e){e&&g(t),r&&r.d(e)}}}function nu(e){let t,n,s,l,i,o,a,r,c,d,u,p=e[0]&&as(e);return{c(){t=R("div"),n=R("button"),s=R("span"),i=K(),o=R("span"),a=Ye(e[2]),r=K(),p&&p.c(),this.h()},l(l){t=G(l,"DIV",{class:!0});var c=le(t);n=G(c,"BUTTON",{class:!0});var d=le(n);s=G(d,"SPAN",{class:!0}),le(s).forEach(g),i=Q(d),o=G(d,"SPAN",{});var u=le(o);a=Ue(u,e[2]),u.forEach(g),d.forEach(g),r=Q(c),p&&p.l(c),c.forEach(g),this.h()},h(){V(s,"class",l=sn(e[0]?"marker rotate-marker":"marker")+" svelte-v9l93j"),V(n,"class","text-sm text-base-content-muted cursor-pointer inline-flex gap-2 svelte-v9l93j"),V(t,"class","mb-4 mt-2")},m(l,f){S(l,t,f),ke(t,n),ke(n,s),ke(n,i),ke(n,o),ke(o,a),ke(t,r),p&&p.m(t,null),c=!0,d||(u=Xe(n,"click",e[10]),d=!0)},p(e,n){(!c||1&n&&l!==(l=sn(e[0]?"marker rotate-marker":"marker")+" svelte-v9l93j"))&&V(s,"class",l),(!c||4&n)&&bt(a,e[2]),e[0]?p?(p.p(e,n),1&n&&b(p,1)):(p=as(e),p.c(),b(p,1),p.m(t,null)):p&&(fe(),C(p,1,1,(()=>{p=null})),ae())},i(e){c||(b(p),c=!0)},o(e){C(p),c=!1},d(e){e&&g(t),p&&p.d(),d=!1,u()}}}function as(e){let t,n,s;const l=e[5].default,i=he(l,e,e[4],null);return{c(){t=R("div"),i&&i.c(),this.h()},l(e){t=G(e,"DIV",{class:!0});var n=le(t);i&&i.l(n),n.forEach(g),this.h()},h(){V(t,"class","pl-[calc(0.5rem+10px)] pt-3 mb-6 text-sm")},m(e,n){S(e,t,n),i&&i.m(t,null),s=!0},p(e,t){i&&i.p&&(!s||16&t)&&_e(i,l,e,e[4],s?be(l,e[4],t,null):ge(e[4]),null)},i(e){s||(b(i,e),e&&nl((()=>{s&&(n||(n=Ql(t,cn,{},!0)),n.run(1))})),s=!0)},o(e){C(i,e),e&&(n||(n=Ql(t,cn,{},!1)),n.run(0)),s=!1},d(e){e&&g(t),i&&i.d(e),e&&n&&n.end()}}}function su(e){let t,n,s,l,i,o;const a=[nu,iu],r=[];function c(e,t){return e[3]&&e[1]?1:0}return t=c(e),n=r[t]=a[t](e),{c(){n.c(),s=te()},l(e){n.l(e),s=te()},m(n,a){r[t].m(n,a),S(n,s,a),l=!0,i||(o=[Xe(window,"beforeprint",e[6]),Xe(window,"afterprint",e[7]),Xe(window,"export-beforeprint",e[8]),Xe(window,"export-afterprint",e[9])],i=!0)},p(e,[l]){let i=t;t=c(e),t===i?r[t].p(e,l):(fe(),C(r[i],1,1,(()=>{r[i]=null})),ae(),n=r[t],n?n.p(e,l):(n=r[t]=a[t](e),n.c()),b(n,1),n.m(s.parentNode,s))},i(e){l||(b(n),l=!0)},o(e){C(n),l=!1},d(e){e&&g(s),r[t].d(e),i=!1,sl(o)}}}function ru(e,t,n){let{$$slots:s={},$$scope:l}=t,{title:i="Details"}=t,{open:o=!1}=t,{printShowAll:a=!0}=t,r=!1;return e.$$set=e=>{"title"in e&&n(2,i=e.title),"open"in e&&n(0,o=e.open),"printShowAll"in e&&n(1,a=e.printShowAll),"$$scope"in e&&n(4,l=e.$$scope)},e.$$.update=()=>{1&e.$$.dirty&&n(0,o=De(o)),2&e.$$.dirty&&n(1,a=De(a))},[o,a,i,r,l,s,()=>n(3,r=!0),()=>n(3,r=!1),()=>n(3,r=!0),()=>n(3,r=!1),()=>n(0,o=!o)]}class ou extends Ae{constructor(e){super(),Se(this,e,ru,su,me,{title:2,open:0,printShowAll:1})}}const il=Symbol.for("__evidence-chart-window-debug__"),fu=(e,t)=>{window[il]||(window[il]={}),window[il][e]=t},au=e=>{window[il]||(window[il]={}),delete window[il][e]},tl=500,uu=(e,t)=>{var n;const s=["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)&&3*e.clientWidth*e.clientHeight*3>16777215;let l;Ol("light",Gi),Ol("dark",ps);const i=()=>{l=qi(e,t.theme,{renderer:s?"svg":t.renderer??"canvas"})};i(),fu(l.id,l),t.connectGroup&&(l.group=t.connectGroup,Mr(t.connectGroup));const o=()=>{if(t.seriesColors){const e=l.getOption();if(!e)return;const n={...e};for(const s of Object.keys(t.seriesColors)){const l=e.series.findIndex((e=>e.name===s));-1!==l&&(n.series[l]={...n.series[l],itemStyle:{...n.series[l].itemStyle,color:t.seriesColors[s]}})}l.setOption(n)}},a=()=>{t.echartsOptions&&l.setOption({...t.echartsOptions})},r=()=>{let e=[];if(t.seriesOptions){const n=t.config.series.reduce(((e,{evidenceSeriesType:t},n)=>(("reference_line"===t||"reference_area"===t||"reference_point"===t)&&e.push(n),e)),[]);for(let s=0;s<t.config.series.length;s++)n.includes(s)?e.push({}):e.push({...t.seriesOptions});l.setOption({series:e})}};l.setOption({...t.config,animationDuration:tl,animationDurationUpdate:tl}),o(),a(),r();const c=t.dispatch;l.on("click",(function(e){c("click",e)}));const d=e.parentElement,u=Ir((()=>{l.resize({animation:{duration:tl}}),f()}),100);let p;window.ResizeObserver&&d?(p=new ResizeObserver(u),p.observe(d)):window.addEventListener("resize",u);const f=()=>{if(t.showAllXAxisLabels){const n=l.getOption();if(!n)return;const s=new Set(n.series.flatMap((e=>{var t;return null==(t=e.data)?void 0:t.map((e=>e[0]))}))),i=.8,o=(null==e?void 0:e.clientWidth)??0;if(!t.swapXY){const e={xAxis:{axisLabel:{interval:0,overflow:t.xAxisLabelOverflow,width:o*i/s.size}}};l.setOption(e)}}};return u(),window[n=Symbol.for("chart renders")]??(window[n]=0),window[Symbol.for("chart renders")]++,{update(e){window[Symbol.for("chart renders")]++,(e=>{e.theme!==t.theme&&(l.dispose(),t=e,i()),t=e,l.setOption({...t.config,animationDuration:tl,animationDurationUpdate:tl},!0),o(),a(),r(),l.resize({animation:{duration:tl}}),f()})(e)},destroy(){p?p.unobserve(d):window.removeEventListener("resize",u),l.dispose(),au(l.id)}}},cu=(e,t)=>{Ol("light",Gi),Ol("dark",ps),console.log("echartsCanvasDownloadAction",t.theme);const n=qi(e,t.theme,{renderer:"canvas"});t.config.animation=!1,n.setOption(t.config),t.echartsOptions&&n.setOption({...t.echartsOptions}),(()=>{if(t.seriesColors){const e=n.getOption();if(!e)return;const s={...e};for(const n of Object.keys(t.seriesColors)){const l=e.series.findIndex((e=>e.name===n));-1!==l&&(s.series[l]={...s.series[l],itemStyle:{...s.series[l].itemStyle,color:t.seriesColors[n]}})}n.setOption(s)}})(),(()=>{let e=[];if(t.seriesOptions){const s=t.config.series.reduce(((e,{evidenceSeriesType:t},n)=>(("reference_line"===t||"reference_area"===t||"reference_point"===t)&&e.push(n),e)),[]);for(let n=0;n<t.config.series.length;n++)s.includes(n)?e.push({}):e.push({...t.seriesOptions});n.setOption({series:e})}})();let s=n.getConnectedDataURL({type:"png",pixelRatio:3,backgroundColor:t.backgroundColor,excludeComponents:["toolbox"]});const l=new Date,i=new Date(l.getTime()-6e4*l.getTimezoneOffset()).toISOString().slice(0,19).replaceAll(":","-");return Pr(s,(t.evidenceChartTitle??t.queryID??"evidence-chart")+`_${i}.png`),n.dispose(),{destroy(){n.dispose()}}},Ll=(e,t)=>{Ol("evidence-light",Gi);const{config:n,ratio:s,echartsOptions:l,seriesOptions:i,seriesColors:o,isMap:a,extraHeight:r,width:c}=t;let d={renderer:"canvas"};a&&(d.height=.5*c+r,e&&e.parentNode&&(e.style.height=d.height+"px",e.parentNode.style.height=d.height+"px"));const u=qi(e,"evidence-light",d);n.animation=!1,u.setOption(n),l&&u.setOption(l),l&&u.setOption({...l}),(()=>{if(o){const e=u.getOption();if(!e)return;const t={...e};for(const n of Object.keys(o)){const s=e.series.findIndex((e=>e.name===n));-1!==s&&(t.series[s]={...t.series[s],itemStyle:{...t.series[s].itemStyle,color:o[n]}})}u.setOption(t)}})(),(()=>{let e=[];if(i){const t=n.series.reduce(((e,{evidenceSeriesType:t},n)=>(("reference_line"===t||"reference_area"===t||"reference_point"===t)&&e.push(n),e)),[]);for(let s=0;s<n.series.length;s++)t.includes(s)?e.push({}):e.push({...i});u.setOption({series:e})}})();let p=u.getConnectedDataURL({type:"jpeg",pixelRatio:s,backgroundColor:"#fff",excludeComponents:["toolbox"]});e.innerHTML=`<img src=${p} width="100%" style="\n        position: absolute; \n        top: 0;\n        user-select: all;\n        -webkit-user-select: all;\n        -moz-user-select: all;\n        -ms-user-select: all;\n    " />`,t.config.animation=!0};function du(e){let t;function n(e,t){return e[9]?_u:hu}let s=n(e),l=s(e);return{c(){l.c(),t=te()},l(e){l.l(e),t=te()},m(e,n){l.m(e,n),S(e,t,n)},p(e,i){s===(s=n(e))&&l?l.p(e,i):(l.d(1),l=s(e),l&&(l.c(),l.m(t.parentNode,t)))},d(e){e&&g(t),l.d(e)}}}function mu(e){let t,n,s,l;return{c(){t=R("div"),this.h()},l(e){t=G(e,"DIV",{class:!0,style:!0}),le(t).forEach(g),this.h()},h(){V(t,"class","chart"),W(t,"height",e[1]),W(t,"width",e[2]),W(t,"margin-left","0"),W(t,"margin-top","15px"),W(t,"margin-bottom","10px"),W(t,"overflow","visible"),W(t,"break-inside","avoid")},m(i,o){S(i,t,o),s||(l=Ze(n=Ll.call(null,t,{config:e[0],ratio:2,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13]})),s=!0)},p(e,s){2&s&&W(t,"height",e[1]),4&s&&W(t,"width",e[2]),n&&jt(n.update)&&8289&s&&n.update.call(null,{config:e[0],ratio:2,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13]})},d(e){e&&g(t),s=!1,l()}}}function hu(e){let t,n,s,l,i,o,a;return{c(){t=R("div"),s=K(),l=R("div"),this.h()},l(e){t=G(e,"DIV",{class:!0,style:!0}),le(t).forEach(g),s=Q(e),l=G(e,"DIV",{class:!0,style:!0}),le(l).forEach(g),this.h()},h(){V(t,"class","chart md:hidden"),W(t,"height",e[1]),W(t,"width","650px"),W(t,"margin-left","0"),W(t,"margin-top","15px"),W(t,"margin-bottom","10px"),W(t,"overflow","visible"),W(t,"break-inside","avoid"),V(l,"class","chart hidden md:block"),W(l,"height",e[1]),W(l,"width","841px"),W(l,"margin-left","0"),W(l,"margin-top","15px"),W(l,"margin-bottom","10px"),W(l,"overflow","visible"),W(l,"break-inside","avoid")},m(r,c){S(r,t,c),S(r,s,c),S(r,l,c),o||(a=[Ze(n=Ll.call(null,t,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:650})),Ze(i=Ll.call(null,l,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:841}))],o=!0)},p(e,s){2&s&&W(t,"height",e[1]),n&&jt(n.update)&&8673&s&&n.update.call(null,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:650}),2&s&&W(l,"height",e[1]),i&&jt(i.update)&&8673&s&&i.update.call(null,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:841})},d(e){e&&(g(t),g(s),g(l)),o=!1,sl(a)}}}function _u(e){let t,n,s,l,i,o,a;return{c(){t=R("div"),s=K(),l=R("div"),this.h()},l(e){t=G(e,"DIV",{class:!0,style:!0}),le(t).forEach(g),s=Q(e),l=G(e,"DIV",{class:!0,style:!0}),le(l).forEach(g),this.h()},h(){V(t,"class","chart md:hidden"),W(t,"height",e[1]),W(t,"width",e[11]+"px"),W(t,"margin-left","0"),W(t,"margin-top","15px"),W(t,"margin-bottom","10px"),W(t,"overflow","visible"),W(t,"break-inside","avoid"),V(l,"class","chart hidden md:block"),W(l,"height",e[1]),W(l,"width",e[10]+"px"),W(l,"margin-left","0"),W(l,"margin-top","15px"),W(l,"margin-bottom","10px"),W(l,"overflow","visible"),W(l,"break-inside","avoid")},m(r,c){S(r,t,c),S(r,s,c),S(r,l,c),o||(a=[Ze(n=Ll.call(null,t,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:e[11]})),Ze(i=Ll.call(null,l,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:e[10]}))],o=!0)},p(e,s){2&s&&W(t,"height",e[1]),2048&s&&W(t,"width",e[11]+"px"),n&&jt(n.update)&&10721&s&&n.update.call(null,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:e[11]}),2&s&&W(l,"height",e[1]),1024&s&&W(l,"width",e[10]+"px"),i&&jt(i.update)&&9697&s&&i.update.call(null,{config:e[0],ratio:4,echartsOptions:e[5],seriesOptions:e[6],seriesColors:e[13],isMap:e[7],extraHeight:e[8],width:e[10]})},d(e){e&&(g(t),g(s),g(l)),o=!1,sl(a)}}}function gu(e){let t;function n(e,t){return e[3]?mu:e[4]?du:void 0}let s=n(e),l=s&&s(e);return{c(){l&&l.c(),t=te()},l(e){l&&l.l(e),t=te()},m(e,n){l&&l.m(e,n),S(e,t,n)},p(e,[i]){s===(s=n(e))&&l?l.p(e,i):(l&&l.d(1),l=s&&s(e),l&&(l.c(),l.m(t.parentNode,t)))},i:we,o:we,d(e){e&&g(t),l&&l.d(e)}}}function bu(e,t,n){let s,l,i,o,a,r,c=we;e.$$.on_destroy.push((()=>c()));const{resolveColorsObject:d}=El();let u,p,{config:f}=t,{height:h="291px"}=t,{width:$="100%"}=t,{copying:g=!1}=t,{printing:m=!1}=t,{echartsOptions:y}=t,{seriesOptions:b}=t,{seriesColors:x}=t,{isMap:v=!1}=t,{extraHeight:w}=t,C=!1;const S=Sl("gridConfig");return S&&(C=!0,({cols:u,gapWidth:p}=S)),e.$$set=e=>{"config"in e&&n(0,f=e.config),"height"in e&&n(1,h=e.height),"width"in e&&n(2,$=e.width),"copying"in e&&n(3,g=e.copying),"printing"in e&&n(4,m=e.printing),"echartsOptions"in e&&n(5,y=e.echartsOptions),"seriesOptions"in e&&n(6,b=e.seriesOptions),"seriesColors"in e&&n(14,x=e.seriesColors),"isMap"in e&&n(7,v=e.isMap),"extraHeight"in e&&n(8,w=e.extraHeight)},e.$$.update=()=>{16384&e.$$.dirty&&(n(12,s=d(x)),c(),c=gt(s,(e=>n(13,r=e)))),32768&e.$$.dirty&&n(18,l=Math.min(Number(u),2)),327680&e.$$.dirty&&n(11,i=(650-Number(p)*(l-1))/l),32768&e.$$.dirty&&n(17,o=Math.min(Number(u),3)),196608&e.$$.dirty&&n(10,a=(841-Number(p)*(o-1))/o)},[f,h,$,g,m,y,b,v,w,C,a,i,s,r,x,u,p,o,l]}class yu extends Ae{constructor(e){super(),Se(this,e,bu,gu,me,{config:0,height:1,width:2,copying:3,printing:4,echartsOptions:5,seriesOptions:6,seriesColors:14,isMap:7,extraHeight:8})}}function ku(e){let t,n,s,l,i,o="Loading...";return{c(){t=R("div"),n=R("span"),n.textContent=o,s=K(),l=R("div"),this.h()},l(e){t=G(e,"DIV",{role:!0,class:!0});var i=le(t);n=G(i,"SPAN",{class:!0,"data-svelte-h":!0}),"svelte-1wtojot"!==Ct(n)&&(n.textContent=o),s=Q(i),l=G(i,"DIV",{class:!0,style:!0}),le(l).forEach(g),i.forEach(g),this.h()},h(){V(n,"class","sr-only"),V(l,"class","bg-base-100 rounded-md max-w-[100%]"),W(l,"height",e[0]),W(l,"margin-top","15px"),W(l,"margin-bottom","31px"),V(t,"role","status"),V(t,"class","animate-pulse")},m(e,i){S(e,t,i),ke(t,n),ke(t,s),ke(t,l)},p(e,[t]){1&t&&W(l,"height",e[0])},i(e){e&&(i||nl((()=>{i=Hi(t,Fr,{}),i.start()})))},o:we,d(e){e&&g(t)}}}function Cu(e,t,n){let{height:s="231px"}=t;return e.$$set=e=>{"height"in e&&n(0,s=e.height)},[s]}class wu extends Ae{constructor(e){super(),Se(this,e,Cu,ku,me,{height:0})}}function us(e){let t,n,s,l;const i=[Su,Au],o=[];return t=1,n=o[1]=i[1](e),{c(){n.c(),s=te()},l(e){n.l(e),s=te()},m(e,t){o[1].m(e,t),S(e,s,t),l=!0},p(e,t){n.p(e,t)},i(e){l||(b(n),l=!0)},o(e){C(n),l=!1},d(e){e&&g(s),o[1].d(e)}}}function Au(e){let t,n,s,l;return{c(){t=R("div"),this.h()},l(e){t=G(e,"DIV",{class:!0,style:!0}),le(t).forEach(g),this.h()},h(){V(t,"class","chart svelte-db4qxn"),W(t,"height",e[3]),W(t,"width",e[4]),W(t,"overflow","visible"),W(t,"display",e[15]?"none":"inherit")},m(i,o){S(i,t,o),s||(l=Ze(n=uu.call(null,t,{config:e[0],...e[25],echartsOptions:e[9],seriesOptions:e[10],dispatch:e[24],renderer:e[6],connectGroup:e[12],xAxisLabelOverflow:e[13],seriesColors:e[19],theme:e[20]})),s=!0)},p(e,s){8&s[0]&&W(t,"height",e[3]),16&s[0]&&W(t,"width",e[4]),32768&s[0]&&W(t,"display",e[15]?"none":"inherit"),n&&jt(n.update)&&35141185&s[0]&&n.update.call(null,{config:e[0],...e[25],echartsOptions:e[9],seriesOptions:e[10],dispatch:e[24],renderer:e[6],connectGroup:e[12],xAxisLabelOverflow:e[13],seriesColors:e[19],theme:e[20]})},i:we,o:we,d(e){e&&g(t),s=!1,l()}}}function Su(e){let t,n;return t=new wu({props:{height:e[3]}}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,n){const s={};8&n[0]&&(s.height=e[3]),t.$set(s)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function cs(e){let t,n,s,l=e[8]&&ds(e),i=e[5]&&e[7]&&ms(e);return{c(){t=R("div"),l&&l.c(),n=K(),i&&i.c(),this.h()},l(e){t=G(e,"DIV",{class:!0});var s=le(t);l&&l.l(s),n=Q(s),i&&i.l(s),s.forEach(g),this.h()},h(){V(t,"class","chart-footer svelte-db4qxn")},m(e,o){S(e,t,o),l&&l.m(t,null),ke(t,n),i&&i.m(t,null),s=!0},p(e,s){e[8]?l?(l.p(e,s),256&s[0]&&b(l,1)):(l=ds(e),l.c(),b(l,1),l.m(t,n)):l&&(fe(),C(l,1,1,(()=>{l=null})),ae()),e[5]&&e[7]?i?(i.p(e,s),160&s[0]&&b(i,1)):(i=ms(e),i.c(),b(i,1),i.m(t,null)):i&&(fe(),C(i,1,1,(()=>{i=null})),ae())},i(e){s||(b(l),b(i),s=!0)},o(e){C(l),C(i),s=!1},d(e){e&&g(t),l&&l.d(),i&&i.d()}}}function ds(e){let t,n;return t=new Ms({props:{text:"Save Image",class:"download-button",downloadData:e[32],display:e[17],queryID:e[1],$$slots:{default:[Ou]},$$scope:{ctx:e}}}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,n){const s={};16384&n[0]&&(s.downloadData=e[32]),131072&n[0]&&(s.display=e[17]),2&n[0]&&(s.queryID=e[1]),32&n[1]&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Ou(e){let t,n,s,l;return{c(){t=Gl("svg"),n=Gl("rect"),s=Gl("circle"),l=Gl("path"),this.h()},l(e){t=ql(e,"svg",{xmlns:!0,width:!0,height:!0,viewBox:!0,fill:!0,stroke:!0,"stroke-width":!0,"stroke-linecap":!0,"stroke-linejoin":!0});var i=le(t);n=ql(i,"rect",{x:!0,y:!0,width:!0,height:!0,rx:!0}),le(n).forEach(g),s=ql(i,"circle",{cx:!0,cy:!0,r:!0}),le(s).forEach(g),l=ql(i,"path",{d:!0}),le(l).forEach(g),i.forEach(g),this.h()},h(){V(n,"x","3"),V(n,"y","3"),V(n,"width","18"),V(n,"height","18"),V(n,"rx","2"),V(s,"cx","8.5"),V(s,"cy","8.5"),V(s,"r","1.5"),V(l,"d","M20.4 14.5L16 10 4 20"),V(t,"xmlns","http://www.w3.org/2000/svg"),V(t,"width","12"),V(t,"height","12"),V(t,"viewBox","0 0 24 24"),V(t,"fill","none"),V(t,"stroke","#000"),V(t,"stroke-width","2"),V(t,"stroke-linecap","round"),V(t,"stroke-linejoin","round")},m(e,i){S(e,t,i),ke(t,n),ke(t,s),ke(t,l)},p:we,d(e){e&&g(t)}}}function ms(e){let t,n;return t=new Ms({props:{text:"Download Data",data:e[5],queryID:e[1],class:"download-button",display:e[17]}}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,n){const s={};32&n[0]&&(s.data=e[5]),2&n[0]&&(s.queryID=e[1]),131072&n[0]&&(s.display=e[17]),t.$set(s)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function hs(e){let t,n;return t=new Nr({props:{source:JSON.stringify(e[0],void 0,3),copyToClipboard:!0,$$slots:{default:[Tu]},$$scope:{ctx:e}}}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,n){const s={};1&n[0]&&(s.source=JSON.stringify(e[0],void 0,3)),1&n[0]|32&n[1]&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Tu(e){let t,n=JSON.stringify(e[0],void 0,3)+"";return{c(){t=Ye(n)},l(e){t=Ue(e,n)},m(e,n){S(e,t,n)},p(e,s){1&s[0]&&n!==(n=JSON.stringify(e[0],void 0,3)+"")&&bt(t,n)},d(e){e&&g(t)}}}function _s(e){let t,n,s,l;return{c(){t=R("div"),this.h()},l(e){t=G(e,"DIV",{class:!0,style:!0}),le(t).forEach(g),this.h()},h(){V(t,"class","chart svelte-db4qxn"),W(t,"display","none"),W(t,"visibility","visible"),W(t,"height",e[3]),W(t,"width","666px"),W(t,"margin-left","0"),W(t,"margin-top","15px"),W(t,"margin-bottom","15px"),W(t,"overflow","visible")},m(i,o){S(i,t,o),s||(l=Ze(n=cu.call(null,t,{config:e[0],...e[25],echartsOptions:e[9],seriesOptions:e[10],seriesColors:e[19],queryID:e[1],evidenceChartTitle:e[2],theme:e[20],backgroundColor:e[21].colors["base-100"]})),s=!0)},p(e,s){8&s[0]&&W(t,"height",e[3]),n&&jt(n.update)&&37225991&s[0]&&n.update.call(null,{config:e[0],...e[25],echartsOptions:e[9],seriesOptions:e[10],seriesColors:e[19],queryID:e[1],evidenceChartTitle:e[2],theme:e[20],backgroundColor:e[21].colors["base-100"]})},d(e){e&&g(t),s=!1,l()}}}function Lu(e){let t,n,s,l,i,o,a,r,c,d,u=!e[16]&&us(e);s=new yu({props:{config:e[0],height:e[3],width:e[4],copying:e[15],printing:e[16],echartsOptions:e[9],seriesOptions:e[10],seriesColors:e[18]}});let p=(e[7]||e[8])&&cs(e),f=e[11]&&!e[16]&&hs(e),h=e[14]&&_s(e);return{c(){t=R("div"),u&&u.c(),n=K(),$(s.$$.fragment),l=K(),p&&p.c(),i=K(),f&&f.c(),o=K(),h&&h.c(),a=te(),this.h()},l(e){t=G(e,"DIV",{role:!0,class:!0});var r=le(t);u&&u.l(r),n=Q(r),x(s.$$.fragment,r),l=Q(r),p&&p.l(r),i=Q(r),f&&f.l(r),r.forEach(g),o=Q(e),h&&h.l(e),a=te(),this.h()},h(){V(t,"role","none"),V(t,"class","chart-container mt-2 mb-3 svelte-db4qxn")},m($,g){S($,t,g),u&&u.m(t,null),ke(t,n),Z(s,t,null),ke(t,l),p&&p.m(t,null),ke(t,i),f&&f.m(t,null),S($,o,g),h&&h.m($,g),S($,a,g),r=!0,c||(d=[Xe(window,"copy",e[27]),Xe(window,"beforeprint",e[28]),Xe(window,"afterprint",e[29]),Xe(window,"export-beforeprint",e[30]),Xe(window,"export-afterprint",e[31]),Xe(t,"mouseenter",e[33]),Xe(t,"mouseleave",e[34])],c=!0)},p(e,l){e[16]?u&&(fe(),C(u,1,1,(()=>{u=null})),ae()):u?(u.p(e,l),65536&l[0]&&b(u,1)):(u=us(e),u.c(),b(u,1),u.m(t,n));const o={};1&l[0]&&(o.config=e[0]),8&l[0]&&(o.height=e[3]),16&l[0]&&(o.width=e[4]),32768&l[0]&&(o.copying=e[15]),65536&l[0]&&(o.printing=e[16]),512&l[0]&&(o.echartsOptions=e[9]),1024&l[0]&&(o.seriesOptions=e[10]),262144&l[0]&&(o.seriesColors=e[18]),s.$set(o),e[7]||e[8]?p?(p.p(e,l),384&l[0]&&b(p,1)):(p=cs(e),p.c(),b(p,1),p.m(t,i)):p&&(fe(),C(p,1,1,(()=>{p=null})),ae()),e[11]&&!e[16]?f?(f.p(e,l),67584&l[0]&&b(f,1)):(f=hs(e),f.c(),b(f,1),f.m(t,null)):f&&(fe(),C(f,1,1,(()=>{f=null})),ae()),e[14]?h?h.p(e,l):(h=_s(e),h.c(),h.m(a.parentNode,a)):h&&(h.d(1),h=null)},i(e){r||(b(u),b(s.$$.fragment,e),b(p),b(f),r=!0)},o(e){C(u),C(s.$$.fragment,e),C(p),C(f),r=!1},d(e){e&&(g(t),g(o),g(a)),u&&u.d(),J(s),p&&p.d(),f&&f.d(),h&&h.d(e),c=!1,sl(d)}}}function vu(e,t,n){let s;const l=["config","queryID","evidenceChartTitle","height","width","data","renderer","downloadableData","downloadableImage","echartsOptions","seriesOptions","printEchartsConfig","seriesColors","connectGroup","xAxisLabelOverflow"];let i,o,a,r=ue(t,l),c=we;e.$$.on_destroy.push((()=>c()));const{activeAppearance:d,theme:u,resolveColorsObject:p}=El();Be(e,d,(e=>n(20,o=e))),Be(e,u,(e=>n(21,a=e)));let{config:f}=t,{queryID:h}=t,{evidenceChartTitle:$}=t,{height:g="291px"}=t,{width:m="100%"}=t,{data:y}=t,{renderer:b}=t,{downloadableData:x}=t,{downloadableImage:v}=t,{echartsOptions:w}=t,{seriesOptions:C}=t,{printEchartsConfig:S}=t,{seriesColors:k}=t,{connectGroup:T}=t,{xAxisLabelOverflow:A}=t;const O=Ks();let L=!1,D=!1,E=!1,V=!1;return e.$$set=e=>{t=U(U({},t),Re(e)),n(25,r=ue(t,l)),"config"in e&&n(0,f=e.config),"queryID"in e&&n(1,h=e.queryID),"evidenceChartTitle"in e&&n(2,$=e.evidenceChartTitle),"height"in e&&n(3,g=e.height),"width"in e&&n(4,m=e.width),"data"in e&&n(5,y=e.data),"renderer"in e&&n(6,b=e.renderer),"downloadableData"in e&&n(7,x=e.downloadableData),"downloadableImage"in e&&n(8,v=e.downloadableImage),"echartsOptions"in e&&n(9,w=e.echartsOptions),"seriesOptions"in e&&n(10,C=e.seriesOptions),"printEchartsConfig"in e&&n(11,S=e.printEchartsConfig),"seriesColors"in e&&n(26,k=e.seriesColors),"connectGroup"in e&&n(12,T=e.connectGroup),"xAxisLabelOverflow"in e&&n(13,A=e.xAxisLabelOverflow)},e.$$.update=()=>{67108864&e.$$.dirty[0]&&(n(18,s=p(k)),c(),c=gt(s,(e=>n(19,i=e))))},[f,h,$,g,m,y,b,x,v,w,C,S,T,A,L,D,E,V,s,i,o,a,d,u,O,r,k,()=>{n(15,D=!0),Js(),setTimeout((()=>{n(15,D=!1)}),0)},()=>n(16,E=!0),()=>n(16,E=!1),()=>n(16,E=!0),()=>n(16,E=!1),()=>{n(14,L=!0),setTimeout((()=>{n(14,L=!1)}),0)},()=>n(17,V=!0),()=>n(17,V=!1)]}class Du extends Ae{constructor(e){super(),Se(this,e,vu,Lu,me,{config:0,queryID:1,evidenceChartTitle:2,height:3,width:4,data:5,renderer:6,downloadableData:7,downloadableImage:8,echartsOptions:9,seriesOptions:10,printEchartsConfig:11,seriesColors:26,connectGroup:12,xAxisLabelOverflow:13},null,[-1,-1])}}function vl(e,t){const n=new Set(e.map((e=>e[t])));return Array.from(n)}function Eu(e,t){return Nt(e,Br({count:jr(t)}))[0].count}function pu(e,t,n){let s;if("object"!=typeof n)s=Nt(e,Bi(t,Gn({xTotal:ji(n)})),Mi({percentOfX:Xn(n,"xTotal")}),qn({percentOfX:n+"_pct"}));else{s=Nt(e,Mi({valueSum:0}));for(let e=0;e<s.length;e++){s[e].valueSum=0;for(let t=0;t<n.length;t++)s[e].valueSum=s[e].valueSum+s[e][n[t]]}s=Nt(s,Bi(t,Gn({xTotal:ji("valueSum")})));for(let e=0;e<n.length;e++)s=Nt(s,Mi({percentOfX:Xn(n[e],"xTotal")}),qn({percentOfX:n[e]+"_pct"}))}return s}function wl(e,t,n){return[...e].sort(((e,s)=>(e[t]<s[t]?-1:1)*(n?1:-1)))}function Ws(e,t,n){return e%(t+n)<t?0:1}function Mu(e){let t,n;return t=new Fs({props:{error:e[14],title:e[8]}}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,n){const s={};16384&n[0]&&(s.error=e[14]),256&n[0]&&(s.title=e[8]),t.$set(s)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Iu(e){let t,n,s;const l=e[136].default,i=he(l,e,e[135],null);return n=new Du({props:{config:e[20],height:e[15],width:e[13],data:e[0],queryID:e[6],evidenceChartTitle:e[7],showAllXAxisLabels:e[1],swapXY:e[3],echartsOptions:e[9],seriesOptions:e[10],printEchartsConfig:e[2],renderer:e[11],downloadableData:e[4],downloadableImage:e[5],connectGroup:e[12],xAxisLabelOverflow:e[23],seriesColors:e[16]}}),{c(){i&&i.c(),t=K(),$(n.$$.fragment)},l(e){i&&i.l(e),t=Q(e),x(n.$$.fragment,e)},m(e,l){i&&i.m(e,l),S(e,t,l),Z(n,e,l),s=!0},p(e,t){i&&i.p&&(!s||2048&t[4])&&_e(i,l,e,e[135],s?be(l,e[135],t,null):ge(e[135]),null);const o={};1048576&t[0]&&(o.config=e[20]),32768&t[0]&&(o.height=e[15]),8192&t[0]&&(o.width=e[13]),1&t[0]&&(o.data=e[0]),64&t[0]&&(o.queryID=e[6]),128&t[0]&&(o.evidenceChartTitle=e[7]),2&t[0]&&(o.showAllXAxisLabels=e[1]),8&t[0]&&(o.swapXY=e[3]),512&t[0]&&(o.echartsOptions=e[9]),1024&t[0]&&(o.seriesOptions=e[10]),4&t[0]&&(o.printEchartsConfig=e[2]),2048&t[0]&&(o.renderer=e[11]),16&t[0]&&(o.downloadableData=e[4]),32&t[0]&&(o.downloadableImage=e[5]),4096&t[0]&&(o.connectGroup=e[12]),65536&t[0]&&(o.seriesColors=e[16]),n.$set(o)},i(e){s||(b(i,e),b(n.$$.fragment,e),s=!0)},o(e){C(i,e),C(n.$$.fragment,e),s=!1},d(e){e&&g(t),i&&i.d(e),J(n,e)}}}function Pu(e){let t,n,s,l;const i=[Iu,Mu],o=[];function a(e,t){return e[14]?1:0}return t=a(e),n=o[t]=i[t](e),{c(){n.c(),s=te()},l(e){n.l(e),s=te()},m(e,n){o[t].m(e,n),S(e,s,n),l=!0},p(e,l){let r=t;t=a(e),t===r?o[t].p(e,l):(fe(),C(o[r],1,1,(()=>{o[r]=null})),ae(),n=o[t],n?n.p(e,l):(n=o[t]=i[t](e),n.c()),b(n,1),n.m(s.parentNode,s))},i(e){l||(b(n),l=!0)},o(e){C(n),l=!1},d(e){e&&g(s),o[t].d(e)}}}function Fu(e,t,n){let s,l,i,o,a,r,c,d,u,p=we,f=we,h=we;e.$$.on_destroy.push((()=>p())),e.$$.on_destroy.push((()=>f())),e.$$.on_destroy.push((()=>h()));let{$$slots:$={},$$scope:g}=t,m=Jl({}),y=Jl({});Be(e,y,(e=>n(20,u=e)));const{theme:b,resolveColor:x,resolveColorsObject:v,resolveColorPalette:w}=El();Be(e,b,(e=>n(132,r=e)));let C,{data:S}=t,{queryID:k}=t,{x:T}=t,{y:A}=t,{y2:O}=t,{series:L}=t,{size:D}=t,{tooltipTitle:E}=t,{showAllXAxisLabels:V}=t,{printEchartsConfig:_=!1}=t,M=!!A,R=!!T,{swapXY:G=!1}=t,{title:F}=t,{subtitle:W}=t,{chartType:I="Chart"}=t,{bubble:B=!1}=t,{hist:U=!1}=t,{boxplot:N=!1}=t,{xType:j}=t,{xAxisTitle:z="false"}=t,{xBaseline:Z=!0}=t,{xTickMarks:P=!1}=t,{xGridlines:Q=!1}=t,{xAxisLabels:J=!0}=t,{sort:K=!0}=t,{xFmt:H}=t,{xMin:X}=t,{xMax:q}=t,{yLog:Y=!1}=t,{yType:ee=(!0===Y?"log":"value")}=t,{yLogBase:te=10}=t,{yAxisTitle:ne="false"}=t,{yBaseline:se=!1}=t,{yTickMarks:le=!1}=t,{yGridlines:ie=!0}=t,{yAxisLabels:oe=!0}=t,{yMin:ae}=t,{yMax:re}=t,{yScale:ce=!1}=t,{yFmt:de}=t,{yAxisColor:ue="true"}=t,{y2AxisTitle:pe="false"}=t,{y2Baseline:fe=!1}=t,{y2TickMarks:he=!1}=t,{y2Gridlines:$e=!0}=t,{y2AxisLabels:ge=!0}=t,{y2Min:me}=t,{y2Max:ye}=t,{y2Scale:be=!1}=t,{y2Fmt:xe}=t,{y2AxisColor:ve="true"}=t,{sizeFmt:Ce}=t,{colorPalette:Se="default"}=t,{legend:ke}=t,{echartsOptions:Te}=t,{seriesOptions:Ae}=t,{seriesColors:Oe}=t,{stackType:Le}=t,{stacked100:Ee=!1}=t,{chartAreaHeight:Ve}=t,{renderer:_e}=t,{downloadableData:Me=!0}=t,{downloadableImage:Re=!0}=t,{connectGroup:Ge}=t,{leftPadding:Fe}=t,{rightPadding:We}=t,{xLabelWrap:Ie=!1}=t;const Ue=Ie?"break":"truncate";let Ne,je,ze,Ze,Pe,Qe,Je,Ke,He,Xe,qe,Ye,et,tt,nt,st,lt,it,ot,at,rt,ct,dt,ut,pt,ft,ht,$t,mt,yt,bt,xt,vt,wt,Ct,St,kt,Tt,At,Lt,Dt,Vt,Mt,Rt,Gt,Ft,Wt,It,Bt=[],Ut=[],Nt=[],jt=!0,zt=[],Zt=[];return e.$$set=e=>{"data"in e&&n(0,S=e.data),"queryID"in e&&n(6,k=e.queryID),"x"in e&&n(24,T=e.x),"y"in e&&n(25,A=e.y),"y2"in e&&n(49,O=e.y2),"series"in e&&n(50,L=e.series),"size"in e&&n(51,D=e.size),"tooltipTitle"in e&&n(52,E=e.tooltipTitle),"showAllXAxisLabels"in e&&n(1,V=e.showAllXAxisLabels),"printEchartsConfig"in e&&n(2,_=e.printEchartsConfig),"swapXY"in e&&n(3,G=e.swapXY),"title"in e&&n(7,F=e.title),"subtitle"in e&&n(53,W=e.subtitle),"chartType"in e&&n(8,I=e.chartType),"bubble"in e&&n(54,B=e.bubble),"hist"in e&&n(55,U=e.hist),"boxplot"in e&&n(56,N=e.boxplot),"xType"in e&&n(26,j=e.xType),"xAxisTitle"in e&&n(27,z=e.xAxisTitle),"xBaseline"in e&&n(28,Z=e.xBaseline),"xTickMarks"in e&&n(29,P=e.xTickMarks),"xGridlines"in e&&n(30,Q=e.xGridlines),"xAxisLabels"in e&&n(31,J=e.xAxisLabels),"sort"in e&&n(32,K=e.sort),"xFmt"in e&&n(57,H=e.xFmt),"xMin"in e&&n(58,X=e.xMin),"xMax"in e&&n(59,q=e.xMax),"yLog"in e&&n(33,Y=e.yLog),"yType"in e&&n(60,ee=e.yType),"yLogBase"in e&&n(61,te=e.yLogBase),"yAxisTitle"in e&&n(34,ne=e.yAxisTitle),"yBaseline"in e&&n(35,se=e.yBaseline),"yTickMarks"in e&&n(36,le=e.yTickMarks),"yGridlines"in e&&n(37,ie=e.yGridlines),"yAxisLabels"in e&&n(38,oe=e.yAxisLabels),"yMin"in e&&n(62,ae=e.yMin),"yMax"in e&&n(63,re=e.yMax),"yScale"in e&&n(39,ce=e.yScale),"yFmt"in e&&n(64,de=e.yFmt),"yAxisColor"in e&&n(65,ue=e.yAxisColor),"y2AxisTitle"in e&&n(40,pe=e.y2AxisTitle),"y2Baseline"in e&&n(41,fe=e.y2Baseline),"y2TickMarks"in e&&n(42,he=e.y2TickMarks),"y2Gridlines"in e&&n(43,$e=e.y2Gridlines),"y2AxisLabels"in e&&n(44,ge=e.y2AxisLabels),"y2Min"in e&&n(66,me=e.y2Min),"y2Max"in e&&n(67,ye=e.y2Max),"y2Scale"in e&&n(45,be=e.y2Scale),"y2Fmt"in e&&n(68,xe=e.y2Fmt),"y2AxisColor"in e&&n(69,ve=e.y2AxisColor),"sizeFmt"in e&&n(70,Ce=e.sizeFmt),"colorPalette"in e&&n(71,Se=e.colorPalette),"legend"in e&&n(46,ke=e.legend),"echartsOptions"in e&&n(9,Te=e.echartsOptions),"seriesOptions"in e&&n(10,Ae=e.seriesOptions),"seriesColors"in e&&n(72,Oe=e.seriesColors),"stackType"in e&&n(73,Le=e.stackType),"stacked100"in e&&n(74,Ee=e.stacked100),"chartAreaHeight"in e&&n(47,Ve=e.chartAreaHeight),"renderer"in e&&n(11,_e=e.renderer),"downloadableData"in e&&n(4,Me=e.downloadableData),"downloadableImage"in e&&n(5,Re=e.downloadableImage),"connectGroup"in e&&n(12,Ge=e.connectGroup),"leftPadding"in e&&n(75,Fe=e.leftPadding),"rightPadding"in e&&n(76,We=e.rightPadding),"xLabelWrap"in e&&n(48,Ie=e.xLabelWrap),"$$scope"in e&&n(135,g=e.$$scope)},e.$$.update=()=>{var t,u,$,g,b,k;if(4&e.$$.dirty[0]&&n(2,_=De(_)),8&e.$$.dirty[0]&&n(3,G=De(G)),268435456&e.$$.dirty[0]&&n(28,Z=De(Z)),536870912&e.$$.dirty[0]&&n(29,P=De(P)),1073741824&e.$$.dirty[0]&&n(30,Q=De(Q)),1&e.$$.dirty[1]&&n(31,J=De(J)),2&e.$$.dirty[1]&&n(32,K=De(K)),4&e.$$.dirty[1]&&n(33,Y=De(Y)),16&e.$$.dirty[1]&&n(35,se=De(se)),32&e.$$.dirty[1]&&n(36,le=De(le)),64&e.$$.dirty[1]&&n(37,ie=De(ie)),128&e.$$.dirty[1]&&n(38,oe=De(oe)),256&e.$$.dirty[1]&&n(39,ce=De(ce)),8&e.$$.dirty[2]&&(n(19,s=x(ue)),h(),h=gt(s,(e=>n(134,d=e)))),1024&e.$$.dirty[1]&&n(41,fe=De(fe)),2048&e.$$.dirty[1]&&n(42,he=De(he)),4096&e.$$.dirty[1]&&n(43,$e=De($e)),8192&e.$$.dirty[1]&&n(44,ge=De(ge)),16384&e.$$.dirty[1]&&n(45,be=De(be)),128&e.$$.dirty[2]&&(n(18,l=x(ve)),f(),f=gt(l,(e=>n(133,c=e)))),512&e.$$.dirty[2]&&(n(17,i=w(Se)),p(),p=gt(i,(e=>n(131,a=e)))),1024&e.$$.dirty[2]&&n(16,o=v(Oe)),16&e.$$.dirty[0]&&n(4,Me=De(Me)),32&e.$$.dirty[0]&&n(5,Re=De(Re)),131072&e.$$.dirty[1]&&n(48,Ie=De(Ie)),2130731403&e.$$.dirty[0]|2147352575&e.$$.dirty[1]|2147481975&e.$$.dirty[2]|2147483647&e.$$.dirty[3]|2047&e.$$.dirty[4])try{if(n(14,Ft=void 0),n(124,Nt=[]),n(83,Ut=[]),n(126,zt=[]),n(127,Zt=[]),n(85,Ze=[]),n(77,M=!!A),n(78,R=!!T),dn(S),n(80,Ne=Ii(S)),n(81,je=Object.keys(Ne)),R||n(24,T=je[0]),!M){n(82,Bt=je.filter((function(e){return![T,L,D].includes(e)})));for(let e=0;e<Bt.length;e++)n(85,Ze=Bt[e]),n(84,ze=Ne[Ze].type),"number"===ze&&Ut.push(Ze);n(25,A=Ut.length>1?Ut:Ut[0])}n(79,C=B?{x:T,y:A,size:D}:U?{x:T}:N?{}:{x:T,y:A});for(let e in C)null==C[e]&&Nt.push(e);if(1===Nt.length)throw Error((new Intl.ListFormat).format(Nt)+" is required");if(Nt.length>1)throw Error((new Intl.ListFormat).format(Nt)+" are required");if(!0===Ee&&A.includes("_pct")&&!1===jt)if("object"==typeof A){for(let e=0;e<A.length;e++)n(25,A[e]=A[e].replace("_pct",""),A);n(125,jt=!1)}else n(25,A=A.replace("_pct","")),n(125,jt=!1);if(T&&zt.push(T),A)if("object"==typeof A)for(n(128,Gt=0);Gt<A.length;n(128,Gt++,Gt))zt.push(A[Gt]);else zt.push(A);if(O)if("object"==typeof O)for(n(128,Gt=0);Gt<O.length;n(128,Gt++,Gt))zt.push(O[Gt]);else zt.push(O);if(D&&zt.push(D),L&&Zt.push(L),E&&Zt.push(E),dn(S,zt,Zt),!0===Ee){if(n(0,S=pu(S,T,A)),"object"==typeof A){for(let e=0;e<A.length;e++)n(25,A[e]=A[e]+"_pct",A);n(125,jt=!1)}else n(25,A+="_pct"),n(125,jt=!1);n(80,Ne=Ii(S))}switch(n(86,Pe=Ne[T].type),Pe){case"number":n(86,Pe="value");break;case"string":n(86,Pe="category");break;case"date":n(86,Pe="time")}if(n(26,j="category"===j?"category":Pe),n(1,V=V?"true"===V||!0===V:"category"===j),G&&"category"!==j)throw Error("Horizontal charts do not support a value or time-based x-axis. You can either change your SQL query to output string values or set swapXY=false.");if(G&&O)throw Error("Horizontal charts do not support a secondary y-axis. You can either set swapXY=false or remove the y2 prop from your chart.");if(G&&n(26,j="category"),n(87,Qe="value"===Pe&&"category"===j),n(0,S=K?"category"===Pe?wl(S,A,!1):wl(S,T,!0):S),"time"===Pe&&n(0,S=wl(S,T,!0)),n(129,Wt=Ii(S,"array")),n(130,It=Wt.filter((e=>"date"===e.type))),n(130,It=It.map((e=>e.id))),It.length>0)for(let e=0;e<It.length;e++)n(0,S=Vr(S,It[e]));n(88,Je=H?Ot(H,null==(t=Ne[T].format)?void 0:t.valueType):Ne[T].format),n(89,Ke=A?de?Ot(de,"object"==typeof A?null==(u=Ne[A[0]].format)?void 0:u.valueType:null==($=Ne[A].format)?void 0:$.valueType):"object"==typeof A?Ne[A[0]].format:Ne[A].format:"str"),O&&n(90,He=xe?Ot(xe,"object"==typeof O?null==(g=Ne[O[0]].format)?void 0:g.valueType:null==(b=Ne[O].format)?void 0:b.valueType):"object"==typeof O?Ne[O[0]].format:Ne[O].format),D&&n(91,Xe=Ce?Ot(Ce,null==(k=Ne[D].format)?void 0:k.valueType):Ne[D].format),n(92,qe=Ne[T].columnUnitSummary),A&&n(93,Ye="object"==typeof A?Ne[A[0]].columnUnitSummary:Ne[A].columnUnitSummary),O&&n(94,et="object"==typeof O?Ne[O[0]].columnUnitSummary:Ne[O].columnUnitSummary),n(27,z="true"===z?_t(T,Je):"false"===z?"":z),n(34,ne="true"===ne?"object"==typeof A?"":_t(A,Ke):"false"===ne?"":ne),n(40,pe="true"===pe?"object"==typeof O?"":_t(O,He):"false"===pe?"":pe);let e,s,l="object"==typeof A?A.length:1,i=L?Eu(S,L):1,o=l*i,p="object"==typeof O?O.length:O?1:0,f=o+p;if(void 0!==ke&&n(46,ke="true"===ke||!0===ke),n(46,ke=ke??f>1),!0===Ee&&!0===Y)throw Error("Log axis cannot be used in a 100% stacked chart");if("stacked"===Le&&f>1&&!0===Y)throw Error("Log axis cannot be used in a stacked chart");if("object"==typeof A){e=Ne[A[0]].columnUnitSummary.min;for(let t=0;t<A.length;t++)Ne[A[t]].columnUnitSummary.min<e&&(e=Ne[A[t]].columnUnitSummary.min)}else A&&(e=Ne[A].columnUnitSummary.min);if(!0===Y&&e<=0&&null!==e)throw Error("Log axis cannot display values less than or equal to zero");if(m.update((e=>({...e,data:S,x:T,y:A,y2:O,series:L,swapXY:G,sort:K,xType:j,xFormat:Je,yFormat:Ke,y2Format:He,sizeFormat:Xe,xMismatch:Qe,size:D,yMin:ae,y2Min:me,columnSummary:Ne,xAxisTitle:z,yAxisTitle:ne,y2AxisTitle:pe,tooltipTitle:E,chartAreaHeight:Ve,chartType:I,yCount:l,y2Count:p}))),n(95,tt=vl(S,T)),n(96,nt=G?{type:ee,logBase:te,position:"top",axisLabel:{show:oe,hideOverlap:!0,showMaxLabel:!0,formatter:e=>Ul(e,Ke,Ye),margin:4},min:ae,max:re,scale:ce,splitLine:{show:ie},axisLine:{show:se,onZero:!1},axisTick:{show:le},boundaryGap:!1,z:2}:{type:j,min:X,max:q,tooltip:{show:!0,position:"inside",formatter(e){if(e.isTruncated())return e.name}},splitLine:{show:Q},axisLine:{show:Z},axisTick:{show:P},axisLabel:{show:J,hideOverlap:!0,showMaxLabel:"category"===j||"value"===j,formatter:"time"!==j&&"category"!==j&&function(e){return Ul(e,Je,qe)},margin:6},scale:!0,z:2}),G?n(97,st={type:j,inverse:"true",splitLine:{show:Q},axisLine:{show:Z},axisTick:{show:P},axisLabel:{show:J,hideOverlap:!0},scale:!0,min:X,max:q,z:2}):(n(97,st={type:ee,logBase:te,splitLine:{show:ie},axisLine:{show:se,onZero:!1},axisTick:{show:le},axisLabel:{show:oe,hideOverlap:!0,margin:4,formatter:e=>Ul(e,Ke,Ye),color:O?"true"===d?a[0]:"false"!==d?d:void 0:void 0},name:ne,nameLocation:"end",nameTextStyle:{align:"left",verticalAlign:"top",padding:[0,5,0,0],color:O?"true"===d?a[0]:"false"!==d?d:void 0:void 0},nameGap:6,min:ae,max:re,scale:ce,boundaryGap:["0%","1%"],z:2}),s={type:"value",show:!1,alignTicks:!0,splitLine:{show:$e},axisLine:{show:fe,onZero:!1},axisTick:{show:he},axisLabel:{show:ge,hideOverlap:!0,margin:4,formatter:e=>Ul(e,He,et),color:"true"===c?a[o]:"false"!==c?c:void 0},name:pe,nameLocation:"end",nameTextStyle:{align:"right",verticalAlign:"top",padding:[0,0,0,5],color:"true"===c?a[o]:"false"!==c?c:void 0},nameGap:6,min:me,max:ye,scale:be,boundaryGap:["0%","1%"],z:2},n(97,st=[st,s])),Ve){if(n(47,Ve=Number(Ve)),isNaN(Ve))throw Error("chartAreaHeight must be a number");if(Ve<=0)throw Error("chartAreaHeight must be a positive number")}else n(47,Ve=180);n(100,ot=!!F),n(101,at=!!W),n(102,rt=ke*(null!==L||"object"==typeof A&&A.length>1)),n(103,ct=""!==ne&&G),n(104,dt=""!==z&&!G),n(105,ut=15),n(106,pt=13),n(107,ft=6*at),n(108,ht=ot*ut+at*pt+ft*Math.max(ot,at)),n(109,$t=10),n(110,mt=10),n(111,yt=14),n(112,bt=14),n(113,xt=15),n(113,xt*=rt),n(114,vt=7),n(114,vt*=Math.max(ot,at)),n(115,wt=ht+vt),n(116,Ct=wt+xt+bt*ct+$t),n(117,St=dt*yt+mt),n(121,Lt=8),n(123,Vt=1),G&&(n(122,Dt=tt.length),n(123,Vt=Math.max(1,Dt/Lt))),n(118,kt=Ve*Vt+Ct+St),n(119,Tt=wt+xt+7),n(15,Mt=kt+"px"),n(13,Rt="100%"),n(120,At=G?ne:z),""!==At&&n(120,At+=" →"),n(98,lt={id:"horiz-axis-title",type:"text",style:{text:At,textAlign:"right",fill:r.colors["base-content-muted"]},cursor:"auto",right:G?"2%":"3%",top:G?Tt:null,bottom:G?null:"2%"}),n(99,it={title:{text:F,subtext:W,subtextStyle:{width:Rt}},tooltip:{trigger:"axis",show:!0,formatter(e){let t,n,s,i;if(f>1){n=e[0].value[G?1:0],t=`<span id="tooltip" style='font-weight: 600;'>${Et(n,Je)}</span>`;for(let n=e.length-1;n>=0;n--)"stackTotal"!==e[n].seriesName&&(s=e[n].value[G?0:1],t+=`<br> <span style='font-size: 11px;'>${e[n].marker} ${e[n].seriesName}<span/><span style='float:right; margin-left: 10px; font-size: 12px;'>${Et(s,0===Ws(e[n].componentIndex,l,p)?Ke:He)}</span>`)}else"value"===j?(n=e[0].value[G?1:0],s=e[0].value[G?0:1],i=e[0].seriesName,t=`<span id="tooltip" style='font-weight: 600;'>${_t(T,Je)}: </span><span style='float:right; margin-left: 10px;'>${Et(n,Je)}</span><br/><span style='font-weight: 600;'>${_t(i,Ke)}: </span><span style='float:right; margin-left: 10px;'>${Et(s,Ke)}</span>`):(n=e[0].value[G?1:0],s=e[0].value[G?0:1],i=e[0].seriesName,t=`<span id="tooltip" style='font-weight: 600;'>${Et(n,Je)}</span><br/><span>${_t(i,Ke)}: </span><span style='float:right; margin-left: 10px;'>${Et(s,Ke)}</span>`);return t},confine:!0,axisPointer:{type:"shadow"},extraCssText:'box-shadow: 0 3px 6px rgba(0,0,0,.15); box-shadow: 0 2px 4px rgba(0,0,0,.12); z-index: 1; font-feature-settings: "cv02", "tnum";',order:"valueDesc"},legend:{show:ke,type:"scroll",top:wt,padding:[0,0,0,0],data:[]},grid:{left:Fe??(G?"1%":"0.8%"),right:We??(G?"4%":"3%"),bottom:St,top:Ct,containLabel:!0},xAxis:nt,yAxis:st,series:[],animation:!0,graphic:lt,color:a}),y.update((()=>it))}catch(e){if(n(14,Ft=e.message),console.error("[31m%s[0m",`Error in ${I}: ${e.message}`),zr)throw Ft;m.update((e=>({...e,error:Ft})))}e.$$.dirty[0]},Al(Is,m),Al(Ps,y),[S,V,_,G,Me,Re,k,F,I,Te,Ae,_e,Ge,Rt,Ft,Mt,o,i,l,s,u,y,b,Ue,T,A,j,z,Z,P,Q,J,K,Y,ne,se,le,ie,oe,ce,pe,fe,he,$e,ge,be,ke,Ve,Ie,O,L,D,E,W,B,U,N,H,X,q,ee,te,ae,re,de,ue,me,ye,xe,ve,Ce,Se,Oe,Le,Ee,Fe,We,M,R,C,Ne,je,Bt,Ut,ze,Ze,Pe,Qe,Je,Ke,He,Xe,qe,Ye,et,tt,nt,st,lt,it,ot,at,rt,ct,dt,ut,pt,ft,ht,$t,mt,yt,bt,xt,vt,wt,Ct,St,kt,Tt,At,Lt,Dt,Vt,Nt,jt,zt,Zt,Gt,Wt,It,a,r,c,d,g,$]}class Nu extends Ae{constructor(e){super(),Se(this,e,Fu,Pu,me,{data:0,queryID:6,x:24,y:25,y2:49,series:50,size:51,tooltipTitle:52,showAllXAxisLabels:1,printEchartsConfig:2,swapXY:3,title:7,subtitle:53,chartType:8,bubble:54,hist:55,boxplot:56,xType:26,xAxisTitle:27,xBaseline:28,xTickMarks:29,xGridlines:30,xAxisLabels:31,sort:32,xFmt:57,xMin:58,xMax:59,yLog:33,yType:60,yLogBase:61,yAxisTitle:34,yBaseline:35,yTickMarks:36,yGridlines:37,yAxisLabels:38,yMin:62,yMax:63,yScale:39,yFmt:64,yAxisColor:65,y2AxisTitle:40,y2Baseline:41,y2TickMarks:42,y2Gridlines:43,y2AxisLabels:44,y2Min:66,y2Max:67,y2Scale:45,y2Fmt:68,y2AxisColor:69,sizeFmt:70,colorPalette:71,legend:46,echartsOptions:9,seriesOptions:10,seriesColors:72,stackType:73,stacked100:74,chartAreaHeight:47,renderer:11,downloadableData:4,downloadableImage:5,connectGroup:12,leftPadding:75,rightPadding:76,xLabelWrap:48},null,[-1,-1,-1,-1,-1])}}function Bu(e){let t;const n=e[7].default,s=he(n,e,e[8],null);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,l){s&&s.p&&(!t||256&l)&&_e(s,n,e,e[8],t?be(n,e[8],l,null):ge(e[8]),null)},i(e){t||(b(s,e),t=!0)},o(e){C(s,e),t=!1},d(e){s&&s.d(e)}}}function ju(e){let t,n;const s=[e[5],{data:Tl.isQuery(e[11])?Array.from(e[11]):e[11]},{queryID:e[6]}];let l={$$slots:{default:[Bu]},$$scope:{ctx:e}};for(let e=0;e<s.length;e+=1)l=U(l,s[e]);return t=new Nu({props:l}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,n){const l=2144&n?Ve(s,[32&n&&Tt(e[5]),2048&n&&{data:Tl.isQuery(e[11])?Array.from(e[11]):e[11]},64&n&&{queryID:e[6]}]):{};256&n&&(l.$$scope={dirty:n,ctx:e}),t.$set(l)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Vu(e){let t,n;return t=new qr({props:{slot:"empty",emptyMessage:e[2],emptySet:e[1],chartType:e[5].chartType,isInitial:e[4]}}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,n){const s={};4&n&&(s.emptyMessage=e[2]),2&n&&(s.emptySet=e[1]),32&n&&(s.chartType=e[5].chartType),16&n&&(s.isInitial=e[4]),t.$set(s)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function zu(e){let t,n;return t=new Fs({props:{slot:"error",title:e[5].chartType,error:e[11].error.message}}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,n){const s={};32&n&&(s.title=e[5].chartType),2048&n&&(s.error=e[11].error.message),t.$set(s)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Hu(e){let t,n;return t=new Hr({props:{data:e[0],height:e[3],$$slots:{error:[zu,({loaded:e})=>({11:e}),({loaded:e})=>e?2048:0],empty:[Vu],default:[ju,({loaded:e})=>({11:e}),({loaded:e})=>e?2048:0]},$$scope:{ctx:e}}}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,[n]){const s={};1&n&&(s.data=e[0]),8&n&&(s.height=e[3]),2358&n&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function qu(e,t,n){let s,{$$slots:l={},$$scope:i}=t,{data:o}=t;const a=Tl.isQuery(o)?o.hash:void 0;let r=(null==o?void 0:o.hash)===a,{emptySet:c}=t,{emptyMessage:d}=t,{height:u=200}=t,p=null==o?void 0:o.id;return e.$$set=e=>{n(10,t=U(U({},t),Re(e))),"data"in e&&n(0,o=e.data),"emptySet"in e&&n(1,c=e.emptySet),"emptyMessage"in e&&n(2,d=e.emptyMessage),"height"in e&&n(3,u=e.height),"$$scope"in e&&n(8,i=e.$$scope)},e.$$.update=()=>{1&e.$$.dirty&&n(4,r=(null==o?void 0:o.hash)===a),n(5,s={...Object.fromEntries(Object.entries(t).filter((([,e])=>void 0!==e)))})},t=Re(t),[o,c,d,u,r,s,p,l,i]}class Gu extends Ae{constructor(e){super(),Se(this,e,qu,Hu,me,{data:0,emptySet:1,emptyMessage:2,height:3})}}function Ru(e,t,n,s,l,i,o,a,r,c,d=void 0,u=void 0,p=void 0,f=void 0){function h(e,t,n,s){let l={name:t,data:e,yAxisIndex:n};return l={...s,...l},l}let $,g,m,y,b,x,v,w,C=[],S=function(e,t){const n=[];function s(e,t){(function(e){return typeof e>"u"})(e)||(Array.isArray(e)?e.forEach((e=>n.push([e,t]))):n.push([e,t]))}return s(e,0),s(t,1),n}(n,p);if(null!=s&&1===S.length)for(v=vl(e,s),$=0;$<v.length;$++){if(b=e.filter((e=>e[s]===v[$])),y=l?b.map((e=>[e[S[0][0]],a?e[t].toString():e[t]])):b.map((e=>[a?e[t].toString():e[t],e[S[0][0]]])),d){let e=b.map((e=>e[d]));y.forEach(((t,n)=>t.push(e[n])))}if(u){let e=b.map((e=>e[u]));y.forEach(((t,n)=>t.push(e[n])))}x=v[$]??"null",w=S[0][1],m=h(y,x,w,i),C.push(m)}if(null!=s&&S.length>1)for(v=vl(e,s),$=0;$<v.length;$++)for(b=e.filter((e=>e[s]===v[$])),g=0;g<S.length;g++){if(y=l?b.map((e=>[e[S[g][0]],a?e[t].toString():e[t]])):b.map((e=>[a?e[t].toString():e[t],e[S[g][0]]])),d){let e=b.map((e=>e[d]));y.forEach(((t,n)=>t.push(e[n])))}if(u){let e=b.map((e=>e[u]));y.forEach(((t,n)=>t.push(e[n])))}x=(v[$]??"null")+" - "+r[S[g][0]].title,w=S[g][1],m=h(y,x,w,i),C.push(m)}if(null==s&&S.length>1)for($=0;$<S.length;$++){if(y=l?e.map((e=>[e[S[$][0]],a?e[t].toString():e[t]])):e.map((e=>[a?e[t].toString():e[t],e[S[$][0]]])),d){let t=e.map((e=>e[d]));y.forEach(((e,n)=>e.push(t[n])))}if(u){let t=e.map((e=>e[u]));y.forEach(((e,n)=>e.push(t[n])))}x=r[S[$][0]].title,w=S[$][1],m=h(y,x,w,i),C.push(m)}if(null==s&&1===S.length){if(y=l?e.map((e=>[e[S[0][0]],a?e[t].toString():e[t]])):e.map((e=>[a?e[t].toString():e[t],e[S[0][0]]])),d){let t=e.map((e=>e[d]));y.forEach(((e,n)=>e.push(t[n])))}if(u){let t=e.map((e=>e[u]));y.forEach(((e,n)=>e.push(t[n])))}x=r[S[0][0]].title,w=S[0][1],m=h(y,x,w,i),C.push(m)}return c&&C.sort(((e,t)=>c.indexOf(e.name)-c.indexOf(t.name))),f&&C.forEach((e=>{e.name=Gr(e.name,f)})),C}function Wu(e){let t=[];for(let n=1;n<e.length;n++)t.push(e[n]-e[n-1]);return t}function Xs(e,t){return("number"!=typeof e||isNaN(e))&&(e=0),("number"!=typeof t||isNaN(t))&&(t=0),e=Math.abs(e),(t=Math.abs(t))<=.01?e:Xs(t,e%t)}function Xu(e,t){if(!Array.isArray(e))throw new TypeError("Cannot calculate extent of non-array value.");let n,s;for(const t of e)"number"==typeof t&&(void 0===n?t>=t&&(n=s=t):(n>t&&(n=t),s<t&&(s=t)));return[n,s]}function Uu(e,t){let[n,s]=Xu(e);const l=[];let i=n;for(;i<=s;)l.push(Math.round(1e8*(i+Number.EPSILON))/1e8),i+=t;return l}function Yu(e){if(e.length<=1)return;e.sort((function(e,t){return e-t}));let t=(e=Wu(e=e.map((function(e){return 1e8*e})))).reduce(((e,t)=>Xs(e,t)))/1e8;return t=Math.round(1e8*(t+Number.EPSILON))/1e8,t}function Fi(e,t,n,s,l=!1,i=!1){var o;let a=!1;const r=e.map((e=>Object.assign({},e,{[t]:e[t]instanceof Date?(a=!0,e[t].toISOString()):e[t]}))).filter((e=>void 0!==e[t]&&null!==e[t])),c=Array.from(r).reduce(((e,n)=>(n[t]instanceof Date&&(n[t]=n[t].toISOString(),a=!0),s?(e[n[s]??"null"]||(e[n[s]??"null"]=[]),e[n[s]??"null"].push(n)):(e.default||(e.default=[]),e.default.push(n)),e)),{}),d={};let u;const p=(null==(o=r.find((e=>e&&null!==e[t]&&void 0!==e[t])))?void 0:o[t])??null;switch(typeof p){case"object":throw null===p?new Error(`Column '${t}' is entirely null. Column must contain at least one non-null value.`):new Error("Unexpected object property, expected string, date, or number");case"number":if(u=vl(r,t),i){const e=Yu(u);d[t]=Uu(u,e)}break;case"string":u=vl(r,t),d[t]=u}const f=[];for(const e of Object.values(c)){const i=s?{[s]:null}:{};if(l)if(n instanceof Array)for(let e=0;e<n.length;e++)i[n[e]]=0;else i[n]=0;else if(n instanceof Array)for(let e=0;e<n.length;e++)i[n[e]]=null;else i[n]=null;s&&(d[s]=s);const o=[];0===Object.keys(d).length?o.push(Rn([t],i)):o.push(Rn(d,i)),f.push(Nt(e,...o))}return a?f.flat().map((e=>({...e,[t]:new Date(e[t])}))):f.flat()}function gs(e,t,n){let s=Nt(e,Bi(t,[Rr(n,ji)]));if("object"==typeof n)for(let e=0;e<s.length;e++){s[e].stackTotal=0;for(let t=0;t<n.length;t++)s[e].stackTotal=s[e].stackTotal+s[e][n[t]]}return s}let Qu=60;function Ku(e,t,n){let s,l,i,o,a,r,c,d,u,p,f,h,$,g,m,y,b,x,v,w,C,S,k,T=we,A=we,O=we,L=we;e.$$.on_destroy.push((()=>T())),e.$$.on_destroy.push((()=>A())),e.$$.on_destroy.push((()=>O())),e.$$.on_destroy.push((()=>L()));const{resolveColor:D}=El();let{y:E}=t;const V=!!E;let{y2:_}=t;const M=!!_;let{series:R}=t;const G=!!R;let F,{options:W}=t,{name:I}=t,{type:B="stacked"}=t,{stackName:U}=t,{fillColor:N}=t,{fillOpacity:j}=t,{outlineColor:z}=t,{outlineWidth:Z}=t,{labels:P=!1}=t,{seriesLabels:Q=!0}=t,{labelSize:J=11}=t,{labelPosition:K}=t,{labelColor:H}=t,{labelFmt:X}=t;X&&(F=Ot(X));let q,{yLabelFmt:Y}=t;Y&&(q=Ot(Y));let ee,{y2LabelFmt:te}=t;te&&(ee=Ot(te));let ne,se,le,ie,{y2SeriesType:oe="bar"}=t,{stackTotalLabel:ae=!0}=t,{showAllLabels:re=!1}=t,{seriesOrder:ce}=t;const de={outside:"top",inside:"inside"},ue={outside:"right",inside:"inside"};let{seriesLabelFmt:pe}=t;return Zs((()=>{W&&l.update((e=>({...e,...W}))),v&&l.update((e=>{if(B.includes("stacked")?e.tooltip={...e.tooltip,order:"seriesDesc"}:e.tooltip={...e.tooltip,order:"seriesAsc"},"stacked100"===B&&(h?e.xAxis={...e.xAxis,max:1}:e.yAxis[0]={...e.yAxis[0],max:1}),h)e.yAxis={...e.yAxis,...v.xAxis},e.xAxis={...e.xAxis,...v.yAxis};else if(e.yAxis[0]={...e.yAxis[0],...v.yAxis},e.xAxis={...e.xAxis,...v.xAxis},_&&(e.yAxis[1]={...e.yAxis[1],show:!0},["line","bar","scatter"].includes(oe)))for(let t=0;t<f;t++)e.series[p+t].type=oe,e.series[p+t].stack=void 0;return e}))})),e.$$set=e=>{"y"in e&&n(4,E=e.y),"y2"in e&&n(5,_=e.y2),"series"in e&&n(6,R=e.series),"options"in e&&n(13,W=e.options),"name"in e&&n(7,I=e.name),"type"in e&&n(14,B=e.type),"stackName"in e&&n(8,U=e.stackName),"fillColor"in e&&n(15,N=e.fillColor),"fillOpacity"in e&&n(16,j=e.fillOpacity),"outlineColor"in e&&n(17,z=e.outlineColor),"outlineWidth"in e&&n(18,Z=e.outlineWidth),"labels"in e&&n(9,P=e.labels),"seriesLabels"in e&&n(10,Q=e.seriesLabels),"labelSize"in e&&n(19,J=e.labelSize),"labelPosition"in e&&n(11,K=e.labelPosition),"labelColor"in e&&n(20,H=e.labelColor),"labelFmt"in e&&n(21,X=e.labelFmt),"yLabelFmt"in e&&n(22,Y=e.yLabelFmt),"y2LabelFmt"in e&&n(23,te=e.y2LabelFmt),"y2SeriesType"in e&&n(24,oe=e.y2SeriesType),"stackTotalLabel"in e&&n(12,ae=e.stackTotalLabel),"showAllLabels"in e&&n(25,re=e.showAllLabels),"seriesOrder"in e&&n(26,ce=e.seriesOrder),"seriesLabelFmt"in e&&n(27,pe=e.seriesLabelFmt)},e.$$.update=()=>{32768&e.$$.dirty[0]&&(n(2,i=D(N)),A(),A=gt(i,(e=>n(50,C=e)))),131072&e.$$.dirty[0]&&(n(1,o=D(z)),T(),T=gt(o,(e=>n(49,w=e)))),512&e.$$.dirty[0]&&n(9,P="true"===P||!0===P),1024&e.$$.dirty[0]&&n(10,Q="true"===Q||!0===Q),1048576&e.$$.dirty[0]&&(n(0,a=D(H)),O(),O=gt(a,(e=>n(51,S=e)))),4096&e.$$.dirty[0]&&n(12,ae="true"===ae||!0===ae),2097152&e.$$.dirty[1]&&n(46,r=k.data),2097152&e.$$.dirty[1]&&n(42,c=k.x),16&e.$$.dirty[0]|2097152&e.$$.dirty[1]&&n(4,E=V?E:k.y),32&e.$$.dirty[0]|2097152&e.$$.dirty[1]&&n(5,_=M?_:k.y2),2097152&e.$$.dirty[1]&&n(40,d=k.yFormat),2097152&e.$$.dirty[1]&&n(47,u=k.y2Format),2097152&e.$$.dirty[1]&&n(35,p=k.yCount),2097152&e.$$.dirty[1]&&n(36,f=k.y2Count),2097152&e.$$.dirty[1]&&n(37,h=k.swapXY),2097152&e.$$.dirty[1]&&n(39,$=k.xType),2097152&e.$$.dirty[1]&&n(43,g=k.xMismatch),2097152&e.$$.dirty[1]&&n(44,m=k.columnSummary),2097152&e.$$.dirty[1]&&n(48,y=k.sort),64&e.$$.dirty[0]|2097152&e.$$.dirty[1]&&n(6,R=G?R:k.series),16848&e.$$.dirty[0]|174403&e.$$.dirty[1]&&(R||"object"==typeof E?(!0===y&&"category"===$&&(n(31,ne=gs(r,c,E)),n(31,ne=wl(ne,"object"==typeof E?"stackTotal":E,!1)),n(32,se=ne.map((e=>e[c]))),n(46,r=[...r].sort((function(e,t){return se.indexOf(e[c])-se.indexOf(t[c])})))),h||("value"===$||"category"===$)&&B.includes("stacked")?(n(46,r=Fi(r,c,E,R,!0,"value"===$)),n(39,$="category")):"time"===$&&B.includes("stacked")&&n(46,r=Fi(r,c,E,R,!0,!0)),B.includes("stacked")?(n(8,U=U??"stack1"),n(33,le="inside")):(n(8,U=void 0),n(33,le=h?"right":"top"))):(n(7,I=I??_t(E,m[E].title)),h&&"category"!==$&&(n(46,r=Fi(r,c,E,R,!0,"time"!==$)),n(39,$="category")),n(8,U="stack1"),n(33,le=h?"right":"top"))),16400&e.$$.dirty[0]|34816&e.$$.dirty[1]&&"stacked"===B&&n(34,ie=gs(r,c,E)),2048&e.$$.dirty[0]|68&e.$$.dirty[1]&&n(11,K=(h?ue[K]:de[K])??le),1913458432&e.$$.dirty[0]|1901168&e.$$.dirty[1]&&n(45,b={type:"bar",stack:U,label:{show:P&&Q,formatter:e=>0===e.value[h?0:1]?"":Et(e.value[h?0:1],[q??F??d,ee??F??u][Ws(e.componentIndex,p,f)]),position:K,fontSize:J,color:S},labelLayout:{hideOverlap:!re},emphasis:{focus:"series"},barMaxWidth:Qu,itemStyle:{color:C,opacity:j,borderColor:w,borderWidth:Z}}),201326832&e.$$.dirty[0]|63552&e.$$.dirty[1]&&n(41,x=Ru(r,c,E,R,h,b,I,g,m,ce,void 0,void 0,_,pe)),268981072&e.$$.dirty[0]|7880&e.$$.dirty[1]&&l.update((e=>(e.series.push(...x),e.legend.data.push(...x.map((e=>e.name.toString()))),!0===P&&"stacked"===B&&"object"==typeof E|void 0!==R&&!0===ae&&R!==c&&(e.series.push({type:"bar",stack:U,name:"stackTotal",color:"none",data:ie.map((e=>[h?0:g?e[c].toString():e[c],h?g?e[c].toString():e[c]:0])),label:{show:!0,position:h?"right":"top",formatter(e){let t=0;return x.forEach((n=>{t+=n.data[e.dataIndex][h?0:1]})),0===t?"":Et(t,F??d)},fontWeight:"bold",fontSize:J,padding:h?[0,0,0,5]:void 0}}),e.legend.selectedMode=!1),e))),256&e.$$.dirty[1]&&(v={xAxis:{boundaryGap:["1%","2%"],type:$}})},n(3,s=Sl(Is)),L(),L=gt(s,(e=>n(52,k=e))),n(38,l=Sl(Ps)),[a,o,i,s,E,_,R,I,U,P,Q,K,ae,W,B,N,j,z,Z,J,H,X,Y,te,oe,re,ce,pe,F,q,ee,ne,se,le,ie,p,f,h,l,$,d,x,c,g,m,b,r,u,y,w,C,S,k]}class Ju extends Ae{constructor(e){super(),Se(this,e,Ku,null,me,{y:4,y2:5,series:6,options:13,name:7,type:14,stackName:8,fillColor:15,fillOpacity:16,outlineColor:17,outlineWidth:18,labels:9,seriesLabels:10,labelSize:19,labelPosition:11,labelColor:20,labelFmt:21,yLabelFmt:22,y2LabelFmt:23,y2SeriesType:24,stackTotalLabel:12,showAllLabels:25,seriesOrder:26,seriesLabelFmt:27},null,[-1,-1])}}function Zu(e){let t,n,s;t=new Ju({props:{type:e[38],fillColor:e[72],fillOpacity:e[39],outlineColor:e[71],outlineWidth:e[40],labels:e[43],labelSize:e[44],labelPosition:e[45],labelColor:e[69],labelFmt:e[46],yLabelFmt:e[47],y2LabelFmt:e[48],stackTotalLabel:e[49],seriesLabels:e[50],showAllLabels:e[51],y2SeriesType:e[9],seriesOrder:e[60],seriesLabelFmt:e[62]}});const l=e[81].default,i=he(l,e,e[82],null);return{c(){$(t.$$.fragment),n=K(),i&&i.c()},l(e){x(t.$$.fragment,e),n=Q(e),i&&i.l(e)},m(e,l){Z(t,e,l),S(e,n,l),i&&i.m(e,l),s=!0},p(e,n){const o={};128&n[1]&&(o.type=e[38]),1024&n[2]&&(o.fillColor=e[72]),256&n[1]&&(o.fillOpacity=e[39]),512&n[2]&&(o.outlineColor=e[71]),512&n[1]&&(o.outlineWidth=e[40]),4096&n[1]&&(o.labels=e[43]),8192&n[1]&&(o.labelSize=e[44]),16384&n[1]&&(o.labelPosition=e[45]),128&n[2]&&(o.labelColor=e[69]),32768&n[1]&&(o.labelFmt=e[46]),65536&n[1]&&(o.yLabelFmt=e[47]),131072&n[1]&&(o.y2LabelFmt=e[48]),262144&n[1]&&(o.stackTotalLabel=e[49]),524288&n[1]&&(o.seriesLabels=e[50]),1048576&n[1]&&(o.showAllLabels=e[51]),512&n[0]&&(o.y2SeriesType=e[9]),536870912&n[1]&&(o.seriesOrder=e[60]),1&n[2]&&(o.seriesLabelFmt=e[62]),t.$set(o),i&&i.p&&(!s||1048576&n[2])&&_e(i,l,e,e[82],s?be(l,e[82],n,null):ge(e[82]),null)},i(e){s||(b(t.$$.fragment,e),b(i,e),s=!0)},o(e){C(t.$$.fragment,e),C(i,e),s=!1},d(e){e&&g(n),J(t,e),i&&i.d(e)}}}function xu(e){let t,n;return t=new Gu({props:{data:e[1],x:e[2],y:e[3],y2:e[4],xFmt:e[12],yFmt:e[10],y2Fmt:e[11],series:e[5],xType:e[6],yLog:e[7],yLogBase:e[8],legend:e[15],xAxisTitle:e[16],yAxisTitle:e[17],y2AxisTitle:e[18],xGridlines:e[19],yGridlines:e[20],y2Gridlines:e[21],xAxisLabels:e[22],yAxisLabels:e[23],y2AxisLabels:e[24],xBaseline:e[25],yBaseline:e[26],y2Baseline:e[27],xTickMarks:e[28],yTickMarks:e[29],y2TickMarks:e[30],yAxisColor:e[68],y2AxisColor:e[67],yMin:e[31],yMax:e[32],yScale:e[33],y2Min:e[34],y2Max:e[35],y2Scale:e[36],swapXY:e[0],title:e[13],subtitle:e[14],chartType:"Bar Chart",stackType:e[38],sort:e[42],stacked100:e[73],chartAreaHeight:e[41],showAllXAxisLabels:e[37],colorPalette:e[70],echartsOptions:e[52],seriesOptions:e[53],printEchartsConfig:e[54],emptySet:e[55],emptyMessage:e[56],renderer:e[57],downloadableData:e[58],downloadableImage:e[59],connectGroup:e[61],xLabelWrap:e[65],seriesColors:e[66],leftPadding:e[63],rightPadding:e[64],$$slots:{default:[Zu]},$$scope:{ctx:e}}}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,n){const s={};2&n[0]&&(s.data=e[1]),4&n[0]&&(s.x=e[2]),8&n[0]&&(s.y=e[3]),16&n[0]&&(s.y2=e[4]),4096&n[0]&&(s.xFmt=e[12]),1024&n[0]&&(s.yFmt=e[10]),2048&n[0]&&(s.y2Fmt=e[11]),32&n[0]&&(s.series=e[5]),64&n[0]&&(s.xType=e[6]),128&n[0]&&(s.yLog=e[7]),256&n[0]&&(s.yLogBase=e[8]),32768&n[0]&&(s.legend=e[15]),65536&n[0]&&(s.xAxisTitle=e[16]),131072&n[0]&&(s.yAxisTitle=e[17]),262144&n[0]&&(s.y2AxisTitle=e[18]),524288&n[0]&&(s.xGridlines=e[19]),1048576&n[0]&&(s.yGridlines=e[20]),2097152&n[0]&&(s.y2Gridlines=e[21]),4194304&n[0]&&(s.xAxisLabels=e[22]),8388608&n[0]&&(s.yAxisLabels=e[23]),16777216&n[0]&&(s.y2AxisLabels=e[24]),33554432&n[0]&&(s.xBaseline=e[25]),67108864&n[0]&&(s.yBaseline=e[26]),134217728&n[0]&&(s.y2Baseline=e[27]),268435456&n[0]&&(s.xTickMarks=e[28]),536870912&n[0]&&(s.yTickMarks=e[29]),1073741824&n[0]&&(s.y2TickMarks=e[30]),64&n[2]&&(s.yAxisColor=e[68]),32&n[2]&&(s.y2AxisColor=e[67]),1&n[1]&&(s.yMin=e[31]),2&n[1]&&(s.yMax=e[32]),4&n[1]&&(s.yScale=e[33]),8&n[1]&&(s.y2Min=e[34]),16&n[1]&&(s.y2Max=e[35]),32&n[1]&&(s.y2Scale=e[36]),1&n[0]&&(s.swapXY=e[0]),8192&n[0]&&(s.title=e[13]),16384&n[0]&&(s.subtitle=e[14]),128&n[1]&&(s.stackType=e[38]),2048&n[1]&&(s.sort=e[42]),1024&n[1]&&(s.chartAreaHeight=e[41]),64&n[1]&&(s.showAllXAxisLabels=e[37]),256&n[2]&&(s.colorPalette=e[70]),2097152&n[1]&&(s.echartsOptions=e[52]),4194304&n[1]&&(s.seriesOptions=e[53]),8388608&n[1]&&(s.printEchartsConfig=e[54]),16777216&n[1]&&(s.emptySet=e[55]),33554432&n[1]&&(s.emptyMessage=e[56]),67108864&n[1]&&(s.renderer=e[57]),134217728&n[1]&&(s.downloadableData=e[58]),268435456&n[1]&&(s.downloadableImage=e[59]),1073741824&n[1]&&(s.connectGroup=e[61]),8&n[2]&&(s.xLabelWrap=e[65]),16&n[2]&&(s.seriesColors=e[66]),2&n[2]&&(s.leftPadding=e[63]),4&n[2]&&(s.rightPadding=e[64]),512&n[0]|538964864&n[1]|1050241&n[2]&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function $u(e,t,n){let s,l,i,o,a,r,c,{$$slots:d={},$$scope:u}=t;const{resolveColor:p,resolveColorsObject:f,resolveColorPalette:h}=El();let{data:$}=t,{x:g}=t,{y:m}=t,{y2:y}=t,{series:b}=t,{xType:x}=t,{yLog:v}=t,{yLogBase:w}=t,{y2SeriesType:C}=t,{yFmt:S}=t,{y2Fmt:k}=t,{xFmt:T}=t,{title:A}=t,{subtitle:O}=t,{legend:L}=t,{xAxisTitle:D}=t,{yAxisTitle:E=(y?"true":void 0)}=t,{y2AxisTitle:V=(y?"true":void 0)}=t,{xGridlines:_}=t,{yGridlines:M}=t,{y2Gridlines:R}=t,{xAxisLabels:G}=t,{yAxisLabels:F}=t,{y2AxisLabels:W}=t,{xBaseline:I}=t,{yBaseline:B}=t,{y2Baseline:U}=t,{xTickMarks:N}=t,{yTickMarks:j}=t,{y2TickMarks:z}=t,{yMin:Z}=t,{yMax:P}=t,{yScale:Q}=t,{y2Min:J}=t,{y2Max:K}=t,{y2Scale:H}=t,{swapXY:X=!1}=t,{showAllXAxisLabels:q}=t,{type:Y="stacked"}=t,ee="stacked100"===Y,{fillColor:te}=t,{fillOpacity:ne}=t,{outlineColor:se}=t,{outlineWidth:le}=t,{chartAreaHeight:ie}=t,{sort:oe}=t,{colorPalette:ae="default"}=t,{labels:re}=t,{labelSize:ce}=t,{labelPosition:de}=t,{labelColor:ue}=t,{labelFmt:pe}=t,{yLabelFmt:fe}=t,{y2LabelFmt:he}=t,{stackTotalLabel:$e}=t,{seriesLabels:ge}=t,{showAllLabels:me}=t,{yAxisColor:ye}=t,{y2AxisColor:be}=t,{echartsOptions:xe}=t,{seriesOptions:ve}=t,{printEchartsConfig:we=!1}=t,{emptySet:Ce}=t,{emptyMessage:Se}=t,{renderer:ke}=t,{downloadableData:Te}=t,{downloadableImage:Ae}=t,{seriesColors:Oe}=t,{seriesOrder:Le}=t,{connectGroup:De}=t,{seriesLabelFmt:Ee}=t,{leftPadding:Ve}=t,{rightPadding:_e}=t,{xLabelWrap:Me}=t;return e.$$set=e=>{"data"in e&&n(1,$=e.data),"x"in e&&n(2,g=e.x),"y"in e&&n(3,m=e.y),"y2"in e&&n(4,y=e.y2),"series"in e&&n(5,b=e.series),"xType"in e&&n(6,x=e.xType),"yLog"in e&&n(7,v=e.yLog),"yLogBase"in e&&n(8,w=e.yLogBase),"y2SeriesType"in e&&n(9,C=e.y2SeriesType),"yFmt"in e&&n(10,S=e.yFmt),"y2Fmt"in e&&n(11,k=e.y2Fmt),"xFmt"in e&&n(12,T=e.xFmt),"title"in e&&n(13,A=e.title),"subtitle"in e&&n(14,O=e.subtitle),"legend"in e&&n(15,L=e.legend),"xAxisTitle"in e&&n(16,D=e.xAxisTitle),"yAxisTitle"in e&&n(17,E=e.yAxisTitle),"y2AxisTitle"in e&&n(18,V=e.y2AxisTitle),"xGridlines"in e&&n(19,_=e.xGridlines),"yGridlines"in e&&n(20,M=e.yGridlines),"y2Gridlines"in e&&n(21,R=e.y2Gridlines),"xAxisLabels"in e&&n(22,G=e.xAxisLabels),"yAxisLabels"in e&&n(23,F=e.yAxisLabels),"y2AxisLabels"in e&&n(24,W=e.y2AxisLabels),"xBaseline"in e&&n(25,I=e.xBaseline),"yBaseline"in e&&n(26,B=e.yBaseline),"y2Baseline"in e&&n(27,U=e.y2Baseline),"xTickMarks"in e&&n(28,N=e.xTickMarks),"yTickMarks"in e&&n(29,j=e.yTickMarks),"y2TickMarks"in e&&n(30,z=e.y2TickMarks),"yMin"in e&&n(31,Z=e.yMin),"yMax"in e&&n(32,P=e.yMax),"yScale"in e&&n(33,Q=e.yScale),"y2Min"in e&&n(34,J=e.y2Min),"y2Max"in e&&n(35,K=e.y2Max),"y2Scale"in e&&n(36,H=e.y2Scale),"swapXY"in e&&n(0,X=e.swapXY),"showAllXAxisLabels"in e&&n(37,q=e.showAllXAxisLabels),"type"in e&&n(38,Y=e.type),"fillColor"in e&&n(74,te=e.fillColor),"fillOpacity"in e&&n(39,ne=e.fillOpacity),"outlineColor"in e&&n(75,se=e.outlineColor),"outlineWidth"in e&&n(40,le=e.outlineWidth),"chartAreaHeight"in e&&n(41,ie=e.chartAreaHeight),"sort"in e&&n(42,oe=e.sort),"colorPalette"in e&&n(76,ae=e.colorPalette),"labels"in e&&n(43,re=e.labels),"labelSize"in e&&n(44,ce=e.labelSize),"labelPosition"in e&&n(45,de=e.labelPosition),"labelColor"in e&&n(77,ue=e.labelColor),"labelFmt"in e&&n(46,pe=e.labelFmt),"yLabelFmt"in e&&n(47,fe=e.yLabelFmt),"y2LabelFmt"in e&&n(48,he=e.y2LabelFmt),"stackTotalLabel"in e&&n(49,$e=e.stackTotalLabel),"seriesLabels"in e&&n(50,ge=e.seriesLabels),"showAllLabels"in e&&n(51,me=e.showAllLabels),"yAxisColor"in e&&n(78,ye=e.yAxisColor),"y2AxisColor"in e&&n(79,be=e.y2AxisColor),"echartsOptions"in e&&n(52,xe=e.echartsOptions),"seriesOptions"in e&&n(53,ve=e.seriesOptions),"printEchartsConfig"in e&&n(54,we=e.printEchartsConfig),"emptySet"in e&&n(55,Ce=e.emptySet),"emptyMessage"in e&&n(56,Se=e.emptyMessage),"renderer"in e&&n(57,ke=e.renderer),"downloadableData"in e&&n(58,Te=e.downloadableData),"downloadableImage"in e&&n(59,Ae=e.downloadableImage),"seriesColors"in e&&n(80,Oe=e.seriesColors),"seriesOrder"in e&&n(60,Le=e.seriesOrder),"connectGroup"in e&&n(61,De=e.connectGroup),"seriesLabelFmt"in e&&n(62,Ee=e.seriesLabelFmt),"leftPadding"in e&&n(63,Ve=e.leftPadding),"rightPadding"in e&&n(64,_e=e.rightPadding),"xLabelWrap"in e&&n(65,Me=e.xLabelWrap),"$$scope"in e&&n(82,u=e.$$scope)},e.$$.update=()=>{1&e.$$.dirty[0]&&n(0,X="true"===X||!0===X),4096&e.$$.dirty[2]&&n(72,s=p(te)),8192&e.$$.dirty[2]&&n(71,l=p(se)),16384&e.$$.dirty[2]&&n(70,i=h(ae)),32768&e.$$.dirty[2]&&n(69,o=p(ue)),65536&e.$$.dirty[2]&&n(68,a=p(ye)),131072&e.$$.dirty[2]&&n(67,r=p(be)),262144&e.$$.dirty[2]&&n(66,c=f(Oe))},[X,$,g,m,y,b,x,v,w,C,S,k,T,A,O,L,D,E,V,_,M,R,G,F,W,I,B,U,N,j,z,Z,P,Q,J,K,H,q,Y,ne,le,ie,oe,re,ce,de,pe,fe,he,$e,ge,me,xe,ve,we,Ce,Se,ke,Te,Ae,Le,De,Ee,Ve,_e,Me,c,r,a,o,i,l,s,ee,te,se,ae,ue,ye,be,Oe,d,u]}class ec extends Ae{constructor(e){super(),Se(this,e,$u,xu,me,{data:1,x:2,y:3,y2:4,series:5,xType:6,yLog:7,yLogBase:8,y2SeriesType:9,yFmt:10,y2Fmt:11,xFmt:12,title:13,subtitle:14,legend:15,xAxisTitle:16,yAxisTitle:17,y2AxisTitle:18,xGridlines:19,yGridlines:20,y2Gridlines:21,xAxisLabels:22,yAxisLabels:23,y2AxisLabels:24,xBaseline:25,yBaseline:26,y2Baseline:27,xTickMarks:28,yTickMarks:29,y2TickMarks:30,yMin:31,yMax:32,yScale:33,y2Min:34,y2Max:35,y2Scale:36,swapXY:0,showAllXAxisLabels:37,type:38,fillColor:74,fillOpacity:39,outlineColor:75,outlineWidth:40,chartAreaHeight:41,sort:42,colorPalette:76,labels:43,labelSize:44,labelPosition:45,labelColor:77,labelFmt:46,yLabelFmt:47,y2LabelFmt:48,stackTotalLabel:49,seriesLabels:50,showAllLabels:51,yAxisColor:78,y2AxisColor:79,echartsOptions:52,seriesOptions:53,printEchartsConfig:54,emptySet:55,emptyMessage:56,renderer:57,downloadableData:58,downloadableImage:59,seriesColors:80,seriesOrder:60,connectGroup:61,seriesLabelFmt:62,leftPadding:63,rightPadding:64,xLabelWrap:65},null,[-1,-1,-1])}}function tc(e){let t,n,s=Te.title+"";return{c(){t=R("h1"),n=Ye(s),this.h()},l(e){t=G(e,"H1",{class:!0});var l=le(t);n=Ue(l,s),l.forEach(g),this.h()},h(){V(t,"class","title")},m(e,s){S(e,t,s),ke(t,n)},p:we,d(e){e&&g(t)}}}function lc(e){return{c(){this.h()},l(e){this.h()},h(){document.title="Evidence"},m:we,p:we,d:we}}function ic(e){let t,n,s,l,i;return document.title=t=Te.title,{c(){n=K(),s=R("meta"),l=K(),i=R("meta"),this.h()},l(e){n=Q(e),s=G(e,"META",{property:!0,content:!0}),l=Q(e),i=G(e,"META",{name:!0,content:!0}),this.h()},h(){var e,t;V(s,"property","og:title"),V(s,"content",(null==(e=Te.og)?void 0:e.title)??Te.title),V(i,"name","twitter:title"),V(i,"content",(null==(t=Te.og)?void 0:t.title)??Te.title)},m(e,t){S(e,n,t),S(e,s,t),S(e,l,t),S(e,i,t)},p(e,n){0&n&&t!==(t=Te.title)&&(document.title=t)},d(e){e&&(g(n),g(s),g(l),g(i))}}}function nc(e){var t,n;let s,l,i=(Te.description||(null==(t=Te.og)?void 0:t.description))&&sc(),o=(null==(n=Te.og)?void 0:n.image)&&rc();return{c(){i&&i.c(),s=K(),o&&o.c(),l=te()},l(e){i&&i.l(e),s=Q(e),o&&o.l(e),l=te()},m(e,t){i&&i.m(e,t),S(e,s,t),o&&o.m(e,t),S(e,l,t)},p(e,t){var n,s;(Te.description||null!=(n=Te.og)&&n.description)&&i.p(e,t),null!=(s=Te.og)&&s.image&&o.p(e,t)},d(e){e&&(g(s),g(l)),i&&i.d(e),o&&o.d(e)}}}function sc(e){let t,n,s,l,i;return{c(){t=R("meta"),n=K(),s=R("meta"),l=K(),i=R("meta"),this.h()},l(e){t=G(e,"META",{name:!0,content:!0}),n=Q(e),s=G(e,"META",{property:!0,content:!0}),l=Q(e),i=G(e,"META",{name:!0,content:!0}),this.h()},h(){var e,n,l;V(t,"name","description"),V(t,"content",Te.description??(null==(e=Te.og)?void 0:e.description)),V(s,"property","og:description"),V(s,"content",(null==(n=Te.og)?void 0:n.description)??Te.description),V(i,"name","twitter:description"),V(i,"content",(null==(l=Te.og)?void 0:l.description)??Te.description)},m(e,o){S(e,t,o),S(e,n,o),S(e,s,o),S(e,l,o),S(e,i,o)},p:we,d(e){e&&(g(t),g(n),g(s),g(l),g(i))}}}function rc(e){let t,n,s;return{c(){t=R("meta"),n=K(),s=R("meta"),this.h()},l(e){t=G(e,"META",{property:!0,content:!0}),n=Q(e),s=G(e,"META",{name:!0,content:!0}),this.h()},h(){var e,n;V(t,"property","og:image"),V(t,"content",mn(null==(e=Te.og)?void 0:e.image)),V(s,"name","twitter:image"),V(s,"content",mn(null==(n=Te.og)?void 0:n.image))},m(e,l){S(e,t,l),S(e,n,l),S(e,s,l)},p:we,d(e){e&&(g(t),g(n),g(s))}}}function oc(e){let t,n='<h3 class="svelte-styow2">🛠️ Development Mode</h3> <p>This is a preview of your Evidence dashboard. Deploy to Domo DDX to access real datasets.</p>';return{c(){t=R("div"),t.innerHTML=n,this.h()},l(e){t=G(e,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-1b532x0"!==Ct(t)&&(t.innerHTML=n),this.h()},h(){V(t,"class","dev-banner svelte-styow2")},m(e,n){S(e,t,n)},d(e){e&&g(t)}}}function fc(e){let t,n='<h3 class="svelte-styow2">🚀 Running in Domo DDX Environment</h3> <p>This Evidence dashboard is connected to your Domo instance and ready to analyze your data!</p>';return{c(){t=R("div"),t.innerHTML=n,this.h()},l(e){t=G(e,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-1uzwljv"!==Ct(t)&&(t.innerHTML=n),this.h()},h(){V(t,"class","domo-banner svelte-styow2")},m(e,n){S(e,t,n)},d(e){e&&g(t)}}}function ac(e){let t;return{c(){t=Ye("This example shows how Evidence creates interactive dashboards from SQL queries. Once you load your Domo datasets, you can create similar analyses with your own data.")},l(e){t=Ue(e,"This example shows how Evidence creates interactive dashboards from SQL queries. Once you load your Domo datasets, you can create similar analyses with your own data.")},m(e,n){S(e,t,n)},d(e){e&&g(t)}}}function bs(e){let t,n;return t=new Ns({props:{queryID:"categories",queryResult:e[1]}}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,n){const s={};2&n&&(s.queryResult=e[1]),t.$set(s)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function uc(e){let t,n;return t=new ll({props:{value:"%",valueLabel:"All Categories"}}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p:we,i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function cc(e){let t,n,s,l,i,o,a,r;return t=new ll({props:{value:"%",valueLabel:"All Years"}}),s=new ll({props:{value:"2019"}}),i=new ll({props:{value:"2020"}}),a=new ll({props:{value:"2021"}}),{c(){$(t.$$.fragment),n=K(),$(s.$$.fragment),l=K(),$(i.$$.fragment),o=K(),$(a.$$.fragment)},l(e){x(t.$$.fragment,e),n=Q(e),x(s.$$.fragment,e),l=Q(e),x(i.$$.fragment,e),o=Q(e),x(a.$$.fragment,e)},m(e,c){Z(t,e,c),S(e,n,c),Z(s,e,c),S(e,l,c),Z(i,e,c),S(e,o,c),Z(a,e,c),r=!0},p:we,i(e){r||(b(t.$$.fragment,e),b(s.$$.fragment,e),b(i.$$.fragment,e),b(a.$$.fragment,e),r=!0)},o(e){C(t.$$.fragment,e),C(s.$$.fragment,e),C(i.$$.fragment,e),C(a.$$.fragment,e),r=!1},d(e){e&&(g(n),g(l),g(o)),J(t,e),J(s,e),J(i,e),J(a,e)}}}function ys(e){let t,n;return t=new Ns({props:{queryID:"orders_by_category",queryResult:e[2]}}),{c(){$(t.$$.fragment)},l(e){x(t.$$.fragment,e)},m(e,s){Z(t,e,s),n=!0},p(e,n){const s={};4&n&&(s.queryResult=e[2]),t.$set(s)},i(e){n||(b(t.$$.fragment,e),n=!0)},o(e){C(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function dc(e){let t,n,s,l,i,o,a,r,c,d,u,p,f,h,m,y,v,w,k,T,A,O,L,D,E,_,M,F,W,I,B,U,N='<a href="#evidence-dashboard-for-domo-ddx">Evidence Dashboard for Domo DDX</a>',j="Welcome to your Evidence dashboard! This application combines the power of Evidence's analytics framework with Domo's data platform, allowing you to create interactive dashboards and reports using your Domo datasets.",z='<a href="#quick-start">Quick Start</a>',P='<div class="quick-start-card svelte-styow2"><h4 class="svelte-styow2">📊 Load Domo Data</h4> <p class="svelte-styow2">Use the workflow picker to select and load Domo datasets into DuckDB for analysis.</p> <a href="/workflow" class="btn-primary svelte-styow2">Open Workflow Picker</a></div> <div class="quick-start-card svelte-styow2"><h4 class="svelte-styow2">📈 View Sample Analysis</h4> <p class="svelte-styow2">See how Evidence works with the sample data below.</p> <a href="#sample-analysis" class="btn-secondary svelte-styow2">View Sample</a></div> <div class="quick-start-card svelte-styow2"><h4 class="svelte-styow2">📚 Learn More</h4> <p class="svelte-styow2">Explore Evidence documentation and best practices.</p> <a href="https://docs.evidence.dev" target="_blank" class="btn-secondary svelte-styow2">Documentation</a></div>',H='<a href="#sample-analysis">Sample Analysis</a>',X='<a href="#whats-next">What&#39;s Next?</a>',q='<div class="step svelte-styow2"><h4 class="svelte-styow2">1. Load Your Data</h4> <p class="svelte-styow2">Use the <a href="/workflow" class="svelte-styow2">workflow picker</a> to select and load Domo datasets into DuckDB</p></div> <div class="step svelte-styow2"><h4 class="svelte-styow2">2. Create Queries</h4> <p class="svelte-styow2">Write SQL queries against your loaded data using Evidence&#39;s query blocks</p></div> <div class="step svelte-styow2"><h4 class="svelte-styow2">3. Build Visualizations</h4> <p class="svelte-styow2">Use Evidence components like BarChart, LineChart, and DataTable to create interactive dashboards</p></div> <div class="step svelte-styow2"><h4 class="svelte-styow2">4. Deploy to Domo</h4> <p class="svelte-styow2">Package your Evidence app and deploy it to Domo DDX for your team to use</p></div>',Y=typeof Te<"u"&&Te.title&&!0!==Te.hide_title&&tc(),ee=(typeof Te<"u"&&Te.title?ic:lc)(e),ne="object"==typeof Te&&nc();function se(e,t){return e[3]?fc:oc}let ie=se(e),oe=ie(e);k=new ou({props:{title:"How Evidence Works with Your Data",$$slots:{default:[ac]},$$scope:{ctx:e}}});let re=e[1]&&bs(e);O=new fs({props:{data:e[1],name:"category",value:"category",$$slots:{default:[uc]},$$scope:{ctx:e}}}),D=new fs({props:{name:"year",$$slots:{default:[cc]},$$scope:{ctx:e}}});let ce=e[2]&&ys(e);return M=new ec({props:{data:e[2],title:"Sales by Month, "+e[0].category.label,x:"month",y:"sales_usd",series:"category"}}),{c(){Y&&Y.c(),t=K(),ee.c(),n=R("meta"),s=R("meta"),ne&&ne.c(),l=te(),i=K(),o=R("h1"),o.innerHTML=N,a=K(),oe.c(),r=K(),c=R("p"),c.textContent=j,d=K(),u=R("h2"),u.innerHTML=z,p=K(),f=R("div"),f.innerHTML=P,h=K(),m=R("h2"),m.innerHTML=H,y=K(),v=R("div"),w=K(),$(k.$$.fragment),T=K(),re&&re.c(),A=K(),$(O.$$.fragment),L=K(),$(D.$$.fragment),E=K(),ce&&ce.c(),_=K(),$(M.$$.fragment),F=K(),W=R("h2"),W.innerHTML=X,I=K(),B=R("div"),B.innerHTML=q,this.h()},l(e){Y&&Y.l(e),t=Q(e);const $=xs("svelte-2igo1p",document.head);ee.l($),n=G($,"META",{name:!0,content:!0}),s=G($,"META",{name:!0,content:!0}),ne&&ne.l($),l=te(),$.forEach(g),i=Q(e),o=G(e,"H1",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-dwxo2v"!==Ct(o)&&(o.innerHTML=N),a=Q(e),oe.l(e),r=Q(e),c=G(e,"P",{class:!0,"data-svelte-h":!0}),"svelte-17kjwqn"!==Ct(c)&&(c.textContent=j),d=Q(e),u=G(e,"H2",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-e7fbvj"!==Ct(u)&&(u.innerHTML=z),p=Q(e),f=G(e,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-1ynrehd"!==Ct(f)&&(f.innerHTML=P),h=Q(e),m=G(e,"H2",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-1941s2m"!==Ct(m)&&(m.innerHTML=H),y=Q(e),v=G(e,"DIV",{id:!0}),le(v).forEach(g),w=Q(e),x(k.$$.fragment,e),T=Q(e),re&&re.l(e),A=Q(e),x(O.$$.fragment,e),L=Q(e),x(D.$$.fragment,e),E=Q(e),ce&&ce.l(e),_=Q(e),x(M.$$.fragment,e),F=Q(e),W=G(e,"H2",{class:!0,id:!0,"data-svelte-h":!0}),"svelte-fy128a"!==Ct(W)&&(W.innerHTML=X),I=Q(e),B=G(e,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-zkky8f"!==Ct(B)&&(B.innerHTML=q),this.h()},h(){V(n,"name","twitter:card"),V(n,"content","summary_large_image"),V(s,"name","twitter:site"),V(s,"content","@evidence_dev"),V(o,"class","markdown"),V(o,"id","evidence-dashboard-for-domo-ddx"),V(c,"class","markdown"),V(u,"class","markdown"),V(u,"id","quick-start"),V(f,"class","quick-start-grid svelte-styow2"),V(m,"class","markdown"),V(m,"id","sample-analysis"),V(v,"id","sample-analysis"),V(W,"class","markdown"),V(W,"id","whats-next"),V(B,"class","next-steps svelte-styow2")},m(e,$){Y&&Y.m(e,$),S(e,t,$),ee.m(document.head,null),ke(document.head,n),ke(document.head,s),ne&&ne.m(document.head,null),ke(document.head,l),S(e,i,$),S(e,o,$),S(e,a,$),oe.m(e,$),S(e,r,$),S(e,c,$),S(e,d,$),S(e,u,$),S(e,p,$),S(e,f,$),S(e,h,$),S(e,m,$),S(e,y,$),S(e,v,$),S(e,w,$),Z(k,e,$),S(e,T,$),re&&re.m(e,$),S(e,A,$),Z(O,e,$),S(e,L,$),Z(D,e,$),S(e,E,$),ce&&ce.m(e,$),S(e,_,$),Z(M,e,$),S(e,F,$),S(e,W,$),S(e,I,$),S(e,B,$),U=!0},p(e,[t]){typeof Te<"u"&&Te.title&&!0!==Te.hide_title&&Y.p(e,t),ee.p(e,t),"object"==typeof Te&&ne.p(e,t),ie!==(ie=se(e))&&(oe.d(1),oe=ie(e),oe&&(oe.c(),oe.m(r.parentNode,r)));const n={};33554432&t&&(n.$$scope={dirty:t,ctx:e}),k.$set(n),e[1]?re?(re.p(e,t),2&t&&b(re,1)):(re=bs(e),re.c(),b(re,1),re.m(A.parentNode,A)):re&&(fe(),C(re,1,1,(()=>{re=null})),ae());const s={};2&t&&(s.data=e[1]),33554432&t&&(s.$$scope={dirty:t,ctx:e}),O.$set(s);const l={};33554432&t&&(l.$$scope={dirty:t,ctx:e}),D.$set(l),e[2]?ce?(ce.p(e,t),4&t&&b(ce,1)):(ce=ys(e),ce.c(),b(ce,1),ce.m(_.parentNode,_)):ce&&(fe(),C(ce,1,1,(()=>{ce=null})),ae());const i={};4&t&&(i.data=e[2]),1&t&&(i.title="Sales by Month, "+e[0].category.label),M.$set(i)},i(e){U||(b(k.$$.fragment,e),b(re),b(O.$$.fragment,e),b(D.$$.fragment,e),b(ce),b(M.$$.fragment,e),U=!0)},o(e){C(k.$$.fragment,e),C(re),C(O.$$.fragment,e),C(D.$$.fragment,e),C(ce),C(M.$$.fragment,e),U=!1},d(e){e&&(g(t),g(i),g(o),g(a),g(r),g(c),g(d),g(u),g(p),g(f),g(h),g(m),g(y),g(v),g(w),g(T),g(A),g(L),g(E),g(_),g(F),g(W),g(I),g(B)),Y&&Y.d(e),ee.d(e),g(n),g(s),ne&&ne.d(e),g(l),oe.d(e),J(k,e),re&&re.d(e),J(O,e),J(D,e),ce&&ce.d(e),J(M,e)}}}const Te={title:"Evidence Dashboard for Domo"};function mc(e,t,n){let s,l;Be(e,Bs,(e=>n(14,s=e))),Be(e,hn,(e=>n(19,l=e)));let{data:i}=t,{data:o={},customFormattingSettings:a,__db:r,inputs:c}=i;zi(hn,l="6666cd76f96956469e7be39d750cc7d9",l);let d=Wr(Jl(c));Ni(d.subscribe((e=>n(0,c=e)))),Al(Yr,{getCustomFormats:()=>a.customFormats||[]});const u=(e,t)=>Qr(r.query,e,{query_name:t});Xr(u),s.params,rl((()=>!0));let p={initialData:void 0,initialError:void 0},f=Yl`select
      category
  from needful_things.orders
  group by category`,h="select\n      category\n  from needful_things.orders\n  group by category";o.categories_data&&(o.categories_data instanceof Error?p.initialError=o.categories_data:p.initialData=o.categories_data,o.categories_columns&&(p.knownColumns=o.categories_columns));let $,g=!1;const m=Tl.createReactive({callback:e=>{n(1,$=e)},execFn:u},{id:"categories",...p});m(h,{noResolve:f,...p}),globalThis[Symbol.for("categories")]={get value(){return $}};let y={initialData:void 0,initialError:void 0},b=Yl`select
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${c.category.value}'
  and date_part('year', order_datetime) like '${c.year.value}'
  group by all
  order by sales_usd desc`,x=`select\n      date_trunc('month', order_datetime) as month,\n      sum(sales) as sales_usd,\n      category\n  from needful_things.orders\n  where category like '${c.category.value}'\n  and date_part('year', order_datetime) like '${c.year.value}'\n  group by all\n  order by sales_usd desc`;o.orders_by_category_data&&(o.orders_by_category_data instanceof Error?y.initialError=o.orders_by_category_data:y.initialData=o.orders_by_category_data,o.orders_by_category_columns&&(y.knownColumns=o.orders_by_category_columns));let v,w=!1;const C=Tl.createReactive({callback:e=>{n(2,v=e)},execFn:u},{id:"orders_by_category",...y});C(x,{noResolve:b,...y}),globalThis[Symbol.for("orders_by_category")]={get value(){return v}};let S=!1;return typeof window<"u"&&(S=typeof window.domo<"u"),e.$$set=e=>{"data"in e&&n(4,i=e.data)},e.$$.update=()=>{16&e.$$.dirty&&n(5,({data:o={},customFormattingSettings:a,__db:r}=i),o),32&e.$$.dirty&&Ur.set(Object.keys(o).length>0),16384&e.$$.dirty&&s.params,960&e.$$.dirty&&(f||!g?f||(m(h,{noResolve:f,...p}),n(9,g=!0)):m(h,{noResolve:f})),1&e.$$.dirty&&n(11,b=Yl`select
      date_trunc('month', order_datetime) as month,
      sum(sales) as sales_usd,
      category
  from needful_things.orders
  where category like '${c.category.value}'
  and date_part('year', order_datetime) like '${c.year.value}'
  group by all
  order by sales_usd desc`),1&e.$$.dirty&&n(12,x=`select\n      date_trunc('month', order_datetime) as month,\n      sum(sales) as sales_usd,\n      category\n  from needful_things.orders\n  where category like '${c.category.value}'\n  and date_part('year', order_datetime) like '${c.year.value}'\n  group by all\n  order by sales_usd desc`),15360&e.$$.dirty&&(b||!w?b||(C(x,{noResolve:b,...y}),n(13,w=!0)):C(x,{noResolve:b}))},n(7,f=Yl`select
      category
  from needful_things.orders
  group by category`),n(8,h="select\n      category\n  from needful_things.orders\n  group by category"),[c,$,v,S,i,o,p,f,h,g,y,b,x,w,s]}class vc extends Ae{constructor(e){super(),Se(this,e,mc,dc,me,{data:4})}}export{vc as component};