import{s as l,c as i,u as r,g as u,a as f}from"../chunks/scheduler.C5eBzNnH.js";import{S as _,i as c,t as p,a as m}from"../chunks/index.BSd9q3aW.js";function $(s){let n;const t=s[1].default,c=i(t,s,s[0],null);return{c(){c&&c.c()},l(s){c&&c.l(s)},m(s,t){c&&c.m(s,t),n=!0},p(s,[e]){c&&c.p&&(!n||1&e)&&r(c,t,s,s[0],n?f(t,s[0],e,null):u(s[0]),null)},i(s){n||(m(c,s),n=!0)},o(s){p(c,s),n=!1},d(s){c&&c.d(s)}}}function d(s,n,t){let{$$slots:c={},$$scope:e}=n;return s.$$set=s=>{"$$scope"in s&&t(0,e=s.$$scope)},[e,c]}class S extends _{constructor(s){super(),c(this,s,d,$,l,{})}}export{S as component};