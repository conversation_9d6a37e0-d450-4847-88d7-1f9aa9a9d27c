import{F as le,G as ne,H as R,I as ae,s as K,d as b,i as k,r as L,v as O,e as E,b as C,x as N,h as v,j as T,y as q,m as $,J as S,l as Q,k as j,n as I,q as B}from"../chunks/scheduler.CXt6djuF.js";import{g as H,t as w,c as P,a as g,S as X,i as Y,d as D,m as M,b as U,e as F}from"../chunks/index.DP2zcclO.js";import{h as A,u as Z,o as ee,I as te,j as se,T as re,k as oe}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.Dia6OioU.js";import"../chunks/entry.CYIy1i0o.js";function ce(r,e){const l=e.token={};function t(n,o,f,_){if(e.token!==l)return;e.resolved=_;let i=e.ctx;f!==void 0&&(i=i.slice(),i[f]=_);const a=n&&(e.current=n)(i);let s=!1;e.block&&(e.blocks?e.blocks.forEach((m,c)=>{c!==o&&m&&(H(),w(m,1,1,()=>{e.blocks[c]===m&&(e.blocks[c]=null)}),P())}):e.block.d(1),a.c(),g(a,1),a.m(e.mount(),e.anchor),s=!0),e.block=a,e.blocks&&(e.blocks[o]=a),s&&ae()}if(le(r)){const n=ne();if(r.then(o=>{R(n),t(e.then,1,e.value,o),R(null)},o=>{if(R(n),t(e.catch,2,e.error,o),R(null),!e.hasCatch)throw o}),e.current!==e.pending)return t(e.pending,0),!0}else{if(e.current!==e.then)return t(e.then,1,e.value,r),!0;e.resolved=r}}function ie(r,e,l){const t=e.slice(),{resolved:n}=r;r.current===r.then&&(t[r.value]=n),r.current===r.catch&&(t[r.error]=n),r.block.p(t,l)}function V(r,e,l){const t=r.slice();return t[8]=e[l][0],t[9]=e[l][1],t}function W(r,e,l){const t=r.slice();return t[12]=e[l][0],t[13]=e[l][1],t}function ue(r){let e,l,t=r[16].message+"",n;return{c(){e=q(`An error was encountered while loading project schema.

	`),l=$("pre"),n=q(t),this.h()},l(o){e=N(o,`An error was encountered while loading project schema.

	`),l=v(o,"PRE",{class:!0});var f=T(l);n=N(f,t),f.forEach(b),this.h()},h(){C(l,"class","px-4 py-2 bg-negative")},m(o,f){k(o,e,f),k(o,l,f),E(l,n)},p:O,i:O,o:O,d(o){o&&(b(e),b(l))}}}function fe(r){let e,l,t,n=[],o=new Map,f,_=A(Object.entries(r[7]));const i=a=>a[8];for(let a=0;a<_.length;a+=1){let s=V(r,_,a),m=i(s);o.set(m,n[a]=z(m,s))}return{c(){e=$("section"),l=$("div"),t=$("ul");for(let a=0;a<n.length;a+=1)n[a].c();this.h()},l(a){e=v(a,"SECTION",{});var s=T(e);l=v(s,"DIV",{});var m=T(l);t=v(m,"UL",{class:!0});var c=T(t);for(let p=0;p<n.length;p+=1)n[p].l(c);c.forEach(b),m.forEach(b),s.forEach(b),this.h()},h(){C(t,"class","list-none m-0 p-0 flex flex-col gap-1 mb-1")},m(a,s){k(a,e,s),E(e,l),E(l,t);for(let m=0;m<n.length;m+=1)n[m]&&n[m].m(t,null);f=!0},p(a,s){s&7&&(_=A(Object.entries(a[7])),H(),n=Z(n,s,i,1,a,_,o,t,ee,z,null,V),P())},i(a){if(!f){for(let s=0;s<_.length;s+=1)g(n[s]);f=!0}},o(a){for(let s=0;s<n.length;s+=1)w(n[s]);f=!1},d(a){a&&b(e);for(let s=0;s<n.length;s+=1)n[s].d()}}}function x(r){let e,l=[],t=new Map,n,o,f=A(Object.entries(r[9]));const _=i=>i[12];for(let i=0;i<f.length;i+=1){let a=W(r,f,i),s=_(a);t.set(s,l[i]=J(s,a))}return{c(){e=$("ul");for(let i=0;i<l.length;i+=1)l[i].c();n=I(),this.h()},l(i){e=v(i,"UL",{class:!0});var a=T(e);for(let s=0;s<l.length;s+=1)l[s].l(a);n=j(a),a.forEach(b),this.h()},h(){C(e,"class","list-none m-0 flex flex-col gap-1")},m(i,a){k(i,e,a);for(let s=0;s<l.length;s+=1)l[s]&&l[s].m(e,null);E(e,n),o=!0},p(i,a){a&5&&(f=A(Object.entries(i[9])),H(),l=Z(l,a,_,1,i,f,t,e,ee,J,n,W),P())},i(i){if(!o){for(let a=0;a<f.length;a+=1)g(l[a]);o=!0}},o(i){for(let a=0;a<l.length;a+=1)w(l[a]);o=!1},d(i){i&&b(e);for(let a=0;a<l.length;a+=1)l[a].d()}}}function G(r){let e,l;return e=new oe({props:{columns:r[13].columns,rowClass:"ml-6 "}}),{c(){F(e.$$.fragment)},l(t){U(e.$$.fragment,t)},m(t,n){M(e,t,n),l=!0},p:O,i(t){l||(g(e.$$.fragment,t),l=!0)},o(t){w(e.$$.fragment,t),l=!1},d(t){D(e,t)}}}function J(r,e){let l,t,n,o,f=e[12]+"",_,i,a,s,m,c;n=new te({props:{src:re,class:"w-5 h-5 mr-1"}});function p(){return e[5](e[13])}let u=e[0]===e[13]&&G(e);return{key:r,first:null,c(){l=$("li"),t=$("button"),F(n.$$.fragment),o=I(),_=q(f),i=I(),u&&u.c(),a=L(),this.h()},l(h){l=v(h,"LI",{class:!0});var d=T(l);t=v(d,"BUTTON",{class:!0});var y=T(t);U(n.$$.fragment,y),o=j(y),_=N(y,f),y.forEach(b),d.forEach(b),i=j(h),u&&u.l(h),a=L(),this.h()},h(){C(t,"class","bg-base-200 px-2 py-1 rounded-sm flex w-full hover:bg-base-300 hover:text-base-content"),S(t,"bg-info",e[0]===e[13]),S(t,"text-info-content",e[0]===e[13]),C(l,"class","font-mono m-0 text-sm font-bold ml-3"),this.first=l},m(h,d){k(h,l,d),E(l,t),M(n,t,null),E(t,o),E(t,_),k(h,i,d),u&&u.m(h,d),k(h,a,d),s=!0,m||(c=Q(t,"click",p),m=!0)},p(h,d){e=h,(!s||d&5)&&S(t,"bg-info",e[0]===e[13]),(!s||d&5)&&S(t,"text-info-content",e[0]===e[13]),e[0]===e[13]?u?(u.p(e,d),d&1&&g(u,1)):(u=G(e),u.c(),g(u,1),u.m(a.parentNode,a)):u&&(H(),w(u,1,1,()=>{u=null}),P())},i(h){s||(g(n.$$.fragment,h),g(u),s=!0)},o(h){w(n.$$.fragment,h),w(u),s=!1},d(h){h&&(b(l),b(i),b(a)),D(n),u&&u.d(h),m=!1,c()}}}function z(r,e){let l,t,n,o,f=e[8]+"",_,i,a,s,m,c;n=new te({props:{src:se,class:"w-5 h-5 mr-1"}});function p(){return e[4](e[8])}let u=e[1]===e[8]&&x(e);return{key:r,first:null,c(){l=$("li"),t=$("button"),F(n.$$.fragment),o=I(),_=q(f),i=I(),u&&u.c(),a=L(),this.h()},l(h){l=v(h,"LI",{class:!0});var d=T(l);t=v(d,"BUTTON",{class:!0});var y=T(t);U(n.$$.fragment,y),o=j(y),_=N(y,f),y.forEach(b),d.forEach(b),i=j(h),u&&u.l(h),a=L(),this.h()},h(){C(t,"class","bg-base-200 px-2 py-1 rounded-sm font-bold flex w-full hover:bg-base-300 hover:text-base-content"),S(t,"bg-info",e[1]===e[8]),S(t,"text-info-content",e[1]===e[8]),C(l,"class","font-mono m-0 text-sm"),this.first=l},m(h,d){k(h,l,d),E(l,t),M(n,t,null),E(t,o),E(t,_),k(h,i,d),u&&u.m(h,d),k(h,a,d),s=!0,m||(c=Q(t,"click",p),m=!0)},p(h,d){e=h,(!s||d&6)&&S(t,"bg-info",e[1]===e[8]),(!s||d&6)&&S(t,"text-info-content",e[1]===e[8]),e[1]===e[8]?u?(u.p(e,d),d&2&&g(u,1)):(u=x(e),u.c(),g(u,1),u.m(a.parentNode,a)):u&&(H(),w(u,1,1,()=>{u=null}),P())},i(h){s||(g(n.$$.fragment,h),g(u),s=!0)},o(h){w(n.$$.fragment,h),w(u),s=!1},d(h){h&&(b(l),b(i),b(a)),D(n),u&&u.d(h),m=!1,c()}}}function he(r){let e;return{c(){e=q("Loading Schema Information...")},l(l){e=N(l,"Loading Schema Information...")},m(l,t){k(l,e,t)},p:O,i:O,o:O,d(l){l&&b(e)}}}function _e(r){let e,l,t={ctx:r,current:null,token:null,hasCatch:!0,pending:he,then:fe,catch:ue,value:7,error:16,blocks:[,,,]};return ce(r[2](),t),{c(){e=L(),t.block.c()},l(n){e=L(),t.block.l(n)},m(n,o){k(n,e,o),t.block.m(n,t.anchor=o),t.mount=()=>e.parentNode,t.anchor=e,l=!0},p(n,[o]){r=n,ie(t,r,o)},i(n){l||(g(t.block),l=!0)},o(n){for(let o=0;o<3;o+=1){const f=t.blocks[o];w(f)}l=!1},d(n){n&&b(e),t.block.d(n),t.token=null,t=null}}}function me(r,e,l){let{data:t}=e,{__db:n}=t;async function o(){const s=await n.query("SELECT * FROM information_schema.tables WHERE table_catalog = 'memory' AND table_name != 'stats'"),m={};return await Promise.all(s.map(async c=>{const p=await n.query(`SELECT * FROM information_schema.columns WHERE table_name = '${c.table_name}' AND table_schema = '${c.table_schema}'`);m[c.table_schema]||(m[c.table_schema]={}),m[c.table_schema][c.table_name]={table:c,columns:p}})),m}let f="",_="";const i=s=>{l(1,_=_===s?"":s),l(0,f="")},a=s=>{l(0,f=f===s?"":s)};return r.$$set=s=>{"data"in s&&l(3,t=s.data)},[f,_,o,t,i,a]}class be extends X{constructor(e){super(),Y(this,e,me,_e,K,{data:3})}}function de(r){let e,l="Project Schema",t,n,o="This page details the tables and columns that are currently loaded in your project.",f,_,i="Sources",a,s,m;return s=new be({props:{data:r[0]}}),{c(){e=$("h1"),e.textContent=l,t=I(),n=$("p"),n.textContent=o,f=I(),_=$("h2"),_.textContent=i,a=I(),F(s.$$.fragment),this.h()},l(c){e=v(c,"H1",{class:!0,"data-svelte-h":!0}),B(e)!=="svelte-15777oi"&&(e.textContent=l),t=j(c),n=v(c,"P",{class:!0,"data-svelte-h":!0}),B(n)!=="svelte-ak948l"&&(n.textContent=o),f=j(c),_=v(c,"H2",{class:!0,"data-svelte-h":!0}),B(_)!=="svelte-9qt1ro"&&(_.textContent=i),a=j(c),U(s.$$.fragment,c),this.h()},h(){C(e,"class","markdown"),C(n,"class","markdown"),C(_,"class","markdown")},m(c,p){k(c,e,p),k(c,t,p),k(c,n,p),k(c,f,p),k(c,_,p),k(c,a,p),M(s,c,p),m=!0},p(c,[p]){const u={};p&1&&(u.data=c[0]),s.$set(u)},i(c){m||(g(s.$$.fragment,c),m=!0)},o(c){w(s.$$.fragment,c),m=!1},d(c){c&&(b(e),b(t),b(n),b(f),b(_),b(a)),D(s,c)}}}function pe(r,e,l){let{data:t}=e;return r.$$set=n=>{"data"in n&&l(0,t=n.data)},[t]}class we extends X{constructor(e){super(),Y(this,e,pe,de,K,{data:0})}}export{we as component};
