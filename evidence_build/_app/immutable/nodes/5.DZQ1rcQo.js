import{s as Y,d as h,z as ne,i as k,e as Q,A as re,l as V,b as C,h as S,q as le,k as B,j as q,m as R,n as D,v as Z,B as ie,t as ae,w as oe,x as H,y as T,p as ue,C as fe,D as ce,r as N,E as me}from"../chunks/scheduler.CXt6djuF.js";import{S as $,i as x,t as E,a as p,g as O,c as j,d as z,m as A,b as I,e as P,f as L}from"../chunks/index.DP2zcclO.js";import{s as de,b as M,a as _e,g as he,E as W,c as F,d as be,D as ge,e as G}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.DJJFwHIT.js";import"../chunks/entry.CnNRdqOo.js";import{B as J}from"../chunks/Button.GxjirqJK.js";function K(l){let e,t,r,s,n;return t=new J({props:{size:"sm",outline:!0,icon:l[1]?W:F,$$slots:{default:[ye]},$$scope:{ctx:l}}}),t.$on("click",l[12]),s=new J({props:{size:"sm",variant:"positive",outline:!0,icon:be,$$slots:{default:[we]},$$scope:{ctx:l}}}),s.$on("click",l[13]),{c(){e=R("div"),P(t.$$.fragment),r=D(),P(s.$$.fragment),this.h()},l(i){e=S(i,"DIV",{class:!0});var m=q(e);I(t.$$.fragment,m),r=B(m),I(s.$$.fragment,m),m.forEach(h),this.h()},h(){C(e,"class","absolute bottom-2 right-2 z-10 flex gap-2")},m(i,m){k(i,e,m),A(t,e,null),Q(e,r),A(s,e,null),n=!0},p(i,m){const g={};m&2&&(g.icon=i[1]?W:F),m&131074&&(g.$$scope={dirty:m,ctx:i}),t.$set(g);const b={};m&131072&&(b.$$scope={dirty:m,ctx:i}),s.$set(b)},i(i){n||(p(t.$$.fragment,i),p(s.$$.fragment,i),n=!0)},o(i){E(t.$$.fragment,i),E(s.$$.fragment,i),n=!1},d(i){i&&h(e),z(t),z(s)}}}function pe(l){let e;return{c(){e=T("Show Results")},l(t){e=H(t,"Show Results")},m(t,r){k(t,e,r)},d(t){t&&h(e)}}}function ke(l){let e;return{c(){e=T("Hide Results")},l(t){e=H(t,"Hide Results")},m(t,r){k(t,e,r)},d(t){t&&h(e)}}}function ye(l){let e;function t(n,i){return n[1]?ke:pe}let r=t(l),s=r(l);return{c(){s.c(),e=N()},l(n){s.l(n),e=N()},m(n,i){s.m(n,i),k(n,e,i)},p(n,i){r!==(r=t(n))&&(s.d(1),s=r(n),s&&(s.c(),s.m(e.parentNode,e)))},d(n){n&&h(e),s.d(n)}}}function we(l){let e;return{c(){e=T("Submit")},l(t){e=H(t,"Submit")},m(t,r){k(t,e,r)},d(t){t&&h(e)}}}function U(l){let e,t=l[9].error+"",r;return{c(){e=R("pre"),r=T(t),this.h()},l(s){e=S(s,"PRE",{class:!0});var n=q(e);r=H(n,t),n.forEach(h),this.h()},h(){C(e,"class","text-negative text-xs font-mono")},m(s,n){k(s,e,n),Q(e,r)},p(s,n){n&512&&t!==(t=s[9].error+"")&&oe(r,t)},d(s){s&&h(e)}}}function X(l){let e,t,r,s;return t=new ge({props:{data:l[9]}}),{c(){e=R("div"),P(t.$$.fragment)},l(n){e=S(n,"DIV",{});var i=q(e);I(t.$$.fragment,i),i.forEach(h)},m(n,i){k(n,e,i),A(t,e,null),s=!0},p(n,i){const m={};i&512&&(m.data=n[9]),t.$set(m)},i(n){s||(p(t.$$.fragment,n),n&&ue(()=>{s&&(r||(r=L(e,G,{},!0)),r.run(1))}),s=!0)},o(n){E(t.$$.fragment,n),n&&(r||(r=L(e,G,{},!1)),r.run(0)),s=!1},d(n){n&&h(e),z(t),n&&r&&r.end()}}}function Ee(l){let e,t="SQL Console",r,s,n,i,m,g=l[9].error&&!l[2]&&!!l[4],b,y,_,w,a=!l[3]&&K(l),u=g&&U(l),f=l[1]&&X(l);return{c(){e=R("h1"),e.textContent=t,r=D(),s=R("section"),n=R("div"),a&&a.c(),m=D(),u&&u.c(),b=D(),f&&f.c(),this.h()},l(o){e=S(o,"H1",{class:!0,"data-svelte-h":!0}),le(e)!=="svelte-7ylf69"&&(e.textContent=t),r=B(o),s=S(o,"SECTION",{class:!0,role:!0});var d=q(s);n=S(d,"DIV",{class:!0});var v=q(n);a&&a.l(v),v.forEach(h),m=B(d),u&&u.l(d),b=B(d),f&&f.l(d),d.forEach(h),this.h()},h(){C(e,"class","markdown"),C(n,"class","w-full relative rounded-sm border border-base-300 min-h-[8rem] cursor-text **:[&.cm-editor]:min-h-[8rem] **:[&.cm-editor]:rounded-sm"),C(s,"class","px-0 py-2 flex flex-col gap-2 min-h-[8rem]"),C(s,"role","none")},m(o,d){k(o,e,d),k(o,r,d),k(o,s,d),Q(s,n),a&&a.m(n,null),l[14](n),Q(s,m),u&&u.m(s,null),Q(s,b),f&&f.m(s,null),y=!0,_||(w=[re(i=de.call(null,n,{...l[5],theme:l[8]})),V(s,"click",l[15]),V(s,"keydown",l[16])],_=!0)},p(o,[d]){o[3]?a&&(O(),E(a,1,1,()=>{a=null}),j()):a?(a.p(o,d),d&8&&p(a,1)):(a=K(o),a.c(),p(a,1),a.m(n,null)),i&&me(i.update)&&d&288&&i.update.call(null,{...o[5],theme:o[8]}),d&532&&(g=o[9].error&&!o[2]&&!!o[4]),g?u?u.p(o,d):(u=U(o),u.c(),u.m(s,b)):u&&(u.d(1),u=null),o[1]?f?(f.p(o,d),d&2&&p(f,1)):(f=X(o),f.c(),p(f,1),f.m(s,null)):f&&(O(),E(f,1,1,()=>{f=null}),j())},i(o){y||(p(a),p(f),y=!0)},o(o){E(a),E(f),y=!1},d(o){o&&(h(e),h(r),h(s)),a&&a.d(),l[14](null),u&&u.d(),f&&f.d(),_=!1,ne(w)}}}function Ce(l,e,t){let r,s,n=Z,i=()=>(n(),n=fe(a,c=>t(9,s=c)),a);l.$$.on_destroy.push(()=>n());let{hideErrors:m=!1}=e,{initialQuery:g="select 'ABC' as category, 123 as num, 26400000 as sales_usd"}=e,{showResults:b=!0}=e,{disabled:y=!1}=e,_=g,w=_,{data:a=M(_)}=e;i();let u,f;ie(async()=>{a&&a.fetch(),t(5,f={initialState:g,disabled:y,schema:await _e(),onChange:c=>{c.docChanged&&w.trim()!==c.state.doc.toString().trim()&&t(6,w=c.state.doc.toString())},onSubmit:()=>(t(4,_=w.trim()),_.endsWith(";")&&t(4,_=_.substring(0,_.length-1)),t(1,b=!0),!0)})});const{theme:o}=he();ae(l,o,c=>t(8,r=c));const d=()=>t(1,b=!b),v=()=>{t(4,_=w),t(1,b=!0)};function ee(c){ce[c?"unshift":"push"](()=>{u=c,t(7,u)})}const te=()=>u==null?void 0:u.focus(),se=c=>c.key==="Enter"&&(u==null?void 0:u.focus());return l.$$set=c=>{"hideErrors"in c&&t(2,m=c.hideErrors),"initialQuery"in c&&t(11,g=c.initialQuery),"showResults"in c&&t(1,b=c.showResults),"disabled"in c&&t(3,y=c.disabled),"data"in c&&i(t(0,a=c.data))},l.$$.update=()=>{l.$$.dirty&17&&_&&(i(t(0,a=M(_))),a.fetch()),l.$$.dirty&40&&f&&t(5,f.disabled=y,f)},[a,b,m,y,_,f,w,u,r,s,o,g,d,v,ee,te,se]}class Se extends ${constructor(e){super(),x(this,e,Ce,Ee,Y,{hideErrors:2,initialQuery:11,showResults:1,disabled:3,data:0})}}function Re(l){let e,t;return e=new Se({}),{c(){P(e.$$.fragment)},l(r){I(e.$$.fragment,r)},m(r,s){A(e,r,s),t=!0},p:Z,i(r){t||(p(e.$$.fragment,r),t=!0)},o(r){E(e.$$.fragment,r),t=!1},d(r){z(e,r)}}}class ze extends ${constructor(e){super(),x(this,e,null,Re,Y,{})}}export{ze as component};
